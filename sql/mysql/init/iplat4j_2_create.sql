CREATE TABLE ED_XM_2PC_LOG (
                               LOG_ID VARCHAR(36) NOT NULL,
                               TRANSACTION_ID VARCHAR(50) NOT NULL,
                               SERVICE_NAME VARCHAR(20) NOT NULL,
                               SERVICE_ID VARCHAR(20) NOT NULL,
                               METHOD_NAME VARCHAR(50) NOT NULL,
                               STATUS VARCHAR(2) NOT NULL,
                               LOG_INFO VARCHAR(1000) NOT NULL,
                               TIME_STAMP VARCHAR(17),
                               TRACE_ID VARCHAR(50) NOT NULL,
                               PRIMARY KEY (LOG_ID)
)COMMENT='2PC日志信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
CREATE INDEX IDX_2PC_LOG_TRACE_TRANS_ID ON ED_XM_2PC_LOG (TRACE_ID, TRANSACTION_ID);
CREATE INDEX IDX_2PC_LOG_TIME_STAMP ON ED_XM_2PC_LOG (TIME_STAMP);

CREATE TABLE ED_XM_EVENT (
                             EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
                             EVENT_DESC VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '事件描述',
                             SYNC_TYPE VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '同/异步标志',
                             REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                             REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                             REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                             REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                             IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '是否授权',
                             TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                             ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                             CONSTRAINT PK_ED_XM_EVENT PRIMARY KEY (EVENT_ID)
)COMMENT='微服务事件表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT_PARAM (
                                   PARAM_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微事件参数标识',
                                   EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
                                   PARAM_KEY VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '参数英文名',
                                   PARAM_KEY_CNAME VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '参数中文名',
                                   PARAM_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '参数类型',
                                   PARAM_DEF_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '参数缺省值',
                                   REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                                   REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                                   REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                                   REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                                   TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                                   ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                                   CONSTRAINT PK_ED_XM_EVENT_PARAM PRIMARY KEY (PARAM_ID)
)COMMENT='微事件参数信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT_ROUTE (
                                   EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
                                   SERVICE_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务标识',
                                   ROUTE_KEY VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '路由键',
                                   ROUTE_VALUE VARCHAR(100) NOT NULL COMMENT '路由值',
                                   REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                                   REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                                   REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                                   REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                                   TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                                   ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                                   CONSTRAINT PK_ED_XM_EVENT_ROUTE PRIMARY KEY (EVENT_ID, SERVICE_ID, ROUTE_KEY)
)COMMENT='路由表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE ED_XM_EVENT_SERVICE_RELA (
                                          EVENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '事件标识',
                                          SERVICE_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务标识',
                                          SORT_INDEX VARCHAR(3) DEFAULT ' ' NOT NULL COMMENT '排序',
                                          TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                                          ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                                          REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                                          REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                                          REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                                          REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                                          CONSTRAINT PK_ED_XM_EVENT_SERVICE_RELA PRIMARY KEY (EVENT_ID, SERVICE_ID, SORT_INDEX)
)COMMENT='微服务事件服务关联表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE ED_XM_PARAM (
                             PARAM_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务参数标识',
                             SERVICE_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '微服务标识',
                             PARAM_KEY VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '参数英文名',
                             PARAM_KEY_CNAME VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '参数中文名',
                             PARAM_TYPE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '参数类型',
                             PARAM_DEF_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '参数缺省值',
                             REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                             REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                             REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                             REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL  COMMENT '记录修改时刻',
                             TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                             ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                             CONSTRAINT PK_ED_XM_PARAM PRIMARY KEY (PARAM_ID)
)COMMENT='微服务参数信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE ED_XM_SERVICE (
                               SERVICE_ID VARCHAR(64) NOT NULL COMMENT '微服务标识',
                               SERVICE_ENAME VARCHAR(64) COMMENT '服务英文名',
                               METHOD_ENAME VARCHAR(64) COMMENT '方法英文名',
                               SERVICE_TYPE VARCHAR(16) COMMENT '服务类型',
                               SERVICE_DESC VARCHAR(256) COMMENT '中文描述',
                               URL VARCHAR(256) COMMENT 'URL',
                               REMARK VARCHAR(512) COMMENT '备注',
                               REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                               REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                               REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                               REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                               IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '是否授权',
                               TRANS_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '事务类型',
                               TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                               ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                               CONSTRAINT PK_ED_XM_SERVICE PRIMARY KEY (SERVICE_ID)
)COMMENT='微服务信息表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table ED_XM_SERVICE_DOC
(
    SERVICE_ID     varchar(64)            not null comment '服务ID',
    DOMAIN_NAME    varchar(50)            not null comment '参数对象名称',
    DATA_TYPE      varchar(1) default '0' not null comment '参数类型',
    OPERATION_TYPE varchar(1) default '0' not null comment '传入/传出参数',
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
    constraint ED_XM_SERVICE_DOC_PK
        primary key (SERVICE_ID, OPERATION_TYPE)
) comment = '服务文档说明表'  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


create table ED_XM_SERVICE_DOC_FIELD
(
    DOMAIN_NAME    varchar(50)  not null comment '参数对象名称',
    FIELD_NAME    varchar(50)  not null comment '属性名称',
    SORT_INDEX    int not null comment '排序',
    FIELD_DESC    varchar(255)   not null comment '简要描述',
    NOTES    TEXT  comment '详细描述',
    DATA_TYPE  varchar(20)   comment '数据类型',
    ALLOWABLE_VALS  varchar(255)   comment '可允许值',
    NULLABLE varchar(1)  not null default '0' comment '允许为空',
    EXAMPLE varchar(255) not null default ' ' comment '示例值',
    FIELD_REFERENCE varchar(50) not null default ' ' comment '引用类型',
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
    constraint ED_XM_SERVICE_DOC_FIELD_PK
        primary key (DOMAIN_NAME,FIELD_NAME)
) comment = '服务文档对象明细表'  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table ED_XM_SERVICE_DOC_OBJ
(
    DOMAIN_NAME    varchar(50)  not null comment '参数对象名称',
    DOMAIN_DESC    varchar(255)   not null comment '参数对象描述',
    CLASS_PATH    varchar(255)    comment '类完整路径',
    DATA_TYPE      varchar(1)   comment '参数类型',
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
    constraint ED_XM_SERVICE_DOC_OBJ_PK
        primary key (DOMAIN_NAME)
) comment = '服务文档对象表'  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE
    ED_XM_SERVICE_RES_RELA
(
    SERVICE_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '服务英文名',
    METHOD_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '方法英文名',
    FORM_ENAME VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '画面英文名',
    BUTTON_ENAME VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '按钮英文名',
    RES_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '资源ID',
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任人',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '租户ID',
    CONSTRAINT PK_SERVICE_RES_RELA PRIMARY KEY (SERVICE_ENAME,METHOD_ENAME,FORM_ENAME,BUTTON_ENAME),
    KEY IDX_SERVICE_RES_RELA (RES_ID)
)COMMENT='按钮服务关系表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE HEDCC03
(
    LOG_UUID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '记录UUID',
    CONFIG_TYPE VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置项类别',
    STATUS DECIMAL(11) COMMENT '状态：1是正常 0是删除',
    CONFIG_CUE VARCHAR(255) COMMENT '配置项提示',
    CONFIG_ENV_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '配置环境',
    FKEY VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置名称',
    TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '租户ID',
    CONFIG_DESC VARCHAR(255) COMMENT '配置描述',
    OPERATOR VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '操作人工号',
    OPERATE_TYPE VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '操作方式',
    OPERATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '操作时间',
    ORI_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '原有值',
    NEW_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '新fvalue',
    CONSTRAINT PK_HEDCC03 PRIMARY KEY (LOG_UUID)
)COMMENT='配置信息履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create index HEDCC03_INDEX
    on HEDCC03 (FKEY, OPERATE_TIME);

create table HEPLG01
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null,
    CONSTRAINT PK_HEPLG01 PRIMARY KEY (OPERATION_ID)
)COMMENT='审计日志_登录登出认证_履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table HEPLG02
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null,
    CONSTRAINT PK_HEPLG02 PRIMARY KEY (OPERATION_ID)
)COMMENT='审计日志_页面按钮分发_履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


create table HEPLG03
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null,
    CONSTRAINT PK_HEPLG03 PRIMARY KEY (OPERATION_ID)
)COMMENT='审计日志_HTTP请求_履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

-- admin
CREATE TABLE `tedcc01`
(
    `ID`              varchar(100) NOT NULL COMMENT '配置ID',
    `MODULE_NAME`     varchar(100) NOT NULL COMMENT '组件名称',
    `CONFIG_ENV`      varchar(100) NOT NULL COMMENT '配置环境',
    `FKEY`            varchar(100) NOT NULL COMMENT '配置名称',
    `FVALUE`          varchar(100) NOT NULL COMMENT '配置内容',
    `REC_CREATOR`     varchar(100)          DEFAULT NULL COMMENT '创建人',
    `REC_CREATE_TIME` VARCHAR(14) not null default ' ' COMMENT '创建时间',
    `REC_REVISOR`     varchar(100)          DEFAULT NULL COMMENT '修改人',
    `REC_REVISE_TIME`  VARCHAR(14) not null default ' ' COMMENT '修改时间',
    `DES` varchar(255) DEFAULT ' ' COMMENT '描述',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `tedcc01_un` (`MODULE_NAME`,`CONFIG_ENV`,`FKEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='动态加载配置信息';

CREATE TABLE TEDCC02 (
                         CONFIG_ENV_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '配置环境标识',
                         CONFIG_ENV_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置环境中文名',
                         PROJECT VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '项目',
                         VERSION VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '版本',
                         PROJECT_ENV VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '环境',
                         REC_CREATOR VARCHAR(16) COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) COMMENT '记录修改时刻',
                         MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '一级模块',
                         MODULE_ENAME_2 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '二级模块',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDCC02 PRIMARY KEY (CONFIG_ENV_ID)
)COMMENT='配置环境' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDCC03 (
                         STATUS INTEGER DEFAULT '1' COMMENT '状态：1是正常 0是删除',
                         CONFIG_TYPE VARCHAR(255) COMMENT '配置项类别',
                         CONFIG_CUE VARCHAR(255) COMMENT '配置项提示',
                         REC_CREATOR VARCHAR(16) COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) COMMENT '记录修改时刻',
                         CONFIG_ENV_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '配置环境',
                         FKEY VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置名称',
                         FVALUE VARCHAR(4000) COMMENT '配置内容',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CONFIG_DESC VARCHAR(255),
                         CONSTRAINT PK_TEDCC03 PRIMARY KEY (CONFIG_ENV_ID, FKEY)
)COMMENT='配置信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEDCC03`
    MODIFY COLUMN `CONFIG_DESC`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '配置项描述' AFTER `ARCHIVE_FLAG`;


CREATE TABLE
    TEDCC12
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '创建人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '创建时间',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '修改时间',
    URL_CONFIG_ENAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '访问地址配置英文名',
    CONFIG_DESC VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '配置描述',
    MATCH_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '匹配模式',
    MATCH_EXPRESSION VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '匹配表达式',
    IS_AUTHORIZED VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '是否授权',
    CONFIG_ID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '配置ID',
    SORT_INDEX DECIMAL(5,0) DEFAULT 0 NOT NULL COMMENT '排序',
    CONSTRAINT PK_CONFIG_ID PRIMARY KEY (CONFIG_ID)
)COMMENT='访问地址管理表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDCM00 (
                         CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类编号',
                         CODESET_NAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码分类名称',
                         CODESET_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类英文名称',
                         GB_CODE VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '国标编号',
                         REMARK VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '备注',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         CODESET_TYPE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码类型',
                         CODESET_URL VARCHAR(2000) DEFAULT ' ' NOT NULL COMMENT '对应URL',
                         PROJECT_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '应用系统',
                         SUB_CODESET_CODE VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '子代码分类编号',
                         REF_ID VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '关联字段',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CODESET_LEVEL VARCHAR(64) DEFAULT ' ' NOT NULL,
                         CATEGORY VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '目录',
                         CONSTRAINT PK_TEDCM00 PRIMARY KEY (CODESET_CODE, PROJECT_NAME)
)COMMENT='代码大类表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEDCM00`
    MODIFY COLUMN `CODESET_LEVEL`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '代码管理层级' AFTER `ARCHIVE_FLAG`;

CREATE TABLE TEDCM0001 (
                           ID VARCHAR(64) DEFAULT ' ' NOT NULL,
                           CATEGORY_KEY VARCHAR(64) DEFAULT ' ' NOT NULL,
                           CATEGORY_NAME VARCHAR(255) DEFAULT ' ' NOT NULL,
                           PARENT_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
                           REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL,
                           REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
                           REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL,
                           REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
                           ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
                           CONSTRAINT PK_TEDCM0001 PRIMARY KEY (ID)
)COMMENT='代码分类目录表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
ALTER TABLE `TEDCM0001`
    MODIFY COLUMN `ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '树ID' FIRST ,
    MODIFY COLUMN `CATEGORY_KEY`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '树节点英文名' AFTER `ID`,
    MODIFY COLUMN `CATEGORY_NAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '树节点中文名' AFTER `CATEGORY_KEY`,
    MODIFY COLUMN `PARENT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '父节点' AFTER `CATEGORY_NAME`,
    MODIFY COLUMN `REC_CREATOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录创建责任者' AFTER `PARENT_ID`,
    MODIFY COLUMN `REC_CREATE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录创建时刻' AFTER `REC_CREATOR`,
    MODIFY COLUMN `REC_REVISOR`  varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录修改责任者' AFTER `REC_CREATE_TIME`,
    MODIFY COLUMN `REC_REVISE_TIME`  varchar(14) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '记录修改时刻' AFTER `REC_REVISOR`,
    MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '归档标记' AFTER `REC_REVISE_TIME`;

CREATE TABLE TEDCM01 (
                         CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类编号',
                         ITEM_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码明细编号',
                         ITEM_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码明细中文名称',
                         ITEM_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码明细英文名称',
                         REMARK VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '备注',
                         ITEM_STATUS VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '代码状态',
                         SORT_ID VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '顺序号',
                         STATUS VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '字段状态',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         PROJECT_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '应用系统',
                         SUB_CODESET_CODE VARCHAR(64) DEFAULT ' ' COMMENT '子代码分类编号',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ITEM_FILTER_CODE VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '代码明细筛选',
                         ITEM_FILTER_CNAME VARCHAR(510) DEFAULT ' ' NOT NULL COMMENT '代码明细筛选中文名称',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDCM01 PRIMARY KEY (CODESET_CODE, ITEM_CODE, PROJECT_NAME)
)COMMENT='代码详情表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDCM02 (
                         CASCADE_ID VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT 'ID',
                         CASCADE_CODESET_CODE VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '级联代码分类编号',
                         CASCADE_TYPE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '级联关系类型',
                         CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码分类编号',
                         ITEM_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '代码明细编号',
                         SUB_CODESET_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子代码分类编号',
                         SUB_ITEM_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子代码明细编号',
                         ACTIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '启用标记',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         PROJECT_NAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '应用系统',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDCM02 PRIMARY KEY (CASCADE_ID)
)COMMENT='代码级联关系表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDFA00 (
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         FORM_ENAME VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '画面英文名',
                         FORM_CNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '画面中文名',
                         FORM_LOAD_PATH VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '画面载入的路径',
                         FORM_TYPE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '画面类型',
                         MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '一级模块英文名',
                         MODULE_ENAME_2 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '二级模块英文名',
                         INIT_LOAD_SERVICE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '初始处理服务英文名',
                         IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT  '是否授权',
                         FORM_PARAM VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '画面参数',
                         SUBAPP_CODE VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
                         ICON_INFO VARCHAR(128) DEFAULT ' ' NOT NULL COMMENT '图标',
                         BUSINESS_CATEGORY VARCHAR(50) DEFAULT ' ' NOT NULL,
                         OPERATE_TYPE VARCHAR(50) DEFAULT ' ' NOT NULL,
                         IS_EFFECTIVE VARCHAR(1) DEFAULT '1' NOT NULL  comment '是否有效',
                         DIR_ID VARCHAR(32) DEFAULT ' ' NOT NULL comment '目录ID',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDFA00 PRIMARY KEY (FORM_ENAME)
)COMMENT ='画面信息定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;
CREATE INDEX IDX_TEDFA00_DIR_ID ON TEDFA00 (DIR_ID);

CREATE TABLE TEDFA01 (
                         REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
                         FORM_ENAME VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '画面英文名',
                         REGION_ID VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '区域标识',
                         BUTTON_ENAME VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '按钮英文名',
                         BUTTON_CNAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '按钮中文',
                         BUTTON_DESC VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '按钮描述',
                         NODE_SORT_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '节点排序标识',
                         IS_AUTH VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '是否授权',
                         URI VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
                         LAYOUT VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
                         POSITION VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '子系统代码',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         BUSINESS_CATEGORY VARCHAR(50) DEFAULT ' ' NOT NULL,
                         OPERATE_TYPE VARCHAR(50) DEFAULT ' ' NOT NULL,
                         CONSTRAINT PK_TEDFA01 PRIMARY KEY (FORM_ENAME, BUTTON_ENAME)
) COMMENT ='画面按钮信息定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDFA01`
    MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `POSITION`,
    MODIFY COLUMN `BUSINESS_CATEGORY`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '业务类型' AFTER `TENANT_ID`,
    MODIFY COLUMN `OPERATE_TYPE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '操作分类' AFTER `BUSINESS_CATEGORY`;

CREATE TABLE TEDFA10 (
                         PK_TEDFA10_ID VARCHAR(36) NOT NULL,
                         USER_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL,
                         FORM_ENAME VARCHAR(20) DEFAULT ' ' NOT NULL,
                         FORM_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL,
                         REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL,
                         REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
                         REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL,
                         REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL,
                         NODE_SORT_ID VARCHAR(20) DEFAULT ' ' NOT NULL,
                         TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
                         CONSTRAINT PK_TEDFA10 PRIMARY KEY (PK_TEDFA10_ID),
                         CONSTRAINT TEDFA10_UNIQUE UNIQUE (USER_ID, PROJECT_ENAME, FORM_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDFA11 (
                         PK_TEDFA10_ID VARCHAR(36) NOT NULL,
                         USER_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL,
                         FORM_ENAME VARCHAR(20) DEFAULT ' ' NOT NULL,
                         CONTEXT VARCHAR(20) DEFAULT ' ' NOT NULL,
                         CONSTRAINT PK_TEDFA11 PRIMARY KEY (PK_TEDFA10_ID),
                         CONSTRAINT TEDFA11_UNIQUE UNIQUE (USER_ID, PROJECT_ENAME, FORM_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDFA60 (
                         PK_TEDFA60_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(128) DEFAULT ' ' NOT NULL,
                         FORM_ENAME VARCHAR(8) DEFAULT ' ' NOT NULL,
                         GRID_ID VARCHAR(128) DEFAULT ' ' NOT NULL,
                         USER_ID VARCHAR(128) DEFAULT ' ' NOT NULL,
                         COLUMN_ENAME VARCHAR(128) DEFAULT ' ' NOT NULL,
                         COLUMN_CNAME VARCHAR(256) DEFAULT ' ' NOT NULL,
                         COLUMN_LOCKED VARCHAR(1) DEFAULT '0' NOT NULL,
                         COLUMN_HIDDEN VARCHAR(1) DEFAULT '0' NOT NULL,
                         COLUMN_WIDTH INTEGER DEFAULT 120 NOT NULL,
                         COLUMN_ORDER INTEGER DEFAULT 0 NOT NULL,
                         SOFT_DELETE VARCHAR(1) DEFAULT '0',
                         PRIMARY KEY (PK_TEDFA60_ID),
                         UNIQUE (PROJECT_ENAME, FORM_ENAME, GRID_ID, USER_ID, COLUMN_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDFA61 (
                         PK_TEDFA61_ID VARCHAR(36) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(250) DEFAULT ' ' NOT NULL,
                         USER_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
                         STYLE_ENAME VARCHAR(10) DEFAULT ' ' NOT NULL,
                         STYLE_FONT VARCHAR(255) DEFAULT ' ',
                         STYLE_ECOLOR VARCHAR(20) DEFAULT ' ',
                         TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
                         PRIMARY KEY (PK_TEDFA61_ID),
                         UNIQUE (PROJECT_ENAME, USER_ID, STYLE_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE `tedfa62`
(
    `FORM_ENAME` varchar(20) NOT NULL DEFAULT ' ',
    `OPEN_TYPE`  varchar(64) NOT NULL DEFAULT '0',
    PRIMARY KEY (`FORM_ENAME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE TEDFA63 (
                         CONFIG_ID  VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '页面UUID',
                         PAGE_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '画面英文名',
                         PAGE_CNAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '画面中文名',
                         URL VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '跳转地址',
                         ICON_TYPE VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '预留字段（值为1）',
                         ICON_ADDRESS VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '图标名称',
                         ICON_PARAM VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '图标颜色',
                         USER_ID VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '所属用户',
                         REC_CREATE_TIME VARCHAR(64) DEFAULT ' ' NOT NULL ,
                         REC_REVISE_TIME VARCHAR(64) DEFAULT ' ' NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(64) DEFAULT '0' NOT NULL,
                         CONSTRAINT PK_TEDFA63 PRIMARY KEY (CONFIG_ID)
)COMMENT='维护快捷页面表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE
    TEDIN01
(
    I18N_KEY VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '国际化定义',
    I18N_TEXT VARCHAR(4000) DEFAULT ' ' NOT NULL COMMENT '国际化定义值',
    I18N_LOACLE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '国际化语言环境',
    CONSTRAINT PK_TEDIN01 PRIMARY KEY (I18N_KEY, I18N_LOACLE)
)COMMENT='国际化配置表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table TEDLG00
(
    TYPE_NAME         VARCHAR(32)  COLLATE utf8_bin default ' ' not null,
    TYPE_DESC         VARCHAR(255) COLLATE utf8_bin default ' ' not null,
    REC_CREATOR       VARCHAR(16)  COLLATE utf8_bin default ' ' not null,
    REC_CREATE_TIME   VARCHAR(17)  COLLATE utf8_bin default ' ' not null,
    KEYWORD_ONE       VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_TWO       VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_THREE     VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_FOUR      VARCHAR(32)  COLLATE utf8_bin default ' ',
    KEYWORD_FIVE      VARCHAR(32)  COLLATE utf8_bin default ' ',
    REC_REVISOR       VARCHAR(16)  COLLATE utf8_bin default ' ' not null,
    REC_REVISE_TIME   VARCHAR(17)  COLLATE utf8_bin default ' ' not null,
    ARCHIVE_FLAG      VARCHAR(1)   COLLATE utf8_bin default ' ' not null,
    ARCHIVE_TYPE      VARCHAR(1)   COLLATE utf8_bin default '3',
    ARCHIVE_KEEP_TIME DECIMAL(5)   COLLATE utf8_bin default 3   not null,
    constraint PK_TEDLG00
        primary key (TYPE_NAME)
);

CREATE TABLE TEDMDM2 (
                         ACCSET_NO DECIMAL(11,0) DEFAULT 0 NOT NULL COMMENT '帐套序号',
                         SEQ_TYPE_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '序列号类型ID',
                         SEQ_SUBSECS DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '序列号分段数',
                         DATE_CYCLE VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '日期循环级别',
                         SEQ_LMT DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '序列号长度限制',
                         SUBID_LMT_LEN DECIMAL(3,0) DEFAULT 0 NOT NULL COMMENT '子项长度限制',
                         REMARK VARCHAR(512) DEFAULT ' ' NOT NULL COMMENT '备注',
                         REC_CREATOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
                         REC_CREATE_TIME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
                         REC_REVISOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
                         REC_REVISE_TIME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
                         IS_LINKED DECIMAL(1,0) DEFAULT 0 COMMENT '是否连号',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT ' ',
                         CONSTRAINT PK_TEDMDM2 PRIMARY KEY (SEQ_TYPE_ID)
)COMMENT='序列号定义' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDMDM3 (
                         SEQ_TYPE_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '序列号类型ID',
                         SUBSEC_SEQ DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '分段序号',
                         SUBSEC_NAME VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '分段名称',
                         SUBSEC_TYPE VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '分段类型',
                         SUBSEC_CONTENT VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '定义内容',
                         SUBSEC_LEN DECIMAL(2,0) DEFAULT 0 NOT NULL COMMENT '分段长度',
                         DATE_FORMAT VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '日期格式',
                         REC_CREATOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
                         REC_CREATE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
                         REC_REVISOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
                         REC_REVISE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDMDM3 PRIMARY KEY (SEQ_TYPE_ID, SUBSEC_SEQ)
)COMMENT='序列号分段信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDMDM4 (
                         ACCSET_NO DECIMAL(11,0) DEFAULT 0 NOT NULL COMMENT '帐套序号',
                         SEQ_TYPE_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '序列号类型ID',
                         SEQ_PREFIX VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '序列号前缀',
                         YEAR_MON VARCHAR(8) DEFAULT ' ' NOT NULL COMMENT '年月',
                         CURRENT_SEQ DECIMAL(11,0) DEFAULT 0 NOT NULL COMMENT '当前序列',
                         REC_CREATOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
                         REC_CREATE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
                         REC_REVISOR VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
                         REC_REVISE_TIME VARCHAR(25) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDMDM4 PRIMARY KEY (ACCSET_NO, SEQ_TYPE_ID, SEQ_PREFIX, YEAR_MON)
)COMMENT='当前序列' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDNM01 (
                         MACHINE_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '主机标识',
                         MACHINE_NAME VARCHAR(50) COMMENT '主机名称',
                         MACHINE_HOST VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '主机地址',
                         MACHINE_POID VARCHAR(50) COMMENT '机器物理标识',
                         SYSTEM_TYPE VARCHAR(50) COMMENT '系统类型',
                         AUTH_TYPE VARCHAR(50) COMMENT '认证类型',
                         AUTH_USERNAME VARCHAR(50) COMMENT '认证账号',
                         AUTH_CERT VARCHAR(200) COMMENT '认证凭证',
                         AUTH_PORT VARCHAR(50) COMMENT '认证链接端口',
                         PROTOCOL VARCHAR(200) COMMENT '上传协议',
                         REC_CREATOR VARCHAR(16) COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) COMMENT '记录修改时刻',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CONSTRAINT PK_TEDNM01 PRIMARY KEY (MACHINE_ID)
)COMMENT ='主机信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDNM02 (
                         NODE_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '节点标识',
                         NODE_CNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '节点名称',
                         PORT VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT 'PORT',
                         MACHINE_ID VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '主机标识',
                         PROJECT_ENV VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '环境',
                         MM_TYPE VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '中间件类型',
                         MM_PATH VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '中间件路径',
                         STATUS VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '停用标识',
                         SERVER_USERNAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '服务账号',
                         SERVER_PASSWORD VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '账号密码',
                         GROUP_TAG VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '分组标识',
                         MM_CONTENT VARCHAR(1500) DEFAULT ' ' NOT NULL COMMENT '中间件脚本内容',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '组件英文名',
                         PACKAGE_PATH VARCHAR(100) DEFAULT ' ' NOT NULL COMMENT '上传包路径',
                         CONSTRAINT TEDNM02 PRIMARY KEY (NODE_ID)
)COMMENT ='节点信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDNM03 (
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '归档标记',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
                         CMPT_CNAME VARCHAR(255) DEFAULT ' ' NOT NULL,
                         CONTEXT_PATH VARCHAR(255) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL,
                         PRIMARY KEY (CMPT_ENAME)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDNM03`
    MODIFY COLUMN `CMPT_ENAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件英文名' FIRST ,
    MODIFY COLUMN `CMPT_CNAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件中文名' AFTER `CMPT_ENAME`,
    MODIFY COLUMN `CONTEXT_PATH`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '上下文' AFTER `CMPT_CNAME`,
    MODIFY COLUMN `PROJECT_ENAME`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '项目英文名' AFTER `CONTEXT_PATH`,
    MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '归档标记' AFTER `PROJECT_ENAME`;


CREATE TABLE TEDNM04 (
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
                         CMPT_MEMBER_ID VARCHAR(255) DEFAULT ' ' NOT NULL,
                         CMPT_MEMBER_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL,
                         PRIMARY KEY (CMPT_ENAME, CMPT_MEMBER_ID, CMPT_MEMBER_TYPE)
)COMMENT='发布信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDNM04`
    MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `REC_REVISE_TIME`,
    MODIFY COLUMN `CMPT_ENAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件英文名' AFTER `ARCHIVE_FLAG`,
    MODIFY COLUMN `CMPT_MEMBER_ID`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件成员标识' AFTER `CMPT_ENAME`,
    MODIFY COLUMN `CMPT_MEMBER_TYPE`  varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件成员类型（项目、一级、二级模块）' AFTER `CMPT_MEMBER_ID`;

CREATE TABLE TEDNM05 (
                         CMPT_ENAME VARCHAR(255) DEFAULT ' ' NOT NULL,
                         CMPT_VERSION VARCHAR(255) DEFAULT ' ' NOT NULL,
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL,
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL,
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL,
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL,
                         PRIMARY KEY (CMPT_ENAME, CMPT_VERSION)
)COMMENT='NGINX配置' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

ALTER TABLE `TEDNM05`
    MODIFY COLUMN `CMPT_ENAME`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件英文名' FIRST ,
    MODIFY COLUMN `CMPT_VERSION`  varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '组件版本号' AFTER `CMPT_ENAME`,
    MODIFY COLUMN `REC_CREATOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建人' AFTER `CMPT_VERSION`,
    MODIFY COLUMN `REC_CREATE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '创建时间' AFTER `REC_CREATOR`,
    MODIFY COLUMN `REC_REVISOR`  varchar(16) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改人' AFTER `REC_CREATE_TIME`,
    MODIFY COLUMN `REC_REVISE_TIME`  varchar(17) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT ' ' COMMENT '修改时间' AFTER `REC_REVISOR`,
    MODIFY COLUMN `TENANT_ID`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'BDAS' COMMENT '租户ID' AFTER `REC_REVISE_TIME`,
    MODIFY COLUMN `ARCHIVE_FLAG`  varchar(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0' COMMENT '归档标记' AFTER `TENANT_ID`;

CREATE TABLE TEDNM06 (
                         CONFIG_ID VARCHAR(50) NOT NULL,
                         NAME VARCHAR(50),
                         CONFIGURE VARCHAR(50),
                         PATH VARCHAR(250),
                         PRIMARY KEY (CONFIG_ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


ALTER TABLE `TEDNM06`
    MODIFY COLUMN `CONFIG_ID`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '配置ID' FIRST ,
    MODIFY COLUMN `NAME`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '配置名称' AFTER `CONFIG_ID`,
    MODIFY COLUMN `CONFIGURE`  varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '原项目地址' AFTER `NAME`,
    MODIFY COLUMN `PATH`  varchar(250) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '访问地址' AFTER `CONFIGURE`;


CREATE TABLE
    TEDNM07
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '创建人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '创建时间',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '修改时间',
    APP_NAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '应用名称',
    KEY_TYPE VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '密钥类型',
    SECRET_KEY VARCHAR(512) DEFAULT ' ' NOT NULL COMMENT '密钥',
    ALGORITHM VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '算法',
    CONSTRAINT PK_TEDNM07 PRIMARY KEY (APP_NAME,KEY_TYPE,ALGORITHM)
)COMMENT='表EDNM07' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE
    TEDNM08
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '创建人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '创建时间',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '修改时间',
    APP_NAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '应用名称',
    APP_URL VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '应用地址',
    CONSTRAINT PK_TEDNM08_APP_URL PRIMARY KEY (APP_URL)
)COMMENT='表EDNM08' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDPI01 (
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         LEADER VARCHAR(256) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
                         PROJECT_CNAME VARCHAR(250) DEFAULT ' ' NOT NULL COMMENT '项目中文名',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         PROJECT_DESC VARCHAR(1500) DEFAULT ' ' NOT NULL COMMENT '项目描述',
                         CONSTRAINT PK_TEDPI01 PRIMARY KEY (PROJECT_ENAME)
)COMMENT ='项目信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDPI02 (
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         PROJECT_ENAME VARCHAR(50) DEFAULT ' ' NOT NULL COMMENT '项目英文名',
                         MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '模块英文名称',
                         MODULE_CNAME_1 VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '项目中文名',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         INDEX_SPACE_ENAME VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '索引空间英文名称',
                         TABLE_SPACE_ENAME VARCHAR(40) DEFAULT ' ' NOT NULL COMMENT '表空间英文名称',
                         PRIMARY KEY (MODULE_ENAME_1)
)COMMENT ='项目模块信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE TEDPI03 (
                         MODULE_ENAME_2 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '二级模块英文名',
                         MODULE_CNAME_2 VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '二级模块',
                         MODULE_ENAME_1 VARCHAR(10) DEFAULT ' ' NOT NULL COMMENT '一级模块英文名',
                         REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建者',
                         REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时间',
                         REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改人员',
                         REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时间',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL COMMENT '租户ID',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT '0' NOT NULL COMMENT '归档标记',
                         PRIMARY KEY (MODULE_ENAME_2)
)COMMENT='版本表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEDPI10 (
                         REC_CREATOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录创建责任者',
                         REC_CREATE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
                         REC_REVISOR VARCHAR(256) DEFAULT ' ' NOT NULL COMMENT '记录修改责任者',
                         REC_REVISE_TIME VARCHAR(14) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
                         ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
                         TREE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '节点树英文名',
                         NODE_ENAME VARCHAR(30) DEFAULT ' ' NOT NULL COMMENT '节点英文名',
                         NODE_CNAME VARCHAR(80) DEFAULT ' ' NOT NULL COMMENT '节点中文名',
                         NODE_TYPE DECIMAL(1,0) DEFAULT 0 NOT NULL COMMENT '节点类型',
                         NODE_URL VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '节点URL',
                         NODE_SORT_ID VARCHAR(20) DEFAULT ' ' NOT NULL COMMENT '节点排序标识',
                         NODE_PARAM VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '节点参数配置',
                         NODE_IMAGE_PATH VARCHAR(200) DEFAULT ' ' NOT NULL COMMENT '节点图片路径',
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         CONSTRAINT PK_TEDPI10 PRIMARY KEY (TREE_ENAME, NODE_ENAME)
)COMMENT='项目菜单节点信息' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table TEDPI10_EX
(
    TREE_ENAME VARCHAR(30) default ' ' not null comment '节点树代码',
    NODE_ENAME VARCHAR(30) default ' ' not null comment '节点页面代码',
    SHOW_FLAG  VARCHAR(10)             not null comment '是否可见',
    primary key (TREE_ENAME, NODE_ENAME)
) comment '菜单信息管理扩展表';

create table TEPLG01
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null,
    CONSTRAINT PK_TEPLG01 PRIMARY KEY (OPERATION_ID)
)COMMENT='审计日志_登录登出认证_履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table TEPLG02
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null,
    CONSTRAINT PK_TEPLG02 PRIMARY KEY (OPERATION_ID)
)COMMENT='审计日志_页面按钮分发_履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


create table TEPLG03
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null,
    CONSTRAINT PK_TEPLG03 PRIMARY KEY (OPERATION_ID)
)COMMENT='审计日志_HTTP请求_履历表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEUDM01 (
                         DIR_ID          VARCHAR(36) DEFAULT ' '  NOT NULL,
                         DIR_ENAME       VARCHAR(256) DEFAULT ' '  NOT NULL,
                         DIR_CNAME       VARCHAR(64) DEFAULT ' '  NOT NULL,
                         PARENT_ID       VARCHAR(36) DEFAULT ' '  NOT NULL,
                         IS_LEAF         VARCHAR(1) DEFAULT ' '   NOT NULL,
                         DIR_PATH        VARCHAR(512) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME   VARCHAR(250) DEFAULT ' ' NOT NULL,
                         MODULE_ENAME    VARCHAR(50) DEFAULT ' '  NOT NULL,
                         REC_CREATOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
                         REC_CREATE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
                         REC_REVISOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
                         REC_REVISE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
                         ARCHIVE_FLAG    VARCHAR(1) DEFAULT ' '   NOT NULL,
                         REAL_PATH       VARCHAR(512),
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         PRIMARY KEY (DIR_ID, PROJECT_ENAME, MODULE_ENAME)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE TEUDM02 (
                         DOC_ID          VARCHAR(36) DEFAULT ' '  NOT NULL,
                         DIR_ID          VARCHAR(36) DEFAULT ' '  NOT NULL,
                         DOC_NAME        VARCHAR(128) DEFAULT ' ' NOT NULL,
                         CHG_NAME        VARCHAR(128) DEFAULT ' ' NOT NULL,
                         DOC_SIZE        DECIMAL(16, 0)           NOT NULL,
                         DOC_TAG         VARCHAR(64) DEFAULT ' '  NOT NULL,
                         REC_CREATOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
                         REC_CREATE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
                         REC_REVISOR     VARCHAR(512) DEFAULT ' ' NOT NULL,
                         REC_REVISE_TIME VARCHAR(14) DEFAULT ' '  NOT NULL,
                         ARCHIVE_FLAG    VARCHAR(1) DEFAULT ' '   NOT NULL,
                         TENANT_ID VARCHAR(64) DEFAULT 'BDAS' NOT NULL,
                         SHARED_DOC_TYPE VARCHAR(2) DEFAULT '0' NOT NULL,
                         PRIMARY KEY (DOC_ID)
) ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create table TEUDM03
(
    REC_CREATOR     VARCHAR(16)  COLLATE utf8_bin default ' ',
    REC_REVISOR     VARCHAR(16)  COLLATE utf8_bin  default ' ',
    REC_CREATE_TIME VARCHAR(14)  COLLATE utf8_bin  default ' ',
    REC_REVISE_TIME VARCHAR(14)  COLLATE utf8_bin  default ' ',
    ARCHIVE_FLAG    VARCHAR(1)   COLLATE utf8_bin  default ' ',
    FORM_ENAME      VARCHAR(20)  COLLATE utf8_bin  not null,
    LOCALE_LANG     VARCHAR(20)  COLLATE utf8_bin  default 'zh_CN' not null,
    DOC_PATH        VARCHAR(250) COLLATE utf8_bin  default ' ',
    DOC_CNAME       VARCHAR(200) COLLATE utf8_bin  default ' ',
    KEY_WORD        VARCHAR(200) COLLATE utf8_bin  default ' ',
    BOOKMARK        VARCHAR(200) COLLATE utf8_bin  default ' ',
    VERSION        DECIMAL(5) COLLATE utf8_bin  default 0,
    constraint TEUDM03_PRIMARY_KEY
        primary key (FORM_ENAME, LOCALE_LANG)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table TEDFA02
(
    FORM_TYPE       varchar(10)                   not null comment '画面类型',
    DIR_ID          varchar(64)                   not null comment '文件夹ID',
    PARENT_ID       varchar(64) default ' '       not null comment '父目录文件夹ID',
    GRAPH_DIR       varchar(255)                  not null comment '文件夹名称',
    SORT_INDEX      int   default  0  not null comment '排序',
    REC_CREATOR     varchar(16)                   null comment '记录创建责任者',
    REC_CREATE_TIME varchar(17)                   null comment '记录创建时刻',
    REC_REVISOR     varchar(16)                   null comment '记录修改责任者',
    REC_REVISE_TIME varchar(17)                   null comment '记录修改时刻',
    TENANT_ID       varchar(64) default 'IPLAT4J' not null comment '租户',
    ARCHIVE_FLAG    varchar(1)  default '0'       not null comment '归档标记',
    primary key (DIR_ID, FORM_TYPE)
)comment = '画面目录表'  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

create index IDX_TEDFA02_PARENT_ID on TEDFA02 (PARENT_ID);

-- security 安全管理
CREATE TABLE XS_AUTHORIZATION (
                                  SUBJECT_ID      VARCHAR(32) ,
                                  SUBJECT_TYPE    VARCHAR(16) ,
                                  OBJECT_ID       VARCHAR(32) ,
                                  OBJECT_TYPE     VARCHAR(16) ,
                                  OPERATION_TYPE  VARCHAR(32) ,
                                  REC_CREATOR     VARCHAR(32) ,
                                  REC_CREATE_TIME VARCHAR(14) ,
                                  REC_REVISOR     VARCHAR(32) ,
                                  REC_REVISE_TIME VARCHAR(14) ,
                                  ARCHIVE_FLAG        VARCHAR(1) ,
                                  SORT_INDEX      INT(11) DEFAULT 0
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='授权信息表';

CREATE INDEX XS_AUTHORIZATION_INDEX_OBJECT_ID ON XS_AUTHORIZATION (OBJECT_ID);

ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN SUBJECT_ID VARCHAR(32) COMMENT '授权主体ID';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN SUBJECT_TYPE VARCHAR(16) COMMENT '授权主体类别';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN OBJECT_ID VARCHAR(32) COMMENT '授权客体ID';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN OBJECT_TYPE VARCHAR(16) COMMENT '授权客体类别';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN OPERATION_TYPE VARCHAR(32) COMMENT '操作类型:访问:OPT_ACESS,管理:OPT_MANAGE';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_AUTHORIZATION MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_AUTHORIZATION ADD PRIMARY KEY(SUBJECT_ID, OBJECT_ID, OPERATION_TYPE);

CREATE TABLE
    XS_AUTH_TYPE_CONFIG
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '创建人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '创建时间',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '修改时间',
    AUTH_ID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT 'ID',
    AUTH_TYPE_CODE VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '授权类型代码',
    AUTH_TYPE_DESC VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '授权类型描述',
    AUTH_SERVICE_CONFIG VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '授权服务配置',
    AUTH_OBJE_QUERY_SERVICE VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '授权对象查询服务',
    CONSTRAINT PK_AUTH_ID PRIMARY KEY (AUTH_ID)
)COMMENT='鉴权类型配置表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;



CREATE TABLE XS_IP_USER
(
    IP                  varchar(64) default ' ' not null comment 'ip地址'
        primary key,
    LOGIN_NAME          varchar(64) default ' ' not null comment '用户工号',
    REC_CREATOR         varchar(32) default ' ' not null comment '创建人',
    REC_CREATE_TIME     varchar(14) default ' ' null comment '创建时间',
    REC_REVISOR         varchar(32) default ' ' null comment '修改人',
    REC_REVISE_TIME     varchar(14) default ' ' null comment '修改时间',
    ARCHIVE_FLAG        varchar(1)  default ' ' null comment '归档标记'
)
    comment 'IP与用户关系表' collate = utf8_bin;
create index IDX_XS_IP_USER_LOGIN_NAME
    on XS_IP_USER (LOGIN_NAME);

CREATE TABLE XS_RESOURCE (
                             ID VARCHAR(32) NOT NULL COMMENT '资源ID',
                             RESOURCE_ENAME VARCHAR(128) COMMENT '资源英文名',
                             RESOURCE_CNAME VARCHAR(256) COMMENT '资源中文名',
                             TYPE VARCHAR(16) COMMENT 'PAGE:页面，BUTTON:按钮，AREA页面区域，URL:URL',
                             IS_AUTH VARCHAR(2) COMMENT '是否授权(1:授权，-1:不授权)默认:-1',
                             SORT_INDEX INTEGER DEFAULT 0 COMMENT '排序',
                             REC_CREATOR VARCHAR(32) COMMENT '创建人',
                             REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间',
                             REC_REVISOR VARCHAR(32) COMMENT '修改人',
                             REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间',
                             ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记',
                             TENANT_ID VARCHAR(64) DEFAULT 'BDAS',
                             PRIMARY KEY (ID)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='资源信息表';
CREATE UNIQUE INDEX XS_XS_RESOURCE_RESOURCE_ENAME_UINDEX ON XS_RESOURCE (RESOURCE_ENAME);
CREATE INDEX IDX_XS_RESOURCE_RESOURCE_ENAME ON XS_RESOURCE (RESOURCE_ENAME);

CREATE TABLE XS_RESOURCE_GROUP (
                                   ID                   VARCHAR(32) PRIMARY KEY,
                                   RESOURCE_GROUP_ENAME VARCHAR(128) ,
                                   RESOURCE_GROUP_CNAME VARCHAR(128),
                                   RESOURCE_GROUP_TYPE  VARCHAR(16),
                                   SORT_INDEX           INT(11) DEFAULT 0 ,
                                   REC_CREATOR          VARCHAR(32),
                                   REC_CREATE_TIME      VARCHAR(14),
                                   REC_REVISOR          VARCHAR(32),
                                   REC_REVISE_TIME      VARCHAR(14),
                                   ARCHIVE_FLAG             VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='资源组信息表';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN ID VARCHAR(32) COMMENT '资源组ID';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN RESOURCE_GROUP_ENAME VARCHAR(128) COMMENT '资源分组英文名';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN RESOURCE_GROUP_CNAME VARCHAR(128) COMMENT '资源组中文名';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN RESOURCE_GROUP_TYPE VARCHAR(16) COMMENT '类别:资源组,模块';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_RESOURCE_GROUP MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
CREATE UNIQUE INDEX XS_RESOURCE_GROUP_RESOURCE_GROUP_ENAME_UINDEX ON XS_RESOURCE_GROUP (RESOURCE_GROUP_ENAME);
CREATE INDEX IDX_XS_RESOURCE_GROUP_ENAME ON XS_RESOURCE_GROUP (RESOURCE_GROUP_ENAME);


CREATE TABLE XS_RESOURCE_GROUP_MEMBER (
                                          RESOURCE_MEMBER_ID VARCHAR(128) ,
                                          RESOURCE_PARENT_ID VARCHAR(32)  ,
                                          MEMBER_TYPE        VARCHAR(16),
                                          PATH               VARCHAR(255),
                                          SORT_INDEX         INT(11) DEFAULT 0 ,
                                          REC_CREATOR        VARCHAR(32),
                                          REC_CREATE_TIME    VARCHAR(14),
                                          REC_REVISOR        VARCHAR(32),
                                          REC_REVISE_TIME    VARCHAR(14),
                                          ARCHIVE_FLAG           VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='资源组资源信息表';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN RESOURCE_MEMBER_ID VARCHAR(32) COMMENT '资源组成员ID';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN RESOURCE_PARENT_ID VARCHAR(32) COMMENT '资源组父节点英文名';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN MEMBER_TYPE VARCHAR(16) COMMENT '资源体类别，0:资源组,1:资源';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN PATH VARCHAR(255) COMMENT '来源';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER ADD PRIMARY KEY (RESOURCE_MEMBER_ID, RESOURCE_PARENT_ID);

CREATE TABLE
    XS_URL_ANTHORIZATION
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '创建人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '创建时间',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '修改人',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '修改时间',
    URL_CONFIG_ENAME VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '访问地址配置英文名',
    AUTH_TYPE_CODE VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '授权类型代码',
    AUTH_SUBJECT_ID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '授权对象ID',
    AUTH_SUBJECT_ENAME VARCHAR(64) DEFAULT ' ' NOT NULL COMMENT '授权对象英文名',
    AUTH_SUBJECT_DESC VARCHAR(255) DEFAULT ' ' NOT NULL COMMENT '授权对象描述',
    CONSTRAINT PK_URL_AUTH PRIMARY KEY (URL_CONFIG_ENAME,AUTH_TYPE_CODE,AUTH_SUBJECT_ID)
)COMMENT='访问地址授权表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;

CREATE TABLE XS_USER (
                         USER_ID             VARCHAR(32) PRIMARY KEY ,
                         LOGIN_NAME          VARCHAR(64)  ,
                         PASSWORD            VARCHAR(255) ,
                         STATUS              VARCHAR(16)  ,
                         USER_NAME           VARCHAR(128)  ,
                         GENDER VARCHAR(2) DEFAULT '1',
                         MOBILE              VARCHAR(32)  ,
                         EMAIL               VARCHAR(128) ,
                         USER_TYPE           VARCHAR(16)  ,
                         ACCOUNT_EXPIRE_DATE VARCHAR(14)  ,
                         PWD_EXPIRE_DATE     VARCHAR(14)  ,
                         IS_LOCKED           VARCHAR(2)   ,
                         SORT_INDEX          INT(11) DEFAULT 0,
                         REC_CREATOR         VARCHAR(32) ,
                         REC_CREATE_TIME     VARCHAR(14) ,
                         REC_REVISOR         VARCHAR(32) ,
                         REC_REVISE_TIME     VARCHAR(14) ,
                         PWD_REVISE_DATE     VARCHAR(14) ,
                         PWD_REVISOR         VARCHAR(32) ,
                         ARCHIVE_FLAG            VARCHAR(1),
                         USER_GROUP_ENAME    VARCHAR(32),
                         JOB_ID              VARCHAR(32)  default ' ',
                         EHR_ORG_ID          VARCHAR(16)  default ' ',
                         JOB_NAME            VARCHAR(256)  default ' '

)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN COMMENT='用户信息表';
ALTER TABLE XS_USER MODIFY COLUMN USER_ID VARCHAR(32) COMMENT '用户ID';
ALTER TABLE XS_USER MODIFY COLUMN LOGIN_NAME VARCHAR(64) COMMENT '登录账号';
ALTER TABLE XS_USER MODIFY COLUMN PASSWORD VARCHAR(255) COMMENT '登录密码';
ALTER TABLE XS_USER MODIFY COLUMN STATUS VARCHAR(16) COMMENT '账号状态(1：可用,-1不可用)';
ALTER TABLE XS_USER MODIFY COLUMN USER_NAME VARCHAR(128) COMMENT '用户姓名';
ALTER TABLE XS_USER MODIFY COLUMN GENDER VARCHAR(2) COMMENT '性别';
ALTER TABLE XS_USER MODIFY COLUMN MOBILE VARCHAR(32) COMMENT '手机';
ALTER TABLE XS_USER MODIFY COLUMN EMAIL VARCHAR(128) COMMENT '邮箱';
ALTER TABLE XS_USER MODIFY COLUMN USER_TYPE VARCHAR(16) COMMENT '用户类别(COMPANY企业用户，USER个人用户)';
ALTER TABLE XS_USER MODIFY COLUMN ACCOUNT_EXPIRE_DATE VARCHAR(14) COMMENT '账号过期时间';
ALTER TABLE XS_USER MODIFY COLUMN PWD_EXPIRE_DATE VARCHAR(14) COMMENT '密码过期时间';
ALTER TABLE XS_USER MODIFY COLUMN IS_LOCKED VARCHAR(2) COMMENT '是否锁定：-1锁定，1正常';
ALTER TABLE XS_USER MODIFY COLUMN PWD_REVISOR VARCHAR(32) COMMENT '密码修改人';
ALTER TABLE XS_USER MODIFY COLUMN PWD_REVISE_DATE VARCHAR(14) COMMENT '密码修改时间';
ALTER TABLE XS_USER MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_USER MODIFY COLUMN USER_GROUP_ENAME VARCHAR(32)  COMMENT '用户组';
ALTER TABLE XS_USER MODIFY COLUMN JOB_ID VARCHAR(16) COMMENT '用户岗位号';
ALTER TABLE XS_USER MODIFY COLUMN EHR_ORG_ID VARCHAR(16) COMMENT 'EHR组织机构代码';
ALTER TABLE XS_USER MODIFY COLUMN JOB_NAME VARCHAR(32)  COMMENT '用户岗位名';

CREATE UNIQUE INDEX XS_USER_LOGIN_NAME_UINDEX ON XS_USER (LOGIN_NAME);
ALTER TABLE XS_USER MODIFY  LOGIN_NAME VARCHAR(64) NOT NULL;
ALTER TABLE XS_USER MODIFY  PASSWORD VARCHAR(255) NOT NULL;
ALTER TABLE XS_USER MODIFY  STATUS VARCHAR(16) NOT NULL;
ALTER TABLE XS_USER MODIFY  USER_NAME VARCHAR(128) NOT NULL;
ALTER TABLE XS_USER MODIFY  GENDER VARCHAR(2) NOT NULL;
ALTER TABLE XS_USER MODIFY  MOBILE VARCHAR(32) NOT NULL;
ALTER TABLE XS_USER MODIFY  EMAIL VARCHAR(128) NOT NULL;
ALTER TABLE XS_USER MODIFY  USER_TYPE VARCHAR(16) NOT NULL;
ALTER TABLE XS_USER MODIFY  ACCOUNT_EXPIRE_DATE VARCHAR(14) NOT NULL;
ALTER TABLE XS_USER MODIFY  PWD_EXPIRE_DATE VARCHAR(14) NOT NULL;
ALTER TABLE XS_USER MODIFY  IS_LOCKED VARCHAR(2) NOT NULL;
ALTER TABLE XS_USER MODIFY  REC_CREATOR VARCHAR(32) NOT NULL;
CREATE INDEX IDX_XS_USER_LOGIN_NAME ON XS_USER (LOGIN_NAME);

CREATE TABLE XS_USER_EXT (
                             EXT_ID          VARCHAR(32)  PRIMARY KEY,
                             USER_ID         VARCHAR(32) ,
                             FIELD_ID        VARCHAR(128) ,
                             VALUE           VARCHAR(128) ,
                             REC_CREATOR     VARCHAR(16) ,
                             REC_CREATE_TIME VARCHAR(17) ,
                             REC_REVISOR     VARCHAR(16) ,
                             REC_REVISE_TIME VARCHAR(17) ,
                             ARCHIVE_FLAG    VARCHAR(1)  ,
                             TENANT_ID       VARCHAR(64)  DEFAULT ' ' NOT NULL
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='用户拓展信息表';
ALTER TABLE XS_USER_EXT MODIFY COLUMN EXT_ID VARCHAR(32) COMMENT 'ID';
ALTER TABLE XS_USER_EXT MODIFY COLUMN USER_ID VARCHAR(32) COMMENT '用户ID';
ALTER TABLE XS_USER_EXT MODIFY COLUMN FIELD_ID VARCHAR(128) COMMENT '属性ID';
ALTER TABLE XS_USER_EXT MODIFY COLUMN VALUE VARCHAR(128) COMMENT '属性值';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER_EXT MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER_EXT MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER_EXT MODIFY COLUMN TENANT_ID VARCHAR(64) COMMENT '租户ID';

CREATE TABLE XS_USER_GROUP (
                               ID              VARCHAR(32) PRIMARY KEY ,
                               GROUP_ENAME     VARCHAR(32)  ,
                               GROUP_CNAME     VARCHAR(128) ,
                               GROUP_TYPE      VARCHAR(32) ,
                               SORT_INDEX      INT(11) DEFAULT 0  ,
                               REC_CREATOR     VARCHAR(32) ,
                               REC_CREATE_TIME VARCHAR(14) ,
                               REC_REVISOR     VARCHAR(32) ,
                               REC_REVISE_TIME VARCHAR(14) ,
                               ARCHIVE_FLAG        VARCHAR(1),
                               MANAGE_GROUP_ENAME VARCHAR(32)

)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='用户组信息表';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN ID VARCHAR(32) COMMENT '用户群组ID';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN GROUP_ENAME VARCHAR(32) COMMENT '群组英文名';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN GROUP_CNAME VARCHAR(128) COMMENT '群组中文名';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN GROUP_TYPE VARCHAR(32) COMMENT '群组类型';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_USER_GROUP MODIFY COLUMN MANAGE_GROUP_ENAME VARCHAR(32) COMMENT '管辖组';
CREATE UNIQUE INDEX GROUP_ENAME_UINDEX ON XS_USER_GROUP (GROUP_ENAME);
ALTER TABLE XS_USER_GROUP MODIFY  GROUP_ENAME VARCHAR(32) NOT NULL;
CREATE INDEX IDX_XS_USER_GROUP_ENAME ON XS_USER_GROUP (GROUP_ENAME);

CREATE TABLE XS_USER_GROUP_MEMBER (
                                      MEMBER_ID       VARCHAR(32) ,
                                      PARENT_ID       VARCHAR(32) ,
                                      MEMBER_TYPE     VARCHAR(16) ,
                                      SORT_INDEX      INT(11) DEFAULT 0 ,
                                      PATH            VARCHAR(255) ,
                                      REC_CREATOR     VARCHAR(32) ,
                                      REC_CREATE_TIME VARCHAR(14) ,
                                      REC_REVISOR     VARCHAR(32) ,
                                      REC_REVISE_TIME VARCHAR(14) ,
                                      ARCHIVE_FLAG        VARCHAR(1)
)ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN  COMMENT='用户组成员信息表';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN MEMBER_ID VARCHAR(32) COMMENT '成员ID';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN PARENT_ID VARCHAR(32) COMMENT '父节点ID';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN MEMBER_TYPE VARCHAR(16) COMMENT '授权类别:USER,GROUP';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN PATH VARCHAR(255) COMMENT '来源';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_CREATOR VARCHAR(32) COMMENT '创建人';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_CREATE_TIME VARCHAR(14) COMMENT '创建时间';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_REVISOR VARCHAR(32) COMMENT '修改人';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN REC_REVISE_TIME VARCHAR(14) COMMENT '修改时间';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN ARCHIVE_FLAG VARCHAR(1) COMMENT '归档标记';
ALTER TABLE XS_USER_GROUP_MEMBER MODIFY COLUMN SORT_INDEX INTEGER(11) COMMENT '排序';
ALTER TABLE XS_USER_GROUP_MEMBER ADD PRIMARY KEY (PARENT_ID, MEMBER_ID);
CREATE INDEX IDX_XS_USER_GROUP_MEMBER_IDS ON XS_USER_GROUP_MEMBER (MEMBER_ID, PARENT_ID);

CREATE TABLE
    TXSSC01
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任人',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    TENANT_ID VARCHAR(64) DEFAULT ' BDAS' NOT NULL COMMENT '租户ID',
    RULE_UUID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '规则唯一标识',
    RULE_DESC VARCHAR(128) DEFAULT ' ' NOT NULL COMMENT '规则说明',
    TARGET_CHARACTER VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '拦截包含字符',
    REPLACE_CHARACTER VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '过滤替换字符',
    SORT_INDEX DECIMAL(4,0) DEFAULT 0 NOT NULL COMMENT '顺序号',
    CONSTRAINT PK_TXSSC01 PRIMARY KEY (RULE_UUID)
)COMMENT='网页脚本配置表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE
    TXSSC02
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任人',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    TENANT_ID VARCHAR(64) DEFAULT ' BDAS' NOT NULL COMMENT '租户ID',
    RULE_UUID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '规则唯一标识',
    RULE_DESC VARCHAR(128) DEFAULT ' ' NOT NULL COMMENT '规则说明',
    TARGET_CHARACTER VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '拦截包含字符',
    SORT_INDEX DECIMAL(4,0) DEFAULT 0 NOT NULL COMMENT '顺序号',
    CONSTRAINT PK_TXSSC02 PRIMARY KEY (RULE_UUID)
)COMMENT='SQL脚本配置表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;


CREATE TABLE
    TXSSC03
(
    REC_CREATOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录创建责任人',
    REC_CREATE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录创建时刻',
    REC_REVISOR VARCHAR(16) DEFAULT ' ' NOT NULL COMMENT '记录修改责任人',
    REC_REVISE_TIME VARCHAR(17) DEFAULT ' ' NOT NULL COMMENT '记录修改时刻',
    ARCHIVE_FLAG VARCHAR(1) DEFAULT ' ' NOT NULL COMMENT '归档标记',
    TENANT_ID VARCHAR(64) DEFAULT ' BDAS' NOT NULL COMMENT '租户ID',
    RULE_UUID VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '规则唯一标识',
    WHITELIST_TYPE VARCHAR(2) DEFAULT ' ' NOT NULL COMMENT '白名单类型',
    WHITELIST_KEYWORD VARCHAR(32) DEFAULT ' ' NOT NULL COMMENT '白名单关键字',
    WHITELIST_DESC VARCHAR(128) DEFAULT ' ' NOT NULL COMMENT '白名单说明',
    SORT_INDEX DECIMAL(4,0) DEFAULT 0 NOT NULL COMMENT '顺序号',
    CONSTRAINT PK_TXSSC03 PRIMARY KEY (RULE_UUID)
)COMMENT='白名单配置表' ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE UTF8_BIN;



delimiter /


--/
CREATE FUNCTION queryMenuParentInfo(queryId varchar(21845)) returns varchar(21845)
BEGIN
    DECLARE sTemp VARCHAR(21845);
    DECLARE sTempChd VARCHAR(21845);
    SET sTemp = '$';
    SET sTempChd = queryId;
    WHILE sTempChd is not null DO
            SET sTemp = concat(sTemp,',',sTempChd);
SELECT group_concat(tree_ename) INTO sTempChd FROM tedpi10 where node_ename <> tree_ename and  FIND_IN_SET(node_ename,sTempChd)>0;
END WHILE;
RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryMenuPageInfo(queryId VARCHAR(32))
    RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd IS NOT NULL DO
        SET sTemp = concat(sTemp,',',sTempChd);
SELECT group_concat(node_ename) INTO sTempChd FROM tedpi10 WHERE node_ename <> tree_ename AND  FIND_IN_SET(tree_ename,sTempChd)>0;
END WHILE;
RETURN sTemp;
END;
/


--/
CREATE FUNCTION queryAuthInfo(queryId VARCHAR(32))
    RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd IS NOT NULL DO
        SET sTemp = concat(sTemp,',',sTempChd);
SELECT group_concat(object_id) INTO sTempChd FROM XS_AUTHORIZATION WHERE subject_id <> object_id AND  FIND_IN_SET(subject_id,sTempChd)>0;
END WHILE;
RETURN sTemp;
END;
/

--/
CREATE FUNCTION queryParentInfo(queryId VARCHAR(32))
    RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd IS NOT NULL DO
        SET sTemp = concat(sTemp,',',sTempChd);
SELECT group_concat(parent_id) INTO sTempChd FROM xs_user_group_member WHERE member_id <> parent_id AND  FIND_IN_SET(member_id,sTempChd)>0;
END WHILE;
RETURN sTemp;
END;
/

--/
CREATE FUNCTION queryChildInfo(queryId VARCHAR(32))
    RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd IS NOT NULL DO
        SET sTemp = concat(sTemp,',',sTempChd);
SELECT group_concat(member_id) INTO sTempChd FROM xs_user_group_member WHERE  FIND_IN_SET(parent_id,sTempChd)>0;
END WHILE;
RETURN sTemp;
END;
/

--/
CREATE FUNCTION queryResourceParentInfo(queryId VARCHAR(32))
    RETURNS VARCHAR(21845)
BEGIN
        DECLARE sTemp VARCHAR(21845);
        DECLARE sTempChd VARCHAR(21845);
        SET sTemp = '$';
        SET sTempChd = queryId;
        WHILE sTempChd IS NOT NULL DO
        SET sTemp = concat(sTemp,',',sTempChd);
SELECT group_concat(resource_parent_id) INTO sTempChd FROM xs_resource_group_member WHERE resource_member_id <> resource_parent_id AND  FIND_IN_SET(resource_member_id,sTempChd)>0;
END WHILE;
RETURN sTemp;
END;
/


delimiter ;