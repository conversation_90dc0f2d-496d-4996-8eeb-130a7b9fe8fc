CREATE TABLE ED_XM_2PC_LOG (
                               LOG_ID VARCHAR(36) DEFAULT ' '    NOT NULL ,
                               TRANSACTION_ID VARCHAR(50) DEFAULT ' '    NOT NULL ,
                               SERVICE_NAME VARCHAR(20) DEFAULT ' '    NOT NULL ,
                               SERVICE_ID VARCHAR(20) DEFAULT ' '    NOT NULL ,
                               METHOD_NAME VARCHAR(50) DEFAULT ' '    NOT NULL ,
                               STATUS VARCHAR(2) DEFAULT ' '    NOT NULL ,
                               LOG_INFO VARCHAR(1000) DEFAULT ' '    NOT NULL ,
                               TIME_STAMP VARCHAR(17) ,
                               TRACE_ID VARCHAR(50)  DEFAULT ' '    NOT NULL
);
COMMENT ON TABLE ED_XM_2PC_LOG IS '2PC日志信息表';
ALTER TABLE ED_XM_2PC_LOG ADD CONSTRAINT PK_ED_XM_2PC_LOG PRIMARY KEY (LOG_ID) ;
CREATE INDEX IDX_2PC_LOG_TRACE_TRANS_ID ON ED_XM_2PC_LOG (TRACE_ID, TRANSACTION_ID);
CREATE INDEX IDX_2PC_LOG_TIME_STAMP ON ED_XM_2PC_LOG (TIME_STAMP);


CREATE TABLE ED_XM_EVENT (
                             EVENT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                             EVENT_DESC VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                             SYNC_TYPE VARCHAR(2)  DEFAULT ' '    NOT NULL ,
                             REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                             REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                             REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                             REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                             IS_AUTH VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                             TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                             ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT.EVENT_DESC IS '事件描述';
COMMENT ON COLUMN ED_XM_EVENT.SYNC_TYPE IS '同/异步标志';
COMMENT ON COLUMN ED_XM_EVENT.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_EVENT.IS_AUTH IS '是否授权';
COMMENT ON COLUMN ED_XM_EVENT.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_EVENT IS '微服务事件表';
ALTER TABLE ED_XM_EVENT ADD CONSTRAINT PK_ED_XM_EVENT  PRIMARY KEY (EVENT_ID) ;

CREATE TABLE ED_XM_EVENT_PARAM (
                                   PARAM_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                   EVENT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                   PARAM_KEY VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                   PARAM_KEY_CNAME VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                                   PARAM_TYPE VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                   PARAM_DEF_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL,
                                   REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                   REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                                   REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                   REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                                   TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                   ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_ID IS '微事件参数标识';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_KEY IS '参数英文名';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_KEY_CNAME IS '参数中文名';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_TYPE IS '参数类型';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.PARAM_DEF_VALUE IS '参数缺省值';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT_PARAM.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_EVENT_PARAM IS '微事件参数信息表';
ALTER TABLE ED_XM_EVENT_PARAM ADD CONSTRAINT PK_ED_XM_EVENT_PARAM  PRIMARY KEY (PARAM_ID) ;

CREATE TABLE ED_XM_EVENT_ROUTE (
                                   EVENT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                   SERVICE_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                   ROUTE_KEY VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                   ROUTE_VALUE VARCHAR(100)  DEFAULT ' '    NOT NULL ,
                                   REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                   REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                                   REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                   REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                                   TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                   ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.ROUTE_KEY IS '路由键';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.ROUTE_VALUE IS '路由值';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT_ROUTE.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_EVENT_ROUTE IS '路由表';
--TODO
ALTER TABLE ED_XM_EVENT_ROUTE ADD CONSTRAINT PK_ED_XM_EVENT_ROUTE  PRIMARY KEY (EVENT_ID, SERVICE_ID, ROUTE_KEY) ;


CREATE TABLE ED_XM_EVENT_SERVICE_RELA (
                                          EVENT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                          SERVICE_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                          SORT_INDEX VARCHAR(3)  DEFAULT ' '    NOT NULL ,
                                          TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                                          ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                                          REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                          REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                                          REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                                          REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.EVENT_ID IS '事件标识';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.SORT_INDEX IS '排序';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_EVENT_SERVICE_RELA.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON TABLE ED_XM_EVENT_SERVICE_RELA IS '微服务事件服务关联表';
ALTER TABLE ED_XM_EVENT_SERVICE_RELA ADD CONSTRAINT PK_ED_XM_EVENT_SERVICE_RELA  PRIMARY KEY (EVENT_ID,SERVICE_ID,SORT_INDEX) ;

CREATE TABLE ED_XM_PARAM (
                             PARAM_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                             SERVICE_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                             PARAM_KEY VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                             PARAM_KEY_CNAME VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                             PARAM_TYPE VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                             PARAM_DEF_VALUE VARCHAR(4000) DEFAULT ' ' NOT NULL,
                             REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                             REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                             REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                             REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                             TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                             ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_PARAM.PARAM_ID IS '微服务参数标识';
COMMENT ON COLUMN ED_XM_PARAM.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_KEY IS '参数英文名';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_KEY_CNAME IS '参数中文名';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_TYPE IS '参数类型';
COMMENT ON COLUMN ED_XM_PARAM.PARAM_DEF_VALUE IS '参数缺省值';
COMMENT ON COLUMN ED_XM_PARAM.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_PARAM.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_PARAM.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_PARAM.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_PARAM.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_PARAM.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_PARAM IS '微服务参数信息表';
ALTER TABLE ED_XM_PARAM ADD CONSTRAINT PK_ED_XM_PARAM  PRIMARY KEY (PARAM_ID) ;

CREATE TABLE ED_XM_SERVICE (
                               SERVICE_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                               SERVICE_ENAME VARCHAR(64) ,
                               METHOD_ENAME VARCHAR(64) ,
                               SERVICE_TYPE VARCHAR(16) ,
                               SERVICE_DESC VARCHAR(256) ,
                               URL VARCHAR(256) ,
                               REMARK VARCHAR(512) ,
                               REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                               REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                               REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                               REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                               IS_AUTH VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                               TRANS_TYPE VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                               TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                               ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_ID IS '微服务标识';
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_ENAME IS '服务英文名';
COMMENT ON COLUMN ED_XM_SERVICE.METHOD_ENAME IS '方法英文名';
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_TYPE IS '服务类型';
COMMENT ON COLUMN ED_XM_SERVICE.SERVICE_DESC IS '中文描述';
COMMENT ON COLUMN ED_XM_SERVICE.URL IS 'URL';
COMMENT ON COLUMN ED_XM_SERVICE.REMARK IS '备注';
COMMENT ON COLUMN ED_XM_SERVICE.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN ED_XM_SERVICE.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_SERVICE.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN ED_XM_SERVICE.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_SERVICE.IS_AUTH IS '是否授权';
COMMENT ON COLUMN ED_XM_SERVICE.TRANS_TYPE IS '事务类型';
COMMENT ON COLUMN ED_XM_SERVICE.TENANT_ID IS '租户ID';
COMMENT ON COLUMN ED_XM_SERVICE.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE ED_XM_SERVICE IS '微服务信息表';
ALTER TABLE ED_XM_SERVICE ADD CONSTRAINT PK_ED_XM_SERVICE  PRIMARY KEY (SERVICE_ID) ;

create table ED_XM_SERVICE_DOC
(
    SERVICE_ID      VARCHAR(64)             not null,
    DOMAIN_NAME     VARCHAR(50)             not null,
    DATA_TYPE       VARCHAR(1)  default '0' not null,
    OPERATION_TYPE  VARCHAR(1)  default '0' not null,
    REC_CREATOR     VARCHAR(16) default ' ' not null,
    REC_CREATE_TIME VARCHAR(17) default ' ' not null,
    REC_REVISOR     VARCHAR(16) default ' ' not null,
    REC_REVISE_TIME VARCHAR(17) default ' ' not null,
    TENANT_ID       VARCHAR(64) default ' ' not null,
    ARCHIVE_FLAG    VARCHAR(1)  default '0' not null,
    constraint ED_XM_SERVICE_DOC_PK
        primary key (SERVICE_ID, OPERATION_TYPE)
);
comment on table ED_XM_SERVICE_DOC is '服务文档说明表';
comment on column ED_XM_SERVICE_DOC.SERVICE_ID is '服务ID';
comment on column ED_XM_SERVICE_DOC.DOMAIN_NAME is '参数对象名称';
comment on column ED_XM_SERVICE_DOC.DATA_TYPE is '参数类型';
comment on column ED_XM_SERVICE_DOC.OPERATION_TYPE is '传入/传出参数';
comment on column ED_XM_SERVICE_DOC.REC_CREATOR is '记录创建责任者';
comment on column ED_XM_SERVICE_DOC.REC_CREATE_TIME is '记录创建时刻';
comment on column ED_XM_SERVICE_DOC.REC_REVISOR is '记录修改责任者';
comment on column ED_XM_SERVICE_DOC.REC_REVISE_TIME is '记录修改时刻';
comment on column ED_XM_SERVICE_DOC.TENANT_ID is '租户ID';
comment on column ED_XM_SERVICE_DOC.ARCHIVE_FLAG is '归档标记';

create table ED_XM_SERVICE_DOC_FIELD
(
    DOMAIN_NAME     VARCHAR(50)                 not null,
    FIELD_NAME      VARCHAR(50)                 not null,
    SORT_INDEX      INTEGER                     not null,
    FIELD_DESC      VARCHAR(255)                not null,
    NOTES           VARCHAR(2000),
    DATA_TYPE       VARCHAR(20),
    ALLOWABLE_VALS  VARCHAR(255),
    NULLABLE        VARCHAR(1)   default '0'    not null,
    EXAMPLE         VARCHAR(255) default ' '    not null,
    FIELD_REFERENCE VARCHAR(50)  default ' '    not null,
    REC_CREATOR     VARCHAR(16)  default ' '    not null,
    REC_CREATE_TIME VARCHAR(17)  default ' '    not null,
    REC_REVISOR     VARCHAR(16)  default ' '    not null,
    REC_REVISE_TIME VARCHAR(17)  default ' '    not null,
    TENANT_ID       VARCHAR(64)  default 'BDAS' not null,
    ARCHIVE_FLAG    VARCHAR(1)   default '0'    not null,
    constraint ED_XM_SERVICE_DOC_FIELD_PK
        primary key (DOMAIN_NAME, FIELD_NAME)
);

comment on table ED_XM_SERVICE_DOC_FIELD is '服务文档对象明细表';

comment on column ED_XM_SERVICE_DOC_FIELD.DOMAIN_NAME is '参数对象名称';

comment on column ED_XM_SERVICE_DOC_FIELD.FIELD_NAME is '属性名称';

comment on column ED_XM_SERVICE_DOC_FIELD.SORT_INDEX is '排序';

comment on column ED_XM_SERVICE_DOC_FIELD.FIELD_DESC is '简要描述';

comment on column ED_XM_SERVICE_DOC_FIELD.NOTES is '类完整路径';

comment on column ED_XM_SERVICE_DOC_FIELD.DATA_TYPE is '数据类型';

comment on column ED_XM_SERVICE_DOC_FIELD.ALLOWABLE_VALS is '可允许值';

comment on column ED_XM_SERVICE_DOC_FIELD.NULLABLE is '允许为空';

comment on column ED_XM_SERVICE_DOC_FIELD.EXAMPLE is '示例值';

comment on column ED_XM_SERVICE_DOC_FIELD.FIELD_REFERENCE is '引用类型';

comment on column ED_XM_SERVICE_DOC_FIELD.REC_CREATOR is '记录创建责任者';

comment on column ED_XM_SERVICE_DOC_FIELD.REC_CREATE_TIME is '记录创建时刻';

comment on column ED_XM_SERVICE_DOC_FIELD.REC_REVISOR is '记录修改责任者';

comment on column ED_XM_SERVICE_DOC_FIELD.REC_REVISE_TIME is '记录修改时刻';

comment on column ED_XM_SERVICE_DOC_FIELD.TENANT_ID is '租户ID';

comment on column ED_XM_SERVICE_DOC_FIELD.ARCHIVE_FLAG is '归档标记';

create table ED_XM_SERVICE_DOC_OBJ
(
    DOMAIN_NAME     VARCHAR(50)                 not null
        constraint ED_XM_SERVICE_DOC_OBJ_PK
            primary key,
    DOMAIN_DESC     VARCHAR(255)                not null,
    CLASS_PATH      VARCHAR(255) default ' '    not null,
    DATA_TYPE       VARCHAR(1),
    REC_CREATOR     VARCHAR(16)  default ' '    not null,
    REC_CREATE_TIME VARCHAR(17)  default ' '    not null,
    REC_REVISOR     VARCHAR(16)  default ' '    not null,
    REC_REVISE_TIME VARCHAR(17)  default ' '    not null,
    TENANT_ID       VARCHAR(64)  default 'BDAS' not null,
    ARCHIVE_FLAG    VARCHAR(1)   default '0'    not null
);
comment on table ED_XM_SERVICE_DOC_OBJ is '服务文档对象说明表';
comment on column ED_XM_SERVICE_DOC_OBJ.DOMAIN_NAME is '参数对象名称';
comment on column ED_XM_SERVICE_DOC_OBJ.DOMAIN_DESC is '参数对象描述';
comment on column ED_XM_SERVICE_DOC_OBJ.CLASS_PATH is '类完整路径';
comment on column ED_XM_SERVICE_DOC_OBJ.DATA_TYPE is '参数类型';
comment on column ED_XM_SERVICE_DOC_OBJ.REC_CREATOR is '记录创建责任者';
comment on column ED_XM_SERVICE_DOC_OBJ.REC_CREATE_TIME is '记录创建时刻';
comment on column ED_XM_SERVICE_DOC_OBJ.REC_REVISOR is '记录修改责任者';
comment on column ED_XM_SERVICE_DOC_OBJ.REC_REVISE_TIME is '记录修改时刻';
comment on column ED_XM_SERVICE_DOC_OBJ.TENANT_ID is '租户ID';
comment on column ED_XM_SERVICE_DOC_OBJ.ARCHIVE_FLAG is '归档标记';

CREATE TABLE ED_XM_SERVICE_RES_RELA
(
    SERVICE_ENAME         VARCHAR(64)  DEFAULT ' '   NOT NULL,
    METHOD_ENAME          VARCHAR(64)  DEFAULT ' '   NOT NULL,
    FORM_ENAME            VARCHAR(20)  DEFAULT ' '   NOT NULL,
    BUTTON_ENAME          VARCHAR(100)  DEFAULT ' '   NOT NULL,
    RES_ID                VARCHAR(50)  DEFAULT ' '   NOT NULL,
    REC_CREATOR           VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME       VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR           VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME       VARCHAR(17)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG          VARCHAR(1)  DEFAULT ' '   NOT NULL,
    TENANT_ID             VARCHAR(64)  DEFAULT ' '   NOT NULL
);
alter table ED_XM_SERVICE_RES_RELA add constraint PK_SERVICE_RES_RELA  primary key (SERVICE_ENAME,METHOD_ENAME,FORM_ENAME,BUTTON_ENAME);
CREATE  INDEX IDX_SERVICE_RES_RELA ON ED_XM_SERVICE_RES_RELA (RES_ID);
COMMENT ON TABLE ED_XM_SERVICE_RES_RELA IS '按钮服务关系表';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.SERVICE_ENAME IS '服务英文名';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.METHOD_ENAME IS '方法英文名';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.FORM_ENAME IS '画面英文名';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.BUTTON_ENAME IS '按钮英文名';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.RES_ID IS '资源ID';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.REC_CREATOR IS '记录创建责任人';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.REC_REVISOR IS '记录修改责任人';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN ED_XM_SERVICE_RES_RELA.TENANT_ID IS '租户ID';

CREATE TABLE HEDCC03
(
    LOG_UUID            VARCHAR(32)  DEFAULT ' '   NOT NULL,
    CONFIG_TYPE         VARCHAR(255)  DEFAULT ' '   NOT NULL,
    STATUS              DECIMAL(11,0),
    CONFIG_CUE          VARCHAR(255),
    CONFIG_ENV_ID       VARCHAR(50)  DEFAULT ' '   NOT NULL,
    FKEY                VARCHAR(255)  DEFAULT ' '   NOT NULL,
    TENANT_ID           VARCHAR(64)  DEFAULT ' '   NOT NULL,
    CONFIG_DESC         VARCHAR(255),
    OPERATOR            VARCHAR(32)  DEFAULT ' '   NOT NULL,
    OPERATE_TYPE        VARCHAR(32)  DEFAULT ' '   NOT NULL,
    OPERATE_TIME        VARCHAR(17)  DEFAULT ' '   NOT NULL,
    ORI_VALUE           VARCHAR(4000)  DEFAULT ' '   NOT NULL,
    NEW_VALUE           VARCHAR(4000)  DEFAULT ' '   NOT NULL
);
alter table HEDCC03 add constraint PK_HEDCC03  primary key (LOG_UUID);

COMMENT ON TABLE HEDCC03 IS '配置信息履历表';
COMMENT ON COLUMN HEDCC03.LOG_UUID IS '记录UUID';
COMMENT ON COLUMN HEDCC03.CONFIG_TYPE IS '配置项类别';
COMMENT ON COLUMN HEDCC03.STATUS IS '状态：1是正常 0是删除';
COMMENT ON COLUMN HEDCC03.CONFIG_CUE IS '配置项提示';
COMMENT ON COLUMN HEDCC03.CONFIG_ENV_ID IS '配置环境';
COMMENT ON COLUMN HEDCC03.FKEY IS '配置名称';
COMMENT ON COLUMN HEDCC03.TENANT_ID IS '租户ID';
COMMENT ON COLUMN HEDCC03.CONFIG_DESC IS '配置描述';
COMMENT ON COLUMN HEDCC03.OPERATOR IS '操作人工号';
COMMENT ON COLUMN HEDCC03.OPERATE_TYPE IS '操作方式';
COMMENT ON COLUMN HEDCC03.OPERATE_TIME IS '操作时间';
COMMENT ON COLUMN HEDCC03.ORI_VALUE IS '原有值';
COMMENT ON COLUMN HEDCC03.NEW_VALUE IS '新fvalue';

create index HEDCC03_INDEX
    on HEDCC03 (FKEY, OPERATE_TIME);

create table HEPLG01
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null
        constraint PK_HEPLG01
            primary key,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null
);
comment on table HEPLG01 is '审计日志_登录登出认证_履历表';
comment on column HEPLG01.OPERATION_ID is '操作ID';
comment on column HEPLG01.OPERATION_TRACE_ID is '操作行为ID';
comment on column HEPLG01.OPERATION_SPAN_ID is '层级ID';
comment on column HEPLG01.OPERATION_TYPE_ENAME is '操作分类';
comment on column HEPLG01.OPERATION_TYPE_CNAME is '操作分类名称';
comment on column HEPLG01.OPERATOR is '操作人';
comment on column HEPLG01.OPERATION_DESC is '操作描述';
comment on column HEPLG01.OPERATION_TIME is '操作时间';
comment on column HEPLG01.OPERATION_INVOKE_KEY is '关键字';
comment on column HEPLG01.REC_CREATOR is '记录创建责任者';
comment on column HEPLG01.REC_CREATE_TIME is '记录创建时刻';
comment on column HEPLG01.REC_REVISOR is '记录修改责任者';
comment on column HEPLG01.REC_REVISE_TIME is '记录修改时刻';
comment on column HEPLG01.ARCHIVE_FLAG is '归档标记';
comment on column HEPLG01.EXT1 is '扩展字段1';
comment on column HEPLG01.EXT2 is '扩展字段2';
comment on column HEPLG01.EXT3 is '扩展字段3';
comment on column HEPLG01.EXT4 is '扩展字段4';
comment on column HEPLG01.EXT5 is '扩展字段5';
comment on column HEPLG01.CLIENT_IP is '客户端IP';
create   index HEPLG01_IDX_OPERAOTR
    on HEPLG01 (OPERATOR);
create   index HEPLG01_IDX_TIME
    on HEPLG01 (OPERATION_TIME);
create   index HEPLG01_IDX_TYPE
    on HEPLG01 (OPERATION_TYPE_ENAME);

create table HEPLG02
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null
        constraint PK_HEPLG02
            primary key,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null
);
comment on table HEPLG02 is '审计日志_页面按钮分发_履历表';
comment on column HEPLG02.OPERATION_ID is '操作ID';
comment on column HEPLG02.OPERATION_TRACE_ID is '操作行为ID';
comment on column HEPLG02.OPERATION_SPAN_ID is '层级ID';
comment on column HEPLG02.OPERATION_TYPE_ENAME is '操作分类';
comment on column HEPLG02.OPERATION_TYPE_CNAME is '操作分类名称';
comment on column HEPLG02.OPERATOR is '操作人';
comment on column HEPLG02.OPERATION_DESC is '操作描述';
comment on column HEPLG02.OPERATION_TIME is '操作时间';
comment on column HEPLG02.OPERATION_INVOKE_KEY is '关键字';
comment on column HEPLG02.REC_CREATOR is '记录创建责任者';
comment on column HEPLG02.REC_CREATE_TIME is '记录创建时刻';
comment on column HEPLG02.REC_REVISOR is '记录修改责任者';
comment on column HEPLG02.REC_REVISE_TIME is '记录修改时刻';
comment on column HEPLG02.ARCHIVE_FLAG is '归档标记';
comment on column HEPLG02.EXT1 is '扩展字段1';
comment on column HEPLG02.EXT2 is '扩展字段2';
comment on column HEPLG02.EXT3 is '扩展字段3';
comment on column HEPLG02.EXT4 is '扩展字段4';
comment on column HEPLG02.EXT5 is '扩展字段5';

comment on column HEPLG02.CLIENT_IP is '客户端IP';
create   index HEPLG02_IDX_OPERAOTR
    on HEPLG02 (OPERATOR);
create   index HEPLG02_IDX_TIME
    on HEPLG02 (OPERATION_TIME);
create   index HEPLG02_IDX_TYPE
    on HEPLG02 (OPERATION_TYPE_ENAME);

create table HEPLG03
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null
        constraint PK_HEPLG03
            primary key,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    CLIENT_IP            VARCHAR(32)  default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null
);
comment on table HEPLG03 is '审计日志_HTTP请求_履历表';
comment on column HEPLG03.OPERATION_ID is '操作ID';
comment on column HEPLG03.OPERATION_TRACE_ID is '操作行为ID';
comment on column HEPLG03.OPERATION_SPAN_ID is '层级ID';
comment on column HEPLG03.OPERATION_TYPE_ENAME is '操作分类';
comment on column HEPLG03.OPERATION_TYPE_CNAME is '操作分类名称';
comment on column HEPLG03.OPERATOR is '操作人';
comment on column HEPLG03.OPERATION_DESC is '操作描述';
comment on column HEPLG03.OPERATION_TIME is '操作时间';
comment on column HEPLG03.OPERATION_INVOKE_KEY is '关键字';
comment on column HEPLG03.CLIENT_IP is '客户端IP';
comment on column HEPLG03.REC_CREATOR is '记录创建责任者';
comment on column HEPLG03.REC_CREATE_TIME is '记录创建时刻';
comment on column HEPLG03.REC_REVISOR is '记录修改责任者';
comment on column HEPLG03.REC_REVISE_TIME is '记录修改时刻';
comment on column HEPLG03.ARCHIVE_FLAG is '归档标记';
comment on column HEPLG03.EXT1 is '扩展字段1';
comment on column HEPLG03.EXT2 is '扩展字段2';
comment on column HEPLG03.EXT3 is '扩展字段3';
comment on column HEPLG03.EXT4 is '扩展字段4';
comment on column HEPLG03.EXT5 is '扩展字段5';
create   index HEPLG03_IDX_OPERAOTR
    on HEPLG03 (OPERATOR);
create   index HEPLG03_IDX_TIME
    on HEPLG03 (OPERATION_TIME);
create   index HEPLG03_IDX_TYPE
    on HEPLG03 (OPERATION_TYPE_ENAME);

create table TEDCC01
(
    ID              VARCHAR(100) not null,
    MODULE_NAME     VARCHAR(100) not null,
    CONFIG_ENV      VARCHAR(100) not null,
    FKEY            VARCHAR(100) not null,
    FVALUE          VARCHAR(100) not null,
    REC_CREATOR     VARCHAR(100),
    REC_CREATE_TIME VARCHAR(14) not null default ' ',
    REC_REVISOR     VARCHAR(100),
    REC_REVISE_TIME VARCHAR(14) not null default ' ',
    DES             VARCHAR(255) default ' '
);
comment on column TEDCC01.ID is '配置ID';
comment on column TEDCC01.MODULE_NAME is '组件名称';
comment on column TEDCC01.CONFIG_ENV is '配置环境';
comment on column TEDCC01.FKEY is '配置名称';
comment on column TEDCC01.FVALUE is '配置内容';
comment on column TEDCC01.REC_CREATOR is '创建人';
comment on column TEDCC01.REC_CREATE_TIME is '创建时间';
comment on column TEDCC01.REC_REVISOR is '修改人';
comment on column TEDCC01.REC_REVISE_TIME is '修改时间';
comment on column TEDCC01.DES is '描述';
ALTER TABLE TEDCC01 ADD CONSTRAINT TEDCC01_PK PRIMARY KEY (ID);
ALTER TABLE TEDCC01 ADD CONSTRAINT TEDCC01_UN UNIQUE (MODULE_NAME, CONFIG_ENV, FKEY);

CREATE TABLE TEDCC02 (
                         CONFIG_ENV_ID VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         CONFIG_ENV_CNAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         PROJECT VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         VERSION VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         PROJECT_ENV VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(16) ,
                         REC_CREATE_TIME VARCHAR(17) ,
                         REC_REVISOR VARCHAR(16) ,
                         REC_REVISE_TIME VARCHAR(17) ,
                         MODULE_ENAME_1 VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         MODULE_ENAME_2 VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCC02.CONFIG_ENV_ID IS '配置环境标识';
COMMENT ON COLUMN TEDCC02.CONFIG_ENV_CNAME IS '配置环境中文名';
COMMENT ON COLUMN TEDCC02.PROJECT IS '项目';
COMMENT ON COLUMN TEDCC02.VERSION IS '版本';
COMMENT ON COLUMN TEDCC02.PROJECT_ENV IS '环境';
COMMENT ON COLUMN TEDCC02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCC02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCC02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCC02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCC02.MODULE_ENAME_1 IS '一级模块';
COMMENT ON COLUMN TEDCC02.MODULE_ENAME_2 IS '二级模块';
COMMENT ON COLUMN TEDCC02.TENANT_ID IS '租户';
COMMENT ON COLUMN TEDCC02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCC02 IS '配置环境';
ALTER TABLE TEDCC02 ADD CONSTRAINT PK_TEDCC02  PRIMARY KEY (CONFIG_ENV_ID) ;

CREATE TABLE TEDCC03 (
                         STATUS  DECIMAL(11) ,
                         CONFIG_TYPE VARCHAR(255) ,
                         CONFIG_CUE VARCHAR(255) ,
                         REC_CREATOR VARCHAR(16) ,
                         REC_CREATE_TIME VARCHAR(17) ,
                         REC_REVISOR VARCHAR(16) ,
                         REC_REVISE_TIME VARCHAR(17) ,
                         CONFIG_ENV_ID VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         FKEY VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         FVALUE VARCHAR(4000)  DEFAULT ' '    NOT NULL,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         CONFIG_DESC VARCHAR(255)
);
COMMENT ON COLUMN TEDCC03.STATUS IS '状态：1是正常 0是删除';
COMMENT ON COLUMN TEDCC03.CONFIG_TYPE IS '配置项类别';
COMMENT ON COLUMN TEDCC03.CONFIG_CUE IS '配置项提示';
COMMENT ON COLUMN TEDCC03.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCC03.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCC03.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCC03.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCC03.CONFIG_ENV_ID IS '配置环境';
COMMENT ON COLUMN TEDCC03.FKEY IS '配置名称';
COMMENT ON COLUMN TEDCC03.FVALUE IS '配置内容';
COMMENT ON COLUMN TEDCC03.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDCC03.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCC03 IS '配置信息';
ALTER TABLE TEDCC03 ADD CONSTRAINT PK_TEDCC03  PRIMARY KEY (CONFIG_ENV_ID,FKEY) ;

CREATE TABLE TEDCC12
(
    REC_CREATOR            VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME        VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR            VARCHAR(16)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG           VARCHAR(1)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME        VARCHAR(17)  DEFAULT ' '   NOT NULL,
    URL_CONFIG_ENAME       VARCHAR(32)  DEFAULT ' '   NOT NULL,
    CONFIG_DESC            VARCHAR(255)  DEFAULT ' '   NOT NULL,
    MATCH_TYPE             VARCHAR(10)  DEFAULT ' '   NOT NULL,
    MATCH_EXPRESSION       VARCHAR(4000)  DEFAULT ' '   NOT NULL,
    IS_AUTHORIZED          VARCHAR(1)  DEFAULT ' '   NOT NULL,
    CONFIG_ID              VARCHAR(32)  DEFAULT ' '   NOT NULL,
    SORT_INDEX             DECIMAL(5,0)  DEFAULT 0   NOT NULL
);
alter table TEDCC12 add constraint PK_CONFIG_ID  primary key (CONFIG_ID);

COMMENT ON TABLE TEDCC12 IS '访问地址管理表';
COMMENT ON COLUMN TEDCC12.REC_CREATOR IS '创建人';
COMMENT ON COLUMN TEDCC12.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN TEDCC12.REC_REVISOR IS '修改人';
COMMENT ON COLUMN TEDCC12.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDCC12.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN TEDCC12.URL_CONFIG_ENAME IS '访问地址配置英文名';
COMMENT ON COLUMN TEDCC12.CONFIG_DESC IS '配置描述';
COMMENT ON COLUMN TEDCC12.MATCH_TYPE IS '匹配模式';
COMMENT ON COLUMN TEDCC12.MATCH_EXPRESSION IS '匹配表达式';
COMMENT ON COLUMN TEDCC12.IS_AUTHORIZED IS '是否授权';
COMMENT ON COLUMN TEDCC12.CONFIG_ID IS '配置ID';
COMMENT ON COLUMN TEDCC12.SORT_INDEX IS '排序';


CREATE TABLE TEDCM00 (
                         CODESET_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         CODESET_NAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         CODESET_ENAME VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         GB_CODE VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REMARK VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         CODESET_TYPE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         CODESET_URL VARCHAR(2000) DEFAULT ' ' NOT NULL,
                         PROJECT_NAME VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         SUB_CODESET_CODE VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         REF_ID VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         CODESET_LEVEL VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         CATEGORY VARCHAR(255)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCM00.CODESET_CODE IS '代码分类编号';
COMMENT ON COLUMN TEDCM00.CODESET_NAME IS '代码分类名称';
COMMENT ON COLUMN TEDCM00.CODESET_ENAME IS '代码分类英文名称';
COMMENT ON COLUMN TEDCM00.GB_CODE IS '国标编号';
COMMENT ON COLUMN TEDCM00.REMARK IS '备注';
COMMENT ON COLUMN TEDCM00.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCM00.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCM00.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCM00.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCM00.CODESET_TYPE IS '代码类型';
COMMENT ON COLUMN TEDCM00.CODESET_URL IS '对应URL';
COMMENT ON COLUMN TEDCM00.PROJECT_NAME IS '应用系统';
COMMENT ON COLUMN TEDCM00.SUB_CODESET_CODE IS '子代码分类编号';
COMMENT ON COLUMN TEDCM00.REF_ID IS '关联字段';
COMMENT ON COLUMN TEDCM00.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDCM00.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDCM00.CATEGORY IS '目录';
COMMENT ON TABLE TEDCM00 IS '代码大类表';
ALTER TABLE TEDCM00 ADD CONSTRAINT PK_TEDCM00  PRIMARY KEY (CODESET_CODE,PROJECT_NAME) ;

CREATE TABLE TEDCM0001 (
                           ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                           CATEGORY_KEY VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                           CATEGORY_NAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                           PARENT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                           REC_CREATOR VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                           REC_CREATE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                           REC_REVISOR VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                           REC_REVISE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                           ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON TABLE TEDCM0001 IS '代码分类目录表';
ALTER TABLE TEDCM0001 ADD CONSTRAINT PK_TEDCM0001  PRIMARY KEY (ID) ;

CREATE TABLE TEDCM01 (
                         CODESET_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ITEM_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ITEM_CNAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         ITEM_ENAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         REMARK VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         ITEM_STATUS VARCHAR(2)  DEFAULT ' '    NOT NULL ,
                         SORT_ID VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         STATUS VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         PROJECT_NAME VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         SUB_CODESET_CODE VARCHAR(64) ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ITEM_FILTER_CODE VARCHAR(255) DEFAULT ' ' NOT NULL,
                         ITEM_FILTER_CNAME VARCHAR(510) DEFAULT ' ' NOT NULL,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCM01.CODESET_CODE IS '代码分类编号';
COMMENT ON COLUMN TEDCM01.ITEM_CODE IS '代码明细编号';
COMMENT ON COLUMN TEDCM01.ITEM_CNAME IS '代码明细中文名称';
COMMENT ON COLUMN TEDCM01.ITEM_ENAME IS '代码明细英文名称';
COMMENT ON COLUMN TEDCM01.REMARK IS '备注';
COMMENT ON COLUMN TEDCM01.ITEM_STATUS IS '代码状态';
COMMENT ON COLUMN TEDCM01.SORT_ID IS '顺序号';
COMMENT ON COLUMN TEDCM01.STATUS IS '字段状态';
COMMENT ON COLUMN TEDCM01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCM01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCM01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCM01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCM01.PROJECT_NAME IS '应用系统';
COMMENT ON COLUMN TEDCM01.SUB_CODESET_CODE IS '子代码分类编号';
COMMENT ON COLUMN TEDCM01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDCM01.ITEM_FILTER_CODE IS '代码明细筛选';
COMMENT ON COLUMN TEDCM01.ITEM_FILTER_CNAME IS '代码明细筛选中文名称';
COMMENT ON COLUMN TEDCM01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCM01 IS '代码详情表';
ALTER TABLE TEDCM01 ADD CONSTRAINT PK_TEDCM01  PRIMARY KEY (CODESET_CODE, ITEM_CODE, PROJECT_NAME) ;

CREATE TABLE TEDCM02 (
                         CASCADE_ID VARCHAR(100)  DEFAULT ' '    NOT NULL ,
                         CASCADE_CODESET_CODE VARCHAR(100)  DEFAULT ' '    NOT NULL ,
                         CASCADE_TYPE VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         CODESET_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ITEM_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         SUB_CODESET_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         SUB_ITEM_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ACTIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         PROJECT_NAME VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDCM02.CASCADE_ID IS 'ID';
COMMENT ON COLUMN TEDCM02.CASCADE_CODESET_CODE IS '级联代码分类编号';
COMMENT ON COLUMN TEDCM02.CASCADE_TYPE IS '级联关系类型';
COMMENT ON COLUMN TEDCM02.CODESET_CODE IS '代码分类编号';
COMMENT ON COLUMN TEDCM02.ITEM_CODE IS '代码明细编号';
COMMENT ON COLUMN TEDCM02.SUB_CODESET_CODE IS '子代码分类编号';
COMMENT ON COLUMN TEDCM02.SUB_ITEM_CODE IS '子代码明细编号';
COMMENT ON COLUMN TEDCM02.ACTIVE_FLAG IS '启用标记';
COMMENT ON COLUMN TEDCM02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDCM02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDCM02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDCM02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDCM02.PROJECT_NAME IS '应用系统';
COMMENT ON COLUMN TEDCM02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDCM02 IS '代码级联关系表';
ALTER TABLE TEDCM02 ADD CONSTRAINT PK_TEDCM02  PRIMARY KEY (CASCADE_ID) ;

CREATE TABLE TEDFA00 (
                         REC_CREATOR VARCHAR(16)   DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)   DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)   DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)   DEFAULT ' '    NOT NULL ,
                         FORM_ENAME VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         FORM_CNAME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         FORM_LOAD_PATH VARCHAR(250)  DEFAULT ' '    NOT NULL ,
                         FORM_TYPE VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         MODULE_ENAME_1 VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         MODULE_ENAME_2 VARCHAR(10)   DEFAULT ' '    NOT NULL ,
                         INIT_LOAD_SERVICE_ENAME VARCHAR(30)  DEFAULT ' '    NOT NULL ,
                         IS_AUTH VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         FORM_PARAM VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         SUBAPP_CODE VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ICON_INFO VARCHAR(128)   DEFAULT ' '    NOT NULL ,
                         BUSINESS_CATEGORY VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         OPERATE_TYPE VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         DIR_ID varchar(32) DEFAULT ' '    NOT NULL,
                         IS_EFFECTIVE VARCHAR(1) DEFAULT '1' NOT NULL,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)   DEFAULT ' '   NOT NULL
);
COMMENT ON COLUMN TEDFA00.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDFA00.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDFA00.REC_REVISOR IS '记录创建责任者';
COMMENT ON COLUMN TEDFA00.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDFA00.FORM_ENAME IS '画面英文名';
COMMENT ON COLUMN TEDFA00.FORM_CNAME IS '画面中文名';
COMMENT ON COLUMN TEDFA00.FORM_LOAD_PATH IS '画面载入的路径';
COMMENT ON COLUMN TEDFA00.FORM_TYPE IS '画面类型';
COMMENT ON COLUMN TEDFA00.MODULE_ENAME_1 IS '一级模块英文名';
COMMENT ON COLUMN TEDFA00.MODULE_ENAME_2 IS '二级模块英文名';
COMMENT ON COLUMN TEDFA00.INIT_LOAD_SERVICE_ENAME IS '初始处理服务英文名';
COMMENT ON COLUMN TEDFA00.IS_AUTH IS '是否授权';
COMMENT ON COLUMN TEDFA00.FORM_PARAM IS '画面参数';
COMMENT ON COLUMN TEDFA00.SUBAPP_CODE IS '子系统代码';
COMMENT ON COLUMN TEDFA00.ICON_INFO IS '图标';
COMMENT ON COLUMN TEDFA00.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDFA00.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDFA00.IS_EFFECTIVE IS '是否有效';
COMMENT ON COLUMN TEDFA00.DIR_ID IS '文件夹ID';
COMMENT ON TABLE TEDFA00 IS '画面信息定义';
ALTER TABLE TEDFA00 ADD CONSTRAINT PK_TEDFA00  PRIMARY KEY (FORM_ENAME) ;

CREATE TABLE TEDFA01 (
                         REC_CREATOR VARCHAR(256) ,
                         REC_CREATE_TIME VARCHAR(14) ,
                         REC_REVISOR VARCHAR(256) ,
                         REC_REVISE_TIME VARCHAR(14) ,
                         ARCHIVE_FLAG VARCHAR(1) ,
                         FORM_ENAME VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         REGION_ID VARCHAR(100)  DEFAULT ' '    NOT NULL ,
                         BUTTON_ENAME VARCHAR(100)  DEFAULT ' '    NOT NULL ,
                         BUTTON_CNAME VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         BUTTON_DESC VARCHAR(100) ,
                         NODE_SORT_ID VARCHAR(20) ,
                         IS_AUTH VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         URI VARCHAR(64) ,
                         LAYOUT VARCHAR(32) ,
                         POSITION VARCHAR(32) ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         BUSINESS_CATEGORY VARCHAR(50) ,
                         OPERATE_TYPE VARCHAR(50)
);
COMMENT ON COLUMN TEDFA01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDFA01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDFA01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDFA01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDFA01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDFA01.FORM_ENAME IS '画面英文名';
COMMENT ON COLUMN TEDFA01.REGION_ID IS '区域标识';
COMMENT ON COLUMN TEDFA01.BUTTON_ENAME IS '按钮英文名';
COMMENT ON COLUMN TEDFA01.BUTTON_CNAME IS '按钮中文';
COMMENT ON COLUMN TEDFA01.BUTTON_DESC IS '按钮描述';
COMMENT ON COLUMN TEDFA01.NODE_SORT_ID IS '节点排序标识';
COMMENT ON COLUMN TEDFA01.IS_AUTH IS '是否授权';
COMMENT ON COLUMN TEDFA01.URI IS '子系统代码';
COMMENT ON COLUMN TEDFA01.LAYOUT IS '子系统代码';
COMMENT ON COLUMN TEDFA01.POSITION IS '子系统代码';
COMMENT ON TABLE TEDFA01 IS '画面按钮信息定义';
ALTER TABLE TEDFA01 ADD CONSTRAINT PK_TEDFA01  PRIMARY KEY (FORM_ENAME, BUTTON_ENAME) ;

create table TEDFA10
(
    PK_TEDFA10_ID   VARCHAR(36)  default ' ' not null
        constraint PK_TEDFA10
            primary key,
    USER_ID         VARCHAR(255) default ' ' not null,
    PROJECT_ENAME   VARCHAR(250) default ' ' not null,
    FORM_ENAME      VARCHAR(20)  default ' ' not null,
    FORM_CNAME      VARCHAR(255) default ' ' not null,
    REC_CREATOR     VARCHAR(256) default ' ' not null,
    REC_CREATE_TIME VARCHAR(14)  default ' ' not null,
    REC_REVISOR     VARCHAR(256) default ' ' not null,
    REC_REVISE_TIME VARCHAR(14)  default ' ' not null,
    ARCHIVE_FLAG    VARCHAR(1)   default ' ' not null,
    NODE_SORT_ID    VARCHAR(20)  default ' ' not null,
    TENANT_ID       VARCHAR(64)  default ' ' not null
);

create table TEDFA11
(
    PK_TEDFA10_ID VARCHAR(36)  default ' ' not null
        constraint PK_TEDFA11
            primary key,
    USER_ID       VARCHAR(255) default ' ' not null,
    PROJECT_ENAME VARCHAR(250) default ' ' not null,
    FORM_ENAME    VARCHAR(20)  default ' ' not null,
    CONTEXT       VARCHAR(20)  default ' ' not null
);

CREATE TABLE TEDFA60 (
                         PK_TEDFA60_ID VARCHAR(36)  DEFAULT ' '    NOT NULL ,
                         PROJECT_ENAME VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         FORM_ENAME VARCHAR(8)  DEFAULT ' '    NOT NULL ,
                         GRID_ID VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         USER_ID VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         COLUMN_ENAME VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         COLUMN_CNAME VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         COLUMN_LOCKED VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         COLUMN_HIDDEN VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         COLUMN_WIDTH  DECIMAL(11)      NOT NULL ,
                         COLUMN_ORDER  DECIMAL(11)      NOT NULL ,
                         SOFT_DELETE VARCHAR(1)
);
ALTER TABLE TEDFA60 ADD CONSTRAINT PK_TEDFA60  PRIMARY KEY (PK_TEDFA60_ID) ;

create table TEDFA61
(
    PK_TEDFA61_ID VARCHAR(36)  default ' ' not null
        constraint PK_TEDFA61
            primary key,
    PROJECT_ENAME VARCHAR(250) default ' ' not null,
    USER_ID       VARCHAR(255) default ' ' not null,
    STYLE_ENAME   VARCHAR(64)  default ' ' not null,
    STYLE_FONT    VARCHAR(255),
    STYLE_ECOLOR  VARCHAR(20),
    TENANT_ID     VARCHAR(64)  default ' ' not null,
    ARCHIVE_FLAG  VARCHAR(1)   default ' ' not null
);

create table TEDFA62
(
    FORM_ENAME VARCHAR(20) default ' ' not null
        primary key,
    OPEN_TYPE  VARCHAR(64) default '0' not null
);

create table TEDFA63
(
    CONFIG_ID       VARCHAR(64)  default ' ' not null
        constraint PK_TEDFA63
            primary key,
    PAGE_ENAME      VARCHAR(64)  default ' ' not null,
    PAGE_CNAME      VARCHAR(64)  default ' ' not null,
    URL             VARCHAR(250) default ' ' not null,
    ICON_TYPE       VARCHAR(64)  default '1' not null,
    ICON_ADDRESS    VARCHAR(64)  default ' ' not null,
    ICON_PARAM      VARCHAR(64)  default ' ' not null,
    USER_ID         VARCHAR(64)  default ' ' not null,
    REC_CREATE_TIME VARCHAR(64)  default ' ' not null,
    REC_REVISE_TIME VARCHAR(64)  default ' ' not null,
    ARCHIVE_FLAG    VARCHAR(64)  default '0' not null
);
comment on column TEDFA63.CONFIG_ID is '页面UUID';
comment on column TEDFA63.PAGE_ENAME is '画面英文名';
comment on column TEDFA63.PAGE_CNAME is '画面中文名';
comment on column TEDFA63.URL is '跳转地址';
comment on column TEDFA63.ICON_TYPE is '预留字段（值为1）';
comment on column TEDFA63.ICON_ADDRESS is '图标名称';
comment on column TEDFA63.ICON_PARAM is '图标颜色';
comment on column TEDFA63.USER_ID is '所属用户';

CREATE TABLE TEDIN01
(
    I18N_KEY VARCHAR(255) DEFAULT ' ' NOT NULL,
    I18N_TEXT VARCHAR(4000) DEFAULT ' ' NOT NULL,
    I18N_LOACLE VARCHAR(10) DEFAULT ' ' NOT NULL
);
alter table TEDIN01 add constraint PK_TEDIN01  primary key (I18N_KEY, I18N_LOACLE);

COMMENT ON TABLE TEDIN01 IS '国际化配置表';
COMMENT ON COLUMN TEDIN01.I18N_KEY IS '国际化定义';
COMMENT ON COLUMN TEDIN01.I18N_TEXT IS '国际化定义值';
COMMENT ON COLUMN TEDIN01.I18N_LOACLE IS '国际化语言环境';

create table TEDLG00
(
    TYPE_NAME         VARCHAR(32)  default ' ' not null,
    TYPE_DESC         VARCHAR(255) default ' ' not null,
    REC_CREATOR       VARCHAR(16)  default ' ' not null,
    REC_CREATE_TIME   VARCHAR(17)  default ' ' not null,
    KEYWORD_ONE       VARCHAR(32)  default ' ',
    KEYWORD_TWO       VARCHAR(32)  default ' ',
    KEYWORD_THREE     VARCHAR(32)  default ' ',
    KEYWORD_FOUR      VARCHAR(32)  default ' ',
    KEYWORD_FIVE      VARCHAR(32)  default ' ',
    REC_REVISOR       VARCHAR(16)  default ' ' not null,
    REC_REVISE_TIME   VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG      VARCHAR(1)   default ' ' not null,
    ARCHIVE_TYPE      VARCHAR(1)   default '3',
    ARCHIVE_KEEP_TIME DECIMAL(5)   default 3   not null,
    constraint PK_TEDLG00
        primary key (TYPE_NAME)
);

comment on column TEDLG00.TYPE_NAME is '类别名称';
comment on column TEDLG00.TYPE_DESC is '类别描述';
comment on column TEDLG00.REC_CREATOR is '创建人';
comment on column TEDLG00.REC_CREATE_TIME is '创建时间';
comment on column TEDLG00.KEYWORD_ONE is '关键字1';
comment on column TEDLG00.KEYWORD_TWO is '关键字2';
comment on column TEDLG00.KEYWORD_THREE is '关键字3';
comment on column TEDLG00.KEYWORD_FOUR is '关键字4';
comment on column TEDLG00.KEYWORD_FIVE is '关键字5';
comment on column TEDLG00.REC_REVISOR is '修改者';
comment on column TEDLG00.REC_REVISE_TIME is '修改时间';
comment on column TEDLG00.ARCHIVE_FLAG is '归档标记';

CREATE TABLE TEDMDM2 (
                         ACCSET_NO  DECIMAL(11,0) NOT NULL ,
                         SEQ_TYPE_ID VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         SEQ_SUBSECS  DECIMAL(2,0) NOT NULL ,
                         DATE_CYCLE VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         SEQ_LMT  DECIMAL(2,0) NOT NULL ,
                         SUBID_LMT_LEN  DECIMAL(3,0) NOT NULL ,
                         REMARK VARCHAR(512)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         IS_LINKED  DECIMAL(1,0) ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDMDM2.ACCSET_NO IS '帐套序号';
COMMENT ON COLUMN TEDMDM2.SEQ_TYPE_ID IS '序列号类型ID';
COMMENT ON COLUMN TEDMDM2.SEQ_SUBSECS IS '序列号分段数';
COMMENT ON COLUMN TEDMDM2.DATE_CYCLE IS '日期循环级别';
COMMENT ON COLUMN TEDMDM2.SEQ_LMT IS '序列号长度限制';
COMMENT ON COLUMN TEDMDM2.SUBID_LMT_LEN IS '子项长度限制';
COMMENT ON COLUMN TEDMDM2.REMARK IS '备注';
COMMENT ON COLUMN TEDMDM2.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDMDM2.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDMDM2.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDMDM2.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDMDM2.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDMDM2.IS_LINKED IS '是否连号';
COMMENT ON TABLE TEDMDM2 IS '序列号定义';
ALTER TABLE TEDMDM2 ADD CONSTRAINT PK_TEDMDM2  PRIMARY KEY (SEQ_TYPE_ID) ;


CREATE TABLE TEDMDM3 (
                         SEQ_TYPE_ID VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         SUBSEC_SEQ  DECIMAL(2,0) NOT NULL ,
                         SUBSEC_NAME VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         SUBSEC_TYPE VARCHAR(2)  DEFAULT ' '    NOT NULL ,
                         SUBSEC_CONTENT VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         SUBSEC_LEN  DECIMAL(2,0) NOT NULL ,
                         DATE_FORMAT VARCHAR(40)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(25)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(25)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDMDM3.SEQ_TYPE_ID IS '序列号类型ID';
COMMENT ON COLUMN TEDMDM3.SUBSEC_SEQ IS '分段序号';
COMMENT ON COLUMN TEDMDM3.SUBSEC_NAME IS '分段名称';
COMMENT ON COLUMN TEDMDM3.SUBSEC_TYPE IS '分段类型';
COMMENT ON COLUMN TEDMDM3.SUBSEC_CONTENT IS '定义内容';
COMMENT ON COLUMN TEDMDM3.SUBSEC_LEN IS '分段长度';
COMMENT ON COLUMN TEDMDM3.DATE_FORMAT IS '日期格式';
COMMENT ON COLUMN TEDMDM3.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDMDM3.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDMDM3.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDMDM3.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDMDM3.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDMDM3 IS '序列号分段信息';
ALTER TABLE TEDMDM3 ADD CONSTRAINT PK_TEDMDM3  PRIMARY KEY (SEQ_TYPE_ID, SUBSEC_SEQ) ;


CREATE TABLE TEDMDM4 (
                         ACCSET_NO  DECIMAL(11,0) NOT NULL ,
                         SEQ_TYPE_ID VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         SEQ_PREFIX VARCHAR(25)  DEFAULT ' '    NOT NULL ,
                         YEAR_MON VARCHAR(8)  DEFAULT ' '    NOT NULL ,
                         CURRENT_SEQ  DECIMAL(11,0) NOT NULL ,
                         REC_CREATOR VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(25)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(25)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDMDM4.ACCSET_NO IS '帐套序号';
COMMENT ON COLUMN TEDMDM4.SEQ_TYPE_ID IS '序列号类型ID';
COMMENT ON COLUMN TEDMDM4.SEQ_PREFIX IS '序列号前缀';
COMMENT ON COLUMN TEDMDM4.YEAR_MON IS '年月';
COMMENT ON COLUMN TEDMDM4.CURRENT_SEQ IS '当前序列';
COMMENT ON COLUMN TEDMDM4.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDMDM4.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDMDM4.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDMDM4.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDMDM4.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDMDM4 IS '当前序列';
ALTER TABLE TEDMDM4 ADD CONSTRAINT PK_TEDMDM4  PRIMARY KEY (ACCSET_NO, SEQ_TYPE_ID, SEQ_PREFIX, YEAR_MON) ;

CREATE TABLE TEDNM01 (
                         MACHINE_ID VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         MACHINE_NAME VARCHAR(50) ,
                         MACHINE_HOST VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         MACHINE_POID VARCHAR(50) ,
                         SYSTEM_TYPE VARCHAR(50) ,
                         AUTH_TYPE VARCHAR(50) ,
                         AUTH_USERNAME VARCHAR(50) ,
                         AUTH_CERT VARCHAR(200) ,
                         AUTH_PORT VARCHAR(50) ,
                         PROTOCOL VARCHAR(200) ,
                         REC_CREATOR VARCHAR(16) ,
                         REC_CREATE_TIME VARCHAR(17) ,
                         REC_REVISOR VARCHAR(16) ,
                         REC_REVISE_TIME VARCHAR(17) ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDNM01.MACHINE_ID IS '主机标识';
COMMENT ON COLUMN TEDNM01.MACHINE_NAME IS '主机名称';
COMMENT ON COLUMN TEDNM01.MACHINE_HOST IS '主机地址';
COMMENT ON COLUMN TEDNM01.MACHINE_POID IS '机器物理标识';
COMMENT ON COLUMN TEDNM01.SYSTEM_TYPE IS '系统类型';
COMMENT ON COLUMN TEDNM01.AUTH_TYPE IS '认证类型';
COMMENT ON COLUMN TEDNM01.AUTH_USERNAME IS '认证账号';
COMMENT ON COLUMN TEDNM01.AUTH_CERT IS '认证凭证';
COMMENT ON COLUMN TEDNM01.AUTH_PORT IS '认证链接端口';
COMMENT ON COLUMN TEDNM01.PROTOCOL IS '上传协议';
COMMENT ON COLUMN TEDNM01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDNM01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDNM01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDNM01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDNM01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDNM01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDNM01 IS '主机信息';
ALTER TABLE TEDNM01 ADD CONSTRAINT PK_TEDNM01  PRIMARY KEY (MACHINE_ID) ;


CREATE TABLE TEDNM02 (
                         NODE_ID VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         NODE_CNAME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         PORT VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         MACHINE_ID VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         PROJECT_ENV VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         MM_TYPE VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         MM_PATH VARCHAR(50) ,
                         STATUS VARCHAR(200)  DEFAULT ' '    NOT NULL ,
                         SERVER_USERNAME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         SERVER_PASSWORD VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         GROUP_TAG VARCHAR(200)  DEFAULT ' '    NOT NULL ,
                         MM_CONTENT VARCHAR(1500) DEFAULT ' ' NOT NULL,
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         CMPT_ENAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         PACKAGE_PATH VARCHAR(100)
);
COMMENT ON COLUMN TEDNM02.NODE_ID IS '节点标识';
COMMENT ON COLUMN TEDNM02.NODE_CNAME IS '节点名称';
COMMENT ON COLUMN TEDNM02.PORT IS 'PORT';
COMMENT ON COLUMN TEDNM02.MACHINE_ID IS '主机标识';
COMMENT ON COLUMN TEDNM02.PROJECT_ENV IS '环境';
COMMENT ON COLUMN TEDNM02.MM_TYPE IS '中间件类型';
COMMENT ON COLUMN TEDNM02.MM_PATH IS '中间件路径';
COMMENT ON COLUMN TEDNM02.STATUS IS '停用标识';
COMMENT ON COLUMN TEDNM02.SERVER_USERNAME IS '服务账号';
COMMENT ON COLUMN TEDNM02.SERVER_PASSWORD IS '账号密码';
COMMENT ON COLUMN TEDNM02.GROUP_TAG IS '分组标识';
COMMENT ON COLUMN TEDNM02.MM_CONTENT IS '中间件脚本内容';
COMMENT ON COLUMN TEDNM02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDNM02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDNM02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDNM02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDNM02.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDNM02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDNM02.CMPT_ENAME IS '组件英文名';
COMMENT ON COLUMN TEDNM02.PACKAGE_PATH IS '上传包路径';
COMMENT ON TABLE TEDNM02 IS '节点信息';
ALTER TABLE TEDNM02 ADD CONSTRAINT PK_TEDNM02  PRIMARY KEY (NODE_ID) ;


CREATE TABLE TEDNM03 (
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         CMPT_ENAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         CMPT_CNAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         CONTEXT_PATH VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         PROJECT_ENAME VARCHAR(50)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDNM03.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDNM03.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDNM03.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDNM03.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDNM03.TENANT_ID IS '归档标记';
COMMENT ON COLUMN TEDNM03.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE TEDNM03 ADD CONSTRAINT PK_TEDNM03 PRIMARY KEY (CMPT_ENAME,PROJECT_ENAME) ;


CREATE TABLE TEDNM04 (
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         CMPT_ENAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         CMPT_MEMBER_ID VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         CMPT_MEMBER_TYPE VARCHAR(10)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDNM04.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDNM04.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDNM04.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDNM04.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDNM04.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDNM04 IS '发布信息';
ALTER TABLE TEDNM04 ADD CONSTRAINT PK_TEDNM04 PRIMARY KEY (CMPT_ENAME,CMPT_MEMBER_ID,CMPT_MEMBER_TYPE) ;


CREATE TABLE TEDNM05 (
                         CMPT_ENAME VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         CMPT_VERSION VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON TABLE TEDNM05 IS 'NGINX配置';
ALTER TABLE TEDNM05 ADD CONSTRAINT PK_TEDNM05 PRIMARY KEY (CMPT_ENAME,CMPT_VERSION) ;


CREATE TABLE TEDNM06 (
                         CONFIG_ID VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         NAME VARCHAR(50) ,
                         CONFIGURE VARCHAR(50) ,
                         PATH VARCHAR(250)
);
ALTER TABLE TEDNM06 ADD CONSTRAINT PK_TEDNM06 PRIMARY KEY (CONFIG_ID) ;

CREATE TABLE TEDNM07
(
    REC_CREATOR           VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME       VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR           VARCHAR(16)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG          VARCHAR(1)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME       VARCHAR(17)  DEFAULT ' '   NOT NULL,
    APP_NAME              VARCHAR(50)  DEFAULT ' '   NOT NULL,
    KEY_TYPE              VARCHAR(10)  DEFAULT ' '   NOT NULL,
    SECRET_KEY            VARCHAR(512)  DEFAULT ' '   NOT NULL,
    ALGORITHM             VARCHAR(20)  DEFAULT ' '   NOT NULL
);
alter table TEDNM07 add constraint PK_TEDNM07  primary key (APP_NAME,KEY_TYPE,ALGORITHM);

COMMENT ON TABLE TEDNM07 IS '表EDNM07';
COMMENT ON COLUMN TEDNM07.REC_CREATOR IS '创建人';
COMMENT ON COLUMN TEDNM07.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN TEDNM07.REC_REVISOR IS '修改人';
COMMENT ON COLUMN TEDNM07.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDNM07.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN TEDNM07.APP_NAME IS '应用名称';
COMMENT ON COLUMN TEDNM07.KEY_TYPE IS '密钥类型';
COMMENT ON COLUMN TEDNM07.SECRET_KEY IS '密钥';
COMMENT ON COLUMN TEDNM07.ALGORITHM IS '算法';

CREATE TABLE TEDNM08
(
    REC_CREATOR           VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME       VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR           VARCHAR(16)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG          VARCHAR(1)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME       VARCHAR(17)  DEFAULT ' '   NOT NULL,
    APP_NAME              VARCHAR(50)  DEFAULT ' '   NOT NULL,
    APP_URL               VARCHAR(256)  DEFAULT ' '   NOT NULL
);
alter table TEDNM08 add constraint PK_TEDNM08_APP_URL  primary key (APP_URL);

COMMENT ON TABLE TEDNM08 IS '表EDNM08';
COMMENT ON COLUMN TEDNM08.REC_CREATOR IS '创建人';
COMMENT ON COLUMN TEDNM08.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN TEDNM08.REC_REVISOR IS '修改人';
COMMENT ON COLUMN TEDNM08.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDNM08.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN TEDNM08.APP_NAME IS '应用名称';
COMMENT ON COLUMN TEDNM08.APP_URL IS '应用地址';

CREATE TABLE TEDPI01 (
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         LEADER VARCHAR(256) DEFAULT ' ' NOT NULL,
                         PROJECT_ENAME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         PROJECT_CNAME VARCHAR(250)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         PROJECT_DESC VARCHAR(2000) DEFAULT ' ' NOT NULL
);
COMMENT ON COLUMN TEDPI01.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDPI01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDPI01.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDPI01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDPI01.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDPI01.PROJECT_CNAME IS '项目中文名';
COMMENT ON COLUMN TEDPI01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDPI01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDPI01.PROJECT_DESC IS '项目描述';
COMMENT ON TABLE TEDPI01 IS '项目信息';
ALTER TABLE TEDPI01 ADD CONSTRAINT PK_TEDPI01 PRIMARY KEY (PROJECT_ENAME) ;


CREATE TABLE TEDPI02 (
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         PROJECT_ENAME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         MODULE_ENAME_1 VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         MODULE_CNAME_1 VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         INDEX_SPACE_ENAME VARCHAR(40)  DEFAULT ' '    NOT NULL ,
                         TABLE_SPACE_ENAME VARCHAR(40)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDPI02.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDPI02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDPI02.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDPI02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDPI02.PROJECT_ENAME IS '项目英文名';
COMMENT ON COLUMN TEDPI02.MODULE_ENAME_1 IS '模块英文名称';
COMMENT ON COLUMN TEDPI02.MODULE_CNAME_1 IS '项目中文名';
COMMENT ON COLUMN TEDPI02.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDPI02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDPI02.INDEX_SPACE_ENAME IS '索引空间英文名称';
COMMENT ON COLUMN TEDPI02.TABLE_SPACE_ENAME IS '表空间英文名称';
COMMENT ON TABLE TEDPI02 IS '项目模块信息';
ALTER TABLE TEDPI02 ADD CONSTRAINT PK_TEDPI02 PRIMARY KEY (MODULE_ENAME_1) ;


CREATE TABLE TEDPI03 (
                         MODULE_ENAME_2 VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         MODULE_CNAME_2 VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         MODULE_ENAME_1 VARCHAR(10)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(17)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDPI03.MODULE_ENAME_2 IS '二级模块英文名';
COMMENT ON COLUMN TEDPI03.MODULE_CNAME_2 IS '二级模块';
COMMENT ON COLUMN TEDPI03.MODULE_ENAME_1 IS '一级模块英文名';
COMMENT ON COLUMN TEDPI03.REC_CREATOR IS '记录创建者';
COMMENT ON COLUMN TEDPI03.REC_CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN TEDPI03.REC_REVISOR IS '记录修改人员';
COMMENT ON COLUMN TEDPI03.REC_REVISE_TIME IS '记录修改时间';
COMMENT ON COLUMN TEDPI03.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TEDPI03.ARCHIVE_FLAG IS '归档标记';
COMMENT ON TABLE TEDPI03 IS '版本表';
ALTER TABLE TEDPI03 ADD CONSTRAINT PK_TEDPI03 PRIMARY KEY (MODULE_ENAME_1,MODULE_ENAME_2) ;


CREATE TABLE TEDPI10 (
                         REC_CREATOR VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1) ,
                         TREE_ENAME VARCHAR(30)  DEFAULT ' '    NOT NULL ,
                         NODE_ENAME VARCHAR(30)  DEFAULT ' '    NOT NULL ,
                         NODE_CNAME VARCHAR(80)  DEFAULT ' '    NOT NULL ,
                         NODE_TYPE  DECIMAL(1,0) DEFAULT 0  NOT NULL ,
                         NODE_URL VARCHAR(200)  DEFAULT ' '    NOT NULL ,
                         NODE_SORT_ID VARCHAR(20)  DEFAULT ' '    NOT NULL ,
                         NODE_PARAM VARCHAR(200) ,
                         NODE_IMAGE_PATH VARCHAR(200) ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL
);
COMMENT ON COLUMN TEDPI10.REC_CREATOR IS '记录创建责任者';
COMMENT ON COLUMN TEDPI10.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TEDPI10.REC_REVISOR IS '记录修改责任者';
COMMENT ON COLUMN TEDPI10.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TEDPI10.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TEDPI10.TREE_ENAME IS '节点树英文名';
COMMENT ON COLUMN TEDPI10.NODE_ENAME IS '节点英文名';
COMMENT ON COLUMN TEDPI10.NODE_CNAME IS '节点中文名';
COMMENT ON COLUMN TEDPI10.NODE_TYPE IS '节点类型';
COMMENT ON COLUMN TEDPI10.NODE_URL IS '节点URL';
COMMENT ON COLUMN TEDPI10.NODE_SORT_ID IS '节点排序标识';
COMMENT ON COLUMN TEDPI10.NODE_PARAM IS '节点参数配置';
COMMENT ON COLUMN TEDPI10.NODE_IMAGE_PATH IS '节点图片路径';
COMMENT ON TABLE TEDPI10 IS '项目菜单节点信息';
ALTER TABLE TEDPI10 ADD CONSTRAINT PK_TEDPI10 PRIMARY KEY (TREE_ENAME, NODE_ENAME) ;

create table TEDPI10_EX
(
    TREE_ENAME VARCHAR(30) default ' ' not null,
    NODE_ENAME VARCHAR(30) default ' ' not null,
    SHOW_FLAG  VARCHAR(10)             not null,
    primary key (TREE_ENAME, NODE_ENAME)
);
comment on table TEDPI10_EX is '菜单信息管理扩展表';
comment on column TEDPI10_EX.TREE_ENAME is '节点树代码';
comment on column TEDPI10_EX.NODE_ENAME is '节点页面代码';
comment on column TEDPI10_EX.SHOW_FLAG is '是否可见';

create table TEPLG01
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null
        constraint PK_TEPLG01
            primary key,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null
);
comment on table TEPLG01 is '审计日志_登录登出认证';
comment on column TEPLG01.OPERATION_ID is '操作ID';
comment on column TEPLG01.OPERATION_TRACE_ID is '操作行为ID';
comment on column TEPLG01.OPERATION_SPAN_ID is '层级ID';
comment on column TEPLG01.OPERATION_TYPE_ENAME is '操作分类';
comment on column TEPLG01.OPERATION_TYPE_CNAME is '操作分类名称';
comment on column TEPLG01.OPERATOR is '操作人';
comment on column TEPLG01.OPERATION_DESC is '操作描述';
comment on column TEPLG01.OPERATION_TIME is '操作时间';
comment on column TEPLG01.OPERATION_INVOKE_KEY is '关键字';
comment on column TEPLG01.REC_CREATOR is '记录创建责任者';
comment on column TEPLG01.REC_CREATE_TIME is '记录创建时刻';
comment on column TEPLG01.REC_REVISOR is '记录修改责任者';
comment on column TEPLG01.REC_REVISE_TIME is '记录修改时刻';
comment on column TEPLG01.ARCHIVE_FLAG is '归档标记';
comment on column TEPLG01.EXT1 is '扩展字段1';
comment on column TEPLG01.EXT2 is '扩展字段2';
comment on column TEPLG01.EXT3 is '扩展字段3';
comment on column TEPLG01.EXT4 is '扩展字段4';
comment on column TEPLG01.EXT5 is '扩展字段5';
comment on column TEPLG01.CLIENT_IP is '客户端IP';
create index TEPLG01_IDX_OPERAOTR
    on TEPLG01 (OPERATOR);
create index TEPLG01_IDX_TIME
    on TEPLG01 (OPERATION_TIME);
create index TEPLG01_IDX_TYPE
    on TEPLG01 (OPERATION_TYPE_ENAME);

create table TEPLG02
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null
        constraint PK_TEPLG02
            primary key,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null,
    CLIENT_IP          VARCHAR(32)  default ' ' not null
);
comment on table TEPLG02 is '审计日志_页面按钮分发';
comment on column TEPLG02.OPERATION_ID is '操作ID';
comment on column TEPLG02.OPERATION_TRACE_ID is '操作行为ID';
comment on column TEPLG02.OPERATION_SPAN_ID is '层级ID';
comment on column TEPLG02.OPERATION_TYPE_ENAME is '操作分类';
comment on column TEPLG02.OPERATION_TYPE_CNAME is '操作分类名称';
comment on column TEPLG02.OPERATOR is '操作人';
comment on column TEPLG02.OPERATION_DESC is '操作描述';
comment on column TEPLG02.OPERATION_TIME is '操作时间';
comment on column TEPLG02.OPERATION_INVOKE_KEY is '关键字';
comment on column TEPLG02.REC_CREATOR is '记录创建责任者';
comment on column TEPLG02.REC_CREATE_TIME is '记录创建时刻';
comment on column TEPLG02.REC_REVISOR is '记录修改责任者';
comment on column TEPLG02.REC_REVISE_TIME is '记录修改时刻';
comment on column TEPLG02.ARCHIVE_FLAG is '归档标记';
comment on column TEPLG02.EXT1 is '扩展字段1';
comment on column TEPLG02.EXT2 is '扩展字段2';
comment on column TEPLG02.EXT3 is '扩展字段3';
comment on column TEPLG02.EXT4 is '扩展字段4';
comment on column TEPLG02.EXT5 is '扩展字段5';
comment on column TEPLG02.CLIENT_IP is '客户端IP';
create  index TEPLG02_IDX_OPERAOTR
    on TEPLG02 (OPERATOR);
create  index TEPLG02_IDX_TIME
    on TEPLG02 (OPERATION_TIME);
create  index TEPLG02_IDX_TYPE
    on TEPLG02 (OPERATION_TYPE_ENAME);

create table TEPLG03
(
    OPERATION_ID         VARCHAR(32)  default ' ' not null
        constraint PK_TEPLG03
            primary key,
    OPERATION_TRACE_ID   VARCHAR(32)  default ' ' not null,
    OPERATION_SPAN_ID    VARCHAR(32)  default ' ' not null,
    OPERATION_TYPE_ENAME VARCHAR(4)   default ' ' not null,
    OPERATION_TYPE_CNAME VARCHAR(32)  default ' ' not null,
    OPERATOR             VARCHAR(32)  default ' ' not null,
    OPERATION_DESC       VARCHAR(512) default ' ' not null,
    OPERATION_TIME       VARCHAR(17)  default ' ' not null,
    OPERATION_INVOKE_KEY VARCHAR(512) default ' ' not null,
    CLIENT_IP            VARCHAR(32)  default ' ' not null,
    REC_CREATOR          VARCHAR(32)  default ' ' not null,
    REC_CREATE_TIME      VARCHAR(17)  default ' ' not null,
    REC_REVISOR          VARCHAR(32)  default ' ' not null,
    REC_REVISE_TIME      VARCHAR(17)  default ' ' not null,
    ARCHIVE_FLAG         VARCHAR(1)   default ' ' not null,
    EXT1                 VARCHAR(3000) default ' ' not null,
    EXT2                 VARCHAR(3000) default ' ' not null,
    EXT3                 VARCHAR(3000) default ' ' not null,
    EXT4                 VARCHAR(3000) default ' ' not null,
    EXT5                 VARCHAR(3000) default ' ' not null,
    MODULE_NAME          VARCHAR(32)  default ' ' not null
);
comment on table TEPLG03 is '审计日志_HTTP请求';
comment on column TEPLG03.OPERATION_ID is '操作ID';
comment on column TEPLG03.OPERATION_TRACE_ID is '操作行为ID';
comment on column TEPLG03.OPERATION_SPAN_ID is '层级ID';
comment on column TEPLG03.OPERATION_TYPE_ENAME is '操作分类';
comment on column TEPLG03.OPERATION_TYPE_CNAME is '操作分类名称';
comment on column TEPLG03.OPERATOR is '操作人';
comment on column TEPLG03.OPERATION_DESC is '操作描述';
comment on column TEPLG03.OPERATION_TIME is '操作时间';
comment on column TEPLG03.OPERATION_INVOKE_KEY is '关键字';
comment on column TEPLG03.CLIENT_IP is '客户端IP';
comment on column TEPLG03.REC_CREATOR is '记录创建责任者';
comment on column TEPLG03.REC_CREATE_TIME is '记录创建时刻';
comment on column TEPLG03.REC_REVISOR is '记录修改责任者';
comment on column TEPLG03.REC_REVISE_TIME is '记录修改时刻';
comment on column TEPLG03.ARCHIVE_FLAG is '归档标记';
comment on column TEPLG03.EXT1 is '扩展字段1';
comment on column TEPLG03.EXT2 is '扩展字段2';
comment on column TEPLG03.EXT3 is '扩展字段3';
comment on column TEPLG03.EXT4 is '扩展字段4';
comment on column TEPLG03.EXT5 is '扩展字段5';
create   index TEPLG03_IDX_OPERAOTR
    on TEPLG03 (OPERATOR);
create   index TEPLG03_IDX_TIME
    on TEPLG03 (OPERATION_TIME);
create   index TEPLG03_IDX_TYPE
    on TEPLG03 (OPERATION_TYPE_ENAME);

CREATE TABLE TEUDM01 (
                         DIR_ID VARCHAR(36)  DEFAULT ' '    NOT NULL ,
                         DIR_ENAME VARCHAR(256)  DEFAULT ' '    NOT NULL ,
                         DIR_CNAME VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         PARENT_ID VARCHAR(36)  DEFAULT ' '    NOT NULL ,
                         IS_LEAF VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         DIR_PATH VARCHAR(512)  DEFAULT ' '    NOT NULL ,
                         PROJECT_ENAME VARCHAR(250)  DEFAULT ' '    NOT NULL ,
                         MODULE_ENAME VARCHAR(50)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(512)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(512)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         REAL_PATH VARCHAR(512) ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL
);
ALTER TABLE TEUDM01 ADD CONSTRAINT PK_TEUDM01 PRIMARY KEY (DIR_ID, PROJECT_ENAME, MODULE_ENAME) ;


CREATE TABLE TEUDM02 (
                         DOC_ID VARCHAR(36)  DEFAULT ' '    NOT NULL ,
                         DIR_ID VARCHAR(36)  DEFAULT ' '    NOT NULL ,
                         DOC_NAME VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         CHG_NAME VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         DOC_SIZE  DECIMAL(16,0) NOT NULL ,
                         DOC_TAG VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         REC_CREATOR VARCHAR(512)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         REC_REVISOR VARCHAR(512)  DEFAULT ' '    NOT NULL ,
                         REC_REVISE_TIME VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         ARCHIVE_FLAG VARCHAR(1)  DEFAULT ' '    NOT NULL ,
                         TENANT_ID VARCHAR(64)  DEFAULT ' '    NOT NULL,
                         SHARED_DOC_TYPE VARCHAR(2) DEFAULT '0' NOT NULL
);
ALTER TABLE TEUDM02 ADD CONSTRAINT PK_TEUDM02 PRIMARY KEY (DOC_ID) ;

create table TEUDM03
(
    REC_CREATOR     VARCHAR(16)  default ' ',
    REC_REVISOR     VARCHAR(16)  default ' ',
    REC_CREATE_TIME VARCHAR(14)  default ' ',
    REC_REVISE_TIME VARCHAR(14)  default ' ',
    ARCHIVE_FLAG    VARCHAR(1)   default ' ',
    FORM_ENAME      VARCHAR(20)                  not null,
    LOCALE_LANG     VARCHAR(20)  default 'zh_CN' not null,
    DOC_PATH        VARCHAR(250) default ' ',
    DOC_CNAME       VARCHAR(200) default ' ',
    KEY_WORD        VARCHAR(200) default ' ',
    BOOKMARK        VARCHAR(200) default ' ',
    VERSION         DECIMAL(5) default 0,
    constraint TEUDM03_PRIMARY_KEY
        primary key (FORM_ENAME, LOCALE_LANG)
);

comment on table TEUDM03 is '帮助文档管理表';
comment on column TEUDM03.REC_CREATOR is '创建人';
comment on column TEUDM03.REC_REVISOR is '修改人';
comment on column TEUDM03.REC_CREATE_TIME is '创建时间';
comment on column TEUDM03.REC_REVISE_TIME is '修改时间';
comment on column TEUDM03.ARCHIVE_FLAG is '归档标记';
comment on column TEUDM03.FORM_ENAME is '页面英文名';
comment on column TEUDM03.LOCALE_LANG is '国际化语言';
comment on column TEUDM03.DOC_PATH is '文档路径';
comment on column TEUDM03.DOC_CNAME is '文档中文名';
comment on column TEUDM03.KEY_WORD is '关键字';
comment on column TEUDM03.BOOKMARK is '书签';

create table TEDFA02
(
    FORM_TYPE       varchar(10)                   not null ,
    DIR_ID          varchar(64)                   not null ,
    PARENT_ID       varchar(64) default ' '       not null  ,
    GRAPH_DIR       varchar(255)                  not null ,
    SORT_INDEX      int   default  0  not null,
    REC_CREATOR     varchar(16)                   null ,
    REC_CREATE_TIME varchar(17)                   null ,
    REC_REVISOR     varchar(16)                   null ,
    REC_REVISE_TIME varchar(17)                   null ,
    TENANT_ID       varchar(64) default 'IPLAT4J' not null ,
    ARCHIVE_FLAG    varchar(1)  default '0'       not null ,
    primary key (DIR_ID, FORM_TYPE)
);

create index IDX_TEDFA02_PARENT_ID on TEDFA02 (PARENT_ID);

comment on table TEDFA02 is '画面目录表';
comment on column TEDFA02.FORM_TYPE is '画面类型';
comment on column TEDFA02.DIR_ID is '文件夹ID';
comment on column TEDFA02.PARENT_ID is '父目录文件夹ID';
comment on column TEDFA02.GRAPH_DIR is '文件夹名称';
comment on column TEDFA02.REC_CREATOR is '记录创建责任者';
comment on column TEDFA02.REC_CREATE_TIME is '记录创建时刻';
comment on column TEDFA02.REC_REVISOR is '记录修改责任者';
comment on column TEDFA02.REC_REVISE_TIME is '记录修改时刻';
comment on column TEDFA02.TENANT_ID is '租户';
comment on column TEDFA02.ARCHIVE_FLAG is '归档标记';
comment on column TEDFA02.SORT_INDEX is '排序';

-- security 安全管理
CREATE TABLE XS_AUTHORIZATION (
                                  SUBJECT_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                  SUBJECT_TYPE VARCHAR(16) ,
                                  OBJECT_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                  OBJECT_TYPE VARCHAR(16) ,
                                  OPERATION_TYPE VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                  REC_CREATOR VARCHAR(32) ,
                                  REC_CREATE_TIME VARCHAR(14) ,
                                  REC_REVISOR VARCHAR(32) ,
                                  REC_REVISE_TIME VARCHAR(14) ,
                                  ARCHIVE_FLAG VARCHAR(1) ,
                                  SORT_INDEX  DECIMAL(11)
);
COMMENT ON COLUMN XS_AUTHORIZATION.SUBJECT_ID IS '授权主体ID';
COMMENT ON COLUMN XS_AUTHORIZATION.SUBJECT_TYPE IS '授权主体类别';
COMMENT ON COLUMN XS_AUTHORIZATION.OBJECT_ID IS '授权客体ID';
COMMENT ON COLUMN XS_AUTHORIZATION.OBJECT_TYPE IS '授权客体类别';
COMMENT ON COLUMN XS_AUTHORIZATION.OPERATION_TYPE IS '操作类型:访问:OPT_ACESS,管理:OPT_MANAGE';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_AUTHORIZATION.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_AUTHORIZATION.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_AUTHORIZATION.SORT_INDEX IS '排序';
ALTER TABLE XS_AUTHORIZATION ADD CONSTRAINT PK_XS_AUTHORIZATION PRIMARY KEY (SUBJECT_ID, OBJECT_ID, OPERATION_TYPE) ;
CREATE INDEX XS_AUTHORIZATION_INDEX_OBJECT_ID ON XS_AUTHORIZATION (OBJECT_ID);

CREATE TABLE XS_AUTH_TYPE_CONFIG
(
    REC_CREATOR                   VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME               VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR                   VARCHAR(16)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG                  VARCHAR(1)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME               VARCHAR(17)  DEFAULT ' '   NOT NULL,
    AUTH_ID                       VARCHAR(32)  DEFAULT ' '   NOT NULL,
    AUTH_TYPE_CODE                VARCHAR(32)  DEFAULT ' '   NOT NULL,
    AUTH_TYPE_DESC                VARCHAR(255)  DEFAULT ' '   NOT NULL,
    AUTH_SERVICE_CONFIG           VARCHAR(255)  DEFAULT ' '   NOT NULL,
    AUTH_OBJE_QUERY_SERVICE       VARCHAR(255)  DEFAULT ' '   NOT NULL
);
alter table XS_AUTH_TYPE_CONFIG add constraint PK_AUTH_ID  primary key (AUTH_ID);

COMMENT ON TABLE XS_AUTH_TYPE_CONFIG IS '鉴权类型配置表';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.AUTH_ID IS 'ID';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.AUTH_TYPE_CODE IS '授权类型代码';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.AUTH_TYPE_DESC IS '授权类型描述';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.AUTH_SERVICE_CONFIG IS '授权服务配置';
COMMENT ON COLUMN XS_AUTH_TYPE_CONFIG.AUTH_OBJE_QUERY_SERVICE IS '授权对象查询服务';



create table XS_IP_USER
(
    IP              VARCHAR(64) default ' '       not null
        primary key,
    LOGIN_NAME      VARCHAR(64) default ' '       not null,
    REC_CREATOR     VARCHAR(32) default ' '       not null,
    REC_CREATE_TIME VARCHAR(14) default ' '       not null,
    REC_REVISOR     VARCHAR(32) default ' '       not null,
    REC_REVISE_TIME VARCHAR(14) default ' '       not null,
    ARCHIVE_FLAG    VARCHAR(1)  default ' '       not null
);
comment on table XS_IP_USER is 'IP与用户关系表';
comment on column XS_IP_USER.IP is 'ip地址';
comment on column XS_IP_USER.LOGIN_NAME is '用户工号';
comment on column XS_IP_USER.REC_CREATOR is '创建人';
comment on column XS_IP_USER.REC_CREATE_TIME is '创建时间';
comment on column XS_IP_USER.REC_REVISOR is '修改人';
comment on column XS_IP_USER.REC_REVISE_TIME is '修改时间';
comment on column XS_IP_USER.ARCHIVE_FLAG is '归档标记';
create index IDX_XS_IP_USER_LOGIN_NAME
    on XS_IP_USER (LOGIN_NAME);

CREATE TABLE XS_RESOURCE (
                             ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                             RESOURCE_ENAME VARCHAR(128) ,
                             RESOURCE_CNAME VARCHAR(256) ,
                             TYPE VARCHAR(16) ,
                             IS_AUTH VARCHAR(2) ,
                             SORT_INDEX  DECIMAL(11) ,
                             REC_CREATOR VARCHAR(32) ,
                             REC_CREATE_TIME VARCHAR(14) ,
                             REC_REVISOR VARCHAR(32) ,
                             REC_REVISE_TIME VARCHAR(14) ,
                             ARCHIVE_FLAG VARCHAR(1) ,
                             TENANT_ID VARCHAR(64)
);
COMMENT ON COLUMN XS_RESOURCE.ID IS '资源ID';
COMMENT ON COLUMN XS_RESOURCE.RESOURCE_ENAME IS '资源英文名';
COMMENT ON COLUMN XS_RESOURCE.RESOURCE_CNAME IS '资源中文名';
COMMENT ON COLUMN XS_RESOURCE.TYPE IS 'PAGE:页面，BUTTON:按钮，AREA页面区域，URL:URL';
COMMENT ON COLUMN XS_RESOURCE.IS_AUTH IS '是否授权(1:授权，-1:不授权)默认:-1';
COMMENT ON COLUMN XS_RESOURCE.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_RESOURCE.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_RESOURCE.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_RESOURCE.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_RESOURCE.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_RESOURCE.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_RESOURCE ADD CONSTRAINT PK_XS_RESOURCE PRIMARY KEY (ID) ;

CREATE TABLE XS_RESOURCE_GROUP (
                                   ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                   RESOURCE_GROUP_ENAME VARCHAR(128) ,
                                   RESOURCE_GROUP_CNAME VARCHAR(128) ,
                                   RESOURCE_GROUP_TYPE VARCHAR(16) ,
                                   SORT_INDEX  DECIMAL(11) ,
                                   REC_CREATOR VARCHAR(32) ,
                                   REC_CREATE_TIME VARCHAR(14) ,
                                   REC_REVISOR VARCHAR(32) ,
                                   REC_REVISE_TIME VARCHAR(14) ,
                                   ARCHIVE_FLAG VARCHAR(1)
);
COMMENT ON COLUMN XS_RESOURCE_GROUP.ID IS '资源组ID';
COMMENT ON COLUMN XS_RESOURCE_GROUP.RESOURCE_GROUP_ENAME IS '资源分组英文名';
COMMENT ON COLUMN XS_RESOURCE_GROUP.RESOURCE_GROUP_CNAME IS '资源组中文名';
COMMENT ON COLUMN XS_RESOURCE_GROUP.RESOURCE_GROUP_TYPE IS '类别:资源组,模块';
COMMENT ON COLUMN XS_RESOURCE_GROUP.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_RESOURCE_GROUP.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_RESOURCE_GROUP ADD CONSTRAINT PK_XS_RESOURCE_GROUP PRIMARY KEY (ID) ;


CREATE TABLE XS_RESOURCE_GROUP_MEMBER (
                                          RESOURCE_MEMBER_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                          RESOURCE_PARENT_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                          MEMBER_TYPE VARCHAR(16) ,
                                          PATH VARCHAR(255) ,
                                          SORT_INDEX  DECIMAL(11) ,
                                          REC_CREATOR VARCHAR(32) ,
                                          REC_CREATE_TIME VARCHAR(14) ,
                                          REC_REVISOR VARCHAR(32) ,
                                          REC_REVISE_TIME VARCHAR(14) ,
                                          ARCHIVE_FLAG VARCHAR(1)
);
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.RESOURCE_MEMBER_ID IS '资源组成员ID';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.RESOURCE_PARENT_ID IS '资源组父节点英文名';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.MEMBER_TYPE IS '资源体类别，0:资源组,1:资源';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.PATH IS '来源';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_RESOURCE_GROUP_MEMBER.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_RESOURCE_GROUP_MEMBER ADD CONSTRAINT PK_XS_RESOURCE_GROUP_MEMBER PRIMARY KEY (RESOURCE_MEMBER_ID, RESOURCE_PARENT_ID) ;

CREATE TABLE XS_URL_ANTHORIZATION
(
    REC_CREATOR              VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME          VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR              VARCHAR(16)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG             VARCHAR(1)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME          VARCHAR(17)  DEFAULT ' '   NOT NULL,
    URL_CONFIG_ENAME         VARCHAR(32)  DEFAULT ' '   NOT NULL,
    AUTH_TYPE_CODE           VARCHAR(32)  DEFAULT ' '   NOT NULL,
    AUTH_SUBJECT_ID          VARCHAR(32)  DEFAULT ' '   NOT NULL,
    AUTH_SUBJECT_ENAME       VARCHAR(64)  DEFAULT ' '   NOT NULL,
    AUTH_SUBJECT_DESC        VARCHAR(255)  DEFAULT ' '   NOT NULL
);
alter table XS_URL_ANTHORIZATION add constraint PK_URL_AUTH  primary key (URL_CONFIG_ENAME,AUTH_TYPE_CODE,AUTH_SUBJECT_ID);

COMMENT ON TABLE XS_URL_ANTHORIZATION IS '访问地址授权表';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.URL_CONFIG_ENAME IS '访问地址配置英文名';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.AUTH_TYPE_CODE IS '授权类型代码';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.AUTH_SUBJECT_ID IS '授权对象ID';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.AUTH_SUBJECT_ENAME IS '授权对象英文名';
COMMENT ON COLUMN XS_URL_ANTHORIZATION.AUTH_SUBJECT_DESC IS '授权对象描述';

CREATE TABLE XS_USER (
                         USER_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         LOGIN_NAME VARCHAR(64)  DEFAULT ' '    NOT NULL ,
                         PASSWORD VARCHAR(255)  DEFAULT ' '    NOT NULL ,
                         STATUS VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         USER_NAME VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         GENDER VARCHAR(2) DEFAULT '1' NOT NULL,
                         MOBILE VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         EMAIL VARCHAR(128)  DEFAULT ' '    NOT NULL ,
                         USER_TYPE VARCHAR(16)  DEFAULT ' '    NOT NULL ,
                         ACCOUNT_EXPIRE_DATE VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         PWD_EXPIRE_DATE VARCHAR(14)  DEFAULT ' '    NOT NULL ,
                         IS_LOCKED VARCHAR(2)  DEFAULT ' '    NOT NULL ,
                         SORT_INDEX  DECIMAL(11) ,
                         REC_CREATOR VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                         REC_CREATE_TIME VARCHAR(14) ,
                         REC_REVISOR VARCHAR(32) ,
                         REC_REVISE_TIME VARCHAR(14) ,
                         PWD_REVISE_DATE VARCHAR(14) ,
                         PWD_REVISOR VARCHAR(32) ,
                         ARCHIVE_FLAG VARCHAR(1) ,
                         USER_GROUP_ENAME VARCHAR(32),
                         JOB_ID              VARCHAR(32)  default ' ',
                         EHR_ORG_ID          VARCHAR(16)  default ' ',
                         JOB_NAME            VARCHAR(256)  default ' '
);
COMMENT ON COLUMN XS_USER.USER_ID IS '用户ID';
COMMENT ON COLUMN XS_USER.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_USER.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER.GENDER IS '性别1男2女';
COMMENT ON COLUMN XS_USER.PWD_REVISE_DATE IS '密码修改时间';
COMMENT ON COLUMN XS_USER.PWD_REVISOR IS '密码修改人';
COMMENT ON COLUMN XS_USER.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_USER.USER_GROUP_ENAME IS '用户组';
COMMENT ON COLUMN XS_USER.JOB_ID IS '用户岗位号';
COMMENT ON COLUMN XS_USER.JOB_NAME IS '用户岗位名';
COMMENT ON COLUMN XS_USER.EHR_ORG_ID IS 'EHR组织机构代码';
ALTER TABLE XS_USER ADD CONSTRAINT PK_XS_USER PRIMARY KEY (USER_ID) ;

CREATE TABLE XS_USER_EXT (
                             EXT_ID VARCHAR(32)  DEFAULT ' '   NOT NULL ,
                             USER_ID VARCHAR(32) ,
                             FIELD_ID VARCHAR(128) ,
                             VALUE VARCHAR(128) ,
                             REC_CREATOR VARCHAR(16) ,
                             REC_CREATE_TIME VARCHAR(17) ,
                             REC_REVISOR VARCHAR(16) ,
                             REC_REVISE_TIME VARCHAR(17) ,
                             ARCHIVE_FLAG VARCHAR(1) ,
                             TENANT_ID VARCHAR(64) DEFAULT ' ' NOT NULL
);
COMMENT ON COLUMN XS_USER_EXT.EXT_ID IS 'ID';
COMMENT ON COLUMN XS_USER_EXT.USER_ID IS '用户ID';
COMMENT ON COLUMN XS_USER_EXT.FIELD_ID IS '属性ID';
COMMENT ON COLUMN XS_USER_EXT.VALUE IS '属性值';
COMMENT ON COLUMN XS_USER_EXT.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_USER_EXT.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER_EXT.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER_EXT.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER_EXT.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_USER_EXT.TENANT_ID IS '租户ID';
ALTER TABLE XS_USER_EXT ADD CONSTRAINT PK_XS_USER_EXT PRIMARY KEY (EXT_ID) ;

CREATE TABLE XS_USER_GROUP (
                               ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                               GROUP_ENAME VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                               GROUP_CNAME VARCHAR(128) ,
                               GROUP_TYPE VARCHAR(32) ,
                               SORT_INDEX  DECIMAL(11) ,
                               REC_CREATOR VARCHAR(32) ,
                               REC_CREATE_TIME VARCHAR(14) ,
                               REC_REVISOR VARCHAR(32) ,
                               REC_REVISE_TIME VARCHAR(14) ,
                               ARCHIVE_FLAG VARCHAR(1) ,
                               MANAGE_GROUP_ENAME VARCHAR(32)
);
COMMENT ON COLUMN XS_USER_GROUP.ID IS '用户群组ID';
COMMENT ON COLUMN XS_USER_GROUP.GROUP_CNAME IS '群组中文名';
COMMENT ON COLUMN XS_USER_GROUP.GROUP_TYPE IS '群组类型';
COMMENT ON COLUMN XS_USER_GROUP.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_USER_GROUP.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_USER_GROUP.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER_GROUP.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER_GROUP.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER_GROUP.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN XS_USER_GROUP.MANAGE_GROUP_ENAME IS '管辖组';
ALTER TABLE XS_USER_GROUP ADD CONSTRAINT PK_XS_USER_GROUP PRIMARY KEY (ID) ;


CREATE TABLE XS_USER_GROUP_MEMBER (
                                      MEMBER_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                      PARENT_ID VARCHAR(32)  DEFAULT ' '    NOT NULL ,
                                      MEMBER_TYPE VARCHAR(16) ,
                                      SORT_INDEX  DECIMAL(11) ,
                                      PATH VARCHAR(255) ,
                                      REC_CREATOR VARCHAR(32) ,
                                      REC_CREATE_TIME VARCHAR(14) ,
                                      REC_REVISOR VARCHAR(32) ,
                                      REC_REVISE_TIME VARCHAR(14) ,
                                      ARCHIVE_FLAG VARCHAR(1)
);
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.MEMBER_ID IS '成员ID';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.PARENT_ID IS '父节点ID';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.MEMBER_TYPE IS '授权类别:USER,GROUP';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.SORT_INDEX IS '排序';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.PATH IS '来源';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_CREATOR IS '创建人';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_CREATE_TIME IS '创建时间';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_REVISOR IS '修改人';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.REC_REVISE_TIME IS '修改时间';
COMMENT ON COLUMN XS_USER_GROUP_MEMBER.ARCHIVE_FLAG IS '归档标记';
ALTER TABLE XS_USER_GROUP_MEMBER ADD CONSTRAINT PK_XS_USER_GROUP_MEMBER PRIMARY KEY (PARENT_ID, MEMBER_ID) ;

CREATE TABLE TXSSC01
(
    REC_CREATOR             VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME         VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR             VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME         VARCHAR(17)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG            VARCHAR(1)  DEFAULT ' '   NOT NULL,
    TENANT_ID               VARCHAR(64)  DEFAULT ' BDAS'   NOT NULL,
    RULE_UUID               VARCHAR(32)  DEFAULT ' '   NOT NULL,
    RULE_DESC               VARCHAR(128)  DEFAULT ' '   NOT NULL,
    TARGET_CHARACTER        VARCHAR(32)  DEFAULT ' '   NOT NULL,
    REPLACE_CHARACTER       VARCHAR(32)  DEFAULT ' '   NOT NULL,
    SORT_INDEX              DECIMAL(4,0)  DEFAULT 0   NOT NULL
);
alter table TXSSC01 add constraint PK_TXSSC01  primary key (RULE_UUID);
COMMENT ON TABLE TXSSC01 IS '网页脚本配置表';
COMMENT ON COLUMN TXSSC01.REC_CREATOR IS '记录创建责任人';
COMMENT ON COLUMN TXSSC01.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TXSSC01.REC_REVISOR IS '记录修改责任人';
COMMENT ON COLUMN TXSSC01.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TXSSC01.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TXSSC01.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TXSSC01.RULE_UUID IS '规则唯一标识';
COMMENT ON COLUMN TXSSC01.RULE_DESC IS '规则说明';
COMMENT ON COLUMN TXSSC01.TARGET_CHARACTER IS '拦截包含字符';
COMMENT ON COLUMN TXSSC01.REPLACE_CHARACTER IS '过滤替换字符';
COMMENT ON COLUMN TXSSC01.SORT_INDEX IS '顺序号';


CREATE TABLE TXSSC02
(
    REC_CREATOR            VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME        VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR            VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME        VARCHAR(17)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG           VARCHAR(1)  DEFAULT ' '   NOT NULL,
    TENANT_ID              VARCHAR(64)  DEFAULT ' BDAS'   NOT NULL,
    RULE_UUID              VARCHAR(32)  DEFAULT ' '   NOT NULL,
    RULE_DESC              VARCHAR(128)  DEFAULT ' '   NOT NULL,
    TARGET_CHARACTER       VARCHAR(32)  DEFAULT ' '   NOT NULL,
    SORT_INDEX             DECIMAL(4,0)  DEFAULT 0   NOT NULL
);
alter table TXSSC02 add constraint PK_TXSSC02  primary key (RULE_UUID);
COMMENT ON TABLE TXSSC02 IS 'SQL脚本配置表';
COMMENT ON COLUMN TXSSC02.REC_CREATOR IS '记录创建责任人';
COMMENT ON COLUMN TXSSC02.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TXSSC02.REC_REVISOR IS '记录修改责任人';
COMMENT ON COLUMN TXSSC02.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TXSSC02.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TXSSC02.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TXSSC02.RULE_UUID IS '规则唯一标识';
COMMENT ON COLUMN TXSSC02.RULE_DESC IS '规则说明';
COMMENT ON COLUMN TXSSC02.TARGET_CHARACTER IS '拦截包含字符';
COMMENT ON COLUMN TXSSC02.SORT_INDEX IS '顺序号';


CREATE TABLE TXSSC03
(
    REC_CREATOR             VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_CREATE_TIME         VARCHAR(17)  DEFAULT ' '   NOT NULL,
    REC_REVISOR             VARCHAR(16)  DEFAULT ' '   NOT NULL,
    REC_REVISE_TIME         VARCHAR(17)  DEFAULT ' '   NOT NULL,
    ARCHIVE_FLAG            VARCHAR(1)  DEFAULT ' '   NOT NULL,
    TENANT_ID               VARCHAR(64)  DEFAULT ' BDAS'   NOT NULL,
    RULE_UUID               VARCHAR(32)  DEFAULT ' '   NOT NULL,
    WHITELIST_TYPE          VARCHAR(2)  DEFAULT ' '   NOT NULL,
    WHITELIST_KEYWORD       VARCHAR(32)  DEFAULT ' '   NOT NULL,
    WHITELIST_DESC          VARCHAR(128)  DEFAULT ' '   NOT NULL,
    SORT_INDEX              DECIMAL(4,0)  DEFAULT 0   NOT NULL
);
alter table TXSSC03 add constraint PK_TXSSC03  primary key (RULE_UUID);
COMMENT ON TABLE TXSSC03 IS '白名单配置表';
COMMENT ON COLUMN TXSSC03.REC_CREATOR IS '记录创建责任人';
COMMENT ON COLUMN TXSSC03.REC_CREATE_TIME IS '记录创建时刻';
COMMENT ON COLUMN TXSSC03.REC_REVISOR IS '记录修改责任人';
COMMENT ON COLUMN TXSSC03.REC_REVISE_TIME IS '记录修改时刻';
COMMENT ON COLUMN TXSSC03.ARCHIVE_FLAG IS '归档标记';
COMMENT ON COLUMN TXSSC03.TENANT_ID IS '租户ID';
COMMENT ON COLUMN TXSSC03.RULE_UUID IS '规则唯一标识';
COMMENT ON COLUMN TXSSC03.WHITELIST_TYPE IS '白名单类型';
COMMENT ON COLUMN TXSSC03.WHITELIST_KEYWORD IS '白名单关键字';
COMMENT ON COLUMN TXSSC03.WHITELIST_DESC IS '白名单说明';
COMMENT ON COLUMN TXSSC03.SORT_INDEX IS '顺序号';

--授权管理 索引添加--

CREATE INDEX IDX_XS_USER_LOGIN_NAME ON XS_USER (LOGIN_NAME);
CREATE INDEX IDX_XS_USER_GROUP_ENAME ON XS_USER_GROUP (GROUP_ENAME);
CREATE INDEX IDX_XS_USER_GROUP_MEMBER_IDS ON XS_USER_GROUP_MEMBER (MEMBER_ID, PARENT_ID);
CREATE INDEX IDX_XS_RESOURCE_RESOURCE_ENAME ON XS_RESOURCE (RESOURCE_ENAME);
CREATE INDEX IDX_XS_RESOURCE_GROUP_ENAME ON XS_RESOURCE_GROUP (RESOURCE_GROUP_ENAME);
