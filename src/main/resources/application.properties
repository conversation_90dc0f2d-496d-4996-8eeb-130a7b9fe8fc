spring.mvc.servlet.path=/
logging.level.com.baosight=info
spring.main.allow-bean-definition-overriding=true
server.port=8080
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**

spring.profiles.active=@env@

projectName=bsvkkj
componentEname=bsvkkj-zzzc
projectEnv=@env@

platSchema=iplat4j
ggmkSchema=GGMK
zzzcSchema=ZZZC

#"Baosteel Group" unicode encoding in Chinese, and ANY Chinese should be unicode encoded in this file.
enterpriseName=\u5b9d\u4fe1\u8f6f\u4ef6\u667a\u6167\u8fd0\u8425\u4e8b\u4e1a\u90e8
customerName=\u5b9d\u6b66\u96c6\u56e2

configEx=iplat4j;xservices;
xservices.security.accountExpireDays=36500
xservices.security.pwdExpireDays=36500

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8