datasource.type=dbcp

jdbc.driverClassName=dm.jdbc.driver.DmDriver
jdbc.url=jdbc:dm://**********?schema=IPLAT4J&columnNameUpperCase=false
jdbc.username=DMTEST
jdbc.password=Dameng@123
jdbc.maxActive= 50
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00

#thymeleaf
spring.thymeleaf.mode: HTML
spring.thymeleaf.encoding: utf-8
spring.thymeleaf.prefix: classpath:/templates/
spring.thymeleaf.cache=false