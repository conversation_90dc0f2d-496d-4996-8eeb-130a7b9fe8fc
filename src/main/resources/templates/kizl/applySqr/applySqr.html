<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境内专利_申请_申请人信息信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-applySqr-edit" th:object="${applySqr}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="sqrId" th:field="*{sqrId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请主键：</label>
                                <div class="col-sm-8">
                                    <input name="applyId" th:field="*{applyId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">序号：</label>
                                <div class="col-sm-8">
                                    <input name="xh" th:field="*{xh}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请人主键：</label>
                                <div class="col-sm-8">
                                    <input name="legalId" th:field="*{legalId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请人名称：</label>
                                <div class="col-sm-8">
                                    <input name="legalName" th:field="*{legalName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">通信地址：</label>
                                <div class="col-sm-8">
                                    <input name="mailAddress" th:field="*{mailAddress}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">邮政编码：</label>
                                <div class="col-sm-8">
                                    <input name="postOffice" th:field="*{postOffice}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">付费联系人：</label>
                                <div class="col-sm-8">
                                    <input name="lxrMoney" th:field="*{lxrMoney}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系电话：</label>
                                <div class="col-sm-8">
                                    <input name="lxrPhone" th:field="*{lxrPhone}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段1：</label>
                                <div class="col-sm-8">
                                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段2：</label>
                                <div class="col-sm-8">
                                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段3：</label>
                                <div class="col-sm-8">
                                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段4：</label>
                                <div class="col-sm-8">
                                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段5：</label>
                                <div class="col-sm-8">
                                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kizl/applySqr";

        $("#form-applySqr-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/save", $('#form-applySqr-edit').serialize());
            }
        }

    </script>
</body>
</html>