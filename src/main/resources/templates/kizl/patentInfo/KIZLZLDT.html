<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('专利信息')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="container-fluid">
        <form class="form-horizontal">
            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-jbxx" aria-expanded="false"
                           class="collapsed">
                            基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="readonly-jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!-- 第一行：流水号、接收编号 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">流水号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:text="${applyBaseinfo.serialNum ?: ''}"></div>
                                </div>
                                <label class="col-sm-2 control-label">接收编号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:text="${applyBaseinfo.jsbh ?: ''}"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 第二行：鄂钢编号、申请号 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">鄂钢编号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static"
                                         th:text="${applyBaseinfo.patentInfo?.egbh ?: ''}"></div>
                                </div>
                                <label class="col-sm-2 control-label">申请号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static"
                                         th:text="${applyBaseinfo.patentInfo?.patentNo ?: ''}"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 第三行：申请日、授权日期 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">申请日：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static"
                                         th:text="${applyBaseinfo.patentInfo?.slrq ?: ''}"></div>
                                </div>
                                <label class="col-sm-2 control-label">授权日期：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static"
                                         th:text="${applyBaseinfo.patentInfo?.sqrq ?: ''}"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 第四行：专利名称 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">专利名称：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static"
                                         th:text="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 第五行：发明设计人 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">发明设计人：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">
                                        <span th:if="${applyBaseinfo.ryxxList != null and !applyBaseinfo.ryxxList.isEmpty()}">
                                            <span th:each="ryxx, iterStat : ${applyBaseinfo.ryxxList}">
                                                <span th:text="${ryxx.empName}"></span>
                                                <span th:if="${!iterStat.last}">, </span>
                                            </span>
                                        </span>
                                        <span th:unless="${applyBaseinfo.ryxxList != null and !applyBaseinfo.ryxxList.isEmpty()}">暂无</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 第六行：当前状态、法律状态 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">当前状态：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/select :: init(id='patentStatus', name='patentStatus', businessType='KIZL', dictCode='PATENT_STATUS', value=${applyBaseinfo.patentInfo?.patentStatus},see=true)"></div>
                                </div>
                                <label class="col-sm-2 control-label">法律状态：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/select :: init(id='flzt',name='flzt',dictCode='FLZT', businessType='KIZL',value=${applyBaseinfo.patentInfo?.flzt},see=true)"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-sqrxx" aria-expanded="true">
                            主要信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="readonly-sqrxx" class="panel-collapse collapse in" aria-expanded="true">
                    <div class="panel-body">
                        <input type="hidden" th:value="${applyBaseinfo.applyId}" name="applyId" id="applyId"/>
                        <!-- 专利申报信息 -->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">专利申报信息：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static">
                                        <a href="javascript:void(0);"
                                           onclick="viewBasicInfo()"
                                           style="color: blue">
                                            点击查看
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 专利受理信息 -->
                        <div class="row"
                             th:if="${applyBaseinfo.patentInfo != null and applyBaseinfo.patentInfo.patentId != null and applyBaseinfo.patentInfo.patentId != ''}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">专利受理信息：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static">
                                        <a href="javascript:void(0);"
                                           onclick="viewPatentInfo()"
                                           style="color: blue">
                                            点击查看
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 专利授权通知信息 -->
                        <div class="row"
                             th:if="${applyBaseinfo.patentInfo != null and (applyBaseinfo.patentInfo.patentStatus == '04' or applyBaseinfo.patentInfo.patentStatus == '05')}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">专利授权通知信息：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static">
                                        <a href="javascript:void(0);"
                                           onclick="viewAuthNoticeInfo()"
                                           style="color: blue">
                                            点击查看
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 专利授权信息 -->
                        <div class="row"
                             th:if="${applyBaseinfo.patentInfo != null and applyBaseinfo.patentInfo.patentStatus == '05'}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">专利授权信息：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static">
                                        <a href="javascript:void(0);"
                                           onclick="viewAuthInfo()"
                                           style="color: blue">
                                            点击查看
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->
</div>

<script th:inline="javascript" th:src="@{/kizl/kizl-common.js}"></script>
</body>
</html>
