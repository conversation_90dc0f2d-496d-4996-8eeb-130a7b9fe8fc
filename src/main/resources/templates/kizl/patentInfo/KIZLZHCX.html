<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('专利综合查询')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form class="form-horizontal m" id="formId">
                <!--框-->
                <div class="panel-group" role="tablist" aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx"
                                                       aria-expanded="false" class="collapsed">专利综合查询
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right"><i class="fa fa-chevron-down"
                                                            aria-hidden="true"></i></span></a>
                            </h4>
                        </div>
                        <!--折叠区域-->
                        <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                            <div class="panel-body">
                                <!-- 模糊查询区域 -->
                                <fieldset>
                                    <legend style="padding:3px;">模糊查询:</legend>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label class="col-sm-2 control-label">关键词：</label>
                                                <div class="col-sm-8">
                                                    <input type="text" class="form-control" name="comprehensiveFuzzy"
                                                           placeholder="可查询专利名称、流水号、鄂钢编号、接收编号、申请号、摘要"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 高级查询区域 -->
                                <fieldset id="advancedSearchArea" style="display: none;">
                                    <legend style="padding:3px;">基本信息:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">流水号：</label>
                                                <div class="col-sm-8">
                                                    <input type="text" class="form-control" name="serialNumLike"
                                                           placeholder="输入流水号"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">专利类型：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='patentType', name='patentType', businessType='KIZL', dictCode='PATENT_TYPE', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">专利名称：</label>
                                                <div class="col-sm-8">
                                                    <input type="text" class="form-control" name="patentNameLike"
                                                           placeholder="输入专利名称"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">权属：</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="ownership"
                                                           placeholder="输入权属"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">鄂钢编号：</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="egbhLike"
                                                           placeholder="输入鄂钢编号"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">接收编号：</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="jsbhLike"
                                                           placeholder="输入接收编号"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">申请号：</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="patentNoLike"
                                                           placeholder="输入申请号"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">单位类型：</label>
                                                <div class="col-sm-6">
                                                    <select class="form-control" name="unitType"
                                                            onchange="toggleApplyUnit(this)">
                                                        <option value="">全部</option>
                                                        <option value="specified">指定组织</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="applyUnitGroup" style="display:none;">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">申请单位：</label>
                                                <div class="col-sm-8">
                                                    <div th:include="/component/selectOrg :: init(orgCodeId='applyUnitCode', orgNameId='applyUnit')"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 状态信息 -->
                                <fieldset id="statusInfoArea" style="display: none;">
                                    <legend style="padding:3px;">状态信息:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">最新状态：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='patentStatus', name='patentStatus', businessType='KIZL', dictCode='PATENT_STATUS', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">法律状态：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='flzt', name='flzt', businessType='KIZL', dictCode='FLZT', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 日期范围 -->
                                <fieldset id="dateRangeArea" style="display: none;">
                                    <legend style="padding:3px;">日期范围:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">开始日期：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/date :: init(id='startDate', name='startDate')"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">结束日期：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/date :: init(id='endDate', name='endDate')"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 实施情况 -->
                                <fieldset id="implementationArea" style="display: none;">
                                    <legend style="padding:3px;">实施情况:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">实施情况：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='useMethod', name='useMethod', businessType='KIZL', dictCode='USE_METHOD', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">实施部门：</label>
                                                <div class="col-sm-8">
                                                    <div th:include="/component/selectOrg :: init(orgCodeId='implementDeptCode', orgNameId='implementDept')"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 来源信息 -->
                                <fieldset id="sourceInfoArea" style="display: none;">
                                    <legend style="padding:3px;">来源信息:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">来源类别：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='fromType', name='fromType', businessType='KIZL', dictCode='FROM_TYPE', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">来源编号：</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="fromNoLike"
                                                           placeholder="输入来源编号"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">来源名称：</label>
                                                <div class="col-sm-8">
                                                    <input type="text" class="form-control" name="fromNameLike"
                                                           placeholder="输入来源名称"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 代理信息 -->
                                <fieldset id="agentInfoArea" style="display: none;">
                                    <legend style="padding:3px;">代理信息:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">代理事务所：</label>
                                                <div class="col-sm-6">
                                                    <select id="batchSwsGuid" name="swsGuid" class="form-control">
                                                        <option value="">请选择</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">代理人：</label>
                                                <div class="col-sm-6">
                                                    <input id="batchSwsdlr" name="swsdlr" class="form-control"
                                                           type="text"
                                                           placeholder="请输入代理人">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- 发明人信息 -->
                                <fieldset id="inventorInfoArea" style="display: none;">
                                    <legend style="padding:3px;">发明人信息:</legend>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">姓名：</label>
                                                <div class="col-sm-6">
                                                    <input type="text" class="form-control" name="inventorNameLike"
                                                           placeholder="输入发明人姓名"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">工号：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/selectUser :: init(userCodeId='inventorJobNoCode', userNameId='inventorJobNo')"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">岗位：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='inventorPosition', name='inventorPosition', businessType='KIZL', dictCode='POST_LEVEL', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="col-sm-3 control-label">职称：</label>
                                                <div class="col-sm-6">
                                                    <div th:include="/component/select :: init(id='inventorTitle', name='inventorTitle', businessType='KIZL', dictCode='POST_TITLE', isfirst=true)"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <!--折叠区域end-->
                    </div>
                </div>
                <!--框end-->
                <div class="form-group">
                    <div class="col-sm-10" style="text-align: right;margin-left:210px;">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('formId')"><i
                                class="fa fa-search"></i>&nbsp;查询</a>
                        <a id="search1" class="btn btn-primary btn-rounded btn-sm" onclick="toShow(1)"><i
                                class="fa fa-plus"></i>&nbsp;高级查询</a>
                        <a id="search2" style="display: none" class="btn btn-primary btn-rounded btn-sm"
                           onclick="toShow(2)"><i class="fa fa-minus"></i>&nbsp;简单查询</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="doReset()"><i class="fa fa-refresh"></i>&nbsp;清空查询条件</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="exportData()"><i
                                class="fa fa-download"></i>&nbsp;导出专利</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- 输出选项 -->
        <div class="col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">输出选项（可多选）</h4>
                </div>
                <div class="panel-body">
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="outputColumns" value="index" checked> 序号</label>
                        <label><input type="checkbox" name="outputColumns" value="jsbh" checked> 接收编号</label>
                        <label><input type="checkbox" name="outputColumns" value="egbh" checked> 鄂钢编号</label>
                        <label><input type="checkbox" name="outputColumns" value="patentName" checked>
                            专利申报名称</label>
                        <label><input type="checkbox" name="outputColumns" value="patentStatus" checked>
                            最新状态</label>
                        <label><input type="checkbox" name="outputColumns" value="patentType" checked> 专利类型</label>
                        <label><input type="checkbox" name="outputColumns" value="patentNo"> 申请号</label>
                        <label><input type="checkbox" name="outputColumns" value="slrq"> 申请日</label>
                        <label><input type="checkbox" name="outputColumns" value="sqrq"> 授权日</label>
                        <label><input type="checkbox" name="outputColumns" value="updateDate"> 最新处理日期</label>
                        <label><input type="checkbox" name="outputColumns" value="fromNo"> 项目编号</label>
                        <label><input type="checkbox" name="outputColumns" value="fromType"> 项目类别</label>
                        <label><input type="checkbox" name="outputColumns" value="fromName"> 项目名称</label>
                        <label><input type="checkbox" name="outputColumns" value="flzt"> 法律状态</label>
                        <label><input type="checkbox" name="outputColumns" value="inventorNames"> 发明人</label>
                        <label><input type="checkbox" name="outputColumns" value="firstDeptName"> 申请部门</label>
                        <label><input type="checkbox" name="outputColumns" value="useDeptDept"> 实施区域</label>
                        <label><input type="checkbox" name="outputColumns" value="useFirstdate"> 实施初始时间</label>
                        <label><input type="checkbox" name="outputColumns" value="swsName"> 代理事务所</label>
                        <label><input type="checkbox" name="outputColumns" value="swsdlr"> 代理人</label>
                        <label><input type="checkbox" name="outputColumns" value="applicantNames"> 申请人</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="btn-group-sm" id="toolbar" role="group">
        </div>

        <!-- 数据表格 -->
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kizl/patentInfo";
    var currentColumns = []; // 当前显示的列

    $(function () {
        // 初始化表格
        initTable();

        // 初始化专利事务所下拉选择
        initBatchSwsSelect();

        // 监听输出选项变化
        $('input[name="outputColumns"]').change(function () {
            updateTableColumns();
        });

    });

    // 切换显示模式
    function toShow(type) {
        if (type == 1) {
            // 显示高级查询
            $('#advancedSearchArea, #statusInfoArea, #dateRangeArea, #implementationArea, #sourceInfoArea, #agentInfoArea, #inventorInfoArea').show();
            $('#search1').hide();
            $('#search2').show();
        } else {
            // 显示简单查询
            $('#advancedSearchArea, #statusInfoArea, #dateRangeArea, #implementationArea, #sourceInfoArea, #agentInfoArea, #inventorInfoArea').hide();
            $('#search1').show();
            $('#search2').hide();
        }
    }

    // 切换申请单位显示
    function toggleApplyUnit(select) {
        if (select.value === 'specified') {
            $('#applyUnitGroup').show();
        } else {
            $('#applyUnitGroup').hide();
        }
    }

    // 重置表单
    function doReset() {
        $('#formId')[0].reset();
        $('#applyUnitGroup').hide();
        $('select[name="unitType"]').val('');
        $.table.refresh();
    }

    // 初始化批量操作的专利事务所下拉选择
    function initBatchSwsSelect() {
        $.ajax({
            url: ctx + 'kizl/maintainSwsinfo/getAllSwsinfo',
            type: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.code === 0 && result.data) {
                    var options = '<option value="">请选择专利事务所</option>';
                    $.each(result.data, function (index, item) {
                        var displayName = item.swsName || '未知事务所';
                        options += '<option value="' + item.swsId + '">' + displayName + '</option>';
                    });
                    $('#batchSwsGuid').html(options);
                } else {
                    console.error('加载专利事务所信息失败：', result.msg);
                }
            },
            error: function () {
                console.error('加载专利事务所信息失败');
            }
        });
    }

    // 根据swsGuid获取事务所详细信息
    function loadSwsDetailInfo(swsGuid) {
        if (!swsGuid) {
            return;
        }
        $.ajax({
            url: ctx + 'kizl/maintainSwsinfo/getSwsDetailInfo/' + swsGuid,
            type: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.code === 0 && result.data) {
                    var swsDetail = result.data;
                    // 填充代理人相关信息
                    if (swsDetail.lxr) {
                        $('#batchSwsdlr').val(swsDetail.lxr);
                    }
                } else {
                    console.log('获取事务所详细信息失败：', result.msg || '未找到对应的事务所信息');
                }
            },
            error: function () {
                console.error('获取事务所详细信息失败');
            }
        });
    }

    var PATENT_STATUS = /*[[${@SDictUtil.getDictList('KIZL','PATENT_STATUS')}]]*/ null;
    var PATENT_TYPE = /*[[${@SDictUtil.getDictList('KIZL','PATENT_TYPE')}]]*/ null;
    var FROM_TYPE = /*[[${@SDictUtil.getDictList('KIZL','FROM_TYPE')}]]*/ null;
    var FLZT = /*[[${@SDictUtil.getDictList('KIZL','FLZT')}]]*/ null;


    // 初始化表格
    function initTable() {
        var options = {
            url: ctx + "kizl/c/d?serviceName=KIZLPatentInfo&methodName=queryComprehensivePage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "专利信息",
            columns: getDefaultColumns()
        };
        $.table.init(options);
    }

    // 获取默认列配置
    function getDefaultColumns() {
        return [{
            checkbox: true
        }, {
            field: 'index',
            title: '序号',
            visible: true,
            formatter: function (value, row, index) {
                return index + 1;
            }
        }, {
            field: 'jsbh',
            title: '接收编号',
            visible: true
        }, {
            field: 'egbh',
            title: '鄂钢编号',
            visible: true
        }, {
            field: 'patentName',
            title: '专利申报名称',
            visible: true,
            formatter: function(value, row, index) {
                var actions = [];
                actions.push('<a style="color: #00a0e9" onclick="viewDetail(\'' + row.applyId + '\')">'+value+'</a>');
                return actions.join('');
            }
        }, {
            field: 'patentStatus',
            title: '最新状态',
            visible: true,
            formatter: function (value, row, index) {
                return $.table.selectDictLabel(PATENT_STATUS, value);
            }
        }, {
            field: 'patentType',
            title: '专利类型',
            visible: true,
            formatter: function (value, row, index) {
                return $.table.selectDictLabel(PATENT_TYPE, value);
            }
        }];
    }

    // 导出数据 - 使用系统内置的exportExcelBit方法
    function exportData() {
        // 检查是否选择了导出列
        var selectedColumns = [];
        $('input[name="outputColumns"]:checked').each(function () {
            selectedColumns.push($(this).val());
        });

        if (selectedColumns.length === 0) {
            $.modal.alertWarning("请至少选择一个输出列！");
            return;
        }

        // 将选择的列添加到表单中，以便后端接收
        var hiddenInput = $('#hiddenColumns');
        if (hiddenInput.length === 0) {
            $('#formId').append('<input type="hidden" id="hiddenColumns" name="columns" value="" />');
            hiddenInput = $('#hiddenColumns');
        }
        hiddenInput.val(selectedColumns.join(','));

        // 使用系统内置的导出方法
        $.table.exportExcelBit('formId');
    }

    // 自定义导出方法（保留原有功能作为备选）
    function customExportData() {
        // 检查是否选择了导出列
        var selectedColumns = [];
        $('input[name="outputColumns"]:checked').each(function () {
            selectedColumns.push($(this).val());
        });

        if (selectedColumns.length === 0) {
            $.modal.alertWarning("请至少选择一个输出列！");
            return;
        }

        $.modal.confirm("确认要导出专利数据吗？", function () {
            // 显示导出进度提示
            var loadingIndex = layer.load(1, {
                shade: [0.3, '#000'],
                content: '正在导出数据，请稍候...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '200px'
                    });
                }
            });

            // 获取表单数据
            var formData = $('#formId').serialize();

            // 构建导出URL
            var exportUrl = prefix + "/comprehensiveExport?" + formData + "&columns=" + selectedColumns.join(',');

            // 创建隐藏的iframe进行下载
            var iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = exportUrl;
            document.body.appendChild(iframe);

            // 设置下载完成后的处理
            setTimeout(function () {
                layer.close(loadingIndex);
                document.body.removeChild(iframe);
                $.modal.msgSuccess("导出完成！");
            }, 3000);
        });
    }

    // 全选/全不选输出列
    function toggleAllColumns(checkbox) {
        var isChecked = $(checkbox).prop('checked');
        $('input[name="outputColumns"]').prop('checked', isChecked);
        updateTableColumns();
    }

    // 更新表格列显示
    function updateTableColumns() {
        var selectedColumns = [];
        $('input[name="outputColumns"]:checked').each(function () {
            selectedColumns.push($(this).val());
        });

        // 更新表格列配置
        var allColumns = getAllColumns();
        var visibleColumns = allColumns.filter(function (col) {
            return col.checkbox || selectedColumns.indexOf(col.field) !== -1;
        });

        // 重新初始化表格
        $('#bootstrap-table').bootstrapTable('destroy');
        var options = {
            url: ctx + "kizl/c/d?serviceName=KIZLPatentInfo&methodName=queryComprehensivePage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "专利信息",
            columns: visibleColumns
        };
        $.table.init(options);
    }

    // 获取所有列配置
    function getAllColumns() {
        return [{
            checkbox: true
        }, {
            field: 'index',
            title: '序号',
            formatter: function (value, row, index) {
                return index + 1;
            }
        }, {
            field: 'jsbh',
            title: '接收编号'
        }, {
            field: 'egbh',
            title: '鄂钢编号'
        }, {
            field: 'patentName',
            title: '专利申报名称',
            formatter: function(value, row, index) {
            var actions = [];
            actions.push('<a style="color: #00a0e9" onclick="viewDetail(\'' + row.applyId + '\')">'+value+'</a>');
            return actions.join('');
            }
        }, {
            field: 'patentStatus',
            title: '最新状态',
            formatter: function (value, row, index) {
                return $.table.selectDictLabel(PATENT_STATUS, value);
            }
        }, {
            field: 'patentType',
            title: '专利类型',
            formatter: function (value, row, index) {
                return $.table.selectDictLabel(PATENT_TYPE, value);
            }
        }, {
            field: 'patentNo',
            title: '申请号'
        }, {
            field: 'slrq',
            title: '申请日'
        }, {
            field: 'sqrq',
            title: '授权日'
        }, {
            field: 'updateDate',
            title: '最新处理日期'
        }, {
            field: 'fromNo',
            title: '项目编号'
        }, {
            field: 'fromType',
            title: '项目类别',
            formatter: function (value, row, index) {
                return $.table.selectDictLabel(FROM_TYPE, value);
            }
        }, {
            field: 'fromName',
            title: '项目名称'
        }, {
            field: 'flzt',
            title: '法律状态',
            formatter: function (value, row, index) {
                return $.table.selectDictLabel(FLZT, value);
            }
        }, {
            field: 'inventorNames',
            title: '发明人'
        }, {
            field: 'firstDeptName',
            title: '申请部门'
        }, {
            field: 'useDeptDept',
            title: '实施区域'
        }, {
            field: 'useFirstdate',
            title: '实施初始时间'
        }, {
            field: 'swsName',
            title: '代理事务所'
        }, {
            field: 'swsdlr',
            title: '代理人'
        }, {
            field: 'applicantNames',
            title: '申请人'
        }];
    }

    // 查看详情
    function viewDetail(applyId) {
        var url = ctx + "kizl/patentInfo/viewDetail?applyId=" + applyId;
        $.modal.openTab("专利详情", url);
    }
</script>
</body>
</html>