<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境内专利_专利信息信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-patentInfo-edit" th:object="${patentInfo}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="patentId" th:field="*{patentId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请主键：</label>
                                <div class="col-sm-8">
                                    <input name="applyId" th:field="*{applyId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">流水号：</label>
                                <div class="col-sm-8">
                                    <input name="serialNum" th:field="*{serialNum}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">接收编号：</label>
                                <div class="col-sm-8">
                                    <input name="jsbh" th:field="*{jsbh}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">鄂钢编号：</label>
                                <div class="col-sm-8">
                                    <input name="egbh" th:field="*{egbh}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申报名称：</label>
                                <div class="col-sm-8">
                                    <input name="applyName" th:field="*{applyName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">第一申报部门：</label>
                                <div class="col-sm-8">
                                    <input name="firstDeptCode" th:field="*{firstDeptCode}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">第一申报部门名称：</label>
                                <div class="col-sm-8">
                                    <input name="firstDeptName" th:field="*{firstDeptName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">管理单位：</label>
                                <div class="col-sm-8">
                                    <input name="gldwCode" th:field="*{gldwCode}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">管理单位名称：</label>
                                <div class="col-sm-8">
                                    <input name="gldwName" th:field="*{gldwName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">标签：</label>
                                <div class="col-sm-8">
                                    <input name="label" th:field="*{label}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">技术领域：</label>
                                <div class="col-sm-8">
                                    <input name="techArea" th:field="*{techArea}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">用途：</label>
                                <div class="col-sm-8">
                                    <input name="usePropose" th:field="*{usePropose}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">知识分类：</label>
                                <div class="col-sm-8">
                                    <input name="knowledgeClass" th:field="*{knowledgeClass}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系人：</label>
                                <div class="col-sm-8">
                                    <input name="lxr" th:field="*{lxr}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系人姓名：</label>
                                <div class="col-sm-8">
                                    <input name="lxrName" th:field="*{lxrName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系人电话：</label>
                                <div class="col-sm-8">
                                    <input name="lxrphone" th:field="*{lxrphone}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系人邮箱：</label>
                                <div class="col-sm-8">
                                    <input name="lxrEmail" th:field="*{lxrEmail}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系人手机：</label>
                                <div class="col-sm-8">
                                    <input name="lxrMobile" th:field="*{lxrMobile}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                            <label class="col-sm-4 control-label">来源类型：</label>
                            <div class="form-group" th:include="/component/select :: init(id='fromType', name='fromType',businessType=null, dictCode=null, value=${patentInfo.fromType} ,labelName='来源类型')">
                            </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">来源编号：</label>
                                <div class="col-sm-8">
                                    <input name="fromNo" th:field="*{fromNo}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">来源名称：</label>
                                <div class="col-sm-8">
                                    <input name="fromName" th:field="*{fromName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">其他来源情况说明：</label>
                                <div class="col-sm-8">
                                    <input name="fromContent" th:field="*{fromContent}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">应用方式：</label>
                                <div class="col-sm-8">
                                    <input name="useMethod" th:field="*{useMethod}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">应用部门：</label>
                                <div class="col-sm-8">
                                    <input name="useDept" th:field="*{useDept}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">应用部门名称：</label>
                                <div class="col-sm-8">
                                    <input name="useDeptDept" th:field="*{useDeptDept}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">预计应用部门：</label>
                                <div class="col-sm-8">
                                    <input name="useExpected" th:field="*{useExpected}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">预计应用部门名称：</label>
                                <div class="col-sm-8">
                                    <input name="useExpectedName" th:field="*{useExpectedName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">初始应用日期：</label>
                                <div class="col-sm-8">
                                    <input name="useFirstdate" th:field="*{useFirstdate}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">未应用原因：</label>
                                <div class="col-sm-8">
                                    <input name="reasonNouse" th:field="*{reasonNouse}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">未应用原因备注：</label>
                                <div class="col-sm-8">
                                    <input name="contentNouse" th:field="*{contentNouse}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">缴费与资助责任单位：</label>
                                <div class="col-sm-8">
                                    <input name="jfdwCode" th:field="*{jfdwCode}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                            <label class="col-sm-4 control-label">流程状态 draft-草稿 active-流程中 end-结束：</label>
                            <div class="form-group" th:include="/component/radio :: init(id='flowStatus', name='flowStatus',businessType=null, dictCode=null ,value=${patentInfo.flowStatus} ,labelName='流程状态 draft-草稿 active-流程中 end-结束')">
                            </div>
                            </div>
                            <div class="form-group">
                            <label class="col-sm-4 control-label">专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权：</label>
                            <div class="form-group" th:include="/component/radio :: init(id='patentStatus', name='patentStatus',businessType=null, dictCode=null ,value=${patentInfo.patentStatus} ,labelName='专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权')">
                            </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">待交底人员：</label>
                                <div class="col-sm-8">
                                    <input name="djdPerson" th:field="*{djdPerson}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">待交底日期：</label>
                                <div class="col-sm-8">
                                    <input name="djdDate" th:field="*{djdDate}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">交底人员：</label>
                                <div class="col-sm-8">
                                    <input name="jdPerson" th:field="*{jdPerson}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">交底日期：</label>
                                <div class="col-sm-8">
                                    <input name="jdDate" th:field="*{jdDate}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">受理_权属：</label>
                                <div class="col-sm-8">
                                    <input name="qs" th:field="*{qs}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">受理_受理日期：</label>
                                <div class="col-sm-8">
                                    <input name="slrq" th:field="*{slrq}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">受理_申请号：</label>
                                <div class="col-sm-8">
                                    <input name="patentNo" th:field="*{patentNo}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                            <label class="col-sm-4 control-label">受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计：</label>
                            <div class="form-group" th:include="/component/select :: init(id='patentType', name='patentType',businessType=null, dictCode=null, value=${patentInfo.patentType} ,labelName='受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计')">
                            </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">受理_优先权日：</label>
                                <div class="col-sm-8">
                                    <input name="yxqr" th:field="*{yxqr}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">受理_权利要求数量：</label>
                                <div class="col-sm-8">
                                    <input name="qlyqsl" th:field="*{qlyqsl}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">受理_说明书页数：</label>
                                <div class="col-sm-8">
                                    <input name="smsys" th:field="*{smsys}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">代理事务所：</label>
                                <div class="col-sm-8">
                                    <input name="swsGuid" th:field="*{swsGuid}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">代理人：</label>
                                <div class="col-sm-8">
                                    <input name="swsdlr" th:field="*{swsdlr}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">代理人电话：</label>
                                <div class="col-sm-8">
                                    <input name="swsdlrPhone" th:field="*{swsdlrPhone}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">代理人邮箱：</label>
                                <div class="col-sm-8">
                                    <input name="swsdlrEmail" th:field="*{swsdlrEmail}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">授权通知_发文日：</label>
                                <div class="col-sm-8">
                                    <input name="sqtzFwdate" th:field="*{sqtzFwdate}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">授权通知_第一次缴费：</label>
                                <div class="col-sm-8">
                                    <input name="moneyFirst" th:field="*{moneyFirst}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">专利号：</label>
                                <div class="col-sm-8">
                                    <input name="zlh" th:field="*{zlh}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">授权日期：</label>
                                <div class="col-sm-8">
                                    <input name="sqrq" th:field="*{sqrq}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">授权日期：</label>
                                <div class="col-sm-8">
                                    <input name="ipcClassificationNo" th:field="*{ipcClassificationNo}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止：</label>
                                <div class="col-sm-8">
                                    <input name="flzt" th:field="*{flzt}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">是否有效 1-有效 0-无效：</label>
                                <div class="col-sm-8">
                                    <input name="isvalid" th:field="*{isvalid}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段1：</label>
                                <div class="col-sm-8">
                                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段2：</label>
                                <div class="col-sm-8">
                                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段3：</label>
                                <div class="col-sm-8">
                                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段4：</label>
                                <div class="col-sm-8">
                                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段5：</label>
                                <div class="col-sm-8">
                                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kizl/patentInfo";

        $("#form-patentInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/save", $('#form-patentInfo-edit').serialize());
            }
        }

    </script>
</body>
</html>