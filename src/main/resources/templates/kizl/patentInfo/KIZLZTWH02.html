<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('专利状态维护')"/>
	<th:block th:include="include :: baseJs"/>
	<th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">

<div class="wrapper wrapper-content">
	<form class="form-horizontal m" id="form-patentStatus-edit" th:object="${applyBaseinfo}">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" data-parent="#version" href="#ztwhxx" aria-expanded="false"
					   class="collapsed">专利状态维护信息
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
					</a>
				</h4>
			</div>
			<div id="ztwhxx" class="panel-collapse collapse in" aria-expanded="false">
				<div class="panel-body">
					<input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
					<input id="patentId" name="patentId" th:value="${applyBaseinfo.patentInfo?.patentId}" type="hidden" >
					<!-- 查看链接区域 -->
					<div class="form-group">
						<label class="col-sm-2 control-label">专利申报信息：</label>
						<div class="col-sm-4">
							<p class="form-control-static">
								<a href="javascript:void(0);" onclick="viewBasicInfo()" style="color: blue">
									点击查看
								</a>
							</p>
						</div>
						<label class="col-sm-2 control-label">受理信息：</label>
						<div class="col-sm-4">
							<p class="form-control-static">
								<a href="javascript:void(0);" onclick="viewPatentInfo()" style="color: blue">
									点击查看
								</a>
							</p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">授权通知：</label>
						<div class="col-sm-4">
							<p class="form-control-static">
								<a href="javascript:void(0);" onclick="viewAuthNoticeInfo()" style="color: blue">
									点击查看
								</a>
							</p>
						</div>
					</div>

					<!-- 基本信息区域 -->
					<div class="form-group">
						<label class="col-sm-2 control-label">鄂钢编号：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.egbh}"></p>
						</div>
						<label class="col-sm-2 control-label">接收编号：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.jsbh}"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">申请日：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.slrq}"></p>
						</div>
						<label class="col-sm-2 control-label">申请号：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentNo}"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">专利号：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.zlh}"></p>
						</div>
						<label class="col-sm-2 control-label">授权日：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.sqrq}"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">专利名称：</label>
						<div class="col-sm-10">
							<p class="form-control-static"
							   th:text="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">已保护年限：</label>
						<div class="col-sm-4">
							<p class="form-control-static" id="protectedYears" ></p>
						</div>
						<label class="col-sm-2 control-label">发明设计人：</label>
						<div class="col-sm-4">
<!--							<p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.fml}"></p>-->
						</div>
					</div>

					<!-- 应用信息区域 -->
					<div class="form-group">
						<label class="col-sm-2 control-label">应用方式：</label>
						<div class="col-sm-4">
							<div th:include="/component/select :: init(id='useMethod', name='useMethod', businessType='KIZL', dictCode='USE_METHOD', value=${applyBaseinfo.useMethod}, see=true)"></div>
						</div>
						<label class="col-sm-2 control-label">预计应用部门：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.useExpectedName}"></p>
						</div>
					</div>
					<div class="form-group use-date" th:style="${applyBaseinfo.useMethod == '2' or applyBaseinfo.useMethod == '3'} ? 'display: none;' : ''">
						<label class="col-sm-2 control-label">初始应用日期：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.useFirstdate}"></p>
						</div>
					</div>
					<div class="form-group use-dept" th:style="${applyBaseinfo.useMethod != '1'} ? 'display: none;' : ''">
						<label class="col-sm-2 control-label">应用单位部门：</label>
						<div class="col-sm-4">
							<p class="form-control-static" th:text="${applyBaseinfo.useDeptName}"></p>
						</div>
					</div>
					<div class="form-group no-use-reason" th:style="${applyBaseinfo.useMethod != '3'} ? 'display: none;' : ''">
						<label class="col-sm-2 control-label">未应用原因：</label>
						<div class="col-sm-4">
							<div th:include="/component/select :: init(id='reasonNouse', name='reasonNouse', businessType='KIZL', dictCode='REASON_NOUSE', value=${applyBaseinfo.reasonNouse}, see=true)"></div>
						</div>
					</div>
					<!-- 状态维护区域 -->
					<div class="form-group">
						<label class="col-sm-2 control-label is-required">法律状态：</label>
						<div class="col-sm-4">
							<div th:include="/component/select :: init(id='flzt', name='flzt', businessType='KIZL', dictCode='FLZT', value=${applyBaseinfo.patentInfo?.flzt}, isrequired=true)"></div>
						</div>
						<label class="col-sm-2 control-label is-required">终止日期：</label>
						<div class="col-sm-4">
							<div th:include="/component/date :: init(name='extra2', id='zzrq', strValue=${applyBaseinfo.patentInfo?.extra2}, isrequired=true)"></div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">填写人：</label>
						<div class="col-sm-4">
							<div th:include="/component/selectUser :: init (id='extra3', userNameId='extra3Name', userCodeId='extra3', value=${applyBaseinfo.patentInfo.extra3}, isrequired=true)"></div>
						</div>
						<label class="col-sm-2 control-label">填写日期：</label>
						<div class="col-sm-4">
							<input id="fillDate" name="extra4" th:value="${applyBaseinfo.patentInfo?.extra4}"
								   class="form-control" type="text" readonly>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label">备注：</label>
						<div class="col-sm-10">
							<div th:include="/component/textarea :: init(id='remark', name='extra5', value=${applyBaseinfo.patentInfo?.extra5}, rows='3', maxlength='500')"></div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label" id="ztwhfj-label">相关附件：</label>
						<div class="col-sm-10">
							<th:block
									th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='ztwhfj',id='ztwhfj',name='attachmentS[ztwhfj]')"></th:block>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>

	<!--按钮区-->
	<div class="toolbar toolbar-bottom" role="toolbar">
		<button type="button" class="btn btn-primary" onclick="saveDraft()">
			<i class="fa fa-check"></i> 提交
		</button>
		<button type="button" class="btn btn-danger" onclick="closeItem()">
			<i class="fa fa-reply-all"></i> 返回
		</button>
	</div>
</div>

<th:block th:include="include :: datetimepicker-js"/>
<!-- 引入KIZL通用JS -->
<script th:src="@{/kizl/kizl-common.js}"></script>
<!-- 引入附件标签工具类 -->
<script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
<script th:inline="javascript">
	var prefix = ctx + "kizl/patentInfo";

	$(function() {
		// 初始化附件名称映射
		KizlAttachmentUtils.init(function() {
			KizlAttachmentUtils.updateCommonLabels();
		});
		
		// 初始化填写人和填写日期
		initFillInfo();
		// 计算已保护年限
		calculateProtectedYears();
	});

	// 初始化填写人和填写日期
	function initFillInfo() {
		var fillDate = $("#fillDate").val();
		if (!fillDate) {
			$("#fillDate").val(formatDate(new Date()));
		}
	}

	// 计算已保护年限
	function calculateProtectedYears() {
		var slrq = /*[[${applyBaseinfo.patentInfo?.slrq}]]*/ null;
		if (slrq) {
			var applyDate = new Date(slrq);
			var currentDate = new Date();
			var years = currentDate.getFullYear() - applyDate.getFullYear();
			var months = currentDate.getMonth() - applyDate.getMonth();

			if (months < 0 || (months === 0 && currentDate.getDate() < applyDate.getDate())) {
				years--;
			}

			if (years >= 0) {
				$('#protectedYears').text(years + '年');
			}
		}
	}

	// 格式化日期
	function formatDate(date) {
		var year = date.getFullYear();
		var month = ("0" + (date.getMonth() + 1)).slice(-2);
		var day = ("0" + date.getDate()).slice(-2);
		return year + "-" + month + "-" + day;
	}

	// 保存表单
	function saveDraft() {
		if (!$.validate.form()) {
			return false;
		}
		var formData = $('#form-patentStatus-edit').serialize();
		$.modal.confirm("一旦提交维护信息，此专利将作为无效专利。", function () {
			$.operate.saveTabAlert(ctx + 'kizl/c/s?serviceName=KIZLPatentInfo&methodName=savePatentStatus', formData);
		});
	}

</script>
</body>
</html>