<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境内专利_专利信息列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">输入框:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="" placeholder="支持模糊查询"/>
				    </div>
				    <label class="col-sm-1 control-label">下拉框:</label>
					<div class="col-sm-3">
						<select id="state" name="" class="form-control m-b">
							<option value="">请选择</option>
							<option value="0">状态0</option>
							<option value="1">状态1</option>
						</select>
				    </div>
				    <label class="col-sm-1 control-label">日期:</label>
					<div class="col-sm-3">
						<th:block th:include="/component/date::init(name='',id='')"/>
				    </div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcelBit()" >
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary" onclick="$.table.importTemplateBit()" >
                <i class="fa fa-download"></i> 导出模板
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kizl/patentInfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "境内专利_专利信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'patentId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'applyId',
                    title: '申请主键'
                },
                {
                    field: 'serialNum',
                    title: '流水号'
                },
                {
                    field: 'jsbh',
                    title: '接收编号'
                },
                {
                    field: 'egbh',
                    title: '鄂钢编号'
                },
                {
                    field: 'applyName',
                    title: '申报名称'
                },
                {
                    field: 'firstDeptCode',
                    title: '第一申报部门'
                },
                {
                    field: 'firstDeptName',
                    title: '第一申报部门名称'
                },
                {
                    field: 'gldwCode',
                    title: '管理单位'
                },
                {
                    field: 'gldwName',
                    title: '管理单位名称'
                },
                {
                    field: 'label',
                    title: '标签'
                },
                {
                    field: 'techArea',
                    title: '技术领域'
                },
                {
                    field: 'usePropose',
                    title: '用途'
                },
                {
                    field: 'knowledgeClass',
                    title: '知识分类'
                },
                {
                    field: 'lxr',
                    title: '联系人'
                },
                {
                    field: 'lxrName',
                    title: '联系人姓名'
                },
                {
                    field: 'lxrphone',
                    title: '联系人电话'
                },
                {
                    field: 'lxrEmail',
                    title: '联系人邮箱'
                },
                {
                    field: 'lxrMobile',
                    title: '联系人手机'
                },
                {
                    field: 'fromType',
                    title: '来源类型'
                },
                {
                    field: 'fromNo',
                    title: '来源编号'
                },
                {
                    field: 'fromName',
                    title: '来源名称'
                },
                {
                    field: 'fromContent',
                    title: '其他来源情况说明'
                },
                {
                    field: 'useMethod',
                    title: '应用方式'
                },
                {
                    field: 'useDept',
                    title: '应用部门'
                },
                {
                    field: 'useDeptDept',
                    title: '应用部门名称'
                },
                {
                    field: 'useExpected',
                    title: '预计应用部门'
                },
                {
                    field: 'useExpectedName',
                    title: '预计应用部门名称'
                },
                {
                    field: 'useFirstdate',
                    title: '初始应用日期'
                },
                {
                    field: 'reasonNouse',
                    title: '未应用原因'
                },
                {
                    field: 'contentNouse',
                    title: '未应用原因备注'
                },
                {
                    field: 'jfdwCode',
                    title: '缴费与资助责任单位'
                },
                {
                    field: 'flowStatus',
                    title: '流程状态 draft-草稿 active-流程中 end-结束'
                },
                {
                    field: 'patentStatus',
                    title: '专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权'
                },
                {
                    field: 'djdPerson',
                    title: '待交底人员'
                },
                {
                    field: 'djdDate',
                    title: '待交底日期'
                },
                {
                    field: 'jdPerson',
                    title: '交底人员'
                },
                {
                    field: 'jdDate',
                    title: '交底日期'
                },
                {
                    field: 'qs',
                    title: '受理_权属'
                },
                {
                    field: 'slrq',
                    title: '受理_受理日期'
                },
                {
                    field: 'patentNo',
                    title: '受理_申请号'
                },
                {
                    field: 'patentType',
                    title: '受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计'
                },
                {
                    field: 'yxqr',
                    title: '受理_优先权日'
                },
                {
                    field: 'qlyqsl',
                    title: '受理_权利要求数量'
                },
                {
                    field: 'smsys',
                    title: '受理_说明书页数'
                },
                {
                    field: 'swsGuid',
                    title: '代理事务所'
                },
                {
                    field: 'swsdlr',
                    title: '代理人'
                },
                {
                    field: 'swsdlrPhone',
                    title: '代理人电话'
                },
                {
                    field: 'swsdlrEmail',
                    title: '代理人邮箱'
                },
                {
                    field: 'moneyDlf',
                    title: '受理_代理费'
                },
                {
                    field: 'moneyQf',
                    title: '受理_权附'
                },
                {
                    field: 'moneySqf',
                    title: '受理_申请费'
                },
                {
                    field: 'moneyGbysf',
                    title: '受理_公布印刷费'
                },
                {
                    field: 'moneySmsfjf',
                    title: '受理_说明书附加费'
                },
                {
                    field: 'moneyYhs',
                    title: '授权通知_印花税'
                },
                {
                    field: 'sqtzFwdate',
                    title: '授权通知_发文日'
                },
                {
                    field: 'moneyFirst',
                    title: '授权通知_第一次缴费'
                },
                {
                    field: 'zlh',
                    title: '专利号'
                },
                {
                    field: 'sqrq',
                    title: '授权日期'
                },
                {
                    field: 'ipcClassificationNo',
                    title: '授权日期'
                },
                {
                    field: 'flzt',
                    title: '法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止'
                },
                {
                    field: 'isvalid',
                    title: '是否有效 1-有效 0-无效'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.patentId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.patentId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>