<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('交底')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="formId">
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx"
                                               aria-expanded="false" class="collapsed">批量提交信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">专利事务所：</label>
                                    <div class="col-sm-9">
                                        <select id="batchSwsGuid" class="form-control">
                                            <option value="">请选择专利事务所</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">专利事务所联系人：</label>
                                    <div class="col-sm-9">
                                        <input id="batchSwsdlr" class="form-control" type="text"
                                               placeholder="请输入联系人">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">填写意见：</label>
                                </div>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <textarea id="comment" name="comment" class="form-control"
                                              style="white-space: break-spaces;" rows="8"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--折叠区域end-->

            </div>
        </div>
        <!--框end-->

        <div class="btn-group-sm" id="toolbar" role="group">
            <div class="select-list">
                <ul style="margin-left:70px;">
                    <li>
                        <label>模糊查询：</label>
                        <input type="text" name="anythingLike"/>
                    </li>
                    <li>
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </form>


    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-success" onclick="batchSubmitToDL()">
            <i class="fa fa-check"></i> 批量提交到代理中
        </button>
        <button type="button" class="btn btn-warning" onclick="batchSubmitToWD()">
            <i class="fa fa-clock-o"></i> 批量提交到待定
        </button>
        <button type="button" class="btn btn-info" onclick="batchSave()">
            <i class="fa fa-save"></i> 保存
        </button>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kizl/patentInfo";

    $(function () {
        // 初始化专利事务所下拉选择
        initBatchSwsSelect();

        var options = {
            url: ctx + "kizl/c/d?serviceName=KIZLPatentInfo&methodName=queryDBPage&currentActivity=Manual7",
            detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
            exportUrl: prefix + "/export",
            pageList: [50, 100, 200],
            pageSize: 50,
            modalName: "专利申请",
            sortName: "jsbh", // 按接收编号排序
            sortOrder: "asc",
            onLoadSuccess: function (data) {
                // 表格加载完成后，重新设置下拉选择的值
                setTimeout(function () {
                    restoreOperationSelections();
                }, 100);
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'businessId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'patentId',
                    title: '专利ID',
                    visible: false
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '120',
                    formatter: function (value, row, index) {
                        // 根据专利状态确定当前选中的操作
                        var currentOperation = '';
                        if (row.patentStatus === '04') { // 代理中状态
                            currentOperation = 'toDL';
                        } else if (row.extra8 === 'pending') { // 待定状态（通过扩展字段判断）
                            currentOperation = 'toWD';
                        }

                        var selectHtml = '<select class="form-control operation-select" data-business-id="' + row.businessId + '" data-patent-id="' + row.patentId + '">';
                        selectHtml += '<option value="">请选择</option>';
                        selectHtml += '<option value="toDL"' + (currentOperation === 'toDL' ? ' selected' : '') + '>提交到代理中</option>';
                        selectHtml += '<option value="toWD"' + (currentOperation === 'toWD' ? ' selected' : '') + '>提交到待定</option>';
                        selectHtml += '</select>';
                        return selectHtml;
                    }
                },
                {
                    field: 'jsbh',
                    title: '接收编号',
                    sortable: true
                },
                {
                    title: '专利名称',
                    width: '300',
                    formatter: function (value, row, index) {
                        var actions = [];
                        var patentName = row.patentName || row.applyName || '';
                        actions.push('<a style="color: #00a0e9" onclick="$.operate.detailTab(' + "'" + row.businessId + "'" + ')">' + patentName + '</a>');
                        return actions.join('');
                    }
                },
                {
                    field: 'label',
                    title: '标记',
                    formatter: function (value, row, index) {
                        // 根据专利状态设置标记
                        if (row.patentStatus === '04') {
                            return '<span class="label label-primary">代理中</span>';
                        } else if (row.extra8 === 'pending') {
                            return '<span class="label label-warning">待定</span>';
                        } else if (row.patentStatus === '03') {
                            return '<span class="label label-success">已交底</span>';
                        } else if (row.patentStatus === '02') {
                            return '<span class="label label-info">待交底</span>';
                        } else if (row.extra6 === 'delay') {
                            return '<span class="label label-danger">暂缓</span>';
                        } else {
                            return '<span class="label label-default">申请中</span>';
                        }
                    }
                },
                {
                    field: 'ownership',
                    title: '权属',
                    formatter: function (value, row, index) {
                        if (value == '1') {
                            return '独占';
                        } else if (value == '2') {
                            return '共有';
                        } else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'swsName',
                    title: '事务所'
                },
                {
                    field: 'lxrName',
                    title: '联系人'
                },
                {
                    field: 'lxrMobile',
                    title: '联系电话'
                },
                {
                    field: 'reviewComment',
                    title: '评审意见',
                    width: '200',
                    formatter: function (value, row, index) {
                        return value || '';
                    }
                },
                {
                    title: '附件',
                    align: 'center',
                    width: '200',
                    formatter: function (value, row, index) {
                        var actions = [];
                        // 技术交底书附件
                        if (row.attachmentList && row.attachmentList.length > 0) {
                            var attachmentHtml = '<div style="text-align: left;">';
                            for (var i = 0; i < row.attachmentList.length; i++) {
                                var attachment = row.attachmentList[i];
                                if (i > 0) {
                                    attachmentHtml += '<br/>';
                                }
                                attachmentHtml += '<a href="javascript:void(0)" onclick="downloadAttachment(\'' + 
                                    attachment.attachmentId + '\', \'' + attachment.attachmentName + '\')" ' +
                                    'style="color: #337ab7; text-decoration: underline;" title="点击下载">' + 
                                    attachment.attachmentName + '</a>';
                            }
                            attachmentHtml += '</div>';
                            actions.push(attachmentHtml);
                        } else {
                            actions.push('<span class="text-muted">无附件</span>');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '详情',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        var param = "'" + row.processCode + "','" + row.pageNo + "','"
                            + row.businessId + "','" + row.taskId + "','"
                            + row.currentActivity + "','" + row.processInstanceId + "','"
                            + row.businessType + "','" + row.businessName + "','"
                            + row.businessLabel + "'";
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryDBDetail(' + param + ')"><i class="fa fa-edit"></i>处理</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);

        // 监听事务所选择变化事件
        $('#batchSwsGuid').on('change', function () {
            var swsGuid = $(this).val();
            if (swsGuid) {
                loadSwsDetailInfo(swsGuid);
            } else {
                $('#batchSwsdlr').val('');
            }
        });
    });

    // 初始化批量操作的专利事务所下拉选择
    function initBatchSwsSelect() {
        $.ajax({
            url: ctx + 'kizl/maintainSwsinfo/getAllSwsinfo',
            type: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.code === 0 && result.data) {
                    var options = '<option value="">请选择专利事务所</option>';
                    $.each(result.data, function (index, item) {
                        var displayName = item.swsName || '未知事务所';
                        options += '<option value="' + item.swsId + '">' + displayName + '</option>';
                    });
                    $('#batchSwsGuid').html(options);
                } else {
                    console.error('加载专利事务所信息失败：', result.msg);
                }
            },
            error: function () {
                console.error('加载专利事务所信息失败');
            }
        });
    }

    // 根据swsGuid获取事务所详细信息
    function loadSwsDetailInfo(swsGuid) {
        if (!swsGuid) {
            return;
        }

        $.ajax({
            url: ctx + 'kizl/maintainSwsinfo/getSwsDetailInfo/' + swsGuid,
            type: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.code === 0 && result.data) {
                    var swsDetail = result.data;
                    // 填充代理人相关信息
                    if (swsDetail.lxr) {
                        $('#batchSwsdlr').val(swsDetail.lxr);
                    }
                } else {
                    console.log('获取事务所详细信息失败：', result.msg || '未找到对应的事务所信息');
                }
            },
            error: function () {
                console.error('获取事务所详细信息失败');
            }
        });
    }

    // 批量提交到代理中
    function batchSubmitToDL() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (rows.length == 0) {
            $.modal.alertWarning("请选择要操作的数据");
            return;
        }

        var swsGuid = $('#batchSwsGuid').val();
        var swsdlr = $('#batchSwsdlr').val();
        var comment = $('#comment').val();

        if (!swsGuid) {
            $.modal.alertWarning("请选择专利事务所");
            return;
        }

        if (!swsdlr) {
            $.modal.alertWarning("请输入专利事务所联系人");
            return;
        }
        if (!comment) {
            $.modal.alertWarning("请填写意见");
            return;
        }

        // 检查是否有选择了操作类型的数据
        var hasValidOperation = false;
        var businessIds = [];

        $.each(rows, function (index, row) {
            var operation = $('.operation-select[data-business-id="' + row.businessId + '"]').val();
            if (operation === 'toDL') {
                hasValidOperation = true;
                businessIds.push(row.businessId);
            }
        });

        if (!hasValidOperation) {
            $.modal.alertWarning("请在选中的数据中设置操作为\"提交到代理中\"");
            return;
        }

        $.modal.confirm("确认要将选中的 " + businessIds.length + " 条数据提交到代理中吗？", function () {
            var data = {
                businessIds: businessIds.join(','),
                swsGuid: swsGuid,
                swsdlr: swsdlr,
                comment: comment,
                operation: 'submitToDL'
            };
            $.operate.saveTabAlert(ctx + "kizl/c/s?serviceName=KIZLPatentInfo&methodName=batchSubmitToDL", data);
        });
    }

    // 批量提交到待定
    function batchSubmitToWD() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (rows.length == 0) {
            $.modal.alertWarning("请选择要操作的数据");
            return;
        }

        var comment = $('#comment').val();
        if (!comment) {
            $.modal.alertWarning("请填写意见");
            return;
        }

        // 检查是否有选择了操作类型的数据
        var hasValidOperation = false;
        var businessIds = [];

        $.each(rows, function (index, row) {
            var operation = $('.operation-select[data-business-id="' + row.businessId + '"]').val();
            if (operation === 'toWD') {
                hasValidOperation = true;
                businessIds.push(row.businessId);
            }
        });

        if (!hasValidOperation) {
            $.modal.alertWarning("请在选中的数据中设置操作为\"提交到待定\"");
            return;
        }

        $.modal.confirm("确认要将选中的 " + businessIds.length + " 条数据提交到待定吗？", function () {
            var data = {
                businessIds: businessIds.join(','),
                comment: comment,
                operation: 'submitToWD'
            };
            $.operate.saveTabAlert(ctx + "kizl/c/s?serviceName=KIZLPatentInfo&methodName=batchSubmitToWD", data);
        });
    }

    // 批量保存
    function batchSave() {
        console.log("开始执行批量保存操作");

        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        console.log("选中的行数：", rows.length);
        console.log("选中的数据：", rows);

        if (rows.length == 0) {
            $.modal.alertWarning("请选择要保存的数据");
            return;
        }

        var swsGuid = $('#batchSwsGuid').val();
        var swsdlr = $('#batchSwsdlr').val();
        var comment = $('#comment').val();
        console.log("专利事务所：", swsGuid, "联系人：", swsdlr);

        var saveData = [];
        $.each(rows, function (index, row) {
            var operation = $('.operation-select[data-business-id="' + row.businessId + '"]').val();
            console.log("行", index, "业务ID：", row.businessId, "操作：", operation);

            if (operation) {
                var itemData = {
                    businessId: row.businessId,
                    patentId: row.patentId,
                    taskId : row.taskId,
                    operation: operation,
                    comment: comment,
                    swsGuid: swsGuid,
                    swsdlr: swsdlr
                };
                saveData.push(itemData);
                console.log("添加保存数据：", itemData);
            }
        });

        console.log("最终保存数据：", saveData);
        console.log("保存数据条数：", saveData.length);

        if (saveData.length === 0) {
            $.modal.alertWarning("请选择操作类型");
            return;
        }

        var data = {
            saveData: JSON.stringify(saveData)
        };

        console.log("发送到后端的数据：", data);
        console.log("请求URL：", ctx + "kizl/c/s?serviceName=KIZLPatentInfo&methodName=batchSaveJDOperation");

        // 添加成功和失败的回调
        $.ajax({
            url: ctx + "kizl/c/s?serviceName=KIZLPatentInfo&methodName=batchSaveJDOperation",
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function () {
                console.log("开始发送请求");
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                console.log("请求成功，返回结果：", result);
                $.modal.closeLoading();
                if (result.code === 0) {
                    $.modal.msgSuccess(result.msg || "保存成功");
                    // 刷新表格以显示更新后的状态
                    $("#bootstrap-table").bootstrapTable('refresh');
                    // 清空选择状态
                    $("#bootstrap-table").bootstrapTable('uncheckAll');
                } else {
                    $.modal.msgError(result.msg || "保存失败");
                }
            },
            error: function (xhr, status, error) {
                console.error("请求失败：", xhr, status, error);
                $.modal.closeLoading();
                $.modal.msgError("请求失败：" + error);
            },
            complete: function () {
                console.log("请求完成");
            }
        });
    }

    // 恢复操作选择状态
    function restoreOperationSelections() {
        $('.operation-select').each(function () {
            var $select = $(this);
            var businessId = $select.data('business-id');
            var patentId = $select.data('patent-id');

            // 从表格数据中查找对应行的状态信息
            var tableData = $("#bootstrap-table").bootstrapTable('getData');
            for (var i = 0; i < tableData.length; i++) {
                var row = tableData[i];
                if (row.businessId === businessId) {
                    var currentOperation = '';
                    if (row.patentStatus === '04') { // 代理中状态
                        currentOperation = 'toDL';
                    } else if (row.extra8 === 'pending') { // 待定状态（通过扩展字段判断）
                        currentOperation = 'toWD';
                    }

                    if (currentOperation) {
                        $select.val(currentOperation);
                    }
                    break;
                }
            }
        });
    }

    // 下载附件
    function downloadAttachment(attachmentId, attachmentName) {
        if (!attachmentId) {
            $.modal.alertWarning("附件ID不能为空");
            return;
        }
        
        try {
            // 构建下载URL
            var downloadUrl = ctx + "attachment/download/" + attachmentId;
            
            // 创建隐藏的下载链接
            var link = document.createElement('a');
            link.href = downloadUrl;
            link.download = attachmentName || '附件';
            link.style.display = 'none';
            
            // 添加到页面并触发下载
            document.body.appendChild(link);
            link.click();
            
            // 清理
            document.body.removeChild(link);
            
            console.log("开始下载附件：" + attachmentName + "，ID：" + attachmentId);
        } catch (error) {
            console.error("下载附件失败：", error);
            $.modal.alertError("下载附件失败：" + error.message);
        }
    }

    function queryDBDetail(processCode, pageNo, businessId, taskId, activityCode, processInstanceId, businessType, businessName, businessLabel) {
        var url = ctx + "kizl/workFlow/queryDBDetail/" + processCode + "/" + pageNo + "/" + businessId + "/" + taskId;
        console.log(url);
        $.modal.openTab(businessName + "-待办详细", url, true);
    }
</script>
</body>
</html>