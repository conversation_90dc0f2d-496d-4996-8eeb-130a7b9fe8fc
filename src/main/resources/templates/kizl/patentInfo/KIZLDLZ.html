<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('代理中')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>模糊查询：</label>
                                <input type="text" name="anythingLike"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
    	var prefix = ctx + "kizl/patentInfo";

        $(function() {
            var options = {
                url: ctx + "kizl/c/d?serviceName=KIZLPatentInfo&methodName=queryDBPage&currentActivity=Manual8",
                detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
                exportUrl: prefix + "/export",
                pageList: [50, 100, 200],
                pageSize: 50,
                modalName: "专利申请",
                columns: [{
                    checkbox: true
                    },
                    {
                        field: 'businessId',
                        title: '主键',
                        visible: false
                    },
                    {
                        field: 'egbh',
                        title: '鄂钢编号'
                    },
                    {
                        field: 'jsbh',
                        title: '接收编号'
                    },
                    {
                        title: '专利名称',
                        width: '400',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a style="color: #00a0e9" onclick="$.operate.detailTab('+"'"+row.businessId+"'"+')">'+row.applyName+'</a>');
                            return actions.join('');
                        }
                    },
                    {
                        field: 'patentNo',
                        title: '申请号'
                    },
                    {
                        field: 'slrq',
                        title: '申请日'
                    },
                    {
                        field: 'firstDeptName',
                        title: '第一申请部门',
                        width: '260'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            var param = "'" + row.processCode + "','" + row.pageNo + "','"
                                + row.businessId + "','" + row.taskId + "','"
                                + row.currentActivity + "','" + row.processInstanceId + "','"
                                + row.businessType + "','" + row.businessName + "','"
                                + row.businessLabel + "'";
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryDBDetail(' + param + ')"><i class="fa fa-edit"></i>处理</a> ');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });

        function queryDBDetail(processCode, pageNo, businessId, taskId, activityCode, processInstanceId, businessType, businessName, businessLabel) {
            var url = ctx + "kizl/workFlow/queryDBDetail/" + processCode + "/" + pageNo + "/" + businessId + "/" + taskId;
            console.log(url);
            $.modal.openTab(businessName + "-待办详细", url, true);
        }
    </script>
</body>
</html>