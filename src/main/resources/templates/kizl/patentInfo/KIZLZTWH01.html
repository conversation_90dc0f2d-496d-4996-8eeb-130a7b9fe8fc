<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('专利状态维护')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>鄂钢编号：</label>
                                <input type="text" name="egbh"/>
                            </li>
                            <li>
                                <label>接收编号：</label>
                                <input type="text" name="jsbh"/>
                            </li>
                            <li>
                                <label>申请号：</label>
                                <input type="text" name="patentNo"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
    	var prefix = ctx + "kizl/patentInfo";

        var dictData_patentType = /*[[${@SDictUtil.getDictList('KIZL','PATENT_TYPE')}]]*/ [];
        var dictData_patentStatus = /*[[${@SDictUtil.getDictList('KIZL','PATENT_STATUS')}]]*/ [];

        $(function() {
            var options = {
                url: prefix + "/statusMaintenanceList",
                detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
                updateUrl: ctx + "kizl/c/l?pagePath=patentInfo&pageNo=KIZLZTWH02&serviceName=KIZLPatentInfo&methodName=queryDBDetail&businessId={id}",
                exportUrl: prefix + "/export",
                modalName: "专利申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'applyId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'egbh',
                    title: '鄂钢编号'
                },
                {
               	    title: '专利名称',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a style="color: #00a0e9" onclick="$.operate.detailTab('+"'"+row.applyId+"'"+')">'+row.patentName+'</a>');
                        return actions.join('');
                    }
                },
                {
                    field: 'patentNo',
                    title: '申请号'
                },
                {
                    field: 'slrq',
                    title: '申请日'
                },
                {
                    title: '法律状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(row.isvalid=='1'){
                        	actions.push('有效');
                        }else if(row.isvalid=='0'){
                        	actions.push('无效');
                        }else{
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'patentStatus',
                    title: '最新状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(dictData_patentStatus, value);
                    }
                },
                {
                    field: 'patentType',
                    title: '专利类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(dictData_patentType, value);
                    }
                },
                {
                    field: 'zzrq',
                    title: '终止日期'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.applyId + '\')"><i class="fa fa-edit"></i>状态维护</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>