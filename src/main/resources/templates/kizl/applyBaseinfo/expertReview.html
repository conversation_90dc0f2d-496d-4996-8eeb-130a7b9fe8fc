<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_专家评审')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-expert-review" th:object="${expertReview}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        <input id="expertReviewId" name="expertReviewId" th:field="*{expertReviewId}" type="hidden">
        
        <!-- 嵌入申请信息（只读） -->
        <div class="panel panel-info">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fa fa-info-circle"></i> 申请信息
                </h4>
            </div>
            <div class="panel-body">
                <th:block th:include="kizl/applyBaseinfo/applyInfoReadonly :: applyInfoReadonly"></th:block>
            </div>
        </div>

        <!-- 专家评审信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fa fa-user-md"></i> 专家评审信息
                </h4>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label">专家姓名：</label>
                    <div class="col-sm-4">
                        <input name="expertName" th:field="*{expertName}" class="form-control" type="text" readonly>
                    </div>
                    <label class="col-sm-2 control-label">评审日期：</label>
                    <div class="col-sm-4">
                        <input name="reviewDate" th:field="*{reviewDate}" class="form-control" type="text" readonly>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">技术方案评价：</label>
                    <div class="col-sm-8">
                        <textarea name="techEvaluation" th:field="*{techEvaluation}" class="form-control" 
                                rows="4" maxlength="1000" placeholder="请对技术方案进行详细评价" required></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">专利三性评估：</label>
                    <div class="col-sm-8">
                        <textarea name="patentabilityAssessment" th:field="*{patentabilityAssessment}" class="form-control" 
                                rows="4" maxlength="1000" placeholder="请评估新颖性、创造性、实用性" required></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">保护建议：</label>
                    <div class="col-sm-6">
                        <div class="radio-box">
                            <label class="radio-inline">
                                <input type="radio" name="protectionSuggestion" value="1" th:field="*{protectionSuggestion}" required> 建议申请专利
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="protectionSuggestion" value="2" th:field="*{protectionSuggestion}" required> 建议技术秘密保护
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="protectionSuggestion" value="3" th:field="*{protectionSuggestion}" required> 不建议保护
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">专家意见：</label>
                    <div class="col-sm-8">
                        <textarea name="expertOpinion" th:field="*{expertOpinion}" class="form-control" 
                                rows="5" maxlength="1000" placeholder="请填写详细的专家意见" required></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">评审结论：</label>
                    <div class="col-sm-6">
                        <div class="radio-box">
                            <label class="radio-inline">
                                <input type="radio" name="reviewConclusion" value="1" th:field="*{reviewConclusion}" required> 通过
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="reviewConclusion" value="2" th:field="*{reviewConclusion}" required> 不通过
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="reviewConclusion" value="3" th:field="*{reviewConclusion}" required> 需要修改
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 不通过或需要修改时显示的原因输入框 -->
                <div class="form-group" id="modificationReasonGroup" style="display: none;">
                    <label class="col-sm-2 control-label is-required">修改建议/不通过原因：</label>
                    <div class="col-sm-8">
                        <textarea name="modificationReason" th:field="*{modificationReason}" class="form-control" 
                                rows="3" maxlength="500" placeholder="请详细说明修改建议或不通过的原因"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作流信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow}, needComment=${needComment eq true})" />
    </form>

    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-sm btn-default" onclick="saveDraft()">
            <i class="fa fa-save"></i>暂 存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i>提 交
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->
</div>

<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "kizl/applyBaseinfo/expertReview";

    $(function () {
        // 初始化表单验证
        $("#form-expert-review").validate({
            focusCleanup: true,
            onkeyup: false,
            onfocusout: false,
            ignore: "",
            rules: {
                techEvaluation: {
                    required: true,
                    maxlength: 1000
                },
                patentabilityAssessment: {
                    required: true,
                    maxlength: 1000
                },
                protectionSuggestion: {
                    required: true
                },
                expertOpinion: {
                    required: true,
                    maxlength: 1000
                },
                reviewConclusion: {
                    required: true
                },
                modificationReason: {
                    required: function() {
                        var conclusion = $("input[name='reviewConclusion']:checked").val();
                        return conclusion === "2" || conclusion === "3";
                    },
                    maxlength: 500
                }
            },
            messages: {
                techEvaluation: {
                    required: "请填写技术方案评价",
                    maxlength: "技术方案评价不能超过1000个字符"
                },
                patentabilityAssessment: {
                    required: "请填写专利三性评估",
                    maxlength: "专利三性评估不能超过1000个字符"
                },
                protectionSuggestion: {
                    required: "请选择保护建议"
                },
                expertOpinion: {
                    required: "请填写专家意见",
                    maxlength: "专家意见不能超过1000个字符"
                },
                reviewConclusion: {
                    required: "请选择评审结论"
                },
                modificationReason: {
                    required: "选择不通过或需要修改时必须填写原因",
                    maxlength: "修改建议/不通过原因不能超过500个字符"
                }
            }
        });

        // 初始化表单数据
        initFormData();

        // 监听评审结论选择变化
        $("input[name='reviewConclusion']").change(function() {
            toggleModificationReason($(this).val());
        });

        // 初始化修改原因字段显示状态
        var selectedConclusion = $("input[name='reviewConclusion']:checked").val();
        if (selectedConclusion) {
            toggleModificationReason(selectedConclusion);
        }
    });

    // 初始化表单数据
    function initFormData() {
        // 如果是新增表单，初始化评审日期为当前日期
        if (!$("#expertReviewId").val()) {
            var today = new Date();
            var yyyy = today.getFullYear();
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var dd = String(today.getDate()).padStart(2, '0');
            var formattedDate = yyyy + '-' + mm + '-' + dd;
            $("input[name='reviewDate']").val(formattedDate);
            
            // 设置当前用户为专家
            var currentUser = /*[[${@userService.getCurrentUser().userName}]]*/ '';
            $("input[name='expertName']").val(currentUser);
        }
    }

    // 切换修改原因字段的显示
    function toggleModificationReason(conclusion) {
        if (conclusion === "2" || conclusion === "3") { // 不通过或需要修改
            $("#modificationReasonGroup").show();
        } else {
            $("#modificationReasonGroup").hide();
            $("textarea[name='modificationReason']").val('');
        }
        
        // 重新验证表单
        $("#form-expert-review").valid();
    }

    // 表单提交
    function submitHandler() {
        // 执行表单验证
        if (!$("#form-expert-review").valid()) {
            $.modal.alertWarning("表单中有必填项未填写或格式不正确，请检查!");
            return;
        }

        // 收集表单数据
        var formData = $("#form-expert-review").serialize();
        formData += "&flowStatus=submit&start=1";

        $.modal.confirm("确定要提交专家评审意见吗？提交后将进入下一审批环节。", function () {
            $.ajax({
                url: prefix + "/doSave",
                type: "post",
                dataType: "json",
                data: formData,
                success: function (result) {
                    if (result.code === 0) {
                        $.modal.alertSuccess(result.msg, function () {
                            closeItem();
                        });
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });
        });
    }

    // 暂存功能
    function saveDraft() {
        // 暂时禁用验证
        var validator = $("#form-expert-review").validate();
        validator.settings.ignore = "*";

        // 收集表单数据
        var formData = $("#form-expert-review").serialize();
        formData += "&flowStatus=draft&start=0";

        $.ajax({
            url: prefix + "/doSave",
            type: "post",
            dataType: "json",
            data: formData,
            success: function (result) {
                if (result.code === 0) {
                    if ($.common.isNotEmpty(result.data)) {
                        if ($.common.isNotEmpty(result.data.expertReviewId)) {
                            $("#expertReviewId").val(result.data.expertReviewId);
                        }
                    }
                    $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
            },
            complete: function () {
                // 恢复验证规则
                validator.settings.ignore = "";
            }
        });
    }

    // 关闭页面
    function closeItem() {
        parent.$.modal.close();
        if (parent.$.table) {
            parent.$.table.refresh();
        }
    }
</script>

<style>
.panel-info .panel-heading {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f;
}

.radio-box {
    padding-top: 7px;
}

.radio-inline {
    margin-right: 20px;
}

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 34px;
}

/* 确保只读组件的样式正确 */
#readonly-accordion .panel-heading {
    background-color: #f5f5f5;
}

#readonly-accordion .form-control-static {
    background-color: transparent;
    border: none;
    box-shadow: none;
}
</style>
</body>
</html> 