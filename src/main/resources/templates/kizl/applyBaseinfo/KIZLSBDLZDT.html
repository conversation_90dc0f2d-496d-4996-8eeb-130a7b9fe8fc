<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_代理中_查看')"/>
    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="form-horizontal m" th:object="${applyBaseinfo}">
        
        <!-- 申请表信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#sqbxx" aria-expanded="true"
                       class="">申请表信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="sqbxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">接收编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.jsbh ?: applyBaseinfo.jsbh}"></p>
                        </div>
                        <label class="col-sm-2 control-label">鄂钢编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.egbh}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">权属：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.qs}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">发明名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${applyBaseinfo.applyName}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">申请日：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.slrq}"></p>
                        </div>
                        <label class="col-sm-2 control-label">申请号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentNo}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利类型：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='patentType', name='patentType', businessType='KIZL', dictCode='PATENT_TYPE', value=${applyBaseinfo.patentInfo?.patentType},see=true)"></div>
                        </div>
                        <label class="col-sm-2 control-label">优先权日：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.yxqr}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">权利要求数量：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.qlyqsl}"></p>
                        </div>
                        <label class="col-sm-2 control-label">说明书页数：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.smsys}"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请人信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-sqrxx" aria-expanded="true">
                        申请人信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="readonly-sqrxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div th:if="${applyBaseinfo.sqrList != null and not #lists.isEmpty(applyBaseinfo.sqrList)}">
                        <div th:each="sqr,sqrStat : ${applyBaseinfo.sqrList}" class="applicant-readonly-block">
                            <div class="form-group">
                                <label class="col-sm-2 control-label" th:text="'第' + ${sqr.xh} + '申请人：'"></label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${sqr.legalName}"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">通信地址：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${sqr.mailAddress}"></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">邮政编码：</label>
                                <div class="col-sm-2">
                                    <p class="form-control-static" th:text="${sqr.postOffice}"></p>
                                </div>
                                <label class="col-sm-2 control-label">付费联系人：</label>
                                <div class="col-sm-2">
                                    <p class="form-control-static" th:text="${sqr.lxrMoney}"></p>
                                </div>
                                <label class="col-sm-2 control-label">联系电话：</label>
                                <div class="col-sm-2">
                                    <p class="form-control-static" th:text="${sqr.lxrPhone}"></p>
                                </div>
                            </div>
                            <hr th:if="${not sqrStat.last}"/>
                        </div>
                    </div>
                    <div th:if="${applyBaseinfo.sqrList == null or #lists.isEmpty(applyBaseinfo.sqrList)}">
                        <p class="text-muted">暂无申请人信息</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 发明设计人信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-fmsjr" aria-expanded="true">
                        发明设计人
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="readonly-fmsjr" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div th:if="${applyBaseinfo.ryxxList != null and not #lists.isEmpty(applyBaseinfo.ryxxList)}">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>姓名</th>
                                    <th>工号</th>
                                    <th>单位</th>
                                    <th>岗位</th>
                                    <th>职称</th>
                                    <th>类型</th>
                                    <th>身份证号</th>
                                    <th>备注</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="ryxx,ryxxStat : ${applyBaseinfo.ryxxList}">
                                    <td th:text="${ryxx.ryxh}"></td>
                                    <td th:text="${ryxx.empName}"></td>
                                    <td th:text="${ryxx.empId}"></td>
                                    <td th:text="${ryxx.deptName}"></td>
                                    <td>
                                        <div th:include="/component/select :: init(id='postLevel_' + ${ryxxStat.index}, name='postLevel_' + ${ryxxStat.index}, businessType='KIZL', dictCode='POST_LEVEL', value=${ryxx.postLevel}, see=true)"></div>
                                    </td>
                                    <td>
                                        <div th:include="/component/select :: init(id='postTitle_' + ${ryxxStat.index}, name='postTitle_' + ${ryxxStat.index}, businessType='KIZL', dictCode='POST_TITLE', value=${ryxx.postTitle}, see=true)"></div>
                                    </td>
                                    <td th:text="${ryxx.rylx == '01' ? '集团内' : '集团外'}"></td>
                                    <td th:text="${not #strings.isEmpty(ryxx.idCard) ? '已填写' : ''}"></td>
                                    <td th:text="${ryxx.contentMemo}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div th:if="${applyBaseinfo.ryxxList == null or #lists.isEmpty(applyBaseinfo.ryxxList)}">
                        <p class="text-muted">暂无发明人信息</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 事务所信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#swsxx" aria-expanded="true"
                       class="">事务所信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="swsxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">代理事务所：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" id="swsNameDisplay">
                                <!-- 隐藏字段存储swsGuid -->
                                <input type="hidden" id="swsGuid" th:value="${applyBaseinfo.patentInfo?.swsGuid}" />
                                <span id="swsNameText">加载中...</span>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">代理人：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.swsdlr}"></p>
                        </div>
                        <label class="col-sm-2 control-label">联系电话：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.swsdlrPhone}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">代理人邮箱：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.swsdlrEmail}"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请相关文件 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#231231" aria-expanded="true"
                       class="">申请相关文件
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="231231" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label" id="readonly-sqxgwj-label">申请相关文件：</label>
                        <div class="col-sm-8">
                            <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='sqxgwj',id='sqxgwj',name='attachmentS[sqxgwj]',see=true)"></th:block>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" id="readonly-gtsqxy-label">共同申请协议：</label>
                        <div class="col-sm-8">
                            <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='gtsqxy',id='gtsqxy',name='attachmentS[gtsqxy]',see=true)"></th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 确认信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#qrxx" aria-expanded="true"
                       class="">确认信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="qrxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">内部参考意见：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.nbckyj}"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 返回按钮 -->
<div class="toolbar toolbar-bottom" role="toolbar">
    <button type="button" class="btn btn-danger" onclick="closeItem()">
        <i class="fa fa-reply-all"></i>返回
    </button>
</div>

<script th:inline="javascript" th:src="@{/kizl/kizl-common.js}"></script>
<script>
$(document).ready(function() {
    // 使用公共JS方法加载事务所名称
    autoLoadSwsName();
    
    // 初始化附件名称映射
    KizlAttachmentUtils.init(function() {
        KizlAttachmentUtils.updateCommonLabels();
    });

});
</script>

<style>
.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 34px;
}

.panel-title a {
    text-decoration: none;
}

.panel-title a:hover {
    text-decoration: none;
}
</style>
</body>
</html>
