<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_授权信息')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#jsxx" aria-expanded="false"
                       class="collapsed">授权信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="jsxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利申报信息：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">
                                <a href="javascript:void(0);" onclick="viewBasicInfo()" style="color: blue">
                                    点击查看
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">受理信息：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">
                                <a href="javascript:void(0);" onclick="viewPatentInfo()" style="color: blue">
                                    点击查看
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">授权通知：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">
                                <a href="javascript:void(0);" onclick="viewAuthNoticeInfo()" style="color: blue">
                                    点击查看
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">鄂钢编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.egbh}"></p>
                        </div>
                        <label class="col-sm-2 control-label">接收编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.jsbh}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利申报名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static"
                               th:text="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利类型：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='patentType', name='patentType', businessType='KIZL', dictCode='PATENT_TYPE', value=${applyBaseinfo.patentInfo?.patentType},see=true)"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">申请日：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.slrq}"></p>
                        </div>
                        <label class="col-sm-2 control-label">申请号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentNo}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">授权日：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/date :: init(name='patentInfo.sqrq', id='sqrq', strValue=${applyBaseinfo.patentInfo?.sqrq}, isrequired=true)"></div>
                        </div>
                        <label class="col-sm-2 control-label">IPC主分类号：</label>
                        <div class="col-sm-4">
                            <input id="extra1" name="patentInfo.extra1" th:value="${applyBaseinfo.patentInfo?.extra1}"
                                   class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required" id="zlzsfj-label">专利证书：</label>
                        <div class="col-sm-8">
                            <th:block
                                    th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='zlzsfj',id='zlzsfj',name='attachmentS[zlzsfj]', isrequired=true)"></th:block>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" id="sqxxxgfj-label">相关附件：</label>
                        <div class="col-sm-8">
                            <th:block
                                    th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='sqxxxgfj',id='sqxxxgfj',name='attachmentS[sqxxxgfj]')"></th:block>
                        </div>
                    </div>
                    <!-- 隐藏字段 -->
                    <input type="hidden" name="patentInfo.patentId" th:value="${applyBaseinfo.patentInfo?.patentId}">
                    <input type="hidden" name="patentInfo.applyId" th:value="${applyBaseinfo.applyId}">
                    <input type="hidden" name="patentInfo.patentStatus"
                           th:value="${applyBaseinfo.patentInfo?.patentStatus}">
                </div>
            </div>
        </div>
        <!-- 工作流信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow})"/>
    </form>
    <!--按钮区-->
    <th:block th:include="kizl/wfKizlButton :: init(workFlow=${applyBaseinfo.workFlow})"/>
    <!--按钮区-->
</div>

<th:block th:include="include :: datetimepicker-js"/>
<!-- 引入KIZL通用JS -->
<script th:src="@{/kizl/kizl-common.js}"></script>
<!-- 引入附件标签工具类 -->
<script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
<script th:inline="javascript">

    $(function() {
        // 初始化附件名称映射
        KizlAttachmentUtils.init(function() {
            KizlAttachmentUtils.updatePageLabels('maintain');
        });
    });

    // 保存表单
    function saveDraft() {
        var formData = $('#form-applyBaseinfo-edit').serialize();

        $.ajax({
            url: ctx + 'kizl/applyBaseinfo/savePatentInfo',
            type: 'POST',
            data: formData,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code === 0) {
                    $.modal.alertSuccess("保存成功");
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            },
            error: function () {
                $.modal.alertError("保存失败");
            }
        });
    }

    // 注意：查看相关函数（viewBasicInfo, viewPatentInfo, viewAuthNoticeInfo, viewAuthInfo）
    // 现在统一由 kizl-common.js 提供，无需在此重复定义
</script>
</body>
</html>

