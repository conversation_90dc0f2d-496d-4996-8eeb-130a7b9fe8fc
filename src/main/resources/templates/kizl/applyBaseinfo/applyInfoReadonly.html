<!-- 申请信息只读组件 -->
<div class="panel-group" id="readonly-accordion" role="tablist" aria-multiselectable="true"
     th:fragment="applyInfoReadonly">
    <!-- 引入附件工具类 -->
    <script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
    <!-- 基本信息 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-jbxx" aria-expanded="false"
                   class="collapsed">
                    基本信息
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-jbxx" class="panel-collapse collapse in" aria-expanded="false">
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label">流水号：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.serialNum}"></p>
                    </div>
                    <label class="col-sm-2 control-label">填表日期：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.applyDate}"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">专利申报名称：</label>
                    <div class="col-sm-8">
                        <p class="form-control-static" th:text="${applyBaseinfo.applyName}"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">第一申报部门：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/selectOrg :: init(orgCodeId='firstDeptCode', orgNameId='firstDeptName', value=${applyBaseinfo.firstDeptCode}, see=true)"></div>
                    </div>
                </div>
<!--                <div class="form-group">-->
<!--                    <label class="col-sm-2 control-label">标签：</label>-->
<!--                    <div class="col-sm-8">-->
<!--                        <div th:include="/component/checkbox :: init(id='label', name='label', businessType='KIZL', dictCode='LABEL', value=${applyBaseinfo.label}, see=true)"></div>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="form-group">
                    <label class="col-sm-2 control-label">技术领域：</label>
                    <div class="col-sm-8">
                        <p class="form-control-static" th:text="${applyBaseinfo.extra2}"></p>
                    </div>
                </div>
<!--                <div class="form-group">-->
<!--                    <label class="col-sm-2 control-label">用途：</label>-->
<!--                    <div class="col-sm-4">-->
<!--                        <div th:include="/component/checkbox :: init(id='usePropose', name='usePropose', businessType='KIZL', dictCode='USE_PROPOSE', value=${applyBaseinfo.usePropose}, see=true)"></div>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="form-group">
                    <label class="col-sm-2 control-label">是否产品专利：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='knowledgeClass', name='knowledgeClass', businessType='KIZL',dictCode='YES_NO', value=${applyBaseinfo.knowledgeClass}, see=true)"></div>
                    </div>
                </div>
                <div class="form-group" th:if="${applyBaseinfo.knowledgeClass == '1'}">
                    <label class="col-sm-2 control-label">产品领域：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='ishaveph', name='ishaveph', businessType='KIZL', dictCode='ishaveph', value=${applyBaseinfo.ishaveph}, see=true)"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 联系人信息 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-lxrxx" aria-expanded="true">
                    联系人信息
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-lxrxx" class="panel-collapse collapse in" aria-expanded="true">
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label">联系人：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.lxrName}"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">联系人手机：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.lxrMobile}"></p>
                    </div>
                    <label class="col-sm-2 control-label">联系人邮箱：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.lxrPhone}"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 检索信息 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-jsxx" aria-expanded="true">
                    检索信息
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-jsxx" class="panel-collapse collapse in" aria-expanded="true">
            <div class="panel-body">
                <div class="form-group">
                    <div class="mnote-editor-title"><span class="txt-impt"></span>专利检索情况
                    </div>
                    <div class="mnote-editor-box">
                        <div class="col-sm-12">
                                    <textarea class="form-control" name="searchType" id="searchType" rows="5" cols="150"
                                              maxlength="200" th:text="${applyBaseinfo.searchType}" style="white-space: break-spaces;" readonly></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" id="readonly-rhxgfj-label">相关附件：</label>
                    <div class="col-sm-8">
                        <th:block
                                th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='rhxgfj',id='rhxgfj',name='attachmentS[rhxgfj]',see=true)"></th:block>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 来源与应用信息 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-lyxx" aria-expanded="true">
                    来源与应用信息
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-lyxx" class="panel-collapse collapse in" aria-expanded="true">
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label">来源：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='fromType', name='fromType', businessType='KIZL', dictCode='FROM_TYPE', value=${applyBaseinfo.fromType}, see=true)"></div>
                    </div>
                </div>
                <div class="form-group" th:if="${applyBaseinfo.fromType == 'kyxm'}">
                    <label class="col-sm-2 control-label">来源编号：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.fromNo}"></p>
                    </div>
                    <label class="col-sm-2 control-label">来源名称：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.fromName}"></p>
                    </div>
                </div>
                <div class="form-group" th:if="${applyBaseinfo.fromType == 'other'}">
                    <label class="col-sm-2 control-label">其他来源情况说明：</label>
                    <div class="col-sm-8">
                        <p class="form-control-static" th:text="${applyBaseinfo.fromContent}"
                           style="white-space: pre-wrap;"></p>
                    </div>
                </div>
                <div class="form-group" id="sameSourcePatent" style="display: none;">
                    <label class="col-sm-2 control-label">同来源专利：</label>
                    <div class="col-sm-10">
                        <div th:insert="/jsmm/techSecretComponent/sameSourcePatentList :: init"></div>
                    </div>
                </div>
                <div class="form-group" id="sameSourceSecret" style="display: none;">
                    <label class="col-sm-2 control-label">同来源技术秘密：</label>
                    <div class="col-sm-10">
                        <div th:insert="/jsmm/techSecretComponent/sameSourceSecretList :: init"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">应用方式：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='useMethod', name='useMethod', businessType='KIZL', dictCode='USE_METHOD', value=${applyBaseinfo.useMethod}, see=true)"></div>
                    </div>
                </div>
                <div class="form-group" th:if="${applyBaseinfo.useMethod == '1'}">
                    <label class="col-sm-2 control-label">应用部门：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/selectOrg :: init(orgCodeId='useDept', orgNameId='useDeptName', value=${applyBaseinfo.useDept},see=true)"></div>
                    </div>
                    <label class="col-sm-2 control-label">初始应用日：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.useFirstdate}"></p>
                    </div>
                </div>
                <div class="form-group" th:if="${applyBaseinfo.useMethod == '2'}">
                    <label class="col-sm-2 control-label">初始应用日：</label>
                    <div class="col-sm-4">
                        <p class="form-control-static" th:text="${applyBaseinfo.useFirstdate}"></p>
                    </div>
                </div>
                <div class="form-group" th:if="${applyBaseinfo.useMethod == '3'}">
                    <label class="col-sm-2 control-label">预计应用部门：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/selectOrg :: init(orgCodeId='useExpected', orgNameId='useExpectedName', value=${applyBaseinfo.useExpected},see=true)"></div>
                    </div>
                    <label class="col-sm-2 control-label">未应用原因：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='reasonNouse', name='reasonNouse', businessType='KIZL', dictCode='REASON_NOUSE', value=${applyBaseinfo.reasonNouse}, see=true)"></div>
                    </div>
                </div>
                <div class="form-group"
                     th:if="${applyBaseinfo.reasonNouse == '9' and not #strings.isEmpty(applyBaseinfo.contentNouse)}">
                    <label class="col-sm-2 control-label">未应用原因备注：</label>
                    <div class="col-sm-8">
                        <p class="form-control-static" th:text="${applyBaseinfo.contentNouse}"
                           style="white-space: pre-wrap;"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">预计效果：</label>
                    <div class="col-sm-8">
                        <div th:include="/component/checkbox :: init(id='resultExpect', name='resultExpected', businessType='KIZL', dictCode='RESULT_EXPECT', value=${applyBaseinfo.resultExpect}, see=true)"></div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="mnote-editor-title"><span class="txt-impt">*</span>专利实施情况说明：(不得多于200字)</div>
                    <div class="mnote-editor-box">
                        <div class="col-sm-12">
                                    <textarea class="form-control" name="summary" id="summary" rows="5" cols="150"
                                              maxlength="200" th:text="${applyBaseinfo.summary}"
                                              style="white-space: break-spaces;"
                                              readonly>

                                    </textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请人信息 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-sqrxx" aria-expanded="true">
                    申请人信息
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-sqrxx" class="panel-collapse collapse in" aria-expanded="true">
            <div class="panel-body">
                <div th:if="${applyBaseinfo.sqrList != null and not #lists.isEmpty(applyBaseinfo.sqrList)}">
                    <div th:each="sqr,sqrStat : ${applyBaseinfo.sqrList}" class="applicant-readonly-block">
                        <div class="form-group">
                            <label class="col-sm-2 control-label" th:text="'第' + ${sqr.xh} + '申请人：'"></label>
                            <div class="col-sm-8">
                                <p class="form-control-static" th:text="${sqr.legalName}"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">通信地址：</label>
                            <div class="col-sm-8">
                                <p class="form-control-static" th:text="${sqr.mailAddress}"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">邮政编码：</label>
                            <div class="col-sm-2">
                                <p class="form-control-static" th:text="${sqr.postOffice}"></p>
                            </div>
                            <label class="col-sm-2 control-label">联系电话：</label>
                            <div class="col-sm-2">
                                <p class="form-control-static" th:text="${sqr.lxrPhone}"></p>
                            </div>
                        </div>
                        <hr th:if="${not sqrStat.last}"/>
                    </div>
                </div>
                <div th:if="${applyBaseinfo.sqrList == null or #lists.isEmpty(applyBaseinfo.sqrList)}">
                    <p class="text-muted">暂无申请人信息</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 发明设计人 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-fmsjr" aria-expanded="true">
                    发明设计人
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-fmsjr" class="panel-collapse collapse in" aria-expanded="true">
            <div class="panel-body">
                <div th:if="${applyBaseinfo.ryxxList != null and not #lists.isEmpty(applyBaseinfo.ryxxList)}">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                            <tr>
                                <th>序号</th>
                                <th>姓名</th>
                                <th>工号</th>
                                <th>单位</th>
                                <th>岗位</th>
                                <th>职称</th>
                                <th>类型</th>
                                <th>身份证号</th>
                                <th>备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="ryxx,ryxxStat : ${applyBaseinfo.ryxxList}">
                                <td th:text="${ryxx.ryxh}"></td>
                                <td th:text="${ryxx.empName}"></td>
                                <td th:text="${ryxx.empId}"></td>
                                <td th:text="${ryxx.deptName}"></td>
                                <td>
                                    <div th:include="/component/select :: init(id='postLevel_' + ${ryxxStat.index}, name='postLevel_' + ${ryxxStat.index}, businessType='KIZL', dictCode='POST_LEVEL', value=${ryxx.postLevel}, see=true)"></div>
                                </td>
                                <td>
                                    <div th:include="/component/select :: init(id='postTitle_' + ${ryxxStat.index}, name='postTitle_' + ${ryxxStat.index}, businessType='KIZL', dictCode='POST_TITLE', value=${ryxx.postTitle}, see=true)"></div>
                                </td>
                                <td th:text="${ryxx.rylx == '01' ? '集团内' : '集团外'}"></td>
                                <td th:text="${not #strings.isEmpty(ryxx.idCard) ? '已填写' : ''}"></td>
                                <td th:text="${ryxx.contentMemo}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div th:if="${applyBaseinfo.ryxxList == null or #lists.isEmpty(applyBaseinfo.ryxxList)}">
                    <p class="text-muted">暂无发明人信息</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 相关附件 -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#readonly-accordion" href="#readonly-xgfj" aria-expanded="true">
                    相关附件
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                </a>
            </h4>
        </div>
        <div id="readonly-xgfj" class="panel-collapse collapse in" aria-expanded="true">
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required" id="readonly-jsjds-label">技术交底书：</label>
                    <div class="col-sm-4">
                        <th:block
                                th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='jsjds',id='jsjds',name='attachmentS[jsjds]',see=true)"></th:block>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" id="readonly-gtsqxy-label">共同申请协议：</label>
                    <div class="col-sm-4">
                        <th:block
                                th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='gtsqxy',id='gtsqxy',name='attachmentS[gtsqxy]',see=true)"></th:block>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="/component/expertReview :: init(bizId=${applyBaseinfo.applyId},isCkFS=true,moduleCode='kizl_zlps')"></th:block>
</div>

<style>
    .applicant-readonly-block {
        margin-bottom: 20px;
    }

    .form-control-static {
        padding-top: 7px;
        padding-bottom: 7px;
        margin-bottom: 0;
        min-height: 34px;
        word-wrap: break-word;
    }

    .table-responsive {
        border: none;
    }

    /* 表格内的select组件样式调整 */
    .table td .form-control-static {
        padding: 3px 0;
        min-height: auto;
        margin-bottom: 0;
    }
</style>

<script th:inline="javascript">
    $(function () {
        // 使用工具类初始化附件名称映射
        KizlAttachmentUtils.init(function () {
            KizlAttachmentUtils.updateCommonLabels();
        });
    });
</script>
