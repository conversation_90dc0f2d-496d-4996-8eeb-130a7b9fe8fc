<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_主管部门接收')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        <!-- 嵌入申请信息（只读） -->
        <th:block th:include="kizl/applyBaseinfo/applyInfoReadonly :: applyInfoReadonly"></th:block>
        <!-- 审查信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fa fa-search"></i> 审查信息
                </h4>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-sm-2 control-label">内部参考意见：</label>
                    <div class="col-sm-8">
                        <textarea name="zgbmNc" th:field="*{zgbmNc}" class="form-control" 
                                rows="4" maxlength="1000" placeholder="请输入内部参考意见"></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-sm-2 control-label">管理员审查内容：</label>
                    <div class="col-sm-8">
                        <div class="well well-sm">
                            <p><strong>审查要点：</strong></p>
                            <ul>
                                <li>发明人基本信息</li>
                                <li>是否有完整的技术方案</li>
                                <li>该技术方案是否符合专利三性</li>
                                <li>提出是否应该用技术秘密方式保护的建议</li>
                                <li>协助发明人挖掘技术方案的创造点</li>
                                <li>完善技术方案</li>
                                <li>策划集成技术等</li>
                                <li>相关数据附件确认</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">您的意见：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='zgbmCz', name='zgbmCz', businessType='KIZL', dictCode='zgbmCz', value=${applyBaseinfo.zgbmCz}, isrequired=true)"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 工作流信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow})" />
    </form>
    <th:block th:include="kizl/wfKizlButton :: init(workFlow=${applyBaseinfo.workFlow})" />
</div>
<th:block th:include="include :: datetimepicker-js"/>
<!-- 引入KIZL通用JS -->
<script th:src="@{/kizl/kizl-common.js}"></script>

<script th:inline="javascript">
    $(function () {
        // 使用通用方法初始化表单验证
        KizlCommon.initFormValidation();
        var fromNo = /*[[${applyBaseinfo.fromNo}]]*/ null;
        KizlCommon.getSource(fromNo);
    });

    function startPS(){
        var applyId = $("#applyId").val();
        const moduleCode = "kizl_zlps";
        $.modal.confirm("确定启动评审吗？",function (){
            $.post(ctx + "kizl/c/v?serviceName=KIZLApplyBaseinfo&methodName=getKizlPsxx",{
                "bizGuid":applyId,
                "moduleCode":moduleCode
            }, function (result) {
                if (result.code === 0) {
                    const jbxx = result.data.attr.jbxx;
                    $.modal.openTab("启动专家评审", ctxMP + "web/MPPS01?bizGuid=" + applyId + "&moduleCode=" + moduleCode + "&approveKind=MPPS_members_review&jbxx=" + jbxx);
                } else {
                    $.modal.msgError(result.msg || "启动专家评审失败,请联系系统管理员");
                }
            })
        })
    }

</script>
</body>
</html>
