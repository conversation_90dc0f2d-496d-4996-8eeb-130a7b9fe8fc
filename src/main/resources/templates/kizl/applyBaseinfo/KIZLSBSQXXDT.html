<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_授权信息_查看')"/>
    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="form-horizontal m" th:object="${applyBaseinfo}">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#jsxx" aria-expanded="false"
                       class="collapsed">授权信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="jsxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">鄂钢编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.egbh}"></p>
                        </div>
                        <label class="col-sm-2 control-label">接收编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.jsbh}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利申报名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利类型：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='patentType', name='patentType', businessType='KIZL', dictCode='PATENT_TYPE', value=${applyBaseinfo.patentInfo?.patentType},see=true)"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">申请日：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.slrq}"></p>
                        </div>
                        <label class="col-sm-2 control-label">申请号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentNo}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">授权日：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.sqrq}"></p>
                        </div>
                        <label class="col-sm-2 control-label">IPC主分类号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.extra1}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" id="readonly-zlzsfj-label">专利证书：</label>
                        <div class="col-sm-8">
                            <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='zlzsfj',id='zlzsfj',name='attachmentS[zlzsfj]',see=true)"></th:block>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" id="readonly-sqxxxgfj-label">相关附件：</label>
                        <div class="col-sm-8">
                            <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='sqxxxgfj',id='sqxxxgfj',name='attachmentS[sqxxxgfj]',see=true)"></th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 返回按钮 -->
<div class="toolbar toolbar-bottom" role="toolbar">
    <button type="button" class="btn btn-danger" onclick="closeItem()">
        <i class="fa fa-reply-all"></i>返回
    </button>
</div>
<style>
.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 34px;
}

.panel-title a {
    text-decoration: none;
}

.panel-title a:hover {
    text-decoration: none;
}
</style>

<script>
$(function() {
    // 初始化附件名称映射
    KizlAttachmentUtils.init(function() {
        KizlAttachmentUtils.updateCommonLabels();
    });
});
</script>
</body>
</html> 