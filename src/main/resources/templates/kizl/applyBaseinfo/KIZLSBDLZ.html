<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_代理中')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        
        <!-- 申请表信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#sqbxx" aria-expanded="true"
                       class="">申请表信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="sqbxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-4">
                            <a href="javascript:void(0);" onclick="viewBasicInfo()" class="btn btn-info btn-sm">
                                <i class="fa fa-eye"></i> 基本信息查看
                            </a>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">接收编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.jsbh ?: applyBaseinfo.jsbh}"></p>
                        </div>
                        <label class="col-sm-2 control-label">鄂钢编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.egbh}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">权属：</label>
                        <div class="col-sm-4">
                            <input id="qs" name="patentInfo.qs" th:value="${applyBaseinfo.patentInfo?.qs}" class="form-control" type="number" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">发明名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${applyBaseinfo.applyName}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">专利名称：</label>
                        <div class="col-sm-8">
                            <input id="patentName" name="patentInfo.patentName" th:value="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}" class="form-control" type="text" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">申请日：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/date :: init(name='patentInfo.slrq', id='slrq', strValue=${applyBaseinfo.patentInfo?.slrq}, isrequired=true)"></div>
                        </div>
                        <label class="col-sm-2 control-label is-required">申请号：</label>
                        <div class="col-sm-4">
                            <input id="patentNo" name="patentInfo.patentNo" th:value="${applyBaseinfo.patentInfo?.patentNo}" class="form-control" type="text" placeholder="请输入申请号" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利类型：</label>
                        <div class="col-sm-4">
                            <input name="patentInfo.patentType" id="patentType" th:value="${applyBaseinfo.patentInfo?.patentType}" type="hidden" required>
                            <input name="patentTypeName" id="patentTypeName" class="form-control" readonly="true"/>
                        </div>
                        <label class="col-sm-2 control-label">优先权日：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/date :: init(name='patentInfo.yxqr', id='yxqr', strValue=${applyBaseinfo.patentInfo?.yxqr})"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">权利要求数量：</label>
                        <div class="col-sm-4">
                            <input id="qlyqsl" name="patentInfo.qlyqsl" th:value="${applyBaseinfo.patentInfo?.qlyqsl}" class="form-control" type="number" min="1" placeholder="请输入权利要求数量">
                        </div>
                        <label class="col-sm-2 control-label">说明书页数：</label>
                        <div class="col-sm-4">
                            <input id="smsys" name="patentInfo.smsys" th:value="${applyBaseinfo.patentInfo?.smsys}" class="form-control" type="number" min="1" placeholder="请输入说明书页数">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请人信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#sqrxx" aria-expanded="true"
                       class="">申请人信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="sqrxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="btn-group-sm mb-15" id="applicant-toolbar" role="group">
                        <button type="button" class="btn btn-success" onclick="addApplicant()"><i
                                class="fa fa-plus"></i> 添加申请人
                        </button>
                    </div>
                    <br/>
                    <!-- 申请人容器 -->
                    <div id="applicant-container"></div>
                    <input type="hidden" id="applicantData" name="applicantData">
                </div>
            </div>
        </div>

        <!-- 发明设计人信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#fmsjrxx" aria-expanded="true"
                       class="">发明设计人信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="fmsjrxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="btn-group-sm mb-15" id="inventor-toolbar" role="group">
                        <button type="button" class="btn btn-success" onclick="openInventorModal()"><i
                                class="fa fa-plus"></i> 添加发明人
                        </button>
                    </div>
                    <br/>
                    <div class="col-sm-12">
                        <table class="table table-striped table-bordered table-hover" id="inventor-table">
                            <thead>
                            <tr>
                                <th>序号</th>
                                <th>姓名</th>
                                <th>工号</th>
                                <th>单位</th>
                                <th>岗位</th>
                                <th>职称</th>
                                <th>类型</th>
                                <th>身份证号</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <!-- 表格数据由JavaScript动态生成 -->
                            </tbody>
                        </table>
                        <input type="hidden" id="inventorData" name="inventorData">
                    </div>
                    <small class="text-muted">自动带出，可修改</small>
                </div>
            </div>
        </div>

        <!-- 事务所信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#swsxx" aria-expanded="true"
                       class="">事务所信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="swsxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">代理事务所：</label>
                        <div class="col-sm-8">
                            <select id="swsGuid" name="patentInfo.swsGuid" class="form-control" required>
                                <option value="">请选择代理事务所</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">代理人：</label>
                        <div class="col-sm-4">
                            <input id="swsdlr" name="patentInfo.swsdlr" th:value="${applyBaseinfo.patentInfo?.swsdlr}" class="form-control" type="text" placeholder="请输入代理人姓名" required>
                        </div>
                        <label class="col-sm-2 control-label">联系电话：</label>
                        <div class="col-sm-4">
                            <input id="swsdlrPhone" name="patentInfo.swsdlrPhone" th:value="${applyBaseinfo.patentInfo?.swsdlrPhone}" class="form-control" type="text" placeholder="请输入联系电话">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">代理人邮箱：</label>
                        <div class="col-sm-4">
                            <input id="swsdlrEmail" name="patentInfo.swsdlrEmail" th:value="${applyBaseinfo.patentInfo?.swsdlrEmail}" class="form-control" type="email" placeholder="请输入代理人邮箱">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 申请相关文件 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#231231" aria-expanded="true"
                       class="">申请相关文件
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="231231" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">申请相关文件：</label>
                        <div class="col-sm-8">
                            <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='sqxgwj',id='sqxgwj',name='attachmentS[sqxgwj]')"></th:block>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">共同申请协议：</label>
                        <div class="col-sm-8">
                            <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='gtsqxy',id='gtsqxy',name='attachmentS[gtsqxy]')"></th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 确认信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#qrxx" aria-expanded="true"
                       class="">确认信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="qrxx" class="panel-collapse collapse in" aria-expanded="true">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">内部参考意见：</label>
                        <div class="col-sm-8">
                            <div th:include="/component/textarea :: init(id='nbckyj', name='patentInfo.nbckyj', value=${applyBaseinfo.patentInfo?.nbckyj}, rows='4', maxlength='1000', placeholder='请输入内部参考意见')"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 隐藏字段 -->
        <input type="hidden" name="patentInfo.patentId" th:value="${applyBaseinfo.patentInfo?.patentId}">
        <input type="hidden" name="patentInfo.applyId" th:value="${applyBaseinfo.applyId}">
        <input type="hidden" name="patentInfo.patentStatus" th:value="${applyBaseinfo.patentInfo?.patentStatus}">
        
        <!-- 工作流信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow},needComment=true)" />
    </form>
    <th:block th:include="kizl/wfKizlButton :: init(workFlow=${applyBaseinfo.workFlow})" />
</div>

<th:block th:include="include :: datetimepicker-js"/>
<!-- 引入KIZL通用JS -->
<script th:src="@{/kizl/kizl-common.js}"></script>
<!-- 引入专利工具类 -->
<script th:src="@{/kizl/patent-utils.js}"></script>

<script th:inline="javascript">
    var inventors = []; // 发明人数组
    var applicants = []; // 申请人数组

    $(function() {
        // 初始化发明人数据
        initInventorData();
        
        // 初始化申请人数据
        initApplicantData();
        
        // 自动计算权属
        calculateOwnership();
        
        // 初始化事务所下拉选择
        initSwsSelect();
        
        // 初始化专利类型显示
        initPatentTypeDisplay();
        
        // 初始化专利申请号输入框
        PatentUtils.initPatentNoInput('patentNo', 'patentType', function(validation) {
            if (validation.valid) {
                // 校验权利要求数量和说明书页数
                validateClaimAndPages();
                
                // 自动设置专利类型的数字值
                var patentTypeValue = PatentUtils.getPatentTypeValue(validation.patentType);
                var patentTypeName = PatentUtils.getPatentTypeName(validation.patentType);
                if (patentTypeValue) {
                    $('#patentType').val(validation.patentType); // 保存编码：FM、SYXX等
                    $('#patentTypeName').val(patentTypeName); // 显示中文名称
                }
            }
        });
        
        // 绑定权利要求数量校验
        $('#qlyqsl').on('blur', function() {
            validateClaimAndPages();
        });
        
        // 绑定说明书页数校验
        $('#smsys').on('blur', function() {
            validateClaimAndPages();
        });

        // 绑定事务所选择变化事件
        $('#swsGuid').on('change', function() {
            var swsGuid = $(this).val();
            if (swsGuid) {
                // 根据swsGuid获取事务所详细信息
                loadSwsDetailInfo(swsGuid);
            } else {
                // 清空代理人相关信息
                $('#swsdlr').val('');
                $('#swsdlrPhone').val('');
                $('#swsdlrEmail').val('');
            }
        });

        // 初始化发明人表单验证
        $("#inventor-form").validate({
            rules: {
                inventor_order: {
                    required: true,
                    number: true,
                    min: 1
                },
                inventor_name: {
                    required: true
                },
                inventor_dept: {
                    required: true
                },
                inventor_idcard: {
                    maxlength: 18,
                    minlength: 18
                }
            },
            messages: {
                inventor_order: {
                    required: "请输入序号",
                    number: "请输入有效的数字",
                    min: "序号必须大于0"
                },
                inventor_name: {
                    required: "请输入姓名"
                },
                inventor_dept: {
                    required: "请输入/选择单位"
                },
                inventor_idcard: {
                    maxlength: "身份证号必须是18位",
                    minlength: "身份证号必须是18位"
                }
            }
        });
        
        // 初始化表单验证
        $("#form-applyBaseinfo-edit").validate({
            rules: {
                'patentInfo.patentName': {
                    required: true
                },
                'patentInfo.slrq': {
                    required: true
                },
                'patentInfo.patentNo': {
                    required: true
                },
                'patentType': {
                    required: true
                },
                'patentInfo.swsGuid': {
                    required: true
                },
                'patentInfo.swsdlr': {
                    required: true
                }
            },
            messages: {
                'patentInfo.patentName': {
                    required: "请输入专利名称"
                },
                'patentInfo.slrq': {
                    required: "请选择申请日"
                },
                'patentInfo.patentNo': {
                    required: "请输入申请号"
                },
                'patentType': {
                    required: "请输入申请号以自动识别专利类型"
                },
                'patentInfo.swsGuid': {
                    required: "请选择代理事务所"
                },
                'patentInfo.swsdlr': {
                    required: "请输入代理人"
                }
            }
        });
    });

    // 保存表单
    function saveDraft() {
        var formData = $('#form-applyBaseinfo-edit').serialize();

        // 收集表单数据
        var data = $("#form-applyBaseinfo-edit").serializeArray();
        var formData = {};

        $.each(data, function (index, field) {
            formData[field.name] = field.value;
        });

        // 添加申请人数据 - 逐个添加申请人属性
        for (var i = 0; i < applicants.length; i++) {
            var applicant = applicants[i];
            formData["sqrList[" + i + "].xh"] = applicant.index;
            formData["sqrList[" + i + "].legalId"] = applicant.orgCode;
            formData["sqrList[" + i + "].legalName"] = applicant.orgName;
            formData["sqrList[" + i + "].mailAddress"] = applicant.address;
            formData["sqrList[" + i + "].postOffice"] = applicant.zipcode;
            formData["sqrList[" + i + "].lxrMoney"] = applicant.contact;
            formData["sqrList[" + i + "].lxrPhone"] = applicant.phone;
        }

        // 添加发明人数据 - 逐个添加发明人属性
        for (var j = 0; j < inventors.length; j++) {
            var inventor = inventors[j];
            formData["ryxxList[" + j + "].ryxh"] = inventor.order;
            formData["ryxxList[" + j + "].rylx"] = inventor.type;
            formData["ryxxList[" + j + "].empId"] = inventor.code;
            formData["ryxxList[" + j + "].empName"] = inventor.name;
            formData["ryxxList[" + j + "].deptCode"] = inventor.deptCode;
            formData["ryxxList[" + j + "].deptName"] = inventor.dept;
            formData["ryxxList[" + j + "].postTitle"] = inventor.title;
            formData["ryxxList[" + j + "].postLevel"] = inventor.position;
            formData["ryxxList[" + j + "].idCard"] = inventor.idcard;
            formData["ryxxList[" + j + "].contentMemo"] = inventor.remark;
        }

        $.ajax({
            url: ctx + 'kizl/applyBaseinfo/savePatentInfo',
            type: 'POST',
            data: formData,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function(result) {
                if (result.code === 0) {
                    $.modal.alertSuccess("保存成功");
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            },
            error: function() {
                $.modal.alertError("保存失败");
            }
        });
    }

    // 初始化发明人数据
    function initInventorData() {
        var applyId = $("#applyId").val();
        if (applyId) {
            // 编辑模式，从后台传递的数据中读取发明人信息
            var ryxxList = /*[[${applyBaseinfo.ryxxList}]]*/ [];
            if (ryxxList && ryxxList.length > 0) {
                inventors = ryxxList.map(function (item, index) {
                    return {
                        order: item.ryxh || (index + 1),
                        type: item.rylx === "01" ? "1" : "2",
                        code: item.empId || "",
                        name: item.empName || "",
                        deptCode: item.deptCode || "",
                        dept: item.deptName || "",
                        title: item.postTitle || "",
                        position: item.postLevel || "",
                        idcard: item.idCard || "",
                        remark: item.contentMemo || ""
                    };
                });
                updateInventorTable();
            }
        }
    }

    // 初始化申请人数据
    function initApplicantData() {
        var applyId = $("#applyId").val();
        if (applyId) {
            // 编辑模式，从后台传递的数据中读取申请人信息
            var sqrList = /*[[${applyBaseinfo.sqrList}]]*/ [];
            if (sqrList && sqrList.length > 0) {
                applicants = sqrList.map(function (item, index) {
                    return {
                        index: item.xh || (index + 1),
                        type: "1", // 默认为内部法人单位
                        orgCode: item.legalId || "",
                        orgName: item.legalName || "",
                        address: item.mailAddress || "",
                        zipcode: item.postOffice || "",
                        contact: item.lxrMoney || "",
                        phone: item.lxrPhone || ""
                    };
                });
                updateApplicantTable();
            }
        }

        // 如果没有申请人数据，添加默认申请人
        if (applicants.length === 0) {
            addApplicant();
        }
    }

    // 添加申请人
    function addApplicant() {
        var applicant = {
            index: applicants.length + 1,
            type: "1", // 默认内部法人单位
            orgCode: "",
            orgName: "",
            address: "",
            zipcode: "",
            contact: "",
            phone: ""
        };
        applicants.push(applicant);
        updateApplicantTable();
        calculateOwnership();
    }

    // 移除申请人
    function removeApplicant(index) {
        if (applicants.length <= 1) {
            $.modal.alertWarning("至少需要一个申请人！");
            return;
        }

        $.modal.confirm("确定删除该申请人吗？", function () {
            applicants.splice(index, 1);
            // 重新编号
            for (var i = 0; i < applicants.length; i++) {
                applicants[i].index = i + 1;
            }
            updateApplicantTable();
            calculateOwnership();
        });
    }

    // 更新申请人表格
    function updateApplicantTable() {
        var container = $("#applicant-container");
        container.empty();

        $.each(applicants, function (index, applicant) {
            var html = generateApplicantForm(index, applicant);
            container.append(html);
        });

        // 更新隐藏字段
        $("#applicantData").val(JSON.stringify(applicants));
    }

    // 生成申请人表单
    function generateApplicantForm(index, applicant) {
        var readonly = applicant.type === "1" ? "readonly" : "";
        var switchButtonText = applicant.type === "1" ? "填写外部法人单位" : "选择内部法人单位";
        var selectButtonStyle = applicant.type === "1" ? "" : "style='display:none'";

        return `
        <div id="applicant${index}" class="applicant-block">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">第${applicant.index}申请人：</label>
                <div class="col-sm-6">
                    <div class="input-group">
                        <input type="text" class="form-control applicant-org-name" ${readonly} required
                               value="${applicant.orgName}" onchange="updateApplicantField(${index}, 'orgName', this.value)">
                        <span class="input-group-btn">
                            <button class="btn btn-primary select-internal-btn" type="button" ${selectButtonStyle}
                                    onclick="selectLegal(${index})">
                                选择内部法人单位
                            </button>
                            <button class="btn btn-info switch-type-btn" type="button"
                                    onclick="switchApplicantType(${index})">
                                ${switchButtonText}
                            </button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">通信地址：</label>
                <div class="col-sm-8">
                    <input class="form-control applicant-address" type="text" required
                           value="${applicant.address}" onchange="updateApplicantField(${index}, 'address', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">邮政编码：</label>
                <div class="col-sm-2">
                    <input class="form-control applicant-zipcode" type="text" required maxlength="6"
                           value="${applicant.zipcode}" oninput="this.value=this.value.replace(/[^\d]/g,'')"
                           onchange="updateApplicantField(${index}, 'zipcode', this.value)">
                </div>
                <label class="col-sm-2 control-label is-required">付费联系人：</label>
                <div class="col-sm-2">
                    <input class="form-control applicant-contact" type="text" required
                           value="${applicant.contact}" onchange="updateApplicantField(${index}, 'contact', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">联系电话：</label>
                <div class="col-sm-2">
                    <input class="form-control applicant-phone" type="text" required
                           value="${applicant.phone}" onchange="updateApplicantField(${index}, 'phone', this.value)">
                </div>
                <div class="col-sm-2">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeApplicant(${index})">
                        <i class="fa fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
        `;
    }

    // 更新申请人字段
    function updateApplicantField(index, field, value) {
        if (applicants[index]) {
            applicants[index][field] = value;
        }
    }

    // 切换申请人类型
    function switchApplicantType(index) {
        if (!applicants[index]) return;

        var applicant = applicants[index];
        if (applicant.type === "1") {
            // 切换到外部法人单位
            applicant.type = "2";
            applicant.orgCode = "";
            applicant.orgName = "";
        } else {
            // 切换到内部法人单位
            applicant.type = "1";
            applicant.orgCode = "";
            applicant.orgName = "";
        }

        updateApplicantTable();
    }

    // 法人单位选择函数（内部单位）
    function selectLegal(index) {
        if (!applicants[index]) return;

        // 保存当前索引，供回调使用
        window.currentApplicantIndex = index;

        $.modal.open("选择申请人", ctx + "kizl/applyBaseinfo/selectList?callback=selectLegalCallback", '480', '580');
    }

    // 法人单位选择回调函数
    function selectLegalCallback(id, name) {
        var index = window.currentApplicantIndex;
        if (index === undefined || !applicants[index] || !id || !name) return;

        var applicant = applicants[index];
        applicant.orgCode = id;
        applicant.orgName = name;
        applicant.type = "1"; // 设置为内部法人单位

        // 获取附加信息
        $.ajax({
            url: ctx + "kizl/applyBaseinfo/lxrData/" + id,
            type: "GET",
            dataType: "json",
            success: function (result) {
                if (result.code == 0 && result.data) {
                    var data = result.data;
                    applicant.address = data.mailAddress || "";
                    applicant.zipcode = data.postOffice || "";
                    applicant.contact = data.lxrMoney || "";
                    applicant.phone = data.lxrPhone || "";
                }
                updateApplicantTable();
                calculateOwnership();
            },
            error: function () {
                updateApplicantTable();
                calculateOwnership();
            }
        });

        // 清除临时变量
        window.currentApplicantIndex = undefined;
    }

    // 计算权属（申请人数量）
    function calculateOwnership() {
        // 设置权属值为申请人数量
        $("#qs").val(applicants.length);
    }

    // 校验权利要求数量和说明书页数
    function validateClaimAndPages() {
        var patentType = $('#patentType').val(); // 获取专利类型编码
        var claimCount = parseInt($('#qlyqsl').val());
        var pageCount = parseInt($('#smsys').val());
        
        if (patentType && claimCount) {
            var claimValidation = PatentUtils.validateClaimCount(claimCount, patentType);
            if (!claimValidation.valid) {
                $('#qlyqsl').addClass('error');
                PatentUtils.showMessage(claimValidation.message, 'warning');
            } else {
                $('#qlyqsl').removeClass('error');
            }
        }
        
        if (patentType && pageCount) {
            var pageValidation = PatentUtils.validateSpecificationPages(pageCount, patentType);
            if (!pageValidation.valid) {
                $('#smsys').addClass('error');
                PatentUtils.showMessage(pageValidation.message, 'warning');
            } else {
                $('#smsys').removeClass('error');
            }
        }
    }

    // 打开发明人模态窗口
    function openInventorModal(index) {
        // 清空表单
        $("#inventor-form")[0].reset();
        $("#inventor_index").val("");
        $("#inventor_dept_code").val("");

        if (index !== undefined) {
            // 编辑现有发明人
            var inventor = inventors[index];
            $("#inventor_index").val(index);
            $("#inventor_order").val(inventor.order || (index + 1));
            $("input[name='inventor_type'][value='" + (inventor.type || "1") + "']").prop("checked", true);
            $("#inventor_code").val(inventor.code || "");
            $("#inventor_name").val(inventor.name || "");
            $("#inventor_dept_code").val(inventor.deptCode || "");
            $("#inventor_dept").val(inventor.dept || "");
            $("#inventor_title").val(inventor.title || "");
            $("#inventor_position").val(inventor.position || "");
            $("#inventor_idcard").val(inventor.idcard || "");
            $("#inventor_remark").val(inventor.remark || "");

            // 设置类型对应的UI状态
            toggleInventorType(inventor.type || "1");
        } else {
            // 设置默认值
            $("#inventor_order").val(inventors.length + 1);
            $("#inventor_title").val("");
            $("#inventor_position").val("");
            toggleInventorType("1"); // 默认集团内
        }

        $("#inventor-modal").modal('show');

        // 模态窗口显示后，初始化select组件
        setTimeout(function () {
            $("#inventor_title").trigger('change');
            $("#inventor_position").trigger('change');
        }, 200);
    }

    // 保存发明人
    function saveInventor() {
        var form = $("#inventor-form");
        if (!form.valid()) {
            return;
        }

        // 获取表单值
        var index = $("#inventor_index").val();
        var order = $("#inventor_order").val();
        var type = $("input[name='inventor_type']:checked").val();
        var code = $("#inventor_code").val();
        var name = $("#inventor_name").val();
        var deptCode = $("#inventor_dept_code").val();
        var dept = $("#inventor_dept").val();
        var title = $("#inventor_title").val();
        var position = $("#inventor_position").val();
        var idcard = $("#inventor_idcard").val();
        var remark = $("#inventor_remark").val();

        // 验证
        if (order == "1" && !idcard) {
            $.modal.alertWarning("第一发明人必须填写身份证号!");
            return;
        }

        if (type == "1" && !code) {
            $.modal.alertWarning("集团内发明人必须选择工号!");
            return;
        }

        if (!name) {
            $.modal.alertWarning("发明人姓名不能为空!");
            return;
        }

        if (!dept) {
            $.modal.alertWarning("发明人组织不能为空!");
            return;
        }

        // 构建发明人对象
        var inventor = {
            order: order,
            type: type,
            code: code,
            name: name,
            deptCode: deptCode,
            dept: dept,
            title: title,
            position: position,
            idcard: idcard,
            remark: remark
        };

        if (index === "") {
            // 新增
            inventors.push(inventor);
        } else {
            // 更新
            inventors[index] = inventor;
        }

        // 更新表格
        updateInventorTable();

        // 关闭模态窗口
        $("#inventor-modal").modal('hide');

        $.modal.msgSuccess("发明人信息保存成功!");
    }

    // 切换发明人类型
    function toggleInventorType(type) {
        if (type == "1") { // 集团内
            $("#inventor_code_group").show();
            $("#inventor_code").attr("required", "required");
            $("#inventor_code").val("");
            $("#inventor_name").attr("readonly", "readonly");
            $("#inventor_dept").attr("readonly", "readonly");
        } else { // 集团外
            $("#inventor_code_group").hide();
            $("#inventor_code").removeAttr("required");
            $("#inventor_code").val("");
            $("#inventor_name").removeAttr("readonly");
            $("#inventor_dept").removeAttr("readonly");

            // 清空工号和部门相关字段
            if ($("#inventor_name").val() === "") {
                $("#inventor_name").val("");
            }
            if ($("#inventor_dept").val() === "") {
                $("#inventor_dept_code").val("");
                $("#inventor_dept").val("");
            }
        }
    }

    // 选择用户
    function selectUser(codeId, nameId) {
        // 保存全局变量，以便回调使用
        window.tempUserCodeId = codeId;
        window.tempUserNameId = nameId;

        // 简单的方式打开选择器
        var url = ctx + "mpad/user/selectUserList?selectType=S&callback=choiceUserCallback";
        $.modal.open("选择用户", url, '1000', '620');
    }

    // 被选择器页面调用的回调函数
    function choiceUserCallback(userCode, userName) {
        if (userCode && userName) {
            // 获取临时存储的ID
            var codeId = window.tempUserCodeId || "inventor_code";
            var nameId = window.tempUserNameId || "inventor_name";

            // 设置值
            $("#" + codeId).val(userCode);
            $("#" + nameId).val(userName);
        }

        // 清除临时变量
        window.tempUserCodeId = null;
        window.tempUserNameId = null;
    }

    // 更新发明人表格
    function updateInventorTable() {
        var tbody = $("#inventor-table tbody");
        tbody.empty();

        // 按序号排序
        inventors.sort(function (a, b) {
            return parseInt(a.order) - parseInt(b.order);
        });

        $.each(inventors, function (index, inventor) {
            var row = $("<tr></tr>");
            row.append("<td>" + (inventor.order || (index + 1)) + "</td>");
            row.append("<td>" + (inventor.name || "") + "</td>");
            row.append("<td>" + (inventor.code || "") + "</td>");
            row.append("<td>" + (inventor.dept || "") + "</td>");
            row.append("<td>" + (inventor.position || "") + "</td>");
            row.append("<td>" + (inventor.title || "") + "</td>");
            row.append("<td>" + (inventor.type == "1" ? "集团内" : "集团外") + "</td>");
            row.append("<td>" + (inventor.idcard ? "已填写" : "") + "</td>");
            row.append("<td>" + (inventor.remark || "") + "</td>");

            var actionBtn = '<div class="btn-group btn-group-xs">' +
                '<a class="btn btn-primary" onclick="openInventorModal(' + index + ')"><i class="fa fa-edit"></i></a>' +
                '<a class="btn btn-danger" onclick="removeInventor(' + index + ')"><i class="fa fa-trash"></i></a>' +
                '</div>';
            row.append("<td>" + actionBtn + "</td>");

            tbody.append(row);
        });

        // 更新隐藏字段
        $("#inventorData").val(JSON.stringify(inventors));
    }

    // 删除发明人
    function removeInventor(index) {
        $.modal.confirm("确定删除该发明人吗？", function () {
            inventors.splice(index, 1);
            updateInventorTable();
        });
    }

    // 表单提交前的验证
    function validateForm() {
        // 基础表单验证
        if (!$("#form-applyBaseinfo-edit").valid()) {
            return false;
        }
        
        // 验证申请号
        var patentNo = $("#patentNo").val();
        if (!patentNo) {
            $.modal.alertWarning("请输入申请号！");
            return false;
        }
        
        // 使用专利工具类验证申请号
        var validation = PatentUtils.validatePatentNo(patentNo);
        if (!validation.valid) {
            $.modal.alertWarning("申请号格式不正确：" + validation.message);
            return false;
        }
        
        // 验证专利名称
        var patentName = $("#patentName").val();
        if (!patentName) {
            $.modal.alertWarning("请输入专利名称！");
            return false;
        }
        
        // 验证专利类型
        var patentType = $("#patentType").val();
        if (!patentType) {
            $.modal.alertWarning("请输入申请号以自动识别专利类型！");
            return false;
        }
        
        // 验证申请日
        var slrq = $("#slrq").val();
        if (!slrq) {
            $.modal.alertWarning("请选择申请日！");
            return false;
        }
        
        // 验证代理事务所
        var swsGuid = $("#swsGuid").val();
        if (!swsGuid) {
            $.modal.alertWarning("请选择代理事务所！");
            return false;
        }
        
        // 验证代理人
        var swsdlr = $("#swsdlr").val();
        if (!swsdlr) {
            $.modal.alertWarning("请输入代理人！");
            return false;
        }
        
        // 验证申请人
        if (applicants.length === 0) {
            $.modal.alertWarning("至少需要一个申请人！");
            return false;
        }

        // 验证申请人必填项
        for (var i = 0; i < applicants.length; i++) {
            var applicant = applicants[i];
            if (!applicant.orgName) {
                $.modal.alertWarning("第" + (i + 1) + "申请人名称不能为空!");
                return false;
            }
            if (!applicant.address) {
                $.modal.alertWarning("第" + (i + 1) + "申请人通信地址不能为空!");
                return false;
            }
            if (!applicant.zipcode) {
                $.modal.alertWarning("第" + (i + 1) + "申请人邮政编码不能为空!");
                return false;
            }
            if (!applicant.contact) {
                $.modal.alertWarning("第" + (i + 1) + "申请人付费联系人不能为空!");
                return false;
            }
            if (!applicant.phone) {
                $.modal.alertWarning("第" + (i + 1) + "申请人联系电话不能为空!");
                return false;
            }
        }
        
        // 验证发明人
        if (inventors.length === 0) {
            $.modal.alertWarning("至少需要一个发明设计人！");
            return false;
        }
        
        return true;
    }

    // 提交表单
    function submitForm() {
        if (!validateForm()) {
            return;
        }
        
        $.modal.confirm("确定要提交吗？", function() {
            // 收集表单数据
            var formData = $("#form-applyBaseinfo-edit").serializeArray();
            var data = {};
            
            // 转换表单数据为对象
            $.each(formData, function(index, field) {
                data[field.name] = field.value;
            });
            
            // 添加申请人数据
            for (var i = 0; i < applicants.length; i++) {
                var applicant = applicants[i];
                data["sqrList[" + i + "].xh"] = applicant.index;
                data["sqrList[" + i + "].legalId"] = applicant.orgCode;
                data["sqrList[" + i + "].legalName"] = applicant.orgName;
                data["sqrList[" + i + "].mailAddress"] = applicant.address;
                data["sqrList[" + i + "].postOffice"] = applicant.zipcode;
                data["sqrList[" + i + "].lxrMoney"] = applicant.contact;
                data["sqrList[" + i + "].lxrPhone"] = applicant.phone;
            }
            
            // 添加发明人数据
            for (var j = 0; j < inventors.length; j++) {
                var inventor = inventors[j];
                data["ryxxList[" + j + "].ryxh"] = inventor.order;
                data["ryxxList[" + j + "].rylx"] = inventor.type;
                data["ryxxList[" + j + "].empId"] = inventor.code;
                data["ryxxList[" + j + "].empName"] = inventor.name;
                data["ryxxList[" + j + "].deptCode"] = inventor.deptCode;
                data["ryxxList[" + j + "].deptName"] = inventor.dept;
                data["ryxxList[" + j + "].postTitle"] = inventor.title;
                data["ryxxList[" + j + "].postLevel"] = inventor.position;
                data["ryxxList[" + j + "].idCard"] = inventor.idcard;
                data["ryxxList[" + j + "].contentMemo"] = inventor.remark;
            }
            
            // 发送请求
            $.ajax({
                url: ctx + 'kizl/applyBaseinfo/doSubmit',
                type: 'POST',
                data: data,
                beforeSend: function() {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function(result) {
                    if (result.code === 0) {
                        $.operate.alertSuccessTabCallback(result);
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                    }
                },
                error: function() {
                    $.modal.alertError("操作失败");
                    $.modal.closeLoading();
                }
            });
        });
    }

    // 绑定表单提交事件
    $("#form-applyBaseinfo-edit").on("submit", function(e) {
        e.preventDefault();
        submitForm();
        return false;
    });

    // 初始化事务所下拉选择
    function initSwsSelect() {
        // 获取当前用户的管理组织代码（这里需要根据实际情况获取）
        var gldwCode = /*[[${applyBaseinfo.firstDeptCode}]]*/ '';
        
        // 加载事务所选项
        loadSwsOptions(gldwCode);
        
        // 设置当前值
        var currentSwsGuid = /*[[${applyBaseinfo.patentInfo?.swsGuid}]]*/ '';
        if (currentSwsGuid) {
            $('#swsGuid').val(currentSwsGuid);
        }
    }
    
    // 加载事务所选项
    function loadSwsOptions(gldwCode) {
        var url = ctx + 'kizl/maintainSwsinfo/getAllSwsinfo';
        if (gldwCode) {
            url = ctx + 'kizl/maintainSwsinfo/queryByGldwCode/' + gldwCode;
        }
        
        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function(result) {
                if (result.code === 0 && result.data) {
                    var options = '<option value="">请选择代理事务所</option>';
                    $.each(result.data, function(index, item) {
                        // 使用swsName字段显示事务所名称，而不是patentType
                        var displayName = item.swsName || item.patentType || '未知事务所';
                        options += '<option value="' + item.swsId + '">' + displayName + '</option>';
                    });
                    $('#swsGuid').html(options);
                    
                    // 重新设置当前值
                    var currentSwsGuid = /*[[${applyBaseinfo.patentInfo?.swsGuid}]]*/ '';
                    if (currentSwsGuid) {
                        $('#swsGuid').val(currentSwsGuid);
                        // 如果有当前值，加载对应的详细信息
                        loadSwsDetailInfo(currentSwsGuid);
                    }
                } else {
                    console.error('加载事务所信息失败：', result.msg);
                }
            },
            error: function() {
                console.error('加载事务所信息失败');
            }
        });
    }

    // 根据swsGuid获取事务所详细信息
    function loadSwsDetailInfo(swsGuid) {
        if (!swsGuid) {
            return;
        }
        
        $.ajax({
            url: ctx + 'kizl/maintainSwsinfo/getSwsDetailInfo/' + swsGuid,
            type: 'GET',
            dataType: 'json',
            success: function(result) {
                if (result.code === 0 && result.data) {
                    var swsDetail = result.data;
                    // 填充代理人相关信息
                    if (swsDetail.lxr) {
                        $('#swsdlr').val(swsDetail.lxr);
                    }
                    if (swsDetail.lxrPhone) {
                        $('#swsdlrPhone').val(swsDetail.lxrPhone);
                    }
                    if (swsDetail.email) {
                        $('#swsdlrEmail').val(swsDetail.email);
                    }
                } else {
                    console.log('获取事务所详细信息失败：', result.msg || '未找到对应的事务所信息');
                }
            },
            error: function() {
                console.error('获取事务所详细信息失败');
            }
        });
    }

    // 初始化专利类型显示
    function initPatentTypeDisplay() {
        // 获取隐藏字段中的专利类型编码
        var patentType = $('#patentType').val();
        
        // 调试信息
        console.log('初始化专利类型显示 - patentType:', patentType);
        
        if (patentType) {
            var patentTypeName = PatentUtils.getPatentTypeName(patentType);
            console.log('获取到的专利类型名称:', patentTypeName);
            
            if (patentTypeName) {
                $('#patentTypeName').val(patentTypeName);
                console.log('已设置专利类型名称显示字段');
            } else {
                console.warn('未找到专利类型编码对应的中文名称:', patentType);
            }
        } else {
            console.log('专利类型编码为空，检查后台数据');
            
            // 如果隐藏字段为空，尝试从后台数据中获取
            var backendPatentType = /*[[${applyBaseinfo.patentInfo?.patentType}]]*/ '';
            console.log('后台专利类型数据:', backendPatentType);
            
            if (backendPatentType) {
                $('#patentType').val(backendPatentType);
                var backendPatentTypeName = PatentUtils.getPatentTypeName(backendPatentType);
                if (backendPatentTypeName) {
                    $('#patentTypeName').val(backendPatentTypeName);
                    console.log('从后台数据设置专利类型:', backendPatentType, '->', backendPatentTypeName);
                }
            }
        }
        
        // 最终检查
        var finalPatentType = $('#patentType').val();
        var finalPatentTypeName = $('#patentTypeName').val();
        console.log('专利类型初始化完成 - 编码:', finalPatentType, '名称:', finalPatentTypeName);
    }
</script>

<style>
.inventor-item {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e5e6e7;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.applicant-group {
    margin-bottom: 15px;
}

.applicant-block {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e5e6e7;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 34px;
}

.text-muted {
    font-size: 12px;
}

.panel-title a {
    text-decoration: none;
}

.panel-title a:hover {
    text-decoration: none;
}

/* 输入框状态样式 */
.form-control.error {
    border-color: #d73925;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px rgba(215,57,37,.6);
}

.form-control.success {
    border-color: #5cb85c;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px rgba(92,184,92,.6);
}

/* 必填字段标识 */
.is-required:before {
    content: "*";
    color: #d73925;
    margin-right: 4px;
}

/* 申请人区块样式 */
.applicant-org-name {
    border-radius: 4px 0 0 4px;
}

.select-internal-btn {
    border-radius: 0;
    border-left: 0;
}

.switch-type-btn {
    border-radius: 0 4px 4px 0;
    border-left: 0;
}

.mb-15 {
    margin-bottom: 15px;
}
</style>

<!-- 发明人模态窗口 -->
<div class="modal fade" id="inventor-modal" tabindex="-1" role="dialog" aria-labelledby="inventorModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="inventorModalLabel">添加/编辑发明人</h4>
            </div>
            <div class="modal-body">
                <form id="inventor-form" class="form-horizontal">
                    <input type="hidden" id="inventor_index">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">序号：</label>
                        <div class="col-sm-2">
                            <input type="number" class="form-control" id="inventor_order" min="1" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">类型：</label>
                        <div class="col-sm-4">
                            <label class="radio-inline">
                                <input type="radio" name="inventor_type" value="1" checked
                                       onclick="toggleInventorType(1)"> 集团内
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="inventor_type" value="2" onclick="toggleInventorType(2)">
                                集团外
                            </label>
                        </div>
                    </div>
                    <div class="form-group" id="inventor_code_group">
                        <label class="col-sm-3 control-label is-required">工号：</label>
                        <div class="col-sm-4">
                            <div class="input-group">
                                <input type="text" class="form-control" id="inventor_code"
                                       onclick="selectUser('inventor_code', 'inventor_name')" required readonly>
                                <span class="input-group-btn">
                                    <button class="btn btn-default" type="button">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">姓名：</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" id="inventor_name" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">人员组织：</label>
                        <div class="col-sm-8">
                            <input type="hidden" id="inventor_dept_code" name="inventor_dept_code">
                            <input type="text" class="form-control" id="inventor_dept" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">职称：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='inventor_title', name='inventor_title', businessType='KIZL', dictCode='POST_TITLE' )"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">岗位：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='inventor_position', name='inventor_position', businessType='KIZL', dictCode='POST_LEVEL')"></div>
                        </div>
                    </div>
                    <div class="form-group" id="inventor_idcard_group">
                        <label class="col-sm-3 control-label">身份证号：</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="inventor_idcard" maxlength="18"
                                   placeholder="第一发明人必填，18位长度">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">备注：</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" id="inventor_remark">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">返回</button>
                <button type="button" class="btn btn-primary" onclick="saveInventor()">保存</button>
            </div>
        </div>
    </div>
</div>
</body>
</html>
