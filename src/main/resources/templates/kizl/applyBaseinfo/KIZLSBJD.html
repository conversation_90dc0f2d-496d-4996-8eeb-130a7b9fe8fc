<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_交底')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#jsxx" aria-expanded="false"
                       class="collapsed">交底
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="jsxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-4">
                            <a href="javascript:void(0);" onclick="viewBasicInfo()" class="btn btn-info btn-sm">
                                <i class="fa fa-eye"></i> 基本信息查看
                            </a>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">流水号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.serialNum ?: applyBaseinfo.serialNum}"></p>
                        </div>
                        <label class="col-sm-2 control-label">接收编号：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.jsbh ?: applyBaseinfo.jsbh}"></p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">专利申报名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.patentName ?: applyBaseinfo.applyName}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">第一申报部门：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.firstDeptName ?: applyBaseinfo.firstDeptName}"></p>
                        </div>
                    </div>
                    <!-- 待交底信息 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">待交底保存者：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/selectUser :: init (id='djdPerson', userNameId='djdPersonName', userCodeId='djdPerson', value=${applyBaseinfo.patentInfo.djdPerson},see=true)"></div>
                        </div>
                        <label class="col-sm-2 control-label">保存时间：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.djdDate}"></p>
                        </div>
                    </div>
                    <!-- 代理事务所信息 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">代理事务所：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.swsGuid}"></p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">代理人：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.swsdlr}"></p>
                        </div>
                        <label class="col-sm-2 control-label">联系电话：</label>
                        <div class="col-sm-4">
                            <p class="form-control-static" th:text="${applyBaseinfo.patentInfo?.swsdlrPhone}"></p>
                        </div>
                    </div>
                    <!-- 隐藏字段 -->
                    <input type="hidden" name="patentInfo.patentId" th:value="${applyBaseinfo.patentInfo?.patentId}">
                    <input type="hidden" name="patentInfo.applyId" th:value="${applyBaseinfo.applyId}">
                    <input type="hidden" name="patentInfo.patentStatus" th:value="${applyBaseinfo.patentInfo?.patentStatus}">
                </div>
            </div>
        </div>
        <!-- 工作流信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow},needComment=true)" />
    </form>
    <th:block th:include="kizl/wfKizlButton :: init(workFlow=${applyBaseinfo.workFlow})" />
</div>

<th:block th:include="include :: datetimepicker-js"/>
<!-- 引入KIZL通用JS -->
<script th:src="@{/kizl/kizl-common.js}"></script>
</body>
</html>
