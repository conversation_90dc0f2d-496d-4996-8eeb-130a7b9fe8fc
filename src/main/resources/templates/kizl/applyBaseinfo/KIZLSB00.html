<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_申请_基本信息')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
    <script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false"
                           class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">流水号：</label>
                            <div class="col-sm-4">
                                <input name="serialNum" readonly th:field="*{serialNum}" class="form-control"
                                       type="text" placeholder="自动生成">
                            </div>
                            <label class="col-sm-2 control-label">填表日期：</label>
                            <div class="col-sm-2">
                                <input name="applyDate" readonly th:field="*{applyDate}" class="form-control"
                                       type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">专利申报名称：</label>
                            <div class="col-sm-8">
                                <input name="applyName" th:field="*{applyName}" class="form-control" type="text"
                                       required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">第一申报部门：</label>
                            <div class="col-sm-8">
                                <div th:include="/component/selectOrg :: init(orgCodeId='firstDeptCode', orgNameId='firstDeptName', value=${applyBaseinfo.firstDeptCode}, isrequired=true)"></div>
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label">标签：</label>-->
<!--                            <div class="col-sm-8">-->
<!--                                <div th:include="/component/checkbox :: init(id='label', name='label',businessType='KIZL',value=*{label}, dictCode='LABEL')"></div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">技术领域：</label>
                            <input type="hidden" id="techArea" name="techArea"
                                   th:value="*{techArea}"/>
                            <div class="col-sm-8">
                                <input name="extra2" id="techAreaName" class="form-control"
                                        placeholder="点击选择技术领域" th:value="*{extra2}"
                                       onclick="choiceTechArea()" required readonly/>
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label is-required">用途：</label>-->
<!--                            <div class="col-sm-4">-->
<!--                                <div th:include="/component/checkbox :: init(id='usePropose', name='usePropose', businessType='KIZL', dictCode='USE_PROPOSE', value=${applyBaseinfo.usePropose}, multimultiple=true, isrequired=true)"></div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">是否产品专利：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='knowledgeClass', name='knowledgeClass', businessType='KIZL',dictCode='YES_NO', value=${applyBaseinfo.knowledgeClass}, isrequired=true, callback='toggleProductArea')"></div>
                            </div>
                        </div>
                        <div class="form-group" id="productAreaGroup" style="display: none;">
                            <label class="col-sm-2 control-label is-required">产品领域：</label>
                            <div class="col-sm-8">
                                <div th:include="/component/radio :: init(id='ishaveph', name='ishaveph', businessType='KIZL', dictCode='ishaveph', value=${applyBaseinfo.ishaveph}, isrequired=true)"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 联系人信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#lxrxx" aria-expanded="false"
                           class="collapsed">联系人信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="lxrxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">联系人：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/selectUser :: init (id='lxrCode', userNameId='lxrName', userCodeId='lxrCode', value=${applyBaseinfo.lxrCode}, isrequired=true)"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">联系人手机：</label>
                            <div class="col-sm-4">
                                <input name="lxrMobile" th:field="*{lxrMobile}" class="form-control" type="text"
                                       required maxlength="11" oninput="this.value=this.value.replace(/[^\d]/g,'')">
                            </div>
                            <label class="col-sm-2 control-label is-required">联系人邮箱：</label>
                            <div class="col-sm-2">
                                <input name="lxrPhone" th:field="*{lxrPhone}" class="form-control" type="text" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 检索信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#jsxx" aria-expanded="false"
                       class="collapsed">检索信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="jsxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="form-group">
                        <div class="mnote-editor-title"><span class="txt-impt"></span>专利检索情况
                        </div>
                        <div class="mnote-editor-box">
                            <div class="col-sm-12">
                                    <textarea class="form-control" name="searchType" id="searchType" rows="5" cols="150"
                                              maxlength="200" th:text="${applyBaseinfo.searchType}" style="white-space: break-spaces;"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" id="rhxgfj-label">相关附件：</label>
                        <div class="col-sm-8">
                            <th:block
                                    th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='rhxgfj',id='rhxgfj',name='attachmentS[rhxgfj]')"></th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 来源与应用信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#lyxx" aria-expanded="false"
                       class="collapsed">来源与应用信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="lyxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">来源：</label>
                        <div class="col-sm-8">
                            <div th:include="/component/radio :: init(id='fromType', name='fromType', businessType='KIZL', dictCode='FROM_TYPE', value=${applyBaseinfo.fromType}, isrequired=true, callback='toggleFromType')"></div>
                        </div>
                    </div>
                    <div class="form-group from-project" style="display: none;">
                        <label class="col-sm-2 control-label is-required">来源编号：</label>
                        <div class="col-sm-4">
                            <input name="fromNo" th:field="*{fromNo}" class="form-control" type="text"
                                   onclick="selectSource()" readonly>
                        </div>
                    </div>
                    <div class="form-group from-project" style="display: none;">
                        <label class="col-sm-2 control-label is-required">来源名称：</label>
                        <div class="col-sm-4">
                            <input name="fromName" th:field="*{fromName}" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group from-other" style="display: none;">
                        <label class="col-sm-2 control-label is-required">其他来源情况说明：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/textarea :: init(id='fromContent', name='fromContent', value=${applyBaseinfo.fromContent}, rows='3', maxlength='500')"></div>
                        </div>
                    </div>
                    <div class="form-group" id="sameSourcePatent" style="display: none;">
                        <label class="col-sm-2 control-label">同来源专利：</label>
                        <div class="col-sm-10">
                            <div th:insert="/jsmm/techSecretComponent/sameSourcePatentList :: init"></div>
                        </div>
                    </div>
                    <div class="form-group" id="sameSourceSecret" style="display: none;">
                        <label class="col-sm-2 control-label">同来源技术秘密：</label>
                        <div class="col-sm-10">
                            <div th:insert="/jsmm/techSecretComponent/sameSourceSecretList :: init"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">应用方式：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/radio :: init(id='useMethod', name='useMethod', businessType='KIZL', dictCode='USE_METHOD', value=${applyBaseinfo.useMethod}, isrequired=true, callback='toggleUseMethod')"></div>
                        </div>
                    </div>
                    <div class="form-group use-internal" style="display: none;">
                        <label class="col-sm-2 control-label is-required">应用部门：</label>
                        <div class="col-sm-8">
                            <div th:include="/component/selectOrg :: init(orgCodeId='useDept', orgNameId='useDeptName', value=${applyBaseinfo.useDept}, isrequired=true)"></div>
                        </div>
                    </div>
                    <div class="form-group use-date" style="display: none;">
                        <label class="col-sm-2 control-label is-required">初始应用日：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/date :: init(id='useFirstdate', name='useFirstdate', strValue=${applyBaseinfo.useFirstdate}, isrequired=true)"></div>
                        </div>
                    </div>
                    <div class="form-group use-expected" style="display: none;">
                        <label class="col-sm-2 control-label">预计应用部门：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/selectOrg :: init(orgCodeId='useExpected', orgNameId='useExpectedName', value=${applyBaseinfo.useExpected})"></div>
                        </div>
                    </div>
                    <div class="form-group use-reason" style="display: none;">
                        <label class="col-sm-2 control-label is-required">未应用原因：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/radio :: init(id='reasonNouse', name='reasonNouse', businessType='KIZL', dictCode='REASON_NOUSE', value=${applyBaseinfo.reasonNouse}, isrequired=true, callback='toggleReasonNouse')"></div>
                        </div>
                    </div>
                    <div class="form-group reason-other" style="display: none;">
                        <label class="col-sm-2 control-label is-required">未应用原因备注：</label>
                        <div class="col-sm-10">
                            <div th:include="/component/textarea :: init(id='contentNouse', name='contentNouse', value=${applyBaseinfo.contentNouse}, rows='3', maxlength='500', isrequired=true)"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label is-required">预计效果：</label>
                        <div class="col-sm-8">
                            <div th:include="/component/checkbox :: init(id='resultExpect', name='resultExpect', businessType='KIZL', dictCode='RESULT_EXPECT', value=*{resultExpect}, isrequired=true)"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>专利实施情况说明：(不得多于200字)
                        </div>
                        <div class="mnote-editor-box">
                            <div class="col-sm-12">
                                    <textarea class="form-control" name="summary" id="summary" rows="5" cols="150"
                                              maxlength="200" th:text="${applyBaseinfo.summary}" style="white-space: break-spaces;"
                                              required="required"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请人信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#sqrxx" aria-expanded="false"
                       class="collapsed">申请人信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="sqrxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="btn-group-sm mb-15" id="applicant-toolbar" role="group">
                        <button type="button" class="btn btn-success" onclick="addApplicant()"><i
                                class="fa fa-plus"></i> 添加申请人
                        </button>
                    </div>
                    <br/>
                    <!-- 申请人容器 -->
                    <div id="applicant-container"></div>
                </div>
            </div>
        </div>

        <!-- 发明设计人 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#fmsjr" aria-expanded="false"
                       class="collapsed">发明设计人
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="fmsjr" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="btn-group-sm mb-15" id="inventor-toolbar" role="group">
                        <button type="button" class="btn btn-success" onclick="addInventor()"><i
                                class="fa fa-plus"></i> 添加发明人
                        </button>
                        <button type="button" class="btn btn-danger" onclick="removeAllInventors()"><i
                                class="fa fa-remove"></i> 删除所有
                        </button>
                    </div>
                    <br/>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="inventor-bootstrap-table"></table>
                    </div>
                    <input type="hidden" id="inventorData" name="inventorData">

                    <div class="form-group" style="margin-top: 15px;">
                        <div class="col-sm-offset-1 col-sm-11">
                            <div class="checkbox check-box">
                                <input type="checkbox" id="inventor_confirm" required>
                                <label for="inventor_confirm">填报人承诺：专利申请的技术内容及排名已得到所有发明人（含外单位）的确认与认可。</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相关附件 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#xgfj" aria-expanded="false"
                       class="collapsed">相关附件
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </div>
                <div id="xgfj" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required" id="jsjds-label">技术交底书：</label>
                            <div class="col-sm-8">
                                <th:block th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='jsjds',id='jsjds',name='attachmentS[jsjds]', isrequired=true)"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label" id="gtsqxy-label">共同申请协议：</label>
                            <div class="col-sm-8">
                                <th:block
                                        th:include="/component/attachment::init(sourceId=${applyBaseinfo.applyId},sourceModule='KIZL',sourceLabel1='gtsqxy',id='gtsqxy',name='attachmentS[gtsqxy]')"></th:block>
                            </div>
                        </div>
                    </div>
                </div>
                <th:block
                        th:include="component/wfWorkFlow :: init(workFlow=*{workFlow}, needComment=${needComment eq true})"/>
            </div>
        </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-primary" onclick="saveDraft()">
            <i class="fa fa-save"></i>暂 存
        </button>
        <button type="button" class="btn btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i>提 交
        </button>&nbsp;
        <button type="button" class="btn btn-danger" onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返回
        </button>
    </div>
    <!--按钮区end-->
</div>
</body>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "kizl/applyBaseinfo";
    var inventors = []; // 发明人数组
    var applicants = []; // 申请人数组
    var attachmentNameMap = {}; // 附件名称映射

    $(function () {
        // 初始化附件名称映射
        KizlAttachmentUtils.init(function() {
            KizlAttachmentUtils.updateCommonLabels();
        });

        // 初始化表单验证
        $("#form-applyBaseinfo-edit").validate({
            focusCleanup: true,
            // 关闭键盘输入时的实时校验
            onkeyup: false,
            // 关闭失去焦点时的校验，只在提交时校验
            onfocusout: false,
            // 设置忽略验证的元素
            ignore: ""
        });

        // 初始化发明人Bootstrap Table
        initInventorBootstrapTable();

        // 初始化表单数据（包括发明人数据）
        initFormData();

        // 发明人表单验证
        $("#inventor-form").validate({
            rules: {
                inventor_order: {
                    required: true,
                    number: true,
                    min: 1
                },
                inventor_name: {
                    required: true
                },
                inventor_dept: {
                    required: true
                },
                inventor_idcard: {
                    maxlength: 18,
                    minlength: 18
                }
            },
            messages: {
                inventor_order: {
                    required: "请输入序号",
                    number: "请输入有效的数字",
                    min: "序号必须大于0"
                },
                inventor_name: {
                    required: "请输入姓名"
                },
                inventor_dept: {
                    required: "请输入/选择单位"
                },
                inventor_idcard: {
                    maxlength: "身份证号必须是18位",
                    minlength: "身份证号必须是18位"
                }
            }
        });

        // 申请人表单验证
        $("#applicant-form").validate({
            rules: {
                applicant_org_name: {
                    required: true
                },
                applicant_address: {
                    required: true
                },
                applicant_zipcode: {
                    required: true,
                    digits: true,
                    minlength: 6,
                    maxlength: 6
                },
                applicant_phone: {
                    required: true
                }
            },
            messages: {
                applicant_org_name: {
                    required: "请选择或输入申请人名称"
                },
                applicant_address: {
                    required: "请输入通信地址"
                },
                applicant_zipcode: {
                    required: "请输入邮政编码",
                    digits: "邮政编码只能输入数字",
                    minlength: "邮政编码必须是6位",
                    maxlength: "邮政编码必须是6位"
                },
                applicant_phone: {
                    required: "请输入联系电话"
                }
            }
        });
    });

    // 初始化表单数据
    function initFormData() {
        // 调试：检查applyBaseinfo对象
        console.log('initFormData - applyBaseinfo:', /*[[${applyBaseinfo}]]*/ {});

        // 如果是新增表单，初始化日期为当前日期
        if (!$("#applyId").val()) {
            var today = new Date();
            var yyyy = today.getFullYear();
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var dd = String(today.getDate()).padStart(2, '0');
            var formattedDate = yyyy + '-' + mm + '-' + dd;
            $("#applyDate").val(formattedDate);
        }

        // 初始化发明人数据
        initInventorData();

        // 初始化申请人数据
        initApplicantData();

        //初始化来源信息
        getSource();

        // 初始化各表单状态
        toggleProductArea($("input[name='knowledgeClass']:checked").val());
        toggleFromType($("input[name='fromType']:checked").val());
        toggleUseMethod($("input[name='useMethod']:checked").val());
        toggleReasonNouse($("input[name='reasonNouse']:checked").val());

        // 确保初始状态下的必填验证正确设置
        setTimeout(function () {
            // 重新触发一次切换函数，确保必填验证状态正确
            toggleProductArea($("input[name='knowledgeClass']:checked").val());
            toggleFromType($("input[name='fromType']:checked").val());
            toggleUseMethod($("input[name='useMethod']:checked").val());
            toggleReasonNouse($("input[name='reasonNouse']:checked").val());
        }, 100);
    }

    // 初始化发明人数据
    function initInventorData() {
        // 从后台传递的数据中读取发明人信息（无论是否有applyId）
        var ryxxList = /*[[${applyBaseinfo.ryxxList}]]*/ [];

        if (ryxxList && ryxxList.length > 0) {
            inventors = ryxxList.map(function (item, index) {
                return {
                    inventorId: Date.now().toString(36) + index,
                    order: item.ryxh || (index + 1),
                    type: item.rylx === "01" ? "1" : "2",
                    code: item.empId || "",
                    name: item.empName || "",
                    deptCode: item.deptCode || "",
                    dept: item.deptName || "",
                    title: item.postTitle || "",
                    position: item.postLevel || "",
                    idcard: item.idCard || "",
                    remark: item.contentMemo || ""
                };
            });

            console.log('initInventorData - mapped inventors:', inventors); // 调试日志

            // 检查Bootstrap Table是否已初始化
            if ($('#inventor-bootstrap-table').length && typeof $('#inventor-bootstrap-table').bootstrapTable === 'function') {
                // 更新Bootstrap Table数据
                $('#inventor-bootstrap-table').bootstrapTable('load', inventors);
                updateInventorData();
                console.log('initInventorData - data loaded to bootstrap table'); // 调试日志
            } else {
                console.warn('initInventorData - Bootstrap table not ready, retrying...'); // 调试日志
                // 如果Bootstrap Table还没准备好，延迟执行
                setTimeout(function() {
                    $('#inventor-bootstrap-table').bootstrapTable('load', inventors);
                    updateInventorData();
                }, 100);
            }
        }
    }

    // 初始化申请人数据
    function initApplicantData() {
        var applyId = $("#applyId").val();
        if (applyId) {
            // 编辑模式，从后台传递的数据中读取申请人信息
            var sqrList = /*[[${applyBaseinfo.sqrList}]]*/ [];
            if (sqrList && sqrList.length > 0) {
                applicants = sqrList.map(function (item, index) {
                    // 默认为内部法人单位
                    return {
                        index: item.xh || (index + 1),
                        type: "1", // 内部法人单位
                        orgCode: item.legalId || "",
                        orgName: item.legalName || "",
                        address: item.mailAddress || "",
                        zipcode: item.postOffice || "",
                        contact: item.lxrMoney || "",
                        phone: item.lxrPhone || ""
                    };
                });
                updateApplicantTable();
                return; // 编辑模式有数据，直接返回，不添加默认申请人
            }
        }

        // 新增模式或编辑模式没有申请人数据，添加默认申请人
        if (applicants.length === 0) {
            addApplicant();
        }
    }

    // 切换是否产品专利
    function toggleProductArea(value) {
        if (value === "1") { // 是
            $("#productAreaGroup").show();
            // 添加产品领域的必填验证
            addRequiredValidation('#productAreaGroup');
        } else {
            $("#productAreaGroup").hide();
            // 清空产品领域的数据
            clearFieldData('#productAreaGroup');
            // 移除产品领域的必填验证
            removeRequiredValidation('#productAreaGroup');
        }
    }

    // 切换来源类型
    function toggleFromType(value) {
        // 先清除所有来源相关字段的必填验证
        removeRequiredValidation('.from-project', '.from-other');

        if (value === "kyxm") { // 科研项目
            $(".from-project").show();
            $(".from-other").hide();
            // 清空隐藏字段的数据
            clearFieldData('.from-other');
            // 添加项目相关字段的必填验证
            addRequiredValidation('.from-project');
            //初始化来源信息
            getSource();

        } else if (value === "other") { // 其他
            $(".from-project").hide();
            $(".from-other").show();
            // 清空隐藏字段的数据
            clearFieldData('.from-project');
            // 添加其他来源字段的必填验证
            addRequiredValidation('.from-other');

            $("#sameSourcePatent").hide();
            $("#sameSourceSecret").hide();
        } else { // 无来源 (noSource)
            $(".from-project").hide();
            $(".from-other").hide();

            // 清空所有来源相关字段的数据
            clearFieldData('.from-project', '.from-other');

            $("#sameSourcePatent").hide();
            $("#sameSourceSecret").hide();
        }
    }

    // 切换应用方式
    function toggleUseMethod(value) {
        // 先清除所有相关字段的必填验证
        removeRequiredValidation('.use-internal', '.use-date', '.use-expected', '.use-reason', '.reason-other');

        if (value === "1") { // 已在集团内部应用
            $(".use-internal").show();
            $(".use-date").show();
            $(".use-expected").hide();
            $(".use-reason").hide();
            $(".reason-other").hide();

            // 清空隐藏字段的数据
            clearFieldData('.use-expected', '.use-reason', '.reason-other');

            // 特别处理未应用原因单选按钮
            resetRadioGroup('reasonNouse');

            // 添加必填验证
            addRequiredValidation('.use-internal', '.use-date');
        } else if (value === "2") { // 已在集团外部应用
            $(".use-internal").hide();
            $(".use-date").show();
            $(".use-expected").hide();
            $(".use-reason").hide();
            $(".reason-other").hide();

            // 清空隐藏字段的数据
            clearFieldData('.use-internal', '.use-expected', '.use-reason', '.reason-other');

            // 特别处理未应用原因单选按钮
            resetRadioGroup('reasonNouse');

            // 添加必填验证
            addRequiredValidation('.use-date');
        } else if (value === "3") { // 尚未应用
            $(".use-internal").hide();
            $(".use-date").hide();
            $(".use-expected").show();
            $(".use-reason").show();

            // 清空隐藏字段的数据
            clearFieldData('.use-internal', '.use-date');

            // 获取当前选中的未应用原因
            var reasonValue = $("input[name='reasonNouse']:checked").val();
            // 如果没有选中值，默认隐藏未应用原因备注
            if (!reasonValue) {
                $(".reason-other").hide();
                clearFieldData('.reason-other');
            } else {
                // 根据未应用原因来决定是否显示备注
                toggleReasonNouse(reasonValue);
            }

            // 添加必填验证
            addRequiredValidation('.use-reason');
        } else {
            $(".use-internal").hide();
            $(".use-date").hide();
            $(".use-expected").hide();
            $(".use-reason").hide();
            $(".reason-other").hide();

            // 清空所有相关字段的数据
            clearFieldData('.use-internal', '.use-date', '.use-expected', '.use-reason', '.reason-other');

            // 特别处理未应用原因单选按钮
            resetRadioGroup('reasonNouse');
        }
    }

    // 切换未应用原因
    function toggleReasonNouse(value) {
        if (value === "9") { // 其他原因
            $(".reason-other").show();
            addRequiredValidation('.reason-other');
        } else {
            $(".reason-other").hide();
            // 清空未应用原因备注的数据
            clearFieldData('.reason-other');
            removeRequiredValidation('.reason-other');
        }
    }

    // 添加必填验证的辅助函数
    function addRequiredValidation() {
        var selectors = Array.prototype.slice.call(arguments);
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            $(selector).find('input, select, textarea').each(function () {
                var $element = $(this);
                // 检查原始HTML中是否有isrequired标识
                var $group = $element.closest('.form-group');
                if ($group.find('.is-required').length > 0) {
                    $element.attr('required', 'required');
                }
            });
        }
    }

    // 移除必填验证的辅助函数
    function removeRequiredValidation() {
        var selectors = Array.prototype.slice.call(arguments);
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            $(selector).find('input, select, textarea').each(function () {
                $(this).removeAttr('required');
            });
        }
    }

    // 清空字段数据的辅助函数
    function clearFieldData() {
        var selectors = Array.prototype.slice.call(arguments);
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            $(selector).find('input[type="text"], input[type="number"], input[type="date"], textarea').each(function () {
                $(this).val('');
            });
            $(selector).find('input[type="radio"], input[type="checkbox"]').each(function () {
                $(this).prop('checked', false);
            });
            $(selector).find('select').each(function () {
                $(this).val('').trigger('change');
            });

            // 特别处理单选按钮组
            var radioNames = [];
            $(selector).find('input[type="radio"]').each(function () {
                if (radioNames.indexOf(this.name) === -1) {
                    radioNames.push(this.name);
                }
            });

            // 重置整个单选按钮组
            for (var j = 0; j < radioNames.length; j++) {
                var name = radioNames[j];
                $('input[name="' + name + '"]').prop('checked', false);
            }
        }
    }

    // 重置单选按钮组的辅助函数
    function resetRadioGroup(name) {
        $('input[name="' + name + '"]').prop('checked', false);
    }

    //选择来源项目
    function selectSource() {
        var url = ctx + 'jsmm/technologySecret/selectSource';
        $.modal.open('选择来源项目',url,'1152','550');
    }

    function selectSourceCallback(data){
        $("#fromNo").val(data.projectCode);
        $("#fromName").val(data.projectName);
        getSource();
    }

    // 获取来源名称和相关信息
    function getSource() {
        var fromNo = $("#fromNo").val();
        if (fromNo && fromNo.trim() !== "") {
            $.ajax({
                url: ctx + "kizl/applyBaseinfo/related",
                type: "GET",
                data: {fromNo: fromNo},
                dataType: "json",
                success: function (result) {
                    if (result && result.code === 0) {
                        // 设置产品信息
                        // if (result.data.productInfo && result.data.productInfo.productName) {
                        //     $("#extra1").val(result.data.productInfo.productName);
                        // } else {
                        //     $("#extra1").val("");
                        // }
                        var secretList = result.data.secretList;
                        var patentList = result.data.patentList
                        // 填充专利列表
                        if ($.common.isNotEmpty(patentList)) {
                            $('#sameSourcePatentTable').bootstrapTable('load', patentList);
                            $('#sameSourcePatent').show();
                        } else {
                            $('#sameSourcePatent').hide();
                        }
                        // 填充技术秘密列表
                        if ($.common.isNotEmpty(secretList)) {
                            $('#sameSourceSecretTable').bootstrapTable('load', secretList);
                            $('#sameSourceSecret').show();
                        } else {
                            $('#sameSourceSecret').hide();
                        }
                    } else {
                        $("#fromName").val("");
                    }
                },
                error: function () {
                    $("#fromName").val("");
                }
            });
        } else {
            $("#fromName").val("");
        }
    }

    // 默认添加发明人
    function addDefaultInventor() {
        var inventor = {
            inventorId: Date.now().toString(36),
            order: 1,
            type: "1", // 默认集团内
            code: "",
            name: "",
            deptCode: "",
            dept: "",
            title: "",
            position: "",
            idcard: "",
            remark: ""
        };
        inventors.push(inventor);
        $('#inventor-bootstrap-table').bootstrapTable('append', inventor);
        updateInventorData();
    }

    // 添加申请人
    function addApplicant() {
        var applicant = {
            index: applicants.length + 1,
            type: "1", // 默认内部法人单位
            orgCode: "",
            orgName: "",
            address: "",
            zipcode: "",
            contact: "",
            phone: ""
        };
        applicants.push(applicant);
        updateApplicantTable();
    }

    // 移除申请人
    function removeApplicant(index) {
        if (applicants.length <= 1) {
            $.modal.alertWarning("至少需要一个申请人！");
            return;
        }

        $.modal.confirm("确定删除该申请人吗？", function () {
            applicants.splice(index, 1);
            // 重新编号
            for (var i = 0; i < applicants.length; i++) {
                applicants[i].index = i + 1;
            }
            updateApplicantTable();
        });
    }

    // 更新申请人表格
    function updateApplicantTable() {
        var container = $("#applicant-container");
        container.empty();

        $.each(applicants, function (index, applicant) {
            var html = generateApplicantForm(index, applicant);
            container.append(html);
        });

        // 更新选择框的值
        $("#applicantCount").val(applicants.length);
    }

    // 生成申请人表单
    function generateApplicantForm(index, applicant) {
        var readonly = applicant.type === "1" ? "readonly" : "";
        var switchButtonText = applicant.type === "1" ? "填写外部法人单位" : "选择内部法人单位";
        var selectButtonStyle = applicant.type === "1" ? "" : "style='display:none'";

        return `
        <div id="applicant${index}" class="applicant-block">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">第${applicant.index}申请人：</label>
                <div class="col-sm-6">
                    <div class="input-group">
                        <input type="text" class="form-control applicant-org-name" ${readonly} required
                               value="${applicant.orgName || ''}" onchange="updateApplicantField(${index}, 'orgName', this.value)">
                        <span class="input-group-btn">
                            <button class="btn btn-primary select-internal-btn" type="button" ${selectButtonStyle}
                                    onclick="selectLegal(${index})">
                                选择内部法人单位
                            </button>
                            <button class="btn btn-info switch-type-btn" type="button"
                                    onclick="switchApplicantType(${index})">
                                ${switchButtonText}
                            </button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">通信地址：</label>
                <div class="col-sm-8">
                    <input class="form-control applicant-address" type="text" required
                           value="${applicant.address || ''}" onchange="updateApplicantField(${index}, 'address', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">邮政编码：</label>
                <div class="col-sm-2">
                    <input class="form-control applicant-zipcode" type="text" required maxlength="6"
                           value="${applicant.zipcode || ''}" oninput="this.value=this.value.replace(/[^\\d]/g,'')"
                           onchange="updateApplicantField(${index}, 'zipcode', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">联系电话：</label>
                <div class="col-sm-2">
                    <input class="form-control applicant-phone" type="text" required
                           value="${applicant.phone || ''}" onchange="updateApplicantField(${index}, 'phone', this.value)">
                </div>
                <div class="col-sm-2">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeApplicant(${index})">
                        <i class="fa fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
        `;
    }

    // 更新申请人字段
    function updateApplicantField(index, field, value) {
        if (applicants[index]) {
            applicants[index][field] = value;
        }
    }

    // 切换申请人类型
    function switchApplicantType(index) {
        if (!applicants[index]) return;

        var applicant = applicants[index];
        if (applicant.type === "1") {
            // 切换到外部法人单位
            applicant.type = "2";
            applicant.orgCode = "";
            applicant.orgName = "";
        } else {
            // 切换到内部法人单位
            applicant.type = "1";
            applicant.orgCode = "";
            applicant.orgName = "";
        }

        updateApplicantTable();
    }

    // 法人单位选择函数（内部单位）
    function selectLegal(index) {
        if (!applicants[index]) return;

        // 保存当前索引，供回调使用
        window.currentApplicantIndex = index;

        $.modal.open("选择申请人", ctx + "kizl/applyBaseinfo/selectList?callback=selectLegalCallback", '480', '580');
    }

    // 法人单位选择回调函数
    function selectLegalCallback(id, name) {
        var index = window.currentApplicantIndex;
        if (index === undefined || !applicants[index] || !id || !name) return;

        var applicant = applicants[index];
        applicant.orgCode = id;
        applicant.orgName = name;
        applicant.type = "1"; // 设置为内部法人单位

        // 获取附加信息
        $.ajax({
            url: ctx + "kizl/applyBaseinfo/lxrData/" + id,
            type: "GET",
            dataType: "json",
            success: function (result) {
                if (result.code == 0 && result.data) {
                    var data = result.data;
                    applicant.address = data.mailAddress || "";
                    applicant.zipcode = data.postOffice || "";
                    applicant.contact = data.lxrMoney || "";
                    applicant.phone = data.lxrPhone || "";
                }
                updateApplicantTable();
            },
            error: function () {
                updateApplicantTable();
            }
        });

        // 清除临时变量
        window.currentApplicantIndex = undefined;
    }

    // ==================== 发明人管理 Bootstrap Table 模式 ====================
    var POST_TITLE = /*[[${@SDictUtil.getDictList('KIZL','POST_TITLE')}]]*/ [];
    var POST_LEVEL = /*[[${@SDictUtil.getDictList('KIZL','POST_LEVEL')}]]*/ [];

    // 初始化发明人Bootstrap Table
    function initInventorBootstrapTable() {
        var options = {
            id: 'inventor-bootstrap-table',
            toolbar: 'inventor-toolbar',
            data: [], // 初始化为空数组，等待后续加载数据
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            pagination: false,
            uniqueId: "inventorId",
            modalName: "发明人信息",
            columns: [
                {
                    field: 'inventorId',
                    title: '发明人记录ID',
                    visible: false
                },
                {
                    field: 'order',
                    title: '序号',
                    align: 'center',
                    width: '60px',
                    formatter: function (value, row, index) {
                        return value || (index + 1);
                    }
                },
                {
                    field: 'name',
                    title: '姓名',
                    width: '100px'
                },
                {
                    field: 'code',
                    title: '工号',
                    width: '100px'
                },
                {
                    field: 'dept',
                    title: '单位',
                    width: '150px'
                },
                {
                    field: 'position',
                    title: '岗位',
                    width: '100px',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(POST_LEVEL, value);
                    }
                },
                {
                    field: 'title',
                    title: '职称',
                    width: '100px',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(POST_TITLE, value);
                    }
                },
                {
                    field: 'type',
                    title: '类型',
                    width: '80px',
                    formatter: function (value, row, index) {
                        return value == "1" ? "集团内" : "集团外";
                    }
                },
                {
                    field: 'idcard',
                    title: '身份证号',
                    width: '100px',
                    formatter: function (value, row, index) {
                        return value ? "已填写" : "";
                    }
                },
                {
                    field: 'remark',
                    title: '备注',
                    width: '100px'
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '120px',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editInventor(\'' + row.inventorId + '\')"><i class="fa fa-edit"></i>编辑</a>');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="removeInventor(\'' + row.inventorId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join(' ');
                    }
                }
            ]
        };
        $('#inventor-bootstrap-table').bootstrapTable(options);
    }

    // 添加发明人
    function addInventor() {
        var url = ctx + "kizl/c/p?pagePath=applyBaseinfo&pageNo=inventorForm&memberType=0&editType=A";
        $.modal.open('添加发明人', url, '1152','550');
    }

    // 编辑发明人
    function editInventor(id) {
        var row = $('#inventor-bootstrap-table').bootstrapTable('getRowByUniqueId', id);
        var url = ctx + "kizl/c/p?pagePath=applyBaseinfo&pageNo=inventorForm&editType=E&id=" + id;
        for (let key in row) {
            if (row[key] !== null && row[key] !== undefined) {
                url = url + '&' + key + "=" + encodeURIComponent(row[key]);
            }
        }
        $.modal.open('修改发明人', url, '1200', '800');
    }

    // 删除发明人
    function removeInventor(id) {
        $.modal.confirm("确定删除该发明人吗？", function () {
            $('#inventor-bootstrap-table').bootstrapTable('removeByUniqueId', id);
            // 重新排列序号
            reorderInventors();
            updateInventorData();
        });
    }

    // 删除所有发明人
    function removeAllInventors() {
        $.modal.confirm("确定删除所有发明人吗？", function () {
            $('#inventor-bootstrap-table').bootstrapTable('removeAll');
            updateInventorData();
        });
    }

    // 重新排列发明人序号
    function reorderInventors() {
        var currentData = $('#inventor-bootstrap-table').bootstrapTable("getData");

        // 确保currentData是数组
        if (!Array.isArray(currentData)) {
            console.warn('currentData is not an array in reorderInventors:', typeof currentData, currentData);
            return;
        }

        // 按当前序号排序
        currentData.sort(function(a, b) {
            return parseInt(a.order || 0) - parseInt(b.order || 0);
        });

        // 重新分配序号
        for (var i = 0; i < currentData.length; i++) {
            currentData[i].order = i + 1;
        }

        // 更新表格数据
        $('#inventor-bootstrap-table').bootstrapTable('load', currentData);
    }

    // 添加发明人回调函数
    function addInventorCallback(formData) {
        var rowData = {};

        // 检查formData是否为FormData对象
        if (formData && typeof formData.get === 'function') {
            try {
                // 使用更兼容的方式处理FormData - 直接获取已知字段
                var fieldNames = ['inventorId', 'type', 'order', 'code', 'name', 'deptCode', 'dept', 'title', 'position', 'idcard', 'remark'];
                for (var i = 0; i < fieldNames.length; i++) {
                    var fieldName = fieldNames[i];
                    var value = formData.get(fieldName);
                    if (!$.common.isEmpty(value)) {
                        rowData[fieldName] = value;
                    }
                }
            } catch (error) {
                console.error('Error processing FormData:', error);
                $.modal.alertError("表单数据处理失败！");
                return;
            }
        } else {
            console.error('formData is not a valid FormData object:', formData);
            $.modal.alertError("表单数据格式错误！");
            return;
        }

        // 验证必填项
        if (!rowData.type) {
            $.modal.alertError("发明人类型不能为空！");
            return;
        }

        if (!rowData.name) {
            $.modal.alertError("发明人姓名不能为空！");
            return;
        }

        if (!rowData.dept) {
            $.modal.alertError("发明人组织不能为空！");
            return;
        }

        // 验证和处理序号
        var currentData = $('#inventor-bootstrap-table').bootstrapTable("getData");
        var inputOrder = parseInt(rowData.order);

        // 确保currentData是数组
        if (!Array.isArray(currentData)) {
            console.warn('currentData is not an array in addInventorCallback:', typeof currentData, currentData);
            currentData = [];
        }

        // 检查序号是否已存在
        var existingOrder = currentData.find(function(item) {
            return parseInt(item.order) == inputOrder;
        });

        if (existingOrder) {
            // 如果序号已存在，自动分配下一个可用序号
            var maxOrder = 0;
            // 确保currentData是数组
            if (Array.isArray(currentData)) {
                for (var i = 0; i < currentData.length; i++) {
                    var item = currentData[i];
                    if (item && item.order) {
                        var order = parseInt(item.order) || 0;
                        if (order > maxOrder) {
                            maxOrder = order;
                        }
                    }
                }
            }
            rowData.order = maxOrder + 1;
            $.modal.alertWarning("序号 " + inputOrder + " 已存在，已自动调整为序号 " + rowData.order + "！");
        }

        // 确保inventorId存在
        if (!rowData.inventorId) {
            rowData.inventorId = Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        $('#inventor-bootstrap-table').bootstrapTable('append', rowData);
        updateInventorData();
        $.modal.msgSuccess("发明人添加成功!");
    }

    // 编辑发明人回调函数
    function editInventorCallback(formData, id) {
        var updateData = {};

        // 检查formData是否为FormData对象
        if (formData && typeof formData.get === 'function') {
            try {
                // 使用更兼容的方式处理FormData - 直接获取已知字段
                var fieldNames = ['inventorId', 'type', 'order', 'code', 'name', 'deptCode', 'dept', 'title', 'position', 'idcard', 'remark'];
                for (var i = 0; i < fieldNames.length; i++) {
                    var fieldName = fieldNames[i];
                    var value = formData.get(fieldName);
                    if (!$.common.isEmpty(value)) {
                        updateData[fieldName] = value;
                    }
                }
            } catch (error) {
                console.error('Error processing FormData:', error);
                $.modal.alertError("表单数据处理失败！");
                return;
            }
        } else {
            console.error('formData is not a valid FormData object:', formData);
            $.modal.alertError("表单数据格式错误！");
            return;
        }

        // 验证必填项
        if (!updateData.type) {
            $.modal.alertError("发明人类型不能为空！");
            return;
        }

        if (!updateData.name) {
            $.modal.alertError("发明人姓名不能为空！");
            return;
        }

        if (!updateData.dept) {
            $.modal.alertError("发明人组织不能为空！");
            return;
        }

        // 验证和处理序号（排除当前编辑的记录）
        var currentData = $('#inventor-bootstrap-table').bootstrapTable("getData");
        var inputOrder = parseInt(updateData.order);

        // 确保currentData是数组
        if (!Array.isArray(currentData)) {
            console.warn('currentData is not an array in editInventorCallback:', typeof currentData, currentData);
            currentData = [];
        }

        // 检查序号是否已存在（排除当前编辑的记录）
        var existingOrder = currentData.find(function(item) {
            return parseInt(item.order) == inputOrder && item.inventorId != id;
        });

        if (existingOrder) {
            // 如果序号已存在，自动分配下一个可用序号
            var maxOrder = 0;
            // 确保currentData是数组
            if (Array.isArray(currentData)) {
                for (var i = 0; i < currentData.length; i++) {
                    var item = currentData[i];
                    if (item && item.inventorId != id && item.order) { // 排除当前编辑的记录
                        var order = parseInt(item.order) || 0;
                        if (order > maxOrder) {
                            maxOrder = order;
                        }
                    }
                }
            }
            updateData.order = maxOrder + 1;
            $.modal.alertWarning("序号 " + inputOrder + " 已存在，已自动调整为序号 " + updateData.order + "！");
        }

        // 确保inventorId存在
        updateData.inventorId = id;

        var rowData = {
            id: id,
            replace: true,
            row: updateData
        };

        $('#inventor-bootstrap-table').bootstrapTable('updateByUniqueId', rowData);
        updateInventorData();
        $.modal.msgSuccess("发明人修改成功!");
    }

    // 更新发明人数据到隐藏字段
    function updateInventorData() {
        var data = $('#inventor-bootstrap-table').bootstrapTable("getData");

        // 确保data是数组
        if (!Array.isArray(data)) {
            console.warn('Bootstrap table data is not an array in updateInventorData:', typeof data, data);
            data = [];
        }

        $("#inventorData").val(JSON.stringify(data));

        // 更新全局inventors数组以保持兼容性
        inventors = data.map(function(item) {
            return {
                order: item.order,
                type: item.type,
                code: item.code,
                name: item.name,
                deptCode: item.deptCode,
                dept: item.dept,
                title: item.title,
                position: item.position,
                idcard: item.idcard,
                remark: item.remark
            };
        });
    }

    // 表单提交
    function submitHandler() {
        // 确保启用所有验证规则
        var validator = $("#form-applyBaseinfo-edit").validate();
        validator.settings.ignore = "";

        // 执行表单验证
        if (!validator.form()) {
            $.modal.alertWarning("表单中有必填项未填写，请检查!");
            return;
        }

        // 验证申请人
        if (applicants.length === 0) {
            $.modal.alertWarning("请至少添加一名申请人！");
            return;
        }

        // 验证申请人必填项
        for (var i = 0; i < applicants.length; i++) {
            var applicant = applicants[i];
            if (!applicant.orgName) {
                $.modal.alertWarning("第" + (i + 1) + "申请人名称不能为空!");
                return;
            }
            if (!applicant.address) {
                $.modal.alertWarning("第" + (i + 1) + "申请人通信地址不能为空!");
                return;
            }
            if (!applicant.zipcode) {
                $.modal.alertWarning("第" + (i + 1) + "申请人邮政编码不能为空!");
                return;
            }
            if (!applicant.phone) {
                $.modal.alertWarning("第" + (i + 1) + "申请人联系电话不能为空!");
                return;
            }
        }

        // 验证发明人 - 从Bootstrap Table获取数据
        var inventorTableData = $('#inventor-bootstrap-table').bootstrapTable("getData");
        if (inventorTableData.length === 0) {
            $.modal.alertWarning("请至少添加一名发明人！");
            return;
        }

        // 验证是否勾选确认
        if (!$("#inventor_confirm").prop("checked")) {
            $.modal.alertWarning("请勾选发明人确认承诺！");
            return;
        }

        // 收集表单数据
        var data = $("#form-applyBaseinfo-edit").serializeArray();
        var formData = {};
        $.each(data, function (index, field) {
            // 检查是否是多选框字段（标签和预计效果）
            if (field.name === 'label' || field.name === 'resultExpect') {
                // 如果已存在该字段，将值追加到数组中
                if (formData[field.name]) {
                    if ($.isArray(formData[field.name])) {
                        formData[field.name].push(field.value);
                    } else {
                        formData[field.name] = [formData[field.name], field.value];
                    }
                } else {
                    formData[field.name] = [field.value];
                }
            } else {
                // 普通字段直接赋值
                formData[field.name] = field.value;
            }
        });
        // 将多选框数组转换为逗号分隔的字符串
        if (formData['label'] && $.isArray(formData['label'])) {
            formData['label'] = formData['label'].join(',');
        }
        if (formData['resultExpect'] && $.isArray(formData['resultExpect'])) {
            formData['resultExpect'] = formData['resultExpect'].join(',');
        }

        // 添加申请人数据 - 逐个添加申请人属性
        for (var i = 0; i < applicants.length; i++) {
            var applicant = applicants[i];
            formData["sqrList[" + i + "].xh"] = applicant.index;
            formData["sqrList[" + i + "].legalId"] = applicant.orgCode;
            formData["sqrList[" + i + "].legalName"] = applicant.orgName;
            formData["sqrList[" + i + "].mailAddress"] = applicant.address;
            formData["sqrList[" + i + "].postOffice"] = applicant.zipcode;
            formData["sqrList[" + i + "].lxrMoney"] = applicant.contact;
            formData["sqrList[" + i + "].lxrPhone"] = applicant.phone;
        }

        // 添加发明人数据 - 从Bootstrap Table获取数据
        for (var j = 0; j < inventorTableData.length; j++) {
            var inventor = inventorTableData[j];
            formData["ryxxList[" + j + "].ryxh"] = inventor.order;
            formData["ryxxList[" + j + "].rylx"] = inventor.type;
            formData["ryxxList[" + j + "].empId"] = inventor.code;
            formData["ryxxList[" + j + "].empName"] = inventor.name;
            formData["ryxxList[" + j + "].deptCode"] = inventor.deptCode;
            formData["ryxxList[" + j + "].deptName"] = inventor.dept;
            formData["ryxxList[" + j + "].postTitle"] = inventor.title;
            formData["ryxxList[" + j + "].postLevel"] = inventor.position;
            formData["ryxxList[" + j + "].idCard"] = inventor.idcard;
            formData["ryxxList[" + j + "].contentMemo"] = inventor.remark;
        }

        // 判断是否为流程退回的情况
        var isWorkflowReturn = $.common.isNotEmpty(workFlow) && $.common.isNotEmpty(workFlow.taskId);
        var submitUrl = isWorkflowReturn ? "kizl/applyBaseinfo/doSubmit" : "kizl/applyBaseinfo/doSave";

        if (!isWorkflowReturn) {
            // 新申请的情况
            formData.flowStatus = "active";
            formData.start = "1"; // 设置启动流程标志
        }
        // 流程退回的情况不设置flowStatus和start参数

        $.modal.confirm("确定要提交申请吗？", function () {
            // 发送请求
            $.ajax({
                url: ctx + submitUrl,
                type: "post",
                dataType: "json",
                data: formData,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    if (result.code === 0) {
                        $.operate.alertSuccessTabCallback(result);
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                    }
                },
                error: function () {
                    $.modal.alertError("提交失败，请稍后重试");
                    $.modal.closeLoading();
                }
            });
        });
    }

    var workFlow = /*[[${applyBaseinfo.workFlow}]]*/ null;

    // 暂存功能 - 不校验必填项
    function saveDraft() {
        // 暂时禁用所有验证规则
        var validator = $("#form-applyBaseinfo-edit").validate();
        validator.settings.ignore = "*";

        // 收集表单数据
        var data = $("#form-applyBaseinfo-edit").serializeArray();
        var formData = {};

        $.each(data, function (index, field) {
            // 检查是否是多选框字段（标签和预计效果）
            if (field.name === 'label' || field.name === 'resultExpect') {
                // 如果已存在该字段，将值追加到数组中
                if (formData[field.name]) {
                    if ($.isArray(formData[field.name])) {
                        formData[field.name].push(field.value);
                    } else {
                        formData[field.name] = [formData[field.name], field.value];
                    }
                } else {
                    formData[field.name] = [field.value];
                }
            } else {
                // 普通字段直接赋值
                formData[field.name] = field.value;
            }
        });

        // 将多选框数组转换为逗号分隔的字符串
        if (formData['label'] && $.isArray(formData['label'])) {
            formData['label'] = formData['label'].join(',');
        }
        if (formData['resultExpect'] && $.isArray(formData['resultExpect'])) {
            formData['resultExpect'] = formData['resultExpect'].join(',');
        }

        // 添加申请人数据 - 逐个添加申请人属性
        for (var i = 0; i < applicants.length; i++) {
            var applicant = applicants[i];
            formData["sqrList[" + i + "].xh"] = applicant.index;
            formData["sqrList[" + i + "].legalId"] = applicant.orgCode;
            formData["sqrList[" + i + "].legalName"] = applicant.orgName;
            formData["sqrList[" + i + "].mailAddress"] = applicant.address;
            formData["sqrList[" + i + "].postOffice"] = applicant.zipcode;
            formData["sqrList[" + i + "].lxrMoney"] = applicant.contact;
            formData["sqrList[" + i + "].lxrPhone"] = applicant.phone;
        }

        // 添加发明人数据 - 从Bootstrap Table获取数据
        var inventorTableDataDraft = $('#inventor-bootstrap-table').bootstrapTable("getData");
        for (var j = 0; j < inventorTableDataDraft.length; j++) {
            var inventor = inventorTableDataDraft[j];
            formData["ryxxList[" + j + "].ryxh"] = inventor.order;
            formData["ryxxList[" + j + "].rylx"] = inventor.type;
            formData["ryxxList[" + j + "].empId"] = inventor.code;
            formData["ryxxList[" + j + "].empName"] = inventor.name;
            formData["ryxxList[" + j + "].deptCode"] = inventor.deptCode;
            formData["ryxxList[" + j + "].deptName"] = inventor.dept;
            formData["ryxxList[" + j + "].postTitle"] = inventor.title;
            formData["ryxxList[" + j + "].postLevel"] = inventor.position;
            formData["ryxxList[" + j + "].idCard"] = inventor.idcard;
            formData["ryxxList[" + j + "].contentMemo"] = inventor.remark;
        }

        // 判断是否为流程退回的情况
        var isWorkflowReturn = $.common.isNotEmpty(workFlow) && $.common.isNotEmpty(workFlow.taskId);

        if (!isWorkflowReturn) {
            // 新申请的情况，设置为草稿状态
            formData.flowStatus = "draft";
            formData.start = "0"; // 不启动流程
        }
        // 流程退回的情况不设置flowStatus和start参数

        // 发送请求
        $.ajax({
            url: ctx + "kizl/applyBaseinfo/doSave",
            type: "post",
            dataType: "json",
            data: formData,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code === 0) {
                    if ($.common.isNotEmpty(result.data)) {
                        if ($.common.isNotEmpty(result.data.applyId)) {
                            $("#applyId").val(result.data.applyId);
                        }
                    }
                    $.modal.alertSuccess(result.msg, function () {
                        parent.$.modal.close();
                        parent.$.table.refresh();
                    });
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            },
            complete: function () {
                // 恢复验证规则
                validator.settings.ignore = "";
            }
        });
    }

    //技术领域
    function choiceTechArea() {
        var url = ctx + "jsmm/technologySecret/selectTechAreaTree?selectType=M";
        url += "&values=" + $("#technologyFieldId").val();
        var options = {
            title: '选择',
            width: "380",
            height: '550',
            url: url,
            callBack: choiceTechAreaCallback
        };
        $.modal.openOptions(options);
    }

    function choiceTechAreaCallback(index, layero) {
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        var body = layer.getChildFrame('body', index);
        layero.find("iframe")[0].contentWindow.saveCheck();
        $("#techArea").val(body.find('#treeId').val());
        $("#techAreaName").val(body.find('#treeName').val());
        layer.close(index);
    }

</script>
</html>

