<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境内专利_申请_基本信息列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">专利名称:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="applyNameLike" placeholder="支持模糊查询"/>
				    </div>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>

			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kizl/applyBaseinfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
				detailUrl: prefix + "/queryDT?businessId={id}",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "专利申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'applyId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'serialNum',
                    title: '流水号'
                },
                {
                    field: 'applyName',
                    title: '申报名称'
                },
				{
					field: 'applyDate',
					title: '申请日期'
				},
                {
                    title: '操作',
                    align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						if ('draft' == row.flowStatus) {
							actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.applyId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
							actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.applyId + '\')"><i class="fa fa-remove"></i>删除</a>');
						} else {
							actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.applyId + '\')"><i class="fa fa-eye"></i>查看</a> ');
						}
						return actions.join('');
					}
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>