<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发明人信息')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-inventor-edit">
        <div class="panel-body">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">类型：</label>
                <div class="col-sm-10">
                    <label class="radio-inline">
                        <input type="radio" name="type" value="1" th:checked="${memberType == '0'}" onclick="onTypeChange('1')"> 集团内
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="type" value="2" th:checked="${memberType == '1'}" onclick="onTypeChange('2')"> 集团外
                    </label>
                </div>
            </div>
            
            <!-- 序号自动生成，不需要用户输入 -->
            <input type="hidden" name="order" th:value="${order}">

            <input id="code" name="code" th:value="${code}" type="hidden">
            <input id="name" name="name" th:value="${name}" type="hidden">
            <input id="deptCode" name="deptCode" th:value="${deptCode}" type="hidden">
            <input id="dept" name="dept" th:value="${dept}" type="hidden">
            <input id="inventorId" name="inventorId" th:value="${id}" type="hidden">

            <!--集团内-->
            <div id="within" style="display: none;">
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">工号：</label>
                    <div class="col-sm-6">
                        <div class="input-group">
                            <input id="codeInside" th:value="${code}" class="form-control" type="text"
                                   placeholder="请选择人员" readonly>
                            <span class="input-group-btn">
                                <button class="btn btn-primary" type="button" onclick="chooseInventor()">选择人员</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">姓名：</label>
                    <div class="col-sm-8">
                        <input id="nameInside" th:value="${name}" class="form-control" type="text"
                               placeholder="请选择人员" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">人员组织：</label>
                    <div class="col-sm-8">
                        <input id="deptInside" th:value="${dept}" class="form-control"
                               placeholder="请选择人员" type="text" readonly>
                    </div>
                </div>
            </div>

            <!--集团外-->
            <div id="outside" style="display: none;">
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">姓名：</label>
                    <div class="col-sm-8">
                        <input id="nameOutside" th:value="${name}" class="form-control" type="text">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">人员组织：</label>
                    <div class="col-sm-8">
                        <input id="deptOutside" th:value="${dept}" class="form-control" type="text">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label">职称：</label>
                <div class="col-sm-4">
                    <div th:include="/component/select :: init(id='title', name='title', businessType='KIZL', dictCode='POST_TITLE', value=${title}, isfirst=true)"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-2 control-label">岗位：</label>
                <div class="col-sm-4">
                    <div th:include="/component/select :: init(id='position', name='position', businessType='KIZL', dictCode='POST_LEVEL', value=${position}, isfirst=true)"></div>
                </div>
            </div>
            
            <div class="form-group" id="idcardGroup">
                <label class="col-sm-2 control-label">身份证号：</label>
                <div class="col-sm-6">
                    <input name="idcard" th:value="${idcard}" class="form-control" type="text" maxlength="18"
                           placeholder="第一发明人必填，18位长度">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" th:utext="${remark}" class="form-control"
                              style="width: 100%; height: 100px;"></textarea>
                </div>
            </div>
        </div>
    </form>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kizl/applyBaseinfo";
    var memberType = /*[[${memberType}]]*/ null;
    var editType = /*[[${editType}]]*/ null;
    var id = /*[[${id}]]*/ null;

    $(function() {
        // 初始化表单验证
        $("#form-inventor-edit").validate({
            focusCleanup: true,
            // 关闭键盘输入时的实时校验
            onkeyup: false,
            // 关闭失去焦点时的校验，只在提交时校验
            onfocusout: false,
            // 忽略隐藏字段的验证
            ignore: ":hidden",
            rules: {
                type: {
                    required: true
                },
                idcard: {
                    maxlength: 18,
                    minlength: 18
                }
            },
            messages: {
                type: {
                    required: "请选择发明人类型"
                },
                idcard: {
                    maxlength: "身份证号必须是18位",
                    minlength: "身份证号必须是18位"
                }
            }
        });

        // 初始化数据回显
        initFormData();
        
        // 初始化显示状态
        initFormDisplay();
        
        // 设置动态必填验证
        setupDynamicValidation();
    });

    // 初始化数据回显
    function initFormData() {
        // 获取URL参数中的type值，如果没有则使用memberType
        var urlParams = new URLSearchParams(window.location.search);
        var typeFromUrl = urlParams.get('type');
        
        var typeValue = '1'; // 默认集团内
        
        if (typeFromUrl) {
            // 如果URL中有type参数，直接使用
            typeValue = typeFromUrl;
        } else if (memberType == '0' || memberType == '1') {
            // 否则根据memberType设置
            typeValue = memberType == '0' ? '1' : '2';
        }
        
        // 设置类型单选按钮
        $("input[name='type'][value='" + typeValue + "']").prop('checked', true);
        
        // 如果是编辑模式，回显数据到相应的字段
        if (editType == 'E') {
            var currentType = $("input[name='type']:checked").val();
            if (currentType == '1') {
                // 集团内数据回显
                var code = /*[[${code}]]*/ '';
                var name = /*[[${name}]]*/ '';
                var dept = /*[[${dept}]]*/ '';
                
                $("#codeInside").val(code);
                $("#nameInside").val(name);
                $("#deptInside").val(dept);
            } else if (currentType == '2') {
                // 集团外数据回显
                var name = /*[[${name}]]*/ '';
                var dept = /*[[${dept}]]*/ '';
                
                $("#nameOutside").val(name);
                $("#deptOutside").val(dept);
            }
        }
    }

    // 初始化表单显示状态
    function initFormDisplay() {
        var currentType = $("input[name='type']:checked").val();
        if (currentType == "1") {
            $("#within").show();
            $("#outside").hide();
        } else if (currentType == "2") {
            $("#within").hide();
            $("#outside").show();
        }
    }

    // 设置动态必填验证
    function setupDynamicValidation() {
        var currentType = $("input[name='type']:checked").val();
        var validator = $("#form-inventor-edit").validate();
        
        // 先移除所有相关字段的required属性和验证规则
        $("#codeInside, #nameInside, #deptInside, #nameOutside, #deptOutside")
            .removeAttr('required')
            .removeClass('error')
            .each(function() {
                // 移除验证规则
                if (validator.settings.rules[this.name]) {
                    delete validator.settings.rules[this.name];
                }
                if (validator.settings.messages[this.name]) {
                    delete validator.settings.messages[this.name];
                }
            });
        
        if (currentType == "1") {
            // 集团内必填项
            validator.settings.rules.codeInside = {
                required: true
            };
            validator.settings.messages.codeInside = {
                required: "请选择集团内人员"
            };
            
            validator.settings.rules.nameInside = {
                required: true
            };
            validator.settings.messages.nameInside = {
                required: "请选择集团内人员"
            };
            
            validator.settings.rules.deptInside = {
                required: true
            };
            validator.settings.messages.deptInside = {
                required: "请选择集团内人员"
            };
        } else if (currentType == "2") {
            // 集团外必填项
            validator.settings.rules.nameOutside = {
                required: true
            };
            validator.settings.messages.nameOutside = {
                required: "请输入集团外人员姓名"
            };
            
            validator.settings.rules.deptOutside = {
                required: true
            };
            validator.settings.messages.deptOutside = {
                required: "请输入集团外人员组织"
            };
        }
    }

    // 类型切换处理
    function onTypeChange(val) {
        // 清除所有验证错误状态
        $("#form-inventor-edit").validate().resetForm();
        
        if (val == "1") {
            $("#within").show();
            $("#outside").hide();
            // 清空集团外的数据
            $("#nameOutside").val('');
            $("#deptOutside").val('');
        } else {
            $("#within").hide();
            $("#outside").show();
            // 清空集团内的数据
            $("#codeInside").val('');
            $("#nameInside").val('');
            $("#deptInside").val('');
            $("#code").val('');
            $("#deptCode").val('');
        }
        memberType = val;
        
        // 重新设置动态验证
        setupDynamicValidation();
    }

    // 选择集团内人员
    function chooseInventor() {
        choiceUser('codeInside', 'nameInside', 'S', null, 'getOrg');
    }

    // 获取组织信息回调
    function getOrg(userCode, userName) {
        var url = ctx + "jsmm/technologyMember/getOrg?userCode=" + userCode;
        $.ajax({
            url: url,
            type: "get",
            async: false,
            dataType: "json",
            contentType: 'application/json;charset=UTF-8',
            success: function (result) {
                if (result && result.data && !$.common.isEmpty(result.data)) {
                    var object = result.data;
                    $("#deptCode").val(object.orgPathCode || '');
                    $("#deptInside").val(object.orgPathName || '');
                } else {
                    $.modal.alertError('未找到该人员的组织信息！');
                    $("#deptCode").val('');
                    $("#deptInside").val('');
                }
            },
            error: function() {
                $.modal.alertError('获取组织信息失败！');
                $("#deptCode").val('');
                $("#deptInside").val('');
            }
        });
    }

    // 表单提交处理
    function submitHandler() {
        var currentType = $("input[name='type']:checked").val();
        
        // 检查是否选择了类型
        if (!currentType) {
            $.modal.alertWarning("请选择发明人类型！");
            return;
        }
        
        // 根据类型设置隐藏字段的值
        if (currentType == "1") {
            // 集团内
            $("#code").val($("#codeInside").val());
            $("#name").val($("#nameInside").val());
            $("#dept").val($("#deptInside").val());
        } else {
            // 集团外
            $("#code").val('');
            $("#name").val($("#nameOutside").val());
            $("#deptCode").val('');
            $("#dept").val($("#deptOutside").val());
        }
        
        // 验证第一发明人身份证号（序号为1时）
        var order = $("input[name='order']").val();
        var idcard = $("input[name='idcard']").val();
        if (order == "1" && !idcard) {
            $.modal.alertWarning("第一发明人必须填写身份证号！");
            return;
        }
        
        // 验证身份证号格式
        if (idcard && idcard.length > 0 && idcard.length != 18) {
            $.modal.alertWarning("身份证号必须是18位！");
            return;
        }
        
        // 手动验证必填项
        var isValid = true;
        var errorMessage = "";
        
        if (currentType == "1") {
            // 集团内验证
            if (!$("#codeInside").val()) {
                isValid = false;
                errorMessage = "请选择集团内人员";
            } else if (!$("#nameInside").val()) {
                isValid = false;
                errorMessage = "请选择集团内人员";
            } else if (!$("#deptInside").val()) {
                isValid = false;
                errorMessage = "请选择集团内人员";
            }
        } else if (currentType == "2") {
            // 集团外验证
            if (!$("#nameOutside").val()) {
                isValid = false;
                errorMessage = "请输入集团外人员姓名";
            } else if (!$("#deptOutside").val()) {
                isValid = false;
                errorMessage = "请输入集团外人员组织";
            }
        }
        
        if (!isValid) {
            $.modal.alertWarning(errorMessage);
            return;
        }
        
        // 执行表单验证
        if ($("#form-inventor-edit").valid()) {
            try {
                // 生成唯一ID（如果是新增）
                if (!$("#inventorId").val() || editType != 'E') {
                    $("#inventorId").val(Date.now().toString(36) + Math.random().toString(36).substr(2));
                }
                
                var formData = new FormData(document.getElementById('form-inventor-edit'));
                
                // 调用父页面回调函数
                if (editType == 'E') {
                    parent.editInventorCallback(formData, id);
                } else {
                    parent.addInventorCallback(formData);
                }
                $.modal.close();
            } catch (error) {
                console.error('Error creating FormData:', error);
                $.modal.alertError("表单数据处理失败：" + error.message);
            }
        } else {
            $.modal.alertWarning("请填写必填项！");
        }
    }
</script>
</body>
</html> 