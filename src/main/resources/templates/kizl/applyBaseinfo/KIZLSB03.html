<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_主管部门领导')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        <!-- 嵌入申请信息（只读） -->
        <th:block th:include="kizl/applyBaseinfo/applyInfoReadonly :: applyInfoReadonly"></th:block>
        <!-- 审查信息 -->
        <th:block th:include="kizl/applyBaseinfo/applyInfoReviewReadonly :: applyInfoReadonly"></th:block>

        <th:block th:include="component/expertReview :: init(bizId=*{applyId},isCkFS=true,moduleCode='kizl_zlps')"></th:block>
        <!-- 工作流信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow},needComment=true)" />
    </form>
    <!--按钮区-->
    <th:block th:include="kizl/wfKizlButton :: init(workFlow=${applyBaseinfo.workFlow})" />
    <!--按钮区-->
</div>

<th:block th:include="include :: datetimepicker-js"/>
<!-- 引入KIZL通用JS -->
<script th:src="@{/kizl/kizl-common.js}"></script>
<script th:inline="javascript">
    $(function () {
        // 使用通用方法初始化表单验证
        KizlCommon.initFormValidation();
        var fromNo = /*[[${applyBaseinfo.fromNo}]]*/ null;
        KizlCommon.getSource(fromNo);
    });
</script>
</body>
</html>
