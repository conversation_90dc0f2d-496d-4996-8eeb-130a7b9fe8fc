<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('申请人选择')" />
<th:block th:include="include :: ztree-css" />
</head>
<style>
body {
	height: auto;
	font-family: "Microsoft YaHei";
}

button {
	font-family: "SimSun", "Helvetica Neue", Helvetica, Arial;
}
</style>
<body class="hold-transition box box-main">
	<input id="treeId" name="treeId" type="hidden" />
	<input id="treeName" name="treeName" type="hidden" />
	<input id="mailAddress" name="mailAddress" type="hidden" />
	<input id="postOffice" name="postOffice" type="hidden" />
	<input id="lxrMoney" name="lxrMoney" type="hidden" />
	<input id="lxrPhone" name="lxrPhone" type="hidden" />
	<div class="wrapper">
		<div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
			<label id="btnShow" title="显示搜索" style="display: none;">︾</label> <label
				id="btnHide" title="隐藏搜索">︽</label>
		</div>
		<div class="treeSearchInput" id="search">
			<label for="keyword">关键字：</label><input type="text" class="empty"
				id="keyword" maxlength="50">
			<button class="btn" id="btn" onclick="$.tree.searchNode()">
				搜索</button>
		</div>
		<div class="treeExpandCollapse" style="float: none;">
		</div>
	
		<div id="tree" class="ztree ztree-border"></div>
	</div>
	
	<th:block th:include="include :: baseJs" />
	<th:block th:include="include :: ztree-js" />
	<script th:inline="javascript">
	
	var values;
	
	$('.treeExpandCollapse input').on('ifChanged', function(obj){
		var type = $(this).val();
		var checked = obj.currentTarget.checked;
		if (type == 1) {
		    if (checked) {
		        $._tree.expandAll(true);
		    } else {
		        $._tree.expandAll(false);
		    }
		} else if (type == "2") {
		    if (checked) {
		        $._tree.checkAllNodes(true);
		    } else {
		        $._tree.checkAllNodes(false);
		    }
		} else if (type == "3") {
		    if (checked) {
		        $._tree.setting.check.chkboxType = { "Y": "ps", "N": "ps" };
		    } else {
		        $._tree.setting.check.chkboxType = { "Y": "", "N": "" };
		    }
		}
	});
	
	$(function() {
		var url= ctx + "kizl/applyBaseinfo/tableData";
		var selectedMulti=false;
		var enable=false;
		var options = {
	        url: url,
	        expandLevel: 1,
	        onClick: zOnClick,
	        view:{selectedMulti:selectedMulti},
			check:{enable:enable,chkboxType:  { "Y": "", "N": "" }},
			async:{
				enable: true,
				type: "get",
				url: ctx + "kizl/applyBaseinfo/tableData",
				otherParam:{"values":function(){return values;}},
				dataFilter: function(treeId, parentNode, resData){
					return resData;
				}
			},
			onAsyncSuccess : zTreeOnAsyncSuccess
	    };
		$.tree.init(options);
	});
	
	// 点击名称
	function zOnClick(event, treeId, treeNode) {
	    var treeId = treeNode.id;
	    var treeName = treeNode.name;
	    $("#treeId").val(treeId);
	    $("#treeName").val(treeName);
	    $.ajax({
	    	url: ctx + "kizl/applyBaseinfo/lxrData/"+treeId,
	  		type:"GET",
	  		dataType:"json",
	  		success:function(result){
  	  			if (result && result.code === 0 && result.data) {
  	  				var data = result.data;
  	  				$("#mailAddress").val(data.mailAddress || "");
  	  				$("#postOffice").val(data.postOffice || "");
  	  				$("#lxrMoney").val(data.lxrMoney || "");
  	  				$("#lxrPhone").val(data.lxrPhone || "");
  	  			} else {
  	  				// 清空或设置默认值
  	  				$("#mailAddress").val("");
  	  				$("#postOffice").val("");
  	  				$("#lxrMoney").val("");
  	  				$("#lxrPhone").val("");
  	  			}
	  		},
	  		error:function(jqXHR, textStatus, errorThrown) {
	  	    	// 清空或设置默认值
	  	    	$("#mailAddress").val("");
	  	    	$("#postOffice").val("");
	  	    	$("#lxrMoney").val("");
	  	    	$("#lxrPhone").val("");
	  	    }
		});
	}
	
	function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
		if (!$._tree) {
			alert("error!");
			return;
		}
		var selectedNode = $._tree.getSelectedNodes();
		var nodes = $._tree.getNodes();
		$._tree.expandNode(nodes[0], true);
	}
	
	// 复选框
	function zonCheck(event, treeId, treeNode){
		console.log("1");
	}
	
	function saveCheck(){
		var callback = [[${callback}]];
		if(callback != null && callback != ''){
			try {
				// 使用eval可能有风险，尝试使用更安全的方式
				if (typeof parent[callback] === 'function') {
					parent[callback]($("#treeId").val(), $("#treeName").val());
				} else {
					// 尝试使用eval方式（兼容旧代码）
					parent.eval(callback+'("'+$("#treeId").val()+'","'+$("#treeName").val()+'")');
				}
			} catch (e) {
				console.error("回调执行错误: ", e);
			}
		}
	}
	
	// 添加提交处理函数
	function submitHandler() {
		// 从URL中获取回调函数参数
		var url = window.location.href;
		var callbackParam = getParameterByName('callback', url);
		var codeIdParam = getParameterByName('codeId', url);
		var nameIdParam = getParameterByName('nameId', url);
		
		// 获取选中的值
		var id = $("#treeId").val();
		var name = $("#treeName").val();
		var mailAddress = $("#mailAddress").val() || "";
		var postOffice = $("#postOffice").val() || "";
		var lxrMoney = $("#lxrMoney").val() || "";
		var lxrPhone = $("#lxrPhone").val() || "";
		
		if (!id || !name) {
			$.modal.alertWarning("请先选择一个单位");
			return;
		}
		
		try {
			// 执行回调
			if (callbackParam) {
				// 确保父窗口有这个函数
				if (typeof parent[callbackParam] === 'function') {
					parent[callbackParam](id, name, codeIdParam, nameIdParam);
					$.modal.close();
				} else {
					console.error("父窗口没有" + callbackParam + "函数");
					// 使用旧方式尝试
					saveCheck();
					$.modal.close();
				}
			} else {
				// 兼容旧方式
				saveCheck();
				$.modal.close();
			}
		} catch (e) {
			console.error("执行回调出错: ", e);
			$.modal.alertError("选择单位时发生错误: " + e.message);
		}
	}
	
	// 获取URL参数
	function getParameterByName(name, url) {
		if (!url) url = window.location.href;
		name = name.replace(/[\[\]]/g, '\\$&');
		var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
		var results = regex.exec(url);
		if (!results) return null;
		if (!results[2]) return '';
		return decodeURIComponent(results[2].replace(/\+/g, ' '));
	}
	
	function closeItem() {
		$.modal.close();
	}
	</script>
</body>
</html>
