<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_详情')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: datetimepicker-css"/>
    <script th:src="@{/kizl/kizl-attachment-utils.js}"></script>
    <script th:src="@{/kizl/kizl-common.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-applyBaseinfo-edit" th:object="${applyBaseinfo}">
        <input id="applyId" name="applyId" th:field="*{applyId}" type="hidden">
        <!-- 嵌入申请信息（只读） -->
        <th:block th:include="kizl/applyBaseinfo/applyInfoReadonly :: applyInfoReadonly"></th:block>
        <!-- 审查信息 -->
        <th:block th:include="kizl/applyBaseinfo/applyInfoReviewReadonly :: applyInfoReadonly"></th:block>
        <!-- 工作流信息（不显示意见输入） -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow},needComment=false)"/>
    </form>
    <th:block th:with="businessId=${businessId}">
        <!-- 流程跟踪3 -->
        <p>
            <th:block th:include="component/wfCommentList4 :: init(businessId=${businessId},variable=${applyBaseinfo.workFlow?.variable})"/>
        </p>
        <!-- 流程跟踪 -->
        <p>
            <th:block th:include="component/wfCommentList :: init(businessId=${businessId})"/>
        </p>
    </th:block>
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-danger" onclick="closeItem()">
            <i class="fa fa-reply-all"></i>
            关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: datetimepicker-js"/>
<script>
$(function() {
    // 初始化附件名称映射
    KizlAttachmentUtils.init(function() {
        KizlAttachmentUtils.updateCommonLabels();
    });

    var fromNo = /*[[${applyBaseinfo.fromNo}]]*/ null;
    KizlCommon.getSource(fromNo);

});
</script>
</body>
</html>
