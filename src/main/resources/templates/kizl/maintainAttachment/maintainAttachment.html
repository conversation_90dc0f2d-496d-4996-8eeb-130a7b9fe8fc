<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('专利维护_附件信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-maintainAttachment-edit" th:object="${maintainAttachment}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="attId" th:field="*{attId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">附件名称：</label>
                                <div class="col-sm-4">
                                    <input name="attName" th:field="*{attName}" class="form-control" type="text" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">附件代码：</label>
                                <div class="col-sm-4">
                                    <input name="attCode" th:field="*{attCode}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
        <div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>&nbsp;
            <button type="button" class="btn btn-danger" onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返回
            </button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kizl/maintainAttachment";

        $("#form-maintainAttachment-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/save", $('#form-maintainAttachment-edit').serialize());
            }
        }

    </script>
</body>
</html>