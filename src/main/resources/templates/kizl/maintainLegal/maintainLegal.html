<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('共同申请人维护')" />
    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="from01" th:object="${maintainLegal}">
        <input name="legalId" id="legalId" th:value="*{legalId}" type="hidden">
        <!--框-->
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">管理单位：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/selectOrg :: init(orgCodeId='manageDept', orgNameId='manageDeptName', value=*{manageDept}, isrequired=true)"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">单位名称：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/selectOrg :: init(orgCodeId='extra1', orgNameId='extra2', value=*{extra1}, isrequired=true)"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">通信地址：</label>
                            <div class="col-sm-6">
                                <input name="mailAddress" id="mailAddress" class="form-control" th:value="*{mailAddress}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">邮政编码：</label>
                            <div class="col-sm-6">
                                <input name="postOffice" id="postOffice" class="form-control" th:value="*{postOffice}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">付费联系人：</label>
                            <div class="col-sm-6">
                                <input name="lxrMoney" id="lxrMoney" class="form-control" th:value="*{lxrMoney}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">联系电话：</label>
                            <div class="col-sm-6">
                                <input name="lxrPhone" id="lxrPhone" class="form-control" th:value="*{lxrPhone}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">邮箱：</label>
                            <div class="col-sm-6">
                                <input name="email" id="email" class="form-control" th:value="*{email}"/>
                            </div>
                        </div>
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--框end-->
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary"
                onclick="submitHandler()">
            <i class="fa fa-check"></i>保 存
        </button>
        <button type="button" class="btn btn-sm btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div><br/><br/>
<script th:inline="javascript">
    var prefix = ctx + "kizl/maintainLegal";
    //验证
    $("#from01").validate({
        focusCleanup : true
    });

    /*保存*/
    function submitHandler() {
        if ($.validate.form()) {
            var config = {
                url: prefix + "/save",
                type: "post",
                dataType: "json",
                data: $('#from01').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    var str = result.msg;
                    if(str.indexOf("重复")!= -1){
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                    }else{
                        $.operate.alertSuccessTabCallback(result);
                    }
                }
            };
            $.ajax(config)
        }
    }
</script>
</body>
</html>