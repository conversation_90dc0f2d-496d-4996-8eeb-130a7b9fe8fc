<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('共同申请人维护列表')" />
	<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
	<div class="row">
		<div class="col-sm-12 search-collapse">
			<form id="formId">
				<div class="select-list">
					<ul>
						<li>
							<label>管理单位名称：</label>
							<input type="text" name="manageDeptName"/>
						</li>
						<li>
							<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
						</li>
					</ul>
				</div>
			</form>
		</div>

		<div class="btn-group-sm" id="toolbar" role="group">
			<a class="btn btn-success" onclick="$.operate.addTab()" >
				<i class="fa fa-plus"></i> 新增
			</a>
			<a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
				<i class="fa fa-edit"></i> 修改
			</a>
			<a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
				<i class="fa fa-remove"></i> 删除
			</a>
			<!-- <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a> -->
		</div>
		<div class="col-sm-12 select-table table-striped">
			<table id="bootstrap-table"></table>
		</div>
	</div>
</div>

<script th:inline="javascript">
	var prefix = ctx + "kizl/maintainLegal";

	$(function() {
		var options = {
			url: prefix + "/list",
			createUrl: prefix + "/add",
			updateUrl: prefix + "/edit/{id}",
			removeUrl: prefix + "/remove",
			exportUrl: prefix + "/export",
			modalName: "共同申请人维护",
			columns: [{
				checkbox: true
			},
				{
					field: 'legalId',
					title: '主键',
					visible: false
				},
				{
					field: 'manageDeptName',
					title: '管理单位'
				},
				{
					field: 'deptFirstName',
					title: '单位名称'
				},
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.legalId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
						actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.legalId + '\')"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
					}
				}]
		};
		$.table.init(options);
	});
</script>
</body>
</html>