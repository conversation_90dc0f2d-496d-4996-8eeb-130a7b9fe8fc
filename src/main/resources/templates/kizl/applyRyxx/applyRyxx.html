<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境内专利_申请_人员信息信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-applyRyxx-edit" th:object="${applyRyxx}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="ryxxId" th:field="*{ryxxId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">申请主键：</label>
                                <div class="col-sm-8">
                                    <input name="applyId" th:field="*{applyId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">专利主键：</label>
                                <div class="col-sm-8">
                                    <input name="patentId" th:field="*{patentId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">序号：</label>
                                <div class="col-sm-8">
                                    <input name="ryxh" th:field="*{ryxh}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">类型 01-宝武人员 02-非宝武人员：</label>
                                <div class="col-sm-8">
                                    <input name="rylx" th:field="*{rylx}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">所属法人：</label>
                                <div class="col-sm-8">
                                    <input name="legalId" th:field="*{legalId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">工号：</label>
                                <div class="col-sm-8">
                                    <input name="empId" th:field="*{empId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">姓名：</label>
                                <div class="col-sm-8">
                                    <input name="empName" th:field="*{empName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">人员组织：</label>
                                <div class="col-sm-8">
                                    <input name="deptCode" th:field="*{deptCode}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">人员组织名称：</label>
                                <div class="col-sm-8">
                                    <input name="deptName" th:field="*{deptName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">职称：</label>
                                <div class="col-sm-8">
                                    <input name="postTitle" th:field="*{postTitle}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">岗位：</label>
                                <div class="col-sm-8">
                                    <input name="postLevel" th:field="*{postLevel}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">岗位名称：</label>
                                <div class="col-sm-8">
                                    <input name="postName" th:field="*{postName}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">贡献系数：</label>
                                <div class="col-sm-8">
                                    <input name="gxxs" th:field="*{gxxs}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">身份证号：</label>
                                <div class="col-sm-8">
                                    <input name="idCard" th:field="*{idCard}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">备注：</label>
                                <div class="col-sm-8">
                                    <input name="contentMemo" th:field="*{contentMemo}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段1：</label>
                                <div class="col-sm-8">
                                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段2：</label>
                                <div class="col-sm-8">
                                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段3：</label>
                                <div class="col-sm-8">
                                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段4：</label>
                                <div class="col-sm-8">
                                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段5：</label>
                                <div class="col-sm-8">
                                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kizl/applyRyxx";

        $("#form-applyRyxx-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/save", $('#form-applyRyxx-edit').serialize());
            }
        }

    </script>
</body>
</html>