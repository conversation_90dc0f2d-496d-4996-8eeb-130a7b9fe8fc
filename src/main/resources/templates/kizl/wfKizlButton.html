<!-- 
	按钮相关
输入参数:
	canDoOther 是否能自由流转 默认 false
-->
<div th:fragment="init">
    <th:block th:with="
               	 	canDoOther=${workFlow?.extraParameters?.get('DO_OTHER')==null ? false : true}
               	 	">
		<!-- 流程跟踪3 -->
		<p>
			<th:block
					th:include="component/wfCommentList4 :: init(businessId=${workFlow?.businessId},variable=${workFlow?.variable})"/>
		</p>
		<!-- 流程跟踪 -->
		<p>
			<th:block th:include="component/wfCommentList :: init(businessId=${workFlow?.businessId})"/>
		</p>
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-primary" onclick="saveDraft()">
				<i class="fa fa-check"></i>
				暂存
			</button>
			<th:block th:if="${workFlow!=null}">
				<th:block th:if="${workFlow.currentActivity=='Manual4'}">
					<button class="btn btn btn-primary" onclick="startPS()" type="button">
						启动专家评审
					</button>
				</th:block>
				<!-- 提交 -->
				<th:block th:include="component/wfSubmit2:: init(taskId=${workFlow.taskId},callback=submitHandler, formId='form-applyBaseinfo-edit',validateUrl='kizl/workFlow/getNextSubmitWF')"/>
				<!-- 退回 -->
				<th:block th:include="component/wfReturn2 :: init(taskId=${workFlow.taskId},callback=returnHandler)"/>
				<!-- 自由流 -->
				<th:block th:include="component/wfDoOther2 :: init(businessId=${workFlow.businessId}, taskId=${workFlow.taskId}, extraParameters=${workFlow.extraParameters} )"/>
			</th:block>
			<button type="button" class="btn btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
	</th:block>
</div>