<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('我的申请')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">专利名称:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="applyNameLike" placeholder="支持模糊查询"/>
				    </div>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>

			</form>
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kizl/c";

        $(function() {
            var options = {
                url: prefix + "/d?serviceName=KIZLApplyBaseinfo&methodName=queryMyApplications",
				detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
                modalName: "我的申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'applyId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'applyName',
                    title: '专利名称',
                    sortable: true
                },
				{
					field: 'lastTime',
					title: '最新处理时间',
					formatter: function (value, row, index) {
						if (value && value.length >= 14) {
							var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
							return timeData;
						}
						return value;
					}
				},
				{
					field: 'currentActivityName',
					title: '当前状态',
					formatter: function(value, row, index) {
						return value ? value : '-';
					}
				},
				{
					field: 'currentOperator',
					title: '当前操作人',
					formatter: function(value, row, index) {
						return value ? value : '-';
					}
				},
                {
                    title: '操作',
                    align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.applyId + '\')"><i class="fa fa-eye"></i>查看</a> ');
						return actions.join('');
					}
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>