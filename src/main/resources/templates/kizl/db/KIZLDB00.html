<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('待办数量')" />
</head>
<body class="white-bg">
	<th:block th:include="include :: baseJs" />
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <table class="db">
			<th:block th:each="db : ${ei.blocks.result.rows}">
				<tr>
					<td class="td1">
						<a th:href="@{''}" th:text="${db.processName}+' / '+${db.currentActivityName}"></a>
					</td>
					<td class="td2">
						<a th:onclick="openDB([[${db.processCode}]], [[${db.currentActivity}]], [[${db.currentActivityName}]], [[${db.businessLabel}]])">
							<span class="span1" th:text="${db.todoNum}"></span>&nbsp;
							<span class="span2">份</span>&nbsp;&nbsp;
							<span>进入处理》</span>
						</a>
					</td>
				</tr>
			</th:block>
			<th:block th:include="/component/mppsDaiban :: init(moduleName='专利申请专家评审',moduleCode='kizl_zlps')"/>

			<img th:unless="${! #lists.isEmpty(ei.blocks.result.rows)}" class="noDb">
		</table>
	</div>
</body>
</html>
<script th:inline="javascript">
	function openDB(processCode, activityCode, currentActivityName, businessLabel){
		var url = ctx + 'kizl/workFlow/toPage/KIZLDB01?businessType=KIZL&processCode='+processCode+'&activityCode='+activityCode+"&businessLabel="+businessLabel;
		$.modal.openTab(currentActivityName+"-待办列表", url, true);
	}
</script>