<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('申请跟踪')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <!-- 功能说明 -->
    <div class="alert alert-info" role="alert">
        <i class="fa fa-info-circle"></i>
        <strong>申请跟踪说明：</strong>
        本功能对主管部门专利管理员开放，按照状态、单位、部门分类显示专利申请情况，并统计每个分类的数量。
    </div>
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <!--			<div class="form-group">-->
            <!--				<label class="col-sm-1 control-label">专利名称:</label>-->
            <!--				<div class="col-sm-2">-->
            <!--					<input type="text" class="form-control" name="applyNameLike" placeholder="支持模糊查询"/>-->
            <!--				</div>-->
            <!--				<label class="col-sm-1 control-label">状态:</label>-->
            <!--				<div class="col-sm-2">-->
            <!--					<input type="text" class="form-control" name="currentActivityName" placeholder="请输入状态"/>-->
            <!--				</div>-->
            <!--				<label class="col-sm-1 control-label">部门:</label>-->
            <!--				<div class="col-sm-2">-->
            <!--					<div th:include="/component/selectOrg :: init(orgCodeId='firstDeptCode', orgNameId='firstDeptName')"></div>-->
            <!--				</div>-->
            <!--				<div class="col-sm-3">-->
            <!--					<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">-->
            <!--						<i class="fa fa-search"></i>-->
            <!--						&nbsp;搜索-->
            <!--					</a>-->
            <!--					<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">-->
            <!--						<i class="fa fa-refresh"></i>-->
            <!--						&nbsp;重置-->
            <!--					</a>-->
            <!--				</div>-->
            <!--			</div>-->
        </form>
    </div>

    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kizl/c";

    $(function () {
        var options = {
            url: prefix + "/d?serviceName=KIZLApplyBaseinfo&methodName=queryApplyTracking",
            detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
            modalName: "申请跟踪",
            detailView: true,
            uniqueId:'currentActivity',
            onExpandRow : function(index, row, $detail) {
                initChildTable(index, row, $detail);
            },
            columns: [{
                field: 'currentActivity',
                title: '状态',
                formatter: function (value, row, index) {
                    var actions = [];
                    if (row.currentActivity != '' && row.currentActivity !== undefined) {
                        actions.push('<a><i class="fa fa-edit"></i>' + row.currentActivityName + '</a> ');
                        return actions.join('');
                    }
                }
            },
                {
                    field: 'gldwName',
                    title: '管理单位'
                },
                { field: 'gldwCode',
                    title: '管理单位CODE',
                    visible: false
                },{
                    field: 'firstDeptName',
                    title: '二级单位'
                },
                {
                    field: 'firstDeptCode',
                    title: '二级单位CODE',
                    visible: false
                },
                {
                    field: 'applyName',
                    title: '专利名称',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if($.common.isNotEmpty(row.applyId)) {
                            actions.push('<a style="color: #00a0e9" onclick="$.operate.detailTab(' + "'" + row.applyId + "'" + ')">' + row.applyName + '</a>');
                            return actions.join('');
                        }
                    }
                },
                {
                    field: 'applyDate',
                    title: '申请日'
                },
                {
                    field: 'jsbh',
                    title: '接收编号'
                },
                {
                    field: 'bgbh',
                    title: '鄂钢编号'
                }, {
                    field: 'lxrName',
                    title: '联系人'
                },
                {
                    field: 'num',
                    title: '数量'
                }]
        };
        $.table.init(options);
        initChildTable = function (index, row, $detail) {
            $detail.html('<table style="table-layout:fixed;word-break: break-all;" id="table' + row.currentActivity + '" ></table>').find('table');
            options["detailView"] = true;
            //修改参数名
            if (row.currentActivity == 'yzz') {
                options["queryParams"] = {"currentIsNull": row.currentActivity};
            } else {
                options["queryParams"] = {"currentActivity": row.currentActivity};
            }
            options["id"] = "table" + row.currentActivity;
            options["url"] = prefix + "/d?serviceName=KIZLApplyBaseinfo&methodName=queryApplyTrackingDept";
            options["toolbar"] = "toolbar2";
            options["pagination"] = false;
            options["showSearch"] = false;
            options["showRefresh"] = false;
            options["showColumns"] = false;
            options["showToggle"] = false;
            options["currentActivity"] = row.currentActivity;
            options["showHeader"] = false;
            options["onExpandRow"] = function (index, row, $detail) {
                initChildTable2(index, row, $detail);
            };
            $.table.init(options);
        };
        initChildTable2 = function (index, row, $detail) {
            options["detailView"] = true;
            var current = options["currentActivity"];
            $detail.html('<table style="table-layout:fixed;word-break: break-all;" id="table' + current + row.gldwCode + '" ></table>').find('table');
            if (current == 'yzz') {
                options["queryParams"] = {
                    "currentIsNull": current.toString(),
                    "gldwCode": row.gldwCode,
                    "gldwName": row.gldwName
                };
            } else {
                options["queryParams"] = {
                    "currentActivity": current.toString(),
                    "gldwCode": row.gldwCode,
                    "gldwName": row.gldwName
                };
            }
            options["id"] = "table" + current + row.gldwCode;
            options["url"] = prefix + "/d?serviceName=KIZLApplyBaseinfo&methodName=queryApplyTrackingDept2";
            options["toolbar"] = "toolbar3";
            options["pagination"] = false;
            options["showSearch"] = false;
            options["showRefresh"] = false;
            options["showColumns"] = false;
            options["showToggle"] = false;
            options["showHeader"] = false;
            options["gldwCode"] = row.gldwCode;
            options["onExpandRow"] = function (index, row, $detail) {
                initChildTable3(index, row, $detail);
            };
            $.table.init(options);
        };
        var data3 = 0
        initChildTable3 = function (index, row, $detail) {
            var current = options["currentActivity"];
            var gldwCode = options["gldwCode"];
            $detail.html('<table style="table-layout:fixed;word-break: break-all;" id="table' + current + gldwCode + data3 + '" ></table>').find('table');
            options["detailView"] = false;
            if (current == 'yzz') {
                options["queryParams"] = {
                    "currentIsNull": current.toString(),
                    "gldwCode": gldwCode.toString(),
                    "firstDeptCode": row.firstDeptCode
                };
            } else {
                options["queryParams"] = {
                    "currentActivity": current.toString(),
                    "gldwCode": gldwCode.toString(),
                    "firstDeptCode": row.firstDeptCode
                };
            }
            options["id"] = "table" + current + gldwCode + data3;
            options["url"] = prefix + "/d?serviceName=KIZLApplyBaseinfo&methodName=queryApplyTrackingProject";
            options["toolbar"] = "toolbar4";
            options["pagination"] = false;
            options["showSearch"] = false;
            options["showRefresh"] = false;
            options["showColumns"] = false;
            options["showToggle"] = false;
            options["showHeader"] = false;
            $.table.init(options);
            data3 += 1
        };
    });
</script>
</body>
</html>