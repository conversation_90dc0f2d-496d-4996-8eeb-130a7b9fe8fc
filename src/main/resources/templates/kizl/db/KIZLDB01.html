<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('待办列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-1 control-label">名称:</label>
                <div class="col-sm-3">
                    <input type="text" class="form-control" id="businessNameLike" name="businessNameLike"
                           placeholder="支持模糊查询"/>
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="col-sm-12 select-table">
        <div th:if="${extraParameters!=null && extraParameters.get('BATCH_SUBMIT')=='1'}" style="color: red">
            以下合同可点击"处理"按钮逐个审批，也可全选或部分选中后批量"流转"审批
        </div>
        <table id="bootstrap-table"></table>
    </div>
    <!-- 批量提交 -->
    <form id="SubmitBatchForm">
        <div th:if="${extraParameters!=null && extraParameters.get('BATCH_SUBMIT')=='1'}"
             class="col-sm-12 select-table">
            <th:block
                    th:include="component/wfSubmitBatch:: init(processCode=${processCode}, currentActivity=${activityCode}, formId='SubmitBatchForm', callback=doSubmitBatch)"/>
        </div>
    </form>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kizl/workFlow";
    var processCode = /*[[${processCode}]]*/ '';
    var activityCode = /*[[${activityCode}]]*/ '';
    
    $(function () {
        // 根据流程节点确定列配置
        var columns = getColumnsConfig();
        
        var options = {
            url: prefix + "/pageDB",
            queryParams: queryParams,
            modalName: "待办列表",
            field: 'taskId',
            title: '任务ID',
            visible: false,
            columns: columns
        };
        $.table.init(options);
    });

    function getColumnsConfig() {
        var baseColumns = [{
            checkbox: true
        }];
        
        // 根据流程节点确定显示的列
        if (activityCode === 'Manual1' || activityCode === 'Manual2' || activityCode === 'Manual3') {
            // Manual1,Manual2,Manual3: 申请部门、申报名称、联系人、当前状态、任务到达时间
            baseColumns = baseColumns.concat([
                {
                    field: 'firstDeptName',
                    title: '申请部门'
                },
                {
                    field: 'applyName',
                    title: '申报名称'
                },
                {
                    field: 'lxrName',
                    title: '联系人'
                },
                {
                    field: 'currentActivityName',
                    title: '当前状态'
                },
                {
                    field: 'lastOperatorName',
                    title: '上一步处理人'
                },
                {
                    field: 'lastTime',
                    title: '上一步处理时间',
                    formatter: function (value, row, index) {
                        if (value && value.length >= 14) {
                            var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                            return timeData;
                        }
                        return value;
                    }
                }
            ]);
        } else if (activityCode === 'Manual4' || activityCode === 'Manual5') {
            // Manual4,Manual5: 接收编号、申报名称、主管部门操作人、下级部门、联系人
            baseColumns = baseColumns.concat([
                {
                    field: 'jsbh',
                    title: '接收编号'
                },
                {
                    field: 'applyName',
                    title: '申报名称'
                },
                {
                    field: 'lastOperatorName',
                    title: '主管部门操作人'
                },
                {
                    field: 'firstDeptName',
                    title: '下级部门'
                },
                {
                    field: 'lxrName',
                    title: '联系人'
                },
                {
                    field: 'currentActivityName',
                    title: '当前状态'
                },
                {
                    field: 'lastOperatorName',
                    title: '上一步处理人'
                },
                {
                    field: 'lastTime',
                    title: '上一步处理时间',
                    formatter: function (value, row, index) {
                        if (value && value.length >= 14) {
                            var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                            return timeData;
                        }
                        return value;
                    }
                }
            ]);
        } else {
            // 默认显示
            baseColumns = baseColumns.concat([
                {
                    field: 'businessName',
                    title: '名称'
                },
                {
                    field: 'currentActivityName',
                    title: '当前节点'
                },
                {
                    field: 'lastOperatorName',
                    title: '上一步处理人'
                },
                {
                    field: 'lastTime',
                    title: '上一步处理时间',
                    formatter: function (value, row, index) {
                        if (value && value.length >= 14) {
                            var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                            return timeData;
                        }
                        return value;
                    }
                }
            ]);
        }
        
        // 添加操作列
        baseColumns.push({
            title: '操作',
            align: 'center',
            formatter: function (value, row, index) {
                var actions = [];
                var param = "'" + row.processCode + "','" + row.pageNo + "','"
                    + row.businessId + "','" + row.taskId + "','"
                    + row.currentActivity + "','" + row.processInstanceId + "','"
                    + row.businessType + "','" + row.businessName + "','"
                    + row.businessLabel + "'";
                actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryDBDetail(' + param + ')"><i class="fa fa-edit"></i>处理</a> ');
                return actions.join('');
            }
        });
        
        return baseColumns;
    }

    function queryParams(params) {
        var search = $.table.queryParams(params);
        search.businessNameLike = $("#businessNameLike").val();
        search.processCode = processCode;
        search.flowCode = processCode;
        search.currentActivity = activityCode;
        search.businessType = 'KIZL';
        search.state = 'open';
        return search;
    }

    function queryDBDetail(processCode, pageNo, businessId, taskId, activityCode, processInstanceId, businessType, businessName, businessLabel) {
        var url = prefix + "/queryDBDetail/" + processCode + "/" + pageNo + "/" + businessId + "/" + taskId;
        console.log(url);
        $.modal.openTab(businessName + "-待办详细", url, true);
    }
</script>
</body>
</html>