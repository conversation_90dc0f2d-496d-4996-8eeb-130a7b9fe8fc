<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境内专利_维护_专家维护列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">输入框:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="" placeholder="支持模糊查询"/>
				    </div>
				    <label class="col-sm-1 control-label">下拉框:</label>
					<div class="col-sm-3">
						<select id="state" name="" class="form-control m-b">
							<option value="">请选择</option>
							<option value="0">状态0</option>
							<option value="1">状态1</option>
						</select>
				    </div>
				    <label class="col-sm-1 control-label">日期:</label>
					<div class="col-sm-3">
						<th:block th:include="/component/date::init(name='',id='')"/>
				    </div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcelBit()" >
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary" onclick="$.table.importTemplateBit()" >
                <i class="fa fa-download"></i> 导出模板
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kizl/maintainZjwh";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "境内专利_维护_专家维护",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'moneyId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'deptCode',
                    title: '组织代码'
                },
                {
                    field: 'deptName',
                    title: '组织名称'
                },
                {
                    field: 'expertGh',
                    title: '专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计'
                },
                {
                    field: 'expertName',
                    title: '费用名称'
                },
                {
                    field: 'area',
                    title: '费用序号'
                },
                {
                    field: 'major',
                    title: '金额'
                },
                {
                    field: 'employedOrNot',
                    title: '金额'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.moneyId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.moneyId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>