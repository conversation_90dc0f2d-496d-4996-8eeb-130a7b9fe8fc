<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('专利事务所维护')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">管理单位：</label>
					<div class="col-sm-3">
						<div th:include="/component/selectOrg :: init(orgCodeId='gldwCode', orgNameId='gldwName')"></div>
					</div>
				    <label class="col-sm-1 control-label">事务所名称：</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="swsName" placeholder="请输入事务所名称"/>
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="kizl:maintainSwsinfo:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()" shiro:hasPermission="kizl:maintainSwsinfo:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="kizl:maintainSwsinfo:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kizl/maintainSwsinfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "专利事务所",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'swsId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'gldwName',
                    title: '管理单位'
                },
                {
                    field: 'swsName',
                    title: '事务所名称'
                },
                {
                    field: 'lxr',
                    title: '联系人'
                },
                {
                    field: 'lxrPhone',
                    title: '联系电话'
                },
                {
                    field: 'address',
                    title: '地址'
                },
                {
                    field: 'email',
                    title: '邮箱'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.swsId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.swsId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>