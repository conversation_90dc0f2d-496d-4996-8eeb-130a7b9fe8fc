<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('专利事务所信息')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-maintainSwsinfo-edit" th:object="${maintainSwsinfo}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false"
                           class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <input name="swsId" th:field="*{swsId}" type="hidden">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">管理单位：</label>
                            <div class="col-sm-6">
                                <div th:include="/component/selectOrg :: init(orgCodeId='gldwCode', orgNameId='gldwName', value=*{gldwCode}, isrequired=true)"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">事务所：</label>
                            <div class="col-sm-4">
                                <input name="swsName" th:field="*{swsName}" class="form-control" type="text"
                                       required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">联系人：</label>
                            <div class="col-sm-2">
                                <input name="lxr" th:field="*{lxr}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">联系电话：</label>
                            <div class="col-sm-2">
                                <input name="lxrPhone" th:field="*{lxrPhone}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">地址：</label>
                            <div class="col-sm-4">
                                <input name="address" th:field="*{address}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">邮箱：</label>
                            <div class="col-sm-2">
                                <input name="email" th:field="*{email}" class="form-control" type="text" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i>保 存
        </button>&nbsp;
        <button type="button" class="btn btn-danger" onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<script th:inline="javascript">
    var prefix = ctx + "kizl/maintainSwsinfo";

    $("#form-maintainSwsinfo-edit").validate({
        focusCleanup: true,
        rules: {
            gldwName: {
                required: true
            },
            swsName: {
                required: true
            },
            lxr: {
                required: true
            },
            lxrPhone: {
                required: true
            },
            address: {
                required: true
            },
            email: {
                required: true,
                email: true
            }
        }
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/save", $('#form-maintainSwsinfo-edit').serialize());
        }
    }

</script>
</body>
</html>