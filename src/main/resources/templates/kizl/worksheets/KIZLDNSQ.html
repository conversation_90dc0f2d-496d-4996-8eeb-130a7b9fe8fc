<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('当年申请专利')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>接收编号：</label>
                                <input type="text" name="jsbhLike"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kizl/c";

        $(function() {
            var options = {
                url: prefix + "/d?serviceName=KIZLWorkSheets&methodName=queryApplyThisYear",
                detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
                modalName: "受理专利",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'applyId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'jsbh',
                    title: '接收编号'
                },
                {
                    field: 'egbh',
                    title: '鄂钢编号'
                },
                {
                    field: 'applyName',
                    title: '专利名称'
                },
                {
                    field: 'slrq',
                    title: '受理日期'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.applyId + '\')"><i class="fa fa-edit"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>