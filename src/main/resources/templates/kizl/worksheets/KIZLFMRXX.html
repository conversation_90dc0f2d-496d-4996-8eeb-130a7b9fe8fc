<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('专利交底发明人信息')" />
	<th:block th:include="include :: baseJs" />
	<style>
		.table-bordered > thead > tr > th,
		.table-bordered > tbody > tr > td {
			border: 1px solid #ddd;
			text-align: center;
			vertical-align: middle;
			padding: 8px;
		}
		.table-bordered > thead > tr > th {
			background-color: #f5f5f5;
			font-weight: bold;
		}
		.auto-width {
			white-space: nowrap;
		}
		.patent-name {
			max-width: 200px;
			word-wrap: break-word;
			white-space: normal;
			text-align: center;
		}
		.inventor-list {
			text-align: center;
			max-width: 150px;
		}
		.address-cell {
			max-width: 150px;
			word-wrap: break-word;
			white-space: normal;
			text-align: center;
		}
		.applicant-info {
			text-align: center;
			max-width: 400px;
		}
		.applicant-item {
			margin-bottom: 5px;
			text-align: center;
		}
		.applicant-item div {
			margin-bottom: 3px;
		}
		/* 确保所有表格内容居中 */
		#bootstrap-table td {
			text-align: center !important;
			vertical-align: middle !important;
		}
		/* 表头居中 */
		#bootstrap-table th {
			text-align: center !important;
			vertical-align: middle !important;
		}
	</style>
</head>
<body class="gray-bg">
<div class="container-div">
	<div class="row">
		<div class="col-sm-12 search-collapse">
			<form id="formId">
				<div class="select-list">
					<ul>
						<li>
							<label>接收编号：</label>
							<input type="text" name="jsbhLike"/>
						</li>
						<li>
							<label>鄂钢编号：</label>
							<input type="text" name="egbhLike"/>
						</li>
						<li>
							<label>申请号：</label>
							<input type="text" name="patentNoLike"/>
						</li>
						<li>
							<label>专利名称：</label>
							<input type="text" name="applyNameLike"/>
						</li>
						<li>
							<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
						</li>
					</ul>
				</div>
			</form>
		</div>

		<div class="btn-group-sm" id="toolbar" role="group">
			<a class="btn btn-info" id="toggleDisplayMode" onclick="toggleDisplayMode()" >
				<i class="fa fa-exchange"></i> <span id="toggleText">切换到不含工号</span>
			</a>
			<a class="btn btn-warning" onclick="exportCurrentMode()" >
				<i class="fa fa-download"></i> 导出
			</a>
		</div>
		<div class="col-sm-12 select-table table-striped">
			<table id="bootstrap-table" class="table table-bordered"></table>
		</div>
	</div>
</div>

<script th:inline="javascript">
	var prefix = ctx + "kizl/c";
	var showJobNumber = true; // 默认显示工号
	var tableOptions;

	$(function() {
		initTable();
	});

	function initTable() {
		tableOptions = {
			url: prefix + "/d?serviceName=KIZLWorkSheets&methodName=queryPatentInventorInfo",
			detailUrl: ctx + "kizl/applyBaseinfo/queryDT?businessId={id}",
			exportUrl: prefix + "/ee?serviceName=KIZLWorkSheets&methodName=queryPatentInventorInfo&fileName=专利交底发明人信息",
			modalName: "专利交底发明人信息",
			striped: true,
			bordered: true,
			columns: getTableColumns()
		};
		$.table.init(tableOptions);
	}

	function getTableColumns() {
		return [{
			field: 'applyId',
			title: '申请主键',
			visible: false
		}, {
			field: 'zgbmNc',
			title: '评审意见',
			width: 80,
			align: 'center',
			valign: 'middle',
			formatter: function(value, row, index) {
				return value || '';
			}
		}, {
			field: 'swsName',
			title: '代理所',
			width: 100,
			align: 'center',
			valign: 'middle',
			formatter: function(value, row, index) {
				return value || '';
			}
		}, {
			field: 'jsbh',
			title: '接收编号',
			width: 120,
			align: 'center',
			valign: 'middle',
			class: 'auto-width'
		}, {
			field: 'applyName',
			title: '专利名称',
			width: 200,
			align: 'center',
			valign: 'middle',
			class: 'patent-name',
			formatter: function(value, row, index) {
				return '<div class="patent-name" title="' + value + '">' + value + '</div>';
			}
		}, {
			field: 'inventorList',
			title: showJobNumber ? '发明人（含工号）' : '发明人',
			width: 150,
			align: 'center',
			valign: 'middle',
			class: 'inventor-list',
			formatter: function(value, row, index) {
				if (!value) return '';
				// 处理发明人列表显示
				var inventors = value.split('；');
				var html = '<div class="inventor-list">';
				for (var i = 0; i < inventors.length; i++) {
					if (i > 0) html += '<br/>';
					var inventorText = inventors[i];
					if (!showJobNumber) {
						// 移除工号部分 - 移除括号及其中的内容
						inventorText = inventorText.replace(/\([^)]*\)/g, '');
					}
					html += inventorText;
				}
				html += '</div>';
				return html;
			}
		}, {
			field: 'firstInventorIdCard',
			title: '第一发明人身份证号',
			width: 150,
			align: 'center',
			valign: 'middle',
			formatter: function(value, row, index) {
				return value || '';
			}
		}, {
			field: 'inventorCount',
			title: '发明人数',
			width: 80,
			align: 'center',
			valign: 'middle'
		}, {
			field: 'applicantList',
			title: '申请人信息',
			width: 400,
			align: 'center',
			valign: 'middle',
			class: 'applicant-info',
			formatter: function(value, row, index) {
				if (!value) return '';
				
				// 处理申请人列表显示
				var applicants = value.split('；');
				var html = '<div class="applicant-info">';
				
				for (var i = 0; i < applicants.length; i++) {
					var applicantData = applicants[i].split('|');
					var name = applicantData[0] || '';
					var address = applicantData[1] || '';
					var postCode = applicantData[2] || '';
					
					if (i > 0) html += '<hr style="margin: 5px 0; border: 0; border-top: 1px solid #eee;"/>';
					
					html += '<div class="applicant-item">';
					html += '<div><strong>申请人' + (i + 1) + ':</strong> ' + name + '</div>';
					if (address) {
						html += '<div><strong>地址:</strong> ' + address + '</div>';
					}
					if (postCode) {
						html += '<div><strong>邮编:</strong> ' + postCode + '</div>';
					}
					html += '</div>';
				}
				
				html += '</div>';
				return html;
			}
		}];
	}

	// 切换显示模式
	function toggleDisplayMode() {
		showJobNumber = !showJobNumber;
		var toggleText = showJobNumber ? '切换到不含工号' : '切换到含工号';
		$('#toggleText').text(toggleText);
		
		// 重新初始化表格
		$('#bootstrap-table').bootstrapTable('destroy');
		initTable();
	}

	// 导出当前模式
	function exportCurrentMode() {
		var fileName = showJobNumber ? '专利交底发明人信息含工号' : '专利交底发明人信息不含工号';
		
		// 定义导出字段
		var exportFields = [
			'zgbmNc_评审意见',
			'swsName_代理所', 
			'jsbh_接收编号',
			'applyName_专利名称',
			'inventorList_' + (showJobNumber ? '发明人含工号' : '发明人'),
			'firstInventorIdCard_第一发明人身份证号',
			'inventorCount_发明人数',
			'applicantList_申请人信息'
		];
		
		// 构建请求数据
		var requestData = {
			exportField: exportFields.join(','),
			showJobNumber: showJobNumber
		};
		
		// 添加当前查询条件
		var searchParams = $('#formId').serializeArray();
		$.each(searchParams, function(i, field) {
			if (field.value) {
				requestData[field.name] = field.value;
			}
		});
		
		// 显示加载提示
		$.modal.loading("正在导出数据，请稍后...");
		
		// 使用AJAX发送JSON请求
		$.ajax({
			url: prefix + "/ee?serviceName=KIZLWorkSheets&methodName=queryPatentInventorInfo&fileName=" + encodeURIComponent(fileName),
			type: 'POST',
			contentType: 'application/json;charset=utf-8',
			data: JSON.stringify(requestData),
			success: function(result) {
				$.modal.closeLoading();
				if (result.code == 0) {  // web_status.SUCCESS = 0
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
				} else {
					$.modal.alertError(result.msg || '导出失败');
				}
			},
			error: function(xhr, status, error) {
				$.modal.closeLoading();
				$.modal.alertError('导出失败：' + error);
			}
		});
	}

</script>
</body>
</html>