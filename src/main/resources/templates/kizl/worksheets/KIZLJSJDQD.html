<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('技术交底清单')" />
	<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
	<div class="row">
		<div class="col-sm-12 search-collapse">
			<form id="formId">
				<div class="select-list">
					<ul>
						<input type="hidden" name="exportField" id="exportField" />
						<li>
							<label>接收编号：</label>
							<input type="text" name="jsbhLike"/>
						</li>
						<li>
							<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
						</li>
					</ul>
				</div>
			</form>
		</div>

		<div class="btn-group-sm" id="toolbar" role="group">
			<a class="btn btn-warning" onclick="$.table.exportExcel()" >
				<i class="fa fa-download"></i> 导出
			</a>
		</div>
		<div class="col-sm-12 select-table table-striped">
			<table id="bootstrap-table"></table>
		</div>
	</div>
</div>

<script th:inline="javascript">
	var prefix = ctx + "kizl/c";

	$(function() {
		var options = {
			url: prefix + "/d?serviceName=KIZLWorkSheets&methodName=queryjsjdqd",
			exportUrl: prefix + "/ee?serviceName=KIZLWorkSheets&methodName=queryjsjdqd&fileName=技术交底清单&ot=e",
			modalName: "技术交底清单",
			columns: [{
				field: 'applyId',
				title: '申请主键',
				visible: false
			},
				{
					field: 'swsName',
					title: '代理事务所'
				},
				{
					title: '序号',
					align: 'center',
					formatter: function(value, row, index) {
						return index + 1;
					}
				},
				{
					field: 'jsbh',
					title: '接收编号'
				},
				{
					field: 'applyName',
					title: '发明名称'
				},
				{
					field: 'lxrName',
					title: '联系人'
				},
				{
					field: 'lxrPhone',
					title: '电话'
				},
				{
					field: 'extra1',
					title: '评审意见'
				},
				{
					field: 'extra2',
					title: '备注'
				}]
		};
		$.table.init(options);

		var exportField = "swsName_代理事务所,jsbh_接收编号,applyName_发明名称,lxrName_联系人,lxrPhone_电话,extra1_评审意见,extra2_备注";
		$("#exportField").val(exportField);
	});
</script>
</body>
</html>