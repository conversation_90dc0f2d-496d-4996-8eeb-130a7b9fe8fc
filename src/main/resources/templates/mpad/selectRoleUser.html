
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择用户')" />
</head>
<body class="gray-bg">
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="role-form">
                    <div class="select-list">
                        <input type="hidden" id="orgCode" name="orgCode" th:value="${orgCode}"/>
                        <ul>
                            <li style="display: none"  onclick="openText('userCode')">当前选中工号：<input type="text"  style="border: none;" id="userCode" disabled th:value="${values}" />
                            </li >
                            <li style="display: none"  onclick="openText('userName')">当前选中名称：<input type="text"  style="border: none;" id="userName" disabled th:value="${userName}" />
                            </li>
                            <li>用户工号：<input type="text" name="userCode"   style="width:113px;"/>
                            </li>
                            <li>用户名称：<input type="text" name="userName"  style="width:113px;"/>
                            </li>
                            <li><a class="btn btn-primary btn-rounded btn-sm"
                                   onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm"
                                   onclick="resetData()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-selectRoleUser"></table>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs" />
<script th:inline="javascript">
    $(function() {
        queryUserList();
    });

    function queryUserList(){
        var singleSelect=false;
        if([[${selectType=='S'}]]){
            singleSelect=true;
        }
        var options = {
            id: "bootstrap-table-selectRoleUser",
            url: ctx+ "mpad/user/roleUserList/"+[[${roleCode}]],
            queryParams: queryParams,
            uniqueId: "userCode",
            modalName: "用户",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            onCheck:onCheck,
            onUncheck:onUncheck,
            onCheckAll: onCheckAll,
            onUncheckAll: onUncheckAll,
            singleSelect: singleSelect,
            showColumns: false,
            clickToSelect: true,
            rememberSelected: true,
            onDblClickRow: function (row, $element) {
                dbClick(row);
            },
            columns: [{
                field: 'state',
                    checkbox: true,
                    formatter: function(value, row, index) {
                        if($("#userCode").val().indexOf(row.userCode)>-1){
                            return true;
                        }
                        return false;
                    }
                },
                {
                    field: 'userCode',
                    title: '用户工号',
                    sortable: true
                },
                {
                    field: 'userName',
                    title: '用户名称'
                },
                {
                    field: 'orgCode',
                    title: '组织编码',
                    visible: false
                },
                {
                    field: 'orgName',
                    title: '组织名称'
                }]
        };
        $.table.init(options);
    }

    function queryParams(params) {
        var search = $.table.queryParams(params);
        return search;
    }

    /* 添加用户-选择用户-提交  */
    function submitHandler() {
        //let data = $("#bootstrap-table-selectRoleUser").bootstrapTable('getData');
        let codes = $.table.selectColumns('userCode');
        let names = $.table.selectColumns('userName');
        parent.choiceRoleUserCallback(codes, names);
        var callback= [[${callback}]]
        if(callback!=null && callback!=''){
            parent.eval(callback+'("'+codes+'","'+names+'")');
        }
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    function onCheck(row, $element){
        var arrUserCode=[];
        var arrUserName=[];
        var userCodeS=$("#userCode").val();
        var userNameS=$("#userName").val();
        if(userCodeS!=null && userCodeS!=''){
            arrUserCode=userCodeS.split(",");
            arrUserName=userNameS.split(",");
        }
        if(!arrUserCode.includes(row.userCode)){
            if([[${selectType=='S'}]]){
                arrUserCode=[row.userCode];
                arrUserName=[row.userName];
                $("#"+userCodeS).remove();
                addNameSpan(row.userCode,row.userName);
            }else{
                arrUserCode.push(row.userCode);
                arrUserName.push(row.userName);
                addNameSpan(row.userCode,row.userName);
            }
        }
        $("#userCode").val(arrUserCode.join(","));
        $("#userName").val(arrUserName.join(","));

    }
    function onUncheck(row, $element){
        var userCodeS=$("#userCode").val();
        var userNameS=$("#userName").val();
        var arrUserCode=userCodeS.split(",");
        var arrUserName=userNameS.split(",");

        arrUserCode.splice(arrUserCode.indexOf(row.userCode),1);
        arrUserName.splice(arrUserName.indexOf(row.userName),1);
        $("#userCode").val(arrUserCode.join(","));
        $("#userName").val(arrUserName.join(","));

        $("#"+row.userCode).remove();
    }


    function openText(id){
        var length = $("#"+id).val().length;
        if (length > 15) {
            layer.alert($("#"+id).val(), {
                title : "信息内容",
                shadeClose : true,
                btn : [ '确认' ],
                btnclass : [ 'btn btn-primary' ],
            });
        }
    }
    function onCheckAll(rowsAfter,rowsBefore){
        for (var i=0;i<rowsAfter.length;i++){
            onCheck(rowsAfter[i]);
        }

    }
    function onUncheckAll(e,rowsAfter,rowsBefore){
        for (var i=0;i<rowsAfter.length;i++){
            onUncheck(rowsAfter[i]);
        }
    }

    function resetData(){
        $.form.reset();
    }

    //双击事件
    function dbClick(row){
        let codes = row.userCode;
        let names = row.userName;
        parent.choiceRoleUserCallback(codes, names);
        var callback= [[${callback}]]
        if(callback!=null && callback!=''){
            parent.eval(callback+'("'+codes+'","'+names+'")');
        }
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }
</script>
</body>
</html>