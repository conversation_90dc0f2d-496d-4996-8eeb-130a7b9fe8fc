<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择用户')"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
</head>
<div class="jsc-container-div">
    <div class="panel-body panel-border">
        <div class="row ">
            <div class="col-md-7">
                <!--tab-->
                <div class="tabs-container form-group">
                    <ul class="nav nav-tabs">
                        <li class="active"><a aria-expanded="true" data-toggle="tab" href="#tab-1" th:onclick="changSource('bootstrap-table-localUser')"> 最近</a></li>
                        <li class=""><a aria-expanded="false" data-toggle="tab" href="#tab-2" th:onclick="changSource('bootstrap-table-deptUser')"> 同部门 </a></li>
                        <li class=""><a aria-expanded="false" data-toggle="tab" href="#tab-3" th:onclick="changSource('bootstrap-table-companyUserList')"> 本公司 </a></li>
                        <li class=""><a aria-expanded="false" data-toggle="tab" href="#tab-4" th:onclick="changSource('bootstrap-table-userList')">组织机构</a></li>
                    </ul>
                    <div class="tab-content">
                        <!--内容-->
                        <div class="tab-pane active" id="tab-1">
                            <div class="panel-body">
                                <!--树-->
                                <div class="row ">
                                    <div class="add-content1">
                                        <div class="treebox">
                                            <table id="bootstrap-table-localUser"></table>
                                        </div>
                                    </div>
                                </div>
                                <!--树end-->
                            </div>
                        </div>
                        <!--内容end-->
                        <!--内容-->
                        <div class="tab-pane" id="tab-2">
                            <div class="panel-body">
                                <div class="row ">
                                    <div class="add-content1">
                                        <div class="treebox">
                                            <table id="bootstrap-table-deptUser"></table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--内容end-->
                        <!--内容-->
                        <div class="tab-pane" id="tab-3">
                            <div class="panel-body">
                                <div class="row ">
                                    <div class="add-content1">
                                        <div class="form-group">
                                            <form id="companyUserForm">
                                                <div class="form-search">
                                                    <input name="excludeOrgCode" type="hidden" th:value="${excludeOrgCode}">
                                                    <input class="form-control" name="userLike" placeholder="根据工号或姓名模糊查询" style="width:100%;" type="text">
                                                    <a class="btn btn-success btn-sm" onclick="selectCompanyUserList()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="resetCompanyData()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="treebox">
                                            <div class="ztree" id="companyUserTree" style="height: 410px; overflow-y: auto;"></div>
                                            <div id="companyUserList" style="display:none;">
                                                <table id="bootstrap-table-companyUserList"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--内容end-->
                        <!--内容-->
                        <div class="tab-pane" id="tab-4">
                            <div class="panel-body">
                                <div class="row ">
                                    <div class="add-content1">
                                        <div class="form-group">
                                            <form id="formUserList">
                                                <div class="form-search">
                                                    <input name="excludeOrgCode" type="hidden" th:value="${excludeOrgCode}">
                                                    <input class="form-control" name="userLike" placeholder="根据工号或姓名模糊查询" style="width:100%;" type="text">
                                                    <a class="btn btn-success btn-sm" onclick="selectUserList()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="resetData()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="treebox">
                                            <div class="ztree" id="tree" style="height: 410px; overflow-y: auto"></div>
                                            <div id="userList" style="display:none;">
                                                <table id="bootstrap-table-userList"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
                <!--tabend-->
            </div>
            <div class="col-md-1">
                <ul class="btnwarp">
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="chooseSelectData()" type="button"><i class="fa fa-chevron-right"></i></button>
                    </li>
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="chooseAllData()" type="button"><i class="fa fa-angle-double-right"></i></button>
                    </li>
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="removeSelectData()" type="button"><i class="fa fa-chevron-left"></i></button>
                    </li>
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="removeAllData()" type="button"><i class="fa fa-angle-double-left"></i></button>
                    </li>
                </ul>
            </div>
            <div class="col-md-4">
                <div class="add-content">
                    <table id="bootstrap-table-selectUser2"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs"/>
<th:block th:include="include :: layout-latest-js"/>
<th:block th:include="include :: ztree-js"/>
</body>
</html>
<script th:inline="javascript">
    let singleSelect = false;
    let currentTableId = 'bootstrap-table-localUser';
    $(function () {
        querySelectUser();

        queryLocalUser();
        //queryDeptUser();
        queryCompanyADTree();
        companyUserList();
        queryAdTree();
        userList();
    });

    function changSource(id) {
        currentTableId = id;
        if (id=='bootstrap-table-deptUser') {
            queryDeptUser();
        }
    }

    //select用户
    function querySelectUser() {
        if ([[${selectType}]] == 'S') {//单选
            singleSelect = true;
        }
        let initUserCode = [[${values}]];
        let initUserName = [[${userName}]];
        let data = [];
        if (initUserCode) {
            let userCodeArray = initUserCode.split(",");
            let userNamArray = initUserName.split(",");
            for (let i = 0; i < userCodeArray.length; i++) {
                data.push({"userId": userCodeArray[i], "userCode": userCodeArray[i], "userName": userNamArray[i]})
            }
        }

        let options = {
            id: "bootstrap-table-selectUser2",
            data: data,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            pagination: false,
            uniqueId: "userId",
            height: 485,
            singleSelect: singleSelect,
            sidePagination: 'client',
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                $("#bootstrap-table-selectUser2").bootstrapTable('remove', {
                    field: 'userId',
                    values: row.userId
                })
            },
            columns:
                [
                    {
                        checkbox: true
                    },
                    {
                        field: 'userId',
                        title: '用户ID',
                        visible: false
                    },
                    {
                        field: 'userCode',
                        title: '',
                        width: 100
                    },
                    {
                        field: 'userName',
                        title: '用户'
                    },
                    {
                        field: 'postName',
                        title: '岗位',
                        visible: false
                    }
                ]
        };
        $.table.init(options);
    }
    //Local用户
    function queryLocalUser() {
        let data = JSON.parse(localStorage.getItem("jtfw_localUserList"));
        if (!$.isArray(data)) {
            data = [];
        }
        let options = {
            id: "bootstrap-table-localUser",
            data: data,
            showHeader: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            pagination: false,
            showColumns: false,
            uniqueId: "userId",
            singleSelect: singleSelect,
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                insertRow(row);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'userId',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'userCode',
                    title: '工号',
                    width: 80
                },
                {
                    field: 'userName',
                    title: '姓名',
                    width: 100,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 4);
                        }
                        return value;
                    }
                },
                {
                    field: 'postName',
                    title: '岗位',
                    width: 220,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 16);
                        }
                        return value;
                    }
                }
            ]
        };
        $.table.init(options);
        $(".fixed-table-toolbar").hide();
    }

    //同部门用户
    function queryDeptUser() {
        let options = {
            id: "bootstrap-table-deptUser",
            url: ctx + "mpad/user/getDeptUser",
            showHeader: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            uniqueId: "userId",
            singleSelect: singleSelect,
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                insertRow(row);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'userId',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'userCode',
                    title: '工号',
                    width: 80
                },
                {
                    field: 'userName',
                    title: '姓名',
                    width: 100,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 4);
                        }
                        return value;
                    }

                },
                {
                    field: 'postName',
                    title: '岗位',
                    width: 200,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 16);
                        }
                        return value;
                    }
                }
            ]
        };
        $.table.init(options);
        $(".fixed-table-toolbar").hide();
    }

    //组织树
    function queryAdTree() {
        let url = ctx + "mpad/org/getADTree?orgCode=" + [[${orgCode}]];
        let excludeOrgCode = /*[[${excludeOrgCode==null?'':excludeOrgCode}]]*/""
        if (excludeOrgCode) {
            url += "&excludeOrgCode=" + excludeOrgCode
        }
        let options = {
            id: "tree",
            url: url,
            async: {
                enable: true,
                type: "get",//根据请求类型自己定义
                url: ctx + "mpad/org/getADTree?excludeOrgCode=" + excludeOrgCode,
                autoParam: ["id=parentCode"],//这个是会自动加上的参数，这里的参数，可以用别名，例如：id=Path,传参的时候就是ids = '1'；但是需要注意的是，这里的参数只支持ztree设置的数据属性，例如我们想传递Path字段，就不能在这里自动匹配了，需要另外写方法了
                dataFilter: function (treeId, parentNode, resData) {
                    //这里要过滤你的数据，把请求回来的数据组装成你想要的格式,resData就是请求接口返回的数据
                    //我们假装这里的数据就是我们自己想要的
                    return resData;
                }
            },
            expandLevel: 1,
            onDblClick: zOnClick
        };
        $.tree.init(options);

        //双击
        function zOnClick(event, treeId, treeNode) {
            if (treeNode && 'user' == treeNode.type) {
                insertRow(treeNode);
            }
        }
    }

    //全用户
    function userList() {
        let options = {
            id: "bootstrap-table-userList",
            url: ctx + "mpad/user/allUserList",
            queryParams: queryParams,
            showHeader: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            firstLoad: false,
            uniqueId: "userId",
            singleSelect: singleSelect,
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                insertRow(row);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'userId',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'userCode',
                    title: '工号',
                    width: 60
                },
                {
                    field: 'userName',
                    title: '姓名',
                    width: 60,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 6);
                        }
                        return value;
                    }
                },
                {
                    field: 'fullShortNamePath',
                    title: '组织',
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 16);
                        }
                        return value;
                    }
                },
                {
                    field: 'postName',
                    title: '岗位',
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 6);
                        }
                        return value;
                    }
                }
            ]
        };
        $.table.init(options);
        $("#userList .fixed-table-toolbar").hide();

        function queryParams(params) {
            let search = $.table.queryParams(params);
            return search;
        }
    }

    /**
     * 组织结构搜索
     */
    function selectUserList() {
        $("#tree").hide();
        $("#userList").show();
        $.table.search('formUserList', 'bootstrap-table-userList');
    }

    /**
     * 组织结构重置
     */
    function resetData() {
        $("#formUserList")[0].reset();
        $("#tree").show();
        $("#userList").hide();
    }

    //公司组织树
    function queryCompanyADTree() {
        let url = ctx + "mpad/org/getCompanyADTree";
        let excludeOrgCode = /*[[${excludeOrgCode==null?'':excludeOrgCode}]]*/""
        if (excludeOrgCode) {
            url += "?excludeOrgCode=" + excludeOrgCode
        }
        let options = {
            id: "companyUserTree",
            url: url,
            async: {
                enable: true,
                type: "get",//根据请求类型自己定义
                url: ctx + "mpad/org/getADTree?excludeOrgCode=" + excludeOrgCode,
                autoParam: ["id=parentCode"],//这个是会自动加上的参数，这里的参数，可以用别名，例如：id=Path,传参的时候就是ids = '1'；但是需要注意的是，这里的参数只支持ztree设置的数据属性，例如我们想传递Path字段，就不能在这里自动匹配了，需要另外写方法了
                dataFilter: function (treeId, parentNode, resData) {
                    return resData;
                }
            },
            expandLevel: 1,
            onDblClick: zOnClick
        };
        $.tree.init(options);

        //双击
        function zOnClick(event, treeId, treeNode) {
            if (treeNode.type == 'user') {
                insertRow(treeNode);
            }
        }
    }

    //本公司用户
    function companyUserList() {
        let options = {
            id: "bootstrap-table-companyUserList",
            url: ctx + "mpad/user/allUserList?queryType=company",
            queryParams: queryParams,
            showHeader: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            firstLoad: false,
            uniqueId: "userId",
            singleSelect: singleSelect,
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                insertRow(row);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'userId',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'userCode',
                    title: '工号',
                    width: 60
                },
                {
                    field: 'userName',
                    title: '姓名',
                    width: 60,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 6);
                        }
                        return value;
                    }
                },
                {
                    field: 'fullShortNamePath',
                    title: '组织',
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 16);
                        }
                        return value;
                    }
                },
                {
                    field: 'postName',
                    title: '岗位',
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 6);
                        }
                        return value;
                    }
                }
            ]
        };
        $.table.init(options);
        $("#companyUserList .fixed-table-toolbar").hide();

        function queryParams(params) {
            let search = $.table.queryParams(params);
            return search;
        }
    }

    /**
     * 组织结构搜索
     */
    function selectCompanyUserList() {
        $("#companyUserTree").hide();
        $("#companyUserList").show();
        $.table.search('companyUserForm', 'bootstrap-table-companyUserList');
    }

    /**
     * 组织结构重置
     */
    function resetCompanyData() {
        $("#companyUserForm")[0].reset();
        $("#comapnyUserTree").show();
        $("#comapnyUserList").hide();
    }

    /* 新增表格行 */
    function insertRow(row) {
        if ([[${selectType}]] == 'S') {//单选
            $("#bootstrap-table-selectUser2").bootstrapTable('removeAll');
        }
        let selectData = $("#bootstrap-table-selectUser2").bootstrapTable('getData');
        let jsonString = JSON.stringify(selectData.map(item => {
            return item.userCode
        }));
        if (jsonString.indexOf(row.userCode) == -1) {
            $("#bootstrap-table-selectUser2").bootstrapTable('insertRow', {
                index: 99, // 你想插入到哪，0表示第一行
                row: {
                    userId: row.userCode,
                    userCode: row.userCode,
                    userName: row.userName,
                    postName: row.postName
                }
            })
        }
    }

    /* 添加用户-选择用户-提交  */
    function submitHandler() {
        let data = $("#bootstrap-table-selectUser2").bootstrapTable('getData');
        setLocalUser(data);

        let codes = data.map(item => item.userCode === "" ? "null" : String(item.userCode)).join(",");
        let names = data.map(item => item.userName === "" ? "null" : String(item.userName)).join(",");
        parent.choiceUserCallback(codes, names);
        let callback = [[${callback}]];
        if (callback != null && callback != '') {
            parent.eval(callback + '("' + codes + '","' + names + '")');
        }
        let index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    /**
     * setClientCache
     * @param data
     */
    function setLocalUser(data) {
        let rtn = [];
        let checkId = [];
        //合并去重
        for (let i = 0; i < data.length; i++) {
            let item = data[i];
            checkId.push(item['userId'])
            rtn.push(item);
        }
        let localUserData = JSON.parse(localStorage.getItem("jtfw_localUserList"));
        if (localUserData) {
            for (let i = 0; i < localUserData.length; i++) {
                let item = localUserData[i];
                if (checkId.includes(item['userId'])) {
                    continue;
                }
                checkId.push(item['userId'])
                rtn.push(item);
            }
        }
        //保留10个
        if (rtn.length > 10) {
            rtn.splice(10, rtn.length);
        }
        localStorage.setItem("jtfw_localUserList", JSON.stringify(rtn));
    }

    //选择选中的
    function chooseSelectData() {
        let data = $("#" + currentTableId).bootstrapTable('getSelections');
        for (let i = 0; i < data.length; i++) {
            insertRow(data[i]);
        }
    }

    //全选
    function chooseAllData() {
        let data = $("#" + currentTableId).bootstrapTable('getData');
        for (let i = 0; i < data.length; i++) {
            insertRow(data[i]);
        }
    }

    //选择选中的
    function removeSelectData() {
        table.set('bootstrap-table-selectUser2');
        let ids = $.table.selectColumns("userId");
        $("#bootstrap-table-selectUser2").bootstrapTable('remove', {
            field: 'userId',
            values: ids
        })
    }

    //删除所有的
    function removeAllData() {
        $("#bootstrap-table-selectUser2").bootstrapTable('removeAll');
    }
</script>