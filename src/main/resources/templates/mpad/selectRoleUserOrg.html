
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择组织')" />
</head>
<body class="gray-bg">
<div class="ui-layout-center">
    <div class="container-div">
        <div class="col-sm-12 select-table table-striped">
            <form id="subOrg-form">
                <input type="hidden" id="parentOrgCode" name="parentOrgCode" th:value="${parentOrgCode}"/>
                <input type="hidden" id="filterOrgCode" name="filterOrgCode" th:value="${filterOrgCode}"/>
                <input type="hidden" id="orgUser" th:value="${values}"/>
                <input type="hidden" id="orgName" th:value="${orgName}"/>
            </form>
            <table id="bootstrap-table-selectRoleUserOrg"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs" />
<script th:inline="javascript">
    $(function() {
        querySubOrgList();
    });

    function querySubOrgList(){
        var singleSelect = false;
        if([[${selectType=='S'}]]){
            singleSelect=true;
        }
        var options = {
            id: "bootstrap-table-selectRoleUserOrg",
            url: ctx+ "mpad/org/listRoleUserOrg/"+[[${parentOrgCode}]]+"/"+[[${roleCode}]],
            queryParams: queryParams,
            uniqueId: "orgCode",
            modalName: "组织",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            onCheck:onCheck,
            onUncheck:onUncheck,
            onCheckAll: onCheckAll,
            onUncheckAll: onUncheckAll,
            singleSelect: singleSelect,
            showColumns: false,
            clickToSelect: true,
            rememberSelected: true,
            pagination: false,
            onDblClickRow: function (row, $element) {
                dbClick(row);
            },
            columns: [
                {
                    field: 'state',
                    checkbox: true,
                    formatter: function(value, row, index) {
                        if($("#orgUser").val().indexOf(row.orgUser)>-1){
                            return true;
                        }
                        return false;
                    }
                },
                {
                    field: 'orgUser',
                    title: '组织用户',
                    visible: false
                },
                {
                    field: 'userCode',
                    title: '工号'
                },
                {
                    field: 'userName',
                    title: '姓名'
                },
                {
                    field: 'orgCode',
                    title: '组织编码'
                },
                {
                    field: 'orgNamePath',
                    title: '组织名称',
                    formatter: function (value, row, index) {
                        if (!value) {
                            return row.orgName;
                        }
                        return value;
                    }
                }
                ]
        };
        $.table.init(options);
    }

    function queryParams(params) {
        var search = $.table.queryParams(params);
        return search;
    }

    /* 选择-提交  */
    function submitHandler() {
        var arrCode=[];
        var arrName=[];
        $.map($("#bootstrap-table-selectRoleUserOrg").bootstrapTable('getSelections'), function (row) {
            arrCode.push($.common.getItemField(row, 'orgUser'));
            arrName.push($.common.getItemField(row, 'userName'))
        });
        //let codes = $.table.selectColumns('orgCode');
        //let names = $.table.selectColumns('orgName');
        let codes = arrCode.join(",");
        let names = arrName.join(",");
        parent.selectDefaultCallback([[${id}]]+'_typeCode', [[${id}]]+'_typeName', codes, names);
        var callback= [[${callback}]]
        if(callback!=null && callback!=''){
            parent.eval(callback+'("'+codes+'","'+names+'")');
        }
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    function onCheck(row, $element){
        var arrCode=[];
        var arrName=[];
        var codeS=$("#orgUser").val();
        var nameS=$("#orgName").val();
        if(codeS!=null && codeS!=''){
            arrCode=codeS.split(",");
            arrName=nameS.split(",");
        }
        if(!arrCode.includes(row.orgUser)){
            if([[${selectType=='S'}]]){
                arrCode=[row.orgUser];
                arrName=[row.orgName];
            }else{
                arrCode.push(row.orgUser);
                arrName.push(row.orgName);
            }
        }
        $("#orgUser").val(arrCode.join(","));
        $("#orgName").val(arrName.join(","));
    }

    function onUncheck(row, $element){
        var codeS=$("#orgUser").val();
        var nameS=$("#orgName").val();
        var arrCode=codeS.split(",");
        var arrName=nameS.split(",");
        arrCode.splice(arrCode.indexOf(row.orgUser),1);
        arrName.splice(arrName.indexOf(row.orgName),1);
        $("#orgUser").val(arrCode.join(","));
        $("#orgName").val(arrName.join(","));
    }

    function onCheckAll(rowsAfter,rowsBefore){
        for (var i=0;i<rowsAfter.length;i++){
            onCheck(rowsAfter[i]);
        }
    }
    function onUncheckAll(e,rowsAfter,rowsBefore){
        for (var i=0;i<rowsAfter.length;i++){
            onUncheck(rowsAfter[i]);
        }
    }

    function resetData(){
        $.form.reset();
    }

    //双击事件
    function dbClick(row){
        let codes = row.orgUser
        let names = row.userName;
        parent.selectDefaultCallback([[${id}]]+'_typeCode', [[${id}]]+'_typeName', codes, names);
        var callback= [[${callback}]]
        if(callback!=null && callback!=''){
            parent.eval(callback+'("'+codes+'","'+names+'")');
        }
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }
</script>
</body>
</html>