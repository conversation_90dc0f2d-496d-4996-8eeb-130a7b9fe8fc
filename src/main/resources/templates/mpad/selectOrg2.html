<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('组织选择')"/>
    <th:block th:include="include :: ztree-css"/>
</head>
<style>
    body {
        height: auto;
        font-family: "Microsoft YaHei";
    }

    button {
        font-family: "SimSun", "Helvetica Neue", Helvetica, Arial;
    }
</style>
<body class="hold-transition box box-main">
<input id="treeId" name="treeId" th:value="${values}" type="hidden"/>
<input id="treeName" name="treeName" th:value="${orgName}" type="hidden"/>
<div class="wrapper">
    <div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
        <label id="btnShow" style="display: none;" title="显示搜索">︾</label> <label
            id="btnHide" title="隐藏搜索">︽</label>
    </div>
    <div class="treeSearchInput" id="search">
        <label for="keyword">关键字：</label>
        <input class="empty" id="keyword" maxlength="50" type="text">
        <button class="btn" id="btn" onclick="$.tree.searchNode()">
            搜索
        </button>
    </div>
    <div class="treeExpandCollapse" style="float: none;">
        <label class="check-box">
            <input type="checkbox" value="1">展开/折叠</label>
        <label class="check-box" th:if="${selectType=='M'}">
            <input type="checkbox" value="2">全选/全不选</label>
        <label class="check-box" th:if="${selectType=='M'}">
            <input type="checkbox" value="3">父子联动</label>
    </div>

    <div class="ztree ztree-border" id="tree"></div>
</div>


<th:block th:include="include :: baseJs"/>
<th:block th:include="include :: ztree-js"/>
<script th:inline="javascript">

    $('.treeExpandCollapse input').on('ifChanged', function (obj) {
        var type = $(this).val();
        var checked = obj.currentTarget.checked;
        if (type == 1) {
            if (checked) {
                $._tree.expandAll(true);
            } else {
                $._tree.expandAll(false);
            }
        } else if (type == "2") {
            if (checked) {
                $._tree.checkAllNodes(true);
                var nodes = $._tree.getCheckedNodes(true);
                var code = $.map(nodes, function (row) {
                    return row['code'];
                }).join();
                var name = $.map(nodes, function (row) {
                    return row['name'];
                }).join();
                $("#treeId").val(code);
                $("#treeName").val(name);
            } else {
                $._tree.checkAllNodes(false);
                $("#treeId").val("");
                $("#treeName").val("");
            }
        } else if (type == "3") {
            if (checked) {
                $._tree.setting.check.chkboxType = {"Y": "ps", "N": "ps"};
            } else {
                $._tree.setting.check.chkboxType = {"Y": "", "N": ""};
            }
        }
    })
    $(function () {
        var url = ctx + "mpad/org/treeData?orgCode=" + [[${orgCode}]];
        var level = [[${level}]];
        var values = [[${values}]];
        if (level != null && level != '') {
            url += "&level=" + level;
        }
        if (values != null && values != '') {
            url += "&values=" + values;
        }
        var selectedMulti = false;
        var enable = false;
        if ([[${selectType=='M'}]]) {
            selectedMulti = true;
            enable = true;
        }
        var options = {
            url: url,
            expandLevel: 1,
            onClick: zOnClick,
            onCheck: zonCheck,
            view: {selectedMulti: selectedMulti},
            check: {enable: enable, chkboxType: {"Y": "", "N": ""}},
            async: {
                enable: true,
                type: "get",//根据请求类型自己定义
                url: ctx + "mpad/org/treeData",
                autoParam: ["id=parentCode"],//这个是会自动加上的参数，这里的参数，可以用别名，例如：id=Path,传参的时候就是ids = '1'；但是需要注意的是，这里的参数只支持ztree设置的数据属性，例如我们想传递Path字段，就不能在这里自动匹配了，需要另外写方法了
                otherParam: {
                    "values": function () {
                        return values
                    }, "level": function () {
                        if (level != null && level != '') {
                            return level;
                        }
                        return null;
                    }
                },
                dataFilter: function (treeId, parentNode, resData) {
                    //这里要过滤你的数据，把请求回来的数据组装成你想要的格式,resData就是请求接口返回的数据
                    //我们假装这里的数据就是我们自己想要的
                    return resData;
                }
            },
            onAsyncSuccess: zTreeOnAsyncSuccess
        };
        $.tree.init(options);
    });

    //点击名称
    function zOnClick(event, treeId, treeNode) {
        //组织编码
        var treeId = treeNode.code;
        var treeName = treeNode.name;
        /*
        var name=getFilePath(treeNode);
        var showLevel=[[${showLevel!=null?showLevel:1}]]
        name=name.substring(find(name,"/",showLevel+1)+1,name.length);
        */
        var name = treeNode.orgPathName;
        if([[${selectType=='M'}]]){
            if(treeNode.checked){
                $._tree.checkNode(treeNode);
            }else{
                $._tree.checkNode(treeNode, true, true);
            }
            var orgCode=$("#treeId").val();
            var orgName=$("#treeName").val();
            var orgCodeArr=[];
            var orgNameArr=[];
            if(treeNode.checked){
                if(orgCode!=null && orgCode!=''){
                    if([[${selectType=='M'}]]){
                        orgCodeArr=orgCode.split(",");
                        orgNameArr=orgName.split(",");
                        console.log(orgCodeArr)
                        orgCodeArr.push(treeId);
                        orgNameArr.push(name);
                    }else{
                        orgCodeArr.push(treeId);
                        orgNameArr.push(name);
                    }
                }else{
                    orgCodeArr.push(treeId);
                    orgNameArr.push(name);
                }
            }else{
                if([[${selectType=='M'}]]){
                    orgCodeArr=orgCode.split(",");
                    orgNameArr=orgName.split(",");
                    orgCodeArr.splice(orgCodeArr.indexOf(treeId),1);
                    orgNameArr.splice(orgCodeArr.indexOf(treeId),1);
                }else{
                }
            }
            $("#treeId").val(orgCodeArr.join(","));
            $("#treeName").val(orgNameArr.join(","));
        }else {
            $("#treeId").val(treeId);
            $("#treeName").val(name);
        }
    }

    function getFilePath(treeObj) {
        if (treeObj == null) return "";
        var filename = treeObj.name;
        var pNode = treeObj.getParentNode();
        if (pNode != null) {
            filename = getFilePath(pNode) + "/" + filename;
        }
        return filename;
    }

    function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
        if (!$._tree) {
            alert("error!");
            return
        }
        var selectedNode = $._tree.getSelectedNodes();
        var nodes = $._tree.getNodes();
        $._tree.expandNode(nodes[0], true);

        let checkedNodes = $._tree.getCheckedNodes(true);
        if(checkedNodes!=null && checkedNodes.length>0){
            $('html, body').animate({
                scrollTop: $("body").find("a[title='"+checkedNodes[0].name+"']").offset().top
            });
        }
    }

    //复选框
    function zonCheck(event, treeId, treeNode) {
        var treeId = treeNode.code;
        var treeName = treeNode.name;
        /*
        var name=getFilePath(treeNode);
        var showLevel=[[${showLevel!=null?showLevel:1}]]
        name=name.substring(find(name,"/",showLevel+1)+1,name.length);
        */
        var name = treeNode.orgPathName;

        var orgCode = $("#treeId").val();
        var orgName = $("#treeName").val();
        var orgCodeArr = [];
        var orgNameArr = [];

        if (treeNode.checked) {
            if (orgCode != null && orgCode != '') {
                if ([[${selectType=='M'}]]) {
                    orgCodeArr = orgCode.split(",");
                    orgNameArr = orgName.split(",");
                    console.log(orgCodeArr)
                    orgCodeArr.push(treeId);
                    orgNameArr.push(name);
                } else {
                    orgCodeArr.push(treeId);
                    orgNameArr.push(name);
                }
            } else {
                orgCodeArr.push(treeId);
                orgNameArr.push(name);
            }
        } else {
            if ([[${selectType=='M'}]]) {
                orgCodeArr = orgCode.split(",");
                orgNameArr = orgName.split(",");
                orgCodeArr.splice(orgCodeArr.indexOf(treeId), 1);
                orgNameArr.splice(orgCodeArr.indexOf(treeId), 1);
            } else {

            }
        }

        $("#treeId").val(orgCodeArr.join(","));
        $("#treeName").val(orgNameArr.join(","));

    }

    function submitHandler() {
        var treeId=$("#treeId").val();
        var treeName=$("#treeName").val();
        parent.choiceOrgCallback2([[${id}]],treeId,treeName);
        var callback= [[${callback}]]
        if(callback!=null && callback!=''){
            parent.eval(callback+'("'+treeId+'","'+treeName+'")');
        }
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    function find(str, cha, num) {
        var x = str.indexOf(cha);
        for (var i = 0; i < num; i++) {
            x = str.indexOf(cha, x + 1);
        }
        return x;
    }
</script>
</body>
</html>
