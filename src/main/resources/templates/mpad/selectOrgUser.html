<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择用户')"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
</head>
<div class="jsc-container-div">
    <div class="panel-body">
        <div class="row ">
            <div class="col-md-7">
                <!--tab-->
                <div class="tabs-container form-group">
                    <div class="tab-content">
                        <!--内容-->
                        <div class="tab-pane active" id="tab-1">
                            <div class="panel-body">
                                <div class="row ">
                                    <div class="add-content1">
                                        <div class="form-group">
                                            <form id="companyUserForm">
                                                <div class="form-search">
                                                    <input class="form-control" name="userLike" placeholder="根据工号或姓名模糊查询" style="width:100%;" type="text">
                                                    <a class="btn btn-success btn-sm" onclick="selectCompanyUserList()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="resetCompanyData()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                                </div>
                                            </form>
                                        </div>
                                        <div id="companyUserList">
                                            <table id="bootstrap-table-companyUserList"></table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
                <!--tabend-->
            </div>
            <div class="col-md-1">
                <ul class="btnwarp" style="margin-top: 80px">
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="chooseSelectData()" type="button"><i class="fa fa-chevron-right"></i></button>
                    </li>
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="chooseAllData()" type="button"><i class="fa fa-angle-double-right"></i></button>
                    </li>
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="removeSelectData()" type="button"><i class="fa fa-chevron-left"></i></button>
                    </li>
                    <li>
                        <button class="btn btn-success btn-circle" th:onclick="removeAllData()" type="button"><i class="fa fa-angle-double-left"></i></button>
                    </li>
                </ul>
            </div>
            <div class="col-md-4">
                <div class="add-content" style="margin-top: -10px">
                    <table id="bootstrap-table-selectUser2"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs"/>
<th:block th:include="include :: layout-latest-js"/>
<th:block th:include="include :: ztree-js"/>
</body>
</html>
<script th:inline="javascript">
    let singleSelect = false;
    let currentTableId = 'bootstrap-table-companyUserList';
    $(function () {
        querySelectUser();
        companyUserList();
    })

    //select用户
    function querySelectUser() {
        if ([[${selectType}]] == 'S') {//单选
            singleSelect = true;
        }
        let initUserCode = [[${values}]];
        let initUserName = [[${userName}]];
        let selectData = [];
        if (initUserCode) {
            let userCodeArray = initUserCode.split(",");
            let userNamArray = initUserName.split(",");
            for (let i = 0; i < userCodeArray.length; i++) {
                selectData.push({"userId": userCodeArray[i], "userCode": userCodeArray[i], "userName": userNamArray[i]})
            }
        }
        let options = {
            id: "bootstrap-table-selectUser2",
            data: selectData,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            pagination: false,
            uniqueId: "userId",
            height: 485,
            singleSelect: singleSelect,
            //sidePagination: "client",
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                $("#bootstrap-table-selectUser2").bootstrapTable('remove', {
                    field: 'userId',
                    values: row.userId
                })
            },
            columns:
                [
                    {
                        checkbox: true
                    },
                    {
                        field: 'userId',
                        title: '用户ID',
                        visible: false
                    },
                    {
                        field: 'userCode',
                        title: '',
                        width: 100
                    },
                    {
                        field: 'userName',
                        title: '用户'
                    },
                    {
                        field: 'postName',
                        title: '岗位',
                        visible: false
                    }
                ]
        };
        $.table.init(options);
    }

    //本部门用户
    function companyUserList() {
        var orgCode = [[${orgCode}]] || '';
        let options = {
            id: "bootstrap-table-companyUserList",
            url: ctx + "mpad/user/orgUserList?orgCode="+orgCode,
            queryParams: queryParams,
            showHeader: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            uniqueId: "userId",
            singleSelect: singleSelect,
            onClickRow: function (row, $element) {
                $element[0].firstElementChild.firstElementChild.click();
            },
            onDblClickRow: function (row, $element) {
                insertRow(row);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'userId',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'userCode',
                    title: '工号',
                    width: 60
                },
                {
                    field: 'userName',
                    title: '姓名',
                    width: 60,
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 6);
                        }
                        return value;
                    }
                },
                {
                    field: 'fullShortNamePath',
                    title: '组织',
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 16);
                        }
                        return value;
                    }
                },
                {
                    field: 'postName',
                    title: '岗位',
                    formatter: function (value, row, index) {
                        if (value != null && value != "") {
                            return $.table.tooltip(value, 6);
                        }
                        return value;
                    }
                }
            ]
        };
        $.table.init(options);
        $("#companyUserList .fixed-table-toolbar").hide();

        function queryParams(params) {
            let search = $.table.queryParams(params);
            return search;
        }
    }

    /**
     * 组织结构搜索
     */
    function selectCompanyUserList() {
        $.table.search('companyUserForm', 'bootstrap-table-companyUserList');
    }

    /**
     * 组织结构重置
     */
    function resetCompanyData() {
        $("#companyUserForm")[0].reset();
        $.table.search('companyUserForm', 'bootstrap-table-companyUserList');
    }

    /* 新增表格行 */
    function insertRow(row) {
        if ([[${selectType}]] == 'S') {//单选
            $("#bootstrap-table-selectUser2").bootstrapTable('removeAll');
        }
        let selectData = $("#bootstrap-table-selectUser2").bootstrapTable('getData');
        let jsonString = JSON.stringify(selectData.map(item => {
            return item.userCode;
        }))
        if (jsonString.indexOf(row.userCode) == -1) {
            $("#bootstrap-table-selectUser2").bootstrapTable('insertRow', {
                index: 99, // 你想插入到哪，0表示第一行
                row: {
                    userId: row.userCode,
                    userCode: row.userCode,
                    userName: row.userName,
                    postName: row.postName
                }
            })
        }
    }

    /* 添加用户-选择用户-提交  */
    function submitHandler() {
        let data = $("#bootstrap-table-selectUser2").bootstrapTable('getData');
        let codes = data.map(item => item.userCode === "" ? "null" : String(item.userCode)).join(",");
        let names = data.map(item => item.userName === "" ? "null" : String(item.userName)).join(",")
        //parent.choiceUserCallback2([[${id}]], codes, names);
        parent.choiceOrgUserCallback(codes, names);
        let callback = [[${callback}]]
        if (callback != null && callback != '') {
            parent.eval(callback + '("' + codes + '","' + names + '")');
        }
        let index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    //选择选中的
    function chooseSelectData() {
        let data = $("#" + currentTableId).bootstrapTable('getSelections');
        for (let i = 0; i < data.length; i++) {
            insertRow(data[i]);
        }
    }

    //全选
    function chooseAllData() {
        let data = $("#" + currentTableId).bootstrapTable('getData');
        for (let i = 0; i < data.length; i++) {
            insertRow(data[i]);
        }
    }

    //选择选中的
    function removeSelectData() {
        table.set('bootstrap-table-selectUser2');
        let ids = $.table.selectColumns("userId");
        $("#bootstrap-table-selectUser2").bootstrapTable('remove', {
            field: 'userId',
            values: ids
        })
    }

    //删除所有的
    function removeAllData() {
        $("#bootstrap-table-selectUser2").bootstrapTable('removeAll');
    }
</script>