<!--
    name:input name 默认 selectbox
    id:input id 默认 name值
    labelName:label的text 默认选择

    see:回显(true,false) 默认false
    isrequired:是否必填 默认false
    value: 回显值
    isfirst: 是否添加首选项
    firstName 首选项名称 默认 请选择
    firstValue 首选项名称 默认 ''
    notShowValue 不显示值（选项）

    data 自定义数据 必传
    dataKey 自定义数据value 的key 默认 dictKey
    dataNameKey 自定义数据name 的key 默认 dictName

    dataName（see=true 需要设置 ） 回显名称 如果不设置 需要传入data dataKey dataNameKey进行筛选查询名称

-->


<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'selectbox':name},
               	 	id=${id==null?name:id},
               	 	firstName=${firstName==null?'请选择':firstName},
               	 	firstValue=${firstValue==null?'':firstValue},
               	 	dataKey=${dataKey==null?'dictValue':dataKey},
               	 	dataNameKey=${dataNameKey==null?'dictName':dataNameKey},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}
               	 	">

        <th:block th:if="${!see}">
            <select th:class="form-control" th:id="${id}" th:if="${!see}" th:name="${name}">
                <option th:if="${isfirst!=null && isfirst}" th:text="${firstName}" th:value="${firstValue}"></option>
                <option th:each="dict : ${data}"
                        th:selected="${value!=null && value==dict.get(dataKey)}"
                        th:text="${dict.get(dataNameKey)}"
                        th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue, dict.get(dataKey))}"
                        th:value="${dict.get(dataKey)}"></option>
            </select>
            <script th:inline="javascript">
                $('#' + [[${id}]]).select2();
                if ([[${isrequired!=null && isrequired}]]) {
                    $("#" + [[${id}]]).attr("required", "");
                }
            </script>
        </th:block>

        <th:block th:unless="${!see}">
            <div class="form-control-static" th:id="${id+'_id'}"></div>
            <input th:id="${id}" th:name="${name}" th:value="${value}" type="hidden"/>
            <script th:if="${see}" th:inline="javascript">
                $('#' + [[${id+'_id'}]]).html([[${value}]]);
                let data = [[${data}]];
                let value = [[${value}]];
                let dataKey = [[${dataKey}]];
                let dataNameKey = [[${dataNameKey}]];
                let showName = '';
                $.each(data, function(index, dict) {
                    if(value==dict[dataKey]){
                        showName = dict[dataNameKey];
                        return;
                    }
                });
                $('#' + [[${id+'_id'}]]).html(showName);
            </script>
        </th:block>
    </th:block>
</div>




