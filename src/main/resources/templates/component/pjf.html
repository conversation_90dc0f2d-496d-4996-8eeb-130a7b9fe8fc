<!-- 平均分封装

moduleCode: 模板code 必填
bizGuid:业务id 必填
reviewId:评审要求主键 默认nul 查询最新一次评审
userType默认 null  查询人员类型平均分  zz 组长平均分  zy 组员平均分
userType默认 null  查询人员类型平均分  zz 组长平均分  zy 组员平均分
isTitile 是否有标题 true false

-->

<div th:fragment="init">
    <th:block  th:if="${bizGuid!=null && moduleCode!=null} " th:with="thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)} ">
        <div  th:id="${thisId}">

        </div>
        <script th:inline="javascript">
            var reviewId=[[${reviewId}]];
            var userType=[[${userType}]];

            var ggmkDfUrl=ctxGGMK+"web/MPMPPS06?moduleCode="+[[${moduleCode}]]+"&bizGuid="+[[${bizGuid}]];
            if(reviewId!=null){
                ggmkDfUrl=ggmkDfUrl +"&reviewId="+reviewId;
            }
            if([[${isTitile!=null}]]){
                ggmkDfUrl+="&isTitile="+[[${isTitile}]];
            }
            if(userType!=null){
                ggmkDfUrl=ggmkDfUrl +"&userType="+userType;
            }
            if([[${titleName!=null}]]){
                ggmkDfUrl+="&titleName="+[[${titleName}]];
            }
            if([[${mark!=null}]]){
                ggmkDfUrl+="&mark="+[[${mark}]];
            }
            $("#"+[[${thisId}]]).load(ggmkDfUrl,null,function(){
                if([[${callback!=null}]]){
                    var callback= [[${callback}]]
                    eval(callback+'()');
                }

            });
        </script>
    </th:block>
</div>