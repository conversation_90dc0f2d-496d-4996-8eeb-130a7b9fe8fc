<!--
	自由流转

	从流程节点扩展属性(extraParameters)里获取相关信息
	是否能自由流转	OTHER_TYPE	C、X、R、R1
		C:协办 双方都可以处理
		X:转办 转办后的用户处理
		R:传阅 传阅的用户只是查看，不影响主流程
		R1:传阅 传阅后的用户处理完后流转给发起传阅的用户
	选用户方式	OTHER_USER_TYPE	   	可为空，ORG、ROLE
		ORG为组织选用户 默认
		ROLE为角色选用户
	是否可多选	OTHER_USER_NUM		可为空，M、S
		M多选(默认)
		S单选
	角色选用户对应的角色编码	OTHER_USER_ROLE_CODE
-->
<div th:fragment="init" xmlns:th="http://www.w3.org/1999/html">
	<span id="wfDoOther" th:if="${taskId != null && extraParameters != null}">
		<input type="hidden" id="doOtherType" value=""/>
		<th:block th:if="${extraParameters.get('OTHER_TYPE') != null}" th:each="otherType:${#strings.listSplit(extraParameters.get('OTHER_TYPE'),',')}">
			<th:block th:with="buttons=${#strings.listSplit(otherType,'-')}">
				<button type="button" class="btn btn-primary" th:onclick="doWorkFlowOther([[${buttons[0]}]])">
					<i class="fa fa-users"></i><span>[[${buttons.size()==2 ? buttons[1] : ''}]]</span>
				</button>
			</th:block>
		</th:block>
		<script type="text/javascript" th:inline="javascript">
			$(function() {
				let taskType = $('#workFlow_taskType').val();
				if ('host'!=taskType) {
					$('#wfDoOther').hide();
				}
			})

			let extraParameters = [[${extraParameters}]];//流程扩展属性
			function doWorkFlowOther(addTaskType){
				$('#doOtherType').val(addTaskType);
				var url = ctx + 'mpad/user/selectUserList';
				let otherUserType = extraParameters.OTHER_USER_TYPE;//选用户类型
				if (otherUserType==='ROLE') {
					let roleCode = extraParameters.OTHER_USER_ROLE_CODE;//角色名称
					if (!roleCode) {
						$.modal.alertWarning("选择用户(角色)未配置角色编码，请联系管理员配置！");
						return false;
					}
					url = ctx + 'mpad/user/selectRoleUserList/' + roleCode;
				} else if (otherUserType==='DEPT') {
					url = ctx + 'mpad/user/selectDeptUser';
				} else if (otherUserType==='TENANT') {
					url = ctx + 'mpad/user/selectTenantUser';
				}
				var selectType = extraParameters.OTHER_USER_NUM;
				if(!selectType){
					selectType = "M";
				}
				url += "?selectType=" + selectType;
				if (otherUserType==='TENANT') {
					url += "&orgCode=" + $('#tenantId').val();
				}
				url += "&callback=doWorkFlowOtherCallback";
				$.modal.open("选择流转用户", url, 1000, 450);
			}

			//选人回调
			function doWorkFlowOtherCallback(userCode, userName) {
				let addTaskType = $('#doOtherType').val();
				if(!userCode){
					$.modal.alertWarning("请选择流转人");
					return false;
				}
				var doOtherUrl = ctx+"mpwf/flowInfo/doOther";
				var jsonData = {};
				jsonData["businessId"] = [[${businessId}]];//业务Id
				jsonData["taskId"] = [[${taskId}]];//任务ID
				if(!addTaskType){
					addTaskType = 'C';
				}
				jsonData["addTaskType"] = addTaskType;//流程类型
				var userLabelM = userCode;//流转人
				jsonData["userLabelM"] = userLabelM;
				var comment = "";//流转意见
				if([[${commentId}]]){
					comment = $("#"+[[${commentId}]]).val();
				} else {
					comment = $("#workFlow_comment").val();
				}
				jsonData["comment"] = comment;//流转意见
				$.modal.confirm("确认流转吗？", function () {
					$.modal.loading("正在处理中，请稍后...");
					$.ajax({
						url: doOtherUrl,
						data: JSON.stringify(jsonData),
						dataType:"json",
						contentType: "application/json",
						type: 'POST',
						success: function (result) {
							$.modal.closeLoading();
							if (result.code == web_status.SUCCESS) {
								if('C'===addTaskType || 'X'===addTaskType || 'R0'===addTaskType ||'R1'===addTaskType){
									$.operate.alertSuccessTabCallback(result);//需要关闭当前tab
								}else{
									$.modal.alertSuccess(result.msg);
								}
								//回调
								var callback = [[${callback}]];
								if(callback){
									eval(callback+'("'+userCode+'","'+userName+'")');
								}
							} else if (result.code == web_status.WARNING) {
								$.modal.alertWarning(result.msg);
							} else {
								$.modal.alertError(result.msg);
							}
						}
					});
				});
			}
		</script>
	</span>
</div>