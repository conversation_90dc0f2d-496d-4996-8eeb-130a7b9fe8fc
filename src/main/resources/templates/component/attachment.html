<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<!-- 附件上传

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
name:附件input name 默认att
id:附件input id 默认att
isrequired:附件是否必须上传 默认false 

see:是否查看
canDownload: 能否下载 默认能 否传 false
canPreview: 能否预览 默认能 否传 false
回显数据：
	sourceId 业务id,
	sourceModule 模块
	sourceLabel1 标签
	sourceLabel2 标签
	sourceLabel3 

上传属性：
maxFileCount:最大限制上传附件 默认是10
type:文件类型 如pdf
-->
<div th:fragment="init">
    <th:block
            th:with="attachmentMap=${@SAttachmentUtil.getAttachmentBySourceIdAndSourceLabelToMap(sourceId, sourceModule,sourceLabel1, sourceLabel2, sourceLabel3)},
                        thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)}">

        <th:bolck th:if="${see==null  || (see!=null && !see)}">
            <input th:name="${name==null?'att':name}" th:id="${id==null?'att':id}" type="text"
                   th:value="${attachmentMap.attachmentIds!=null?attachmentMap.attachmentIds:''}" class="form-control" style="height:0.5px;width:0px;padding:0px;margin:0px;">
            <div class="layui-upload">
                <div th:id="${'div'+thisId}">
                    <button th:if="${see==null  || (see!=null && !see)}" type="button"
                            class="layui-btn layui-btn-normal">选择文件
                    </button>
                    <!--拖拽-->
                    <!--<div class="drag-box" th:if="${see==null  || (see!=null && !see)}" style="border: 1px  dashed  #ddd;width: 200px; height: 33px;background-color:  #fff; display:  inline-block;
                 font-size:  12px;
                 color:  #666;
                 line-height: 33px;
                 cursor:  pointer;">
                         <img th:src="@{/img/line-drag.png}" width="24" height="24"/>可将附件拖拽到框中
                     </div> -->
                    <!--拖拽-->
                </div>
                <div class="layui-upload-list" style="max-width: 1000px;">
                    <table class="layui-table">
                        <colgroup>
                            <col style="width: 50%">
                            <col style="width: 20%">
                            <col style="width: 10%">
                            <col style="width: 20%">
                        </colgroup>
                        <thead>
                        <tr>
                            <th >文件名</th>
                            <th >大小</th>
                            <th>上传进度</th>
                            <th >操作</th>
                        </tr>
                        </thead>
                        <tbody th:id="${'list'+thisId}">
                        <tr th:if="${type!=null}"><font color="red">提示:仅能上传后缀为[[${type}]]的文件</font></tr>
                        <th:block th:if="${attachmentMap!=null && not #lists.isEmpty(attachmentMap.attachmentList)}">
                            <tr th:each="attachment: ${attachmentMap.attachmentList} " class="upload"
                                th:id="${'upload-'+thisId+'-'+attachmentStat.index}">
                                <td th:utext="${attachment.attachmentName}"></td>
                                <td th:utext="${attachment.attachmentSize+'kb'}"></td>
                                <td>已上传</td>
                                <td><input class="key" type="hidden" th:id="${'attachment-id-'+attachmentStat.index}"
                                           th:value="${attachment.attachmentId}">
                                    <button type="button" th:if="${canDownload==null || (canDownload!=null && canDownload)}"
                                            class="btn btn-sm btn-primary"
                                            th:onclick="download([[${attachment.attachmentId}]]);">下载
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary" th:if="${canPreview==null || (canPreview!=null && canPreview)}"
                                            th:onclick="preview([[${attachment.attachmentId}]]);">预览
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary" th:if="${canEdit!=null && canEdit}"
                                            th:onclick="editFile([[${attachment.attachmentId}]]);">编辑
                                    </button>
                                    <button  type="button"  th:if="${see==null  || (see!=null && !see)}"
                                             class="btn btn-sm btn-primary"
                                             th:onclick="deleteFile([[${attachment.attachmentId}]],[[${attachmentStat.index}]],[[${thisId}]],[[${id}]]);">
                                        删除
                                    </button>
                                </td>
                            </tr>
                        </th:block>
                        </tbody>
                    </table>
                </div>
            </div>
        </th:bolck>

        <th:bolck th:unless="${see==null  || (see!=null && !see)}">

            <th:block th:if="${#lists.isEmpty(attachmentMap.attachmentList)}">
                <div class="upload-list fn-clear form-control-static">
                    <p class="fn-left ">无附件 </p>
                </div>
            </th:block>
            <th:block th:unless="${#lists.isEmpty(attachmentMap.attachmentList)}">
                <div class="upload-list fn-clear attachmentShow" th:each="attachment: ${attachmentMap.attachmentList}">
                    <p class="fn-left ">
                        <a th:if="${canDownload==null || (canDownload!=null && canDownload)}" target="_blank" th:href="@{/attachment/download/}+${attachment.attachmentId}"><i class="fa fa-paperclip"></i>[[${attachment.attachmentName}]]</a>
                        <a th:unless="${canDownload==null || (canDownload!=null && canDownload)}">[[${attachment.attachmentName}]]</a>
                    </p>
                        <button th:if="${canDownload==null || (canDownload!=null && canDownload)}" type="button"
                                 class="btn btn-sm btn-primary"
                                 th:onclick="download([[${attachment.attachmentId}]]);">下载
                        </button>

                        <button th:if="${canPreview==null || (canPreview!=null && canPreview)}" type="button" class="btn btn-sm btn-primary"
                                th:onclick="preview([[${attachment.attachmentId}]]);">预览
                        </button>

                        <button th:if="${canEdit!=null && canEdit}" type="button" class="btn btn-sm btn-primary"
                                th:onclick="editFile([[${attachment.attachmentId}]]);">编辑
                        </button>

                </div>
            </th:block>


            <input th:name="${name==null?'att':name}" th:id="${id==null?'att':id}" type="hidden"
                   th:value="${attachmentMap.attachmentIds!=null?attachmentMap.attachmentIds:''}" class="form-control">

        </th:bolck>

        <script type="text/javascript" th:inline="javascript" th:if="${see==null  || (see!=null && !see)}">
            //初始化页面信息
            var isrequired = [[${isrequired}]];
            var id = [[${id==null?'att':id}]]
            if (isrequired != null && isrequired) {
                $("#" + [[${'fjLabel'+thisId}]]).addClass("is-required")
                $("#" + id).attr("required", "");
            }
            layui.use(['upload', 'element', 'layer'], function () {
                var $ = layui.jquery
                    , upload = layui.upload
                    , element = layui.element
                    , layer = layui.layer;
                var uploadListIns = upload.render({
                    elem: '#' + [[${'div'+thisId}]]
                    , elemList: $('#' + [[${'list'+thisId}]]) //列表元素对象
                    , url: ctx + 'attachment/upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                    , accept: 'file'
                    , exts: [[${type==null?'':type}]]
                    , multiple: true
                    , number: [[${maxFileCount==null?30:maxFileCount}]]
                    , auto: true
                    , choose: function (obj) {
                        if(0!=[[${maxFileCount==null?30:maxFileCount}]]){//需要验证附件总数
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            if(attachmentIds){
                                var allFileTotal = attachmentIds.split(",").length;
                                if(allFileTotal>=[[${maxFileCount==null?30:maxFileCount}]]){
                                    layer.msg('附件总数不能超过'+[[${maxFileCount==null?30:maxFileCount}]]);
                                    return false;
                                };
                            };
                        };
                        var that = this;
                        var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">'
                                , '<td>' + file.name + '</td>'
                                , '<td>' + (file.size / 1014).toFixed(1) + 'kb</td>'
                                , '<td><div class="layui-progress" lay-filter="progress-demo-' + index + '"><div class="layui-progress-bar" lay-percent=""></div></div></td>'
                                , '<td> <input class="key" id="attachment-id-' + index + '"  type="hidden"><input   type="hidden" required  id="uploadStatus-' + index + '" name="uploadStatus-' + index + '"  data-msg-required="附件未上传完成,请稍后!">'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-reload layui-hide">重传</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-download">下载</button>'
                               	, '<button  type="button"  class="btn btn-sm btn-primary demo-preview">预览</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-delete">删除</button>'
                                , '  <label for="uploadStatus-' + index + '" class="error" style="position: initial"></label>'
                                , '</td>'
                                , '</tr>'].join(''));


                            //单个重传
                            tr.find('.demo-reload').on('click', function () {
                                obj.upload(index, file);
                            });

                            //删除
                            tr.find('.demo-delete').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                                if (attachmentIds != null && attachmentIds != '') {
                                    var arr = attachmentIds.split(",");
                                    if (arr.includes(key)) {
                                        arr.splice(arr.indexOf(key), 1);
                                        $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                                    }
                                }
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                            });
                            //下载
                            tr.find('.demo-download').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/download/" + key);
                            });
                            //预览
                            tr.find('.demo-preview').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/preview/" + key);
                            });

                            that.elemList.append(tr);
                            element.render('progress'); //渲染新加的进度条组件
                        });
                    }
                    , before: function (obj) {
                        // $("#uploading_" + id).attr("required", "required");
                        // console.log("1")
                    }
                    , done: function (res, index, upload) { //成功的回调
                        var that = this;
                        if (res.status == 1) { //上传成功
                            var that = this;
                            var tr = that.elemList.find('tr#upload-' + index);
                            tr.find("#attachment-id-" + index).val(res.fileid);

                            console.log(index)
                            console.log(this.files)
                            delete this.files[index]; //删除文件队列已经上传成功的文件
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            var arr = attachmentIds.split(",");
                            if (attachmentIds == null || attachmentIds == '') {
                                arr = [];
                            }
                            arr.push(res.fileid);
                            $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                            tr.find("td").eq(2).html("已上传");
                            //上传完成 去掉必填
                            tr.find("#uploadStatus-"+ index).removeAttr("required");

                            return;
                        }
                        this.error(index, upload);
                    }
                    , allDone: function (obj) { //多文件上传完毕后的状态回调

                    }
                    , error: function (index, upload) { //错误回调
                        var that = this;
                        var tr = that.elemList.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
                        alert("上传失败")
                    }
                    , progress: function (n, elem, e, index) { //注意：index 参数为 layui 2.6.6 新增
                        console.log(index)
                        element.progress('progress-demo-' + index, n + '%'); //执行进度条。n 即为返回的进度百分比
                    }
                });

            });

        </script>
        <script type="text/javascript" th:inline="javascript">
            //编辑附件
            function editFile(attachmentId){
                //获取当前id
                $.ajax({
                    method: "post",
                    async: false,
                    url: ctx+"attachment/getMPFileEditUrl",
                    data:{"attachmentId":attachmentId},
                    success: function (data) {
                        console.log(data)
                        window.open(data.data);
                    }
                });
            }
        </script>
    </th:block>
</div>
