<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
    <div th:fragment="init">
        <th:block th:with="pageNo=${pageNo},configType='grid',urlMapping=${urlMapping},listUrl=${listUrl}">
            <script th:inline="javascript">

                var gridPrefix = ctx + [[${urlMapping}]];
                $(function() {
                    getConfigColumns();
                });
                // 获取配置列
                function getConfigColumns() {
                    setConfigColumns([[${@SearchViewUtil.getPageConfigDataList(pageNo,configType)}]]);
                }
                // 设置配置列
                function setConfigColumns(list) {
                    var columns = [{
                        checkbox: true
                    }];

                    list.forEach(function(item) {
                        if($.common.isNotEmpty(item.fieldPlaceHolder)){//转义小代码
                            columns.push({
                                field : item.fieldCode,
                                title : item.fieldName,
                                align: 'center',
                                formatter: function(value, row, index) {
                                    var split = item.fieldPlaceHolder.split("-");
                                    var datas = gridGetDictList(split[0],split[1]);
                                    return $.table.selectDictLabel(datas, value);
                                }
                            })
                        }else {
                            columns.push({
                                field : item.fieldCode,
                                title : item.fieldName
                            })
                        }
                    });
                    if (typeof gridOperator == 'function'){
                        gridOperator(columns);
                    }
                    if(!table.get(table.options.id)){
                        initTable(columns);
                    } else {
                        refreshTable(columns);
                    }
                    initShowAndHidden(list);
                }
                // 刷新表格
                function refreshTable(columns) {
                    var options = {
                        columns: columns
                    };
                    $("#" + table.options.id).bootstrapTable('refreshOptions',options);
                }
                // 初始化表格
                function initTable(columns){
                    var options = {
                        url: ctx + [[${listUrl}]],
                        createUrl: gridPrefix + "/add",
                        updateUrl: gridPrefix + "/edit/{id}",
                        removeUrl: gridPrefix + "/remove",
                        exportUrl: gridPrefix + "/export",
                        modalName: "数据列表",
                        columns: columns
                    };
                    $.table.init(options);
                }


                //获取小代码列表
                function gridGetDictList(businessType,dictCode) {
                    var url = ctx + "configSearch/gridGetDictList";
                    var dataJson = {"businessType":businessType, "dictCode": dictCode};
                    var datas;
                    $.ajax({
                        url: url,
                        type: "get",
                        async: false,
                        data:dataJson,
                        dataType: "json",
                        contentType: 'application/json;charset=UTF-8',
                        success: function (result) {
                            datas = result.data;
                        }
                    });
                    console.info("datas-->" + datas);
                    return datas;
                }

                //隐藏显示状态
                function initShowAndHidden(list) {
                    list.forEach(function(item) {
                        if (item.isChecked == '1') {
                            console.info(item);
                            $.table.showColumn(item.fieldCode);
                        } else {
                            console.info(item);
                            $.table.hideColumn(item.fieldCode);
                        }
                    });
                }
            </script>
        </th:block>
    </div>
</html>