 <!--
	流程审批履历
输入参数:
	processInstanceId 流程实例ID
-->
<div th:fragment="init">
	<div class="panel-group" aria-multiselectable="true" th:if="${not #strings.isEmpty(processInstanceId)}">
		<th:block th:with="commentList=${@SWorkFlowUtil.getCommentList5(processInstanceId)}">
			<div th:if="${commentList.size()>0}" class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#spyj_commentList" aria-expanded="false" class="collapsed">
							审批履历
							<span class="pull-right">
								<i class="fa fa-chevron-down" aria-hidden="true"></i>
							</span>
							<a th:if="${@UserUtil.isAdmin()}" th:onclick="processAdmin([[${businessId}]])" style="text-decoration:underline;">管理员操作</a>
							<th:block th:if="${variable!=null && #maps.containsKey(variable,'oaUrl')}">
								<a th:onclick="openGwUrl([[${variable.get('oaUrl')}]])" style="text-decoration:underline;">查看公文信息>></a>
							</th:block>
						</a>
					</h4>
				</div>
				<div id="spyj_commentList" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group">
							<div class="col-sm-12 select-table table-striped">
								<table id="taskHistoryList"></table>
							</div>
						</div>
					</div>
				</div>
				<script type="text/javascript" th:inline="javascript">
					$(function() {
						var data = [[${commentList}]] || [];
						var options = {
							code: "taskId",
							parentCode: "patentId",
							uniqueId: "taskId",
							id: "taskHistoryList",
							expandAll: false,
							expandFirst: false,
							data: data,
							modalName: "审批历史",
							showSearch: false,
							showRefresh: false,
							showToggle: false,
							showColumns: false,
							clickToSelect: false,
							toolbar: "TaskHistoryToolbar",
							rememberSelected: false,
							pagination: false,
							columns: [{
								field: 'taskId',
								title: '流程ID',
								visible: false
							},
								{
									field: 'taskName',
									title: '任务名称',
									formatter: function (value, row, index) {
										var showName = value;
										var assigneeFullname =  row.assigneeFullname;
										if (assigneeFullname) {
											showName += '-' + assigneeFullname;
										}
										var endTime =  row.endTime;
										if (endTime) {
											showName += '-' + row.endTime
										}
										return showName;
									}
								},
								{
									field: 'opinion',
									title: '审批意见',
									formatter: function (value, row, index) {
										if (value != null && value != "") {
											return $.table.tooltip(value,16,"open");
										}
										return value;
									}
								}
							]
						};
						$.treeTable.init(options);
					});

					function moreComment(){
						if($('.moreComment').is(':hidden')){
							$('.moreComment').show();
							var imgSrc = [[@{/img/moreminus.png}]];
							$('#moreCommentId').html("<img src='"+imgSrc+"'/>隐藏");
						}else{
							$('.moreComment').hide();
							var imgSrc = [[@{/img/moreplus.png}]];
							$('#moreCommentId').html("<img src='"+imgSrc+"'/>更多");
						}
					}

					function processAdmin(businessId){
						var url = ctxMP + "web/MPWF0010?businessId="+businessId;
						window.open(url);
					}
					function openGwUrl(url){
						window.open(url);
					}
				</script>
			</div>
		</th:block>
	</div>
</div>
