<!--
    name:input name 默认 selectbox
    id:input id 默认 name值
    labelName:label的text 默认选择

    see:回显(true,false) 默认false
    isrequired:是否必填 默认false
    value: 回显值
    businessType,dictCode  : 数据字典key
    isfirst: 是否添加首选项
    firstName 首选项名称 默认 请选择
    firstValue 首选项名称 默认 ''
    notShowValue 不显示值（选项）
    multimultiple 是否多选

    data 自定义数据
    dataKey 自定义数据value 的key
    dataNameKey 自定义数据name 的key

    dataName（see=true 需要设置 ） 回显名称 如果不设置 需要传入data dataKey dataNameKey进行筛选查询名称

-->


<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'selectbox':name},
               	 	id=${id==null?name:id},
               	 	multimultiple=${multimultiple==null?false:multimultiple},

               	 	firstName=${firstName==null?'请选择':firstName},
               	 	firstValue=${firstValue==null?'':firstValue},
               	 	arrayValue=${value==null?null:#strings.arraySplit(value, ',')},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}

               	 	">

        <th:block th:if="${!see}">

            <th:block th:if="${#lists.isEmpty(data)}">
                <select th:class="form-control" th:id="${id}" th:if="${!see}" th:multiple="${multimultiple}"
                        th:name="${name}"
                        th:with="dictData=${@SDictUtil.getDictList(businessType,dictCode)}">

                    <option th:if="${isfirst!=null && isfirst}" th:text="${firstName}"
                            th:value="${firstValue}"></option>
                    <option th:each="dict : ${dictData}" th:selected="${arrayValue!=null && #lists.contains(arrayValue,dict.dictValue)}"
                            th:text="${dict.dictName}"
                            th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict.dictValue)}"
                            th:value="${dict.dictValue}"></option>
                </select>
            </th:block>

            <th:block th:unless="${#lists.isEmpty(data)}">
                <select th:class="form-control" th:id="${id}" th:if="${!see}" th:multiple="${multimultiple}"
                        th:name="${name}">

                    <option th:if="${isfirst!=null && isfirst}" th:text="${firstName}"
                            th:value="${firstValue}"></option>

                    <option th:each="dict : ${data}" th:selected="${arrayValue!=null && #lists.contains(arrayValue,@SDictUtil.getValueByKey(dict,dataKey))}"
                            th:text="${@SDictUtil.getValueByKey(dict,dataNameKey)}"
                            th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict[dataKey])}"
                            th:value="${@SDictUtil.getValueByKey(dict,dataKey)}"></option>
                </select>
            </th:block>
        </th:block>

        <th:block th:unless="${!see}">

            <div class="form-control-static" th:id="${id}" th:if="${dataName!=null }" th:utext="${dataName}"></div>

            <div class="form-control-static" th:id="${id}" th:unless="${dataName!=null }"
                 th:utext="${businessType!=null && dictCode!=null ?@SDictUtil.getDictName(businessType,dictCode,value):@SDictUtil.getNameByData(data,dataKey,dataNameKey,value)}"></div>
        </th:block>


        <script th:if="${!see}" th:inline="javascript">
            $('#' + [[${id}]]).select2();
            if ([[${isrequired!=null && isrequired}]]) {
                $("#" + [[${id}]]).attr("required", "");
            }

        </script>

    </th:block>

</div>




