<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('子流程列表')" />
	<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
	<div class="col-sm-12 select-table table-striped">
		<table id="bootstrap-table"></table>
	</div>
</div>
<script th:inline="javascript">
	var prefix = ctx + "mpwf/flowInfo";
	$(function() {
		var options = {
			url: prefix + "/childProcessData/"+[[${businessId}]],
			modalName: "子流程",
			columns: [
				{
					field: 'flowId',
					title: '流程实例ID',
					visible: false
				},
				{
					field: 'flowCode',
					title: '流程编码',
					visible: false
				},
				{
					field: 'createUserLabel',
					title: '创建人',
					visible: false
				},
				{
					field: 'extra1',
					title: '会签节点',
					visible: false
				},
				{
					field: 'extra2',
					title: '会签节点',
					visible: false
				},
				{
					field: 'departmentNo',
					title: '会签部门',
					formatter: function(value, row, index) {
						return value + '-' + row.departmentNoName;
					}
				},
				{
					field: 'lastOperator',
					title: '上一步操作人',
                    formatter: function(value, row, index) {
                        return row.lastOperator + '-' + row.lastOperatorName;
                    }
				},
                {
                    field: 'lastTime',
                    title: '上一步操作时间'
                },
				{
					field: 'currentOperator',
					title: '当前操作人'
				},
				{
					field: 'currentActivity',
					title: '当前节点',
					visible: false
				},
                {
                    field: 'currentActivityName',
                    title: '当前节点'
                },
                {
                    field: 'flowState',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if ('ended'==value) {
                            return "结束";
                        } else if ('active'==value) {
                            return "流转中";
                        } else if ('termination'==value) {
							return "终止";
						} else if ('tempTermination'==value) {
							return "中止";
						} else {
                            return value;
                        }
                    }
                },
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryFlow(\'' + row.flowId + '\')"><i class="fa fa-search"></i>流程跟踪</a> ');
						if ('active'==row.flowState) {
							actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="endProcessForever(\'' + row.flowId + '\')"><i class="fa fa-edit"></i>流程终止</a> ');
						}
						return actions.join('');
					}
				}
            ]
		};
		$.table.init(options);
	});

    function queryFlow(processInstanceId){
        var url = ctxMP + "web/MPWF0011?processInstanceId="+processInstanceId;
        window.open(url);
    }

    function endProcessForever(flowId){
        $.modal.confirm("确认终止该流程吗？", function () {
            $.modal.loading("正在处理中，请稍后...");
            var jsonData = {};
            jsonData["processInstanceId"] = flowId;//流程实例Id
            $.ajax({
                url: prefix + "/endProcessForever",
                data: JSON.stringify(jsonData),
                dataType: "json",
                contentType: "application/json",
                type: 'POST',
                success: function (result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
						$.table.refresh();
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });
        });
    }
</script>
</body>
</html>