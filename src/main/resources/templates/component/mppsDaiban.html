<!-- 
专家评审待办数目
输入参数:
 moduleName
 moduleCode
-->
<div th:fragment="init">
	<script type="text/javascript" th:inline="javascript">
		$(document).ready(function(){
			try {
				var moduleCode = [[${moduleCode}]];
				var moduleName = [[${moduleName}]];
				if(moduleCode){
					var mppsUrl = ctx + "mpwf/flowInfo/queryMPPSDbList/"+moduleCode;
					$.ajax({
						url: mppsUrl,
						dataType: 'json',
						type: "get",
						cache: false,
						success: function (rtn) {
							if(rtn.status==1){
								var html ="";
								var moduleUrl = "";
								for(var i=0;i<rtn.data.length;i++){
									var row = rtn.data[i];
									var mppsDBUrl = ctxMP + 'web/MPPS041?processCode='+row.processCode+'&activityCode='+row.currentActivity+'&moduleCode='+row.moduleCode;
									html += '<tr style="border-left: #191f5c solid 3px; background-color: #f0f5fb; height: 38px; line-height: 38px; border-bottom: 15px solid #fff;">';
									html += '<td width="400px" style="float: left; height: 40px; line-height: 40px; padding-left: 15px;">';
									html += '<a th:href="' + mppsDBUrl + '" style="color: #000;">'+row.moduleName+' / '+row.currentActivityName+'</a>';
									html += '</td>';

									html += '<td width="300px" align="left" style=" padding-left: 100px; text-align:right	">';
									html += '<a href="' + mppsDBUrl + '">'
									html += '<span style="color: #fff; border-radius: 50px; padding: 3px 5px; background-color: red; font-weight: bold;" >'+row.todoNum+'</span>';
									html += '&nbsp;<span style="color: #000;">份</span>&nbsp;&nbsp;';
									html += '<span>进入处理》</span>';
									html += '</a>';
									html += '</td>';
									html += '</tr>';
								}
								//$("#"+moduleCode).append(html);
								$("table").append(html);
								//document.write(html);
							}
						},
						error: function (XMLHttpRequest, textStatus, errorThrown) {
							alert(XMLHttpRequest.status);
							alert(XMLHttpRequest.readyState);
							alert(textStatus);
						}
					});
				}
			} catch (e) {
				console.log(e.name + ": " + e.message);
				console.log(e.stack);
			}
		});
	</script>
</div>