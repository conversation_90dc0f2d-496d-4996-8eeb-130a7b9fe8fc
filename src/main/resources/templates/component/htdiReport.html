<!-- 
	报支清单反馈信息
输入参数:
	businessGuid 业务主键
-->
<div th:fragment="reportStatus">
	<!-- 报支清单状态信息 start -->
	<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
        <div class="panel panel-default">
            <div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#rsListTable" class="collapsed">
					报支清单状态信息
                    <span class="pull-right">
                   		<i class="fa fa-chevron-down"></i>
                   	</span>
                  		</a>
                  	</h4>
            </div>
            <div id="rsListTable" class="panel-collapse collapse in">
            	<div class="panel-body">
                    <!--一列-->
		            <div class="form-group">
                           <div class="col-sm-12">
			                <div class="col-sm-12 select-table">
			                    <table id="rsList-table"></table>
			                </div>
                          	</div>
                  		</div>
                  	<!--一列end-->
            </div>
           </div>
		</div>
	</div>
	<!-- 报支清单状态信息 end -->
    <script type="text/javascript" th:inline="javascript">
    $(function() {
    	// 初始化数据, 可以由后台传过来
    	var data = [[${@HTJSUtil.getRsList(businessGuid)}]];
	    var options = {
    		id: "rsList-table",
	    	data: data,
            pagination: false,
	        showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect: false,
            rememberSelected: false,
            pagination: false,
            sidePagination: "client",
	        columns: [
	        {
	        	field: 'index',
	        	align: 'center',
	        	title: "序号",
                formatter: function (value, row, index) {
                	return $.table.serialNumber(index);
                }
            },
	        {
	            field: 'expanseReportId',
	            align: 'center',
	            title: '发报支清单号'
	        },
	        {
	            field: 'receiptNo',
	            align: 'center',
	            title: '交接单号'
	        },
	        {
	            field: 'billStatus',
	            align: 'center',
	            title: '报支状态',
                formatter: function (value, row, index) {
                	switch(value) {
	                	case "-1":
							value = "抛帐中心接收失败";
							break;
						case "17":
							value = "财务待审核";
							break;
						case "20":
							value = "财务审核";
							break;
						case "21":
							value = "财务驳回";
							break;
						case "22":
							value = "财务复核";
							break;
						case "23":
							value = "财务红冲";
							break;
						case "30":
							value = "部分付款";
							break;
						case "31":
							value = "全部付款";
							break;
               			} 
                    return value;
                }
	        },
	        {
	            field: 'voucherNo',
	            align: 'center',
	            title: '凭证号'
	        },
	        {
	            field: 'voucherDate',
	            align: 'center',
	            title: '凭证日期'
	        },
	        {
	            field: 'invoiceNo',
	            align: 'center',
	            title: '发票号'
	        },
	        {
	            field: 'invoiceCode',
	            align: 'center',
	            title: '发票代码'
	        },
	        {
	            field: 'rejectType',
	            align: 'center',
	            title: '驳回类型',
                formatter: function (value, row, index) {
                    if($.common.equals(value,"1")){
                    	value = "实物";
                    }else if ($.common.equals(value,"2")){
                    	value = "信息";
                    }
                    return value;
                }
	        },
	        {
	            field: 'remark',
	            align: 'center',
	            title: '备注',
	            formatter: function (value, row, index) {
                    // if (value != null && value != "") {
                    //     if (value.length > 16) {
                    //         return '<spen title="' + value + '">' + value.substring(0, 16) + "...</spen>";
                    //     }
                    // }
                    return value;
                }
	        },
	        {
	            field: 'createDate',
	            align: 'center',
	            title: '创建时间',
                formatter: function (value, row, index) {
                    return value.substring(0, 19).replace("T"," ");
                }
	        }]
	    };
	    $.table.init(options);
	});
    </script>
</div>

<!-- 
	导出交接单pdf
输入参数:
	businessGuid 业务主键
-->
<div th:fragment="FKSQPDF">
	<div class="btn-group dropup">
		<button type="button" class="btn btn-primary" th:onclick="downloadFKSQPDF()" ><i class="fa fa-hdd-o"></i>导出交接单pdf</button>
	</div>
	<script type="text/javascript" th:inline="javascript">
		function downloadFKSQPDF() {
	    	var config = {
	            url: ctx+"htdi/reportHeader/queryFKSQPDF",
	            type: "post",
	            data: {'businessGuid' : [[${businessGuid}]] },
	            dataType: "json",
	            beforeSend: function () {
	                $.modal.loading("正在导出交接单pdf，请稍后...");
	            },
	            success: function (result) {
	                if (result.code == web_status.SUCCESS) {
	                    window.location.href = ctx + "common/downloadPDF?fileName=" + encodeURI(result.msg+".pdf") + "&delete=" + false;
	                } else if (result.code == web_status.WARNING) {
	                    $.modal.alertWarning(result.msg)
	                } else {
	                    $.modal.alertError(result.msg);
	                }
	                $.modal.closeLoading();
	            }
	        };
	        $.ajax(config)
	    }
	</script>
</div>