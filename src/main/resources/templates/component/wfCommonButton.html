<!--
	按钮操作
	从流程节点扩展属性(extraParameters)里获取相关信息
	COMMON_BUTTON
	按钮名称-服务号-执行后的操作
	中止-S_HT_GL_C1-CLOSE
		按钮名称:中止
		按钮操作对应的服务号:S_HT_GL_C1
		操作成功后:CLOSE 关闭当前页面
-->
<div th:fragment="init" xmlns:th="http://www.w3.org/1999/html">
	<th:block th:if="${taskId != null && extraParameters != null}">
		<th:block th:if="${extraParameters.get('COMMON_BUTTON') != null}" th:each="commonButton:${#strings.listSplit(extraParameters.get('COMMON_BUTTON'),',')}">
			<th:block th:with="buttons=${#strings.listSplit(commonButton,'-')}">
				<button type="button" class="btn btn-primary" th:onclick="doCommonButton([[${commonButton}]])">
					<i class="fa fa-wrench"></i><span>[[${buttons[0]}]]</span>
				</button>
			</th:block>
		</th:block>
		<script type="text/javascript" th:inline="javascript">
			function doCommonButton(button) {
				var buttons = button.split("-");
				var buttonName = buttons[0];
				var serviceId = buttons[1];
				var callbackOperator = "";
				if (buttons.length>2) {
					callbackOperator = buttons[2];
				}
				var doCommonButtonUrl = ctx+"mpwf/flowInfo/doCommonButton";
				var jsonData = {};
				jsonData["businessId"] = [[${businessId}]];//业务Id
				jsonData["taskId"] = [[${taskId}]];//任务ID
				jsonData["serviceId"] = serviceId;//服务ID
				jsonData["buttonName"] = buttonName;//操作名称
				jsonData["comment"] = $("#workFlow_comment").val();//流转意见
				$.modal.confirm("确认"+buttonName+"吗？", function () {
					$.ajax({
						url: doCommonButtonUrl,
						data: JSON.stringify(jsonData),
						dataType:"json",
						contentType: "application/json",
						type: 'POST',
						success: function (result) {
							if (result.code == web_status.SUCCESS) {
								if('CLOSE'===callbackOperator){//需要关闭当前tab
									$.operate.alertSuccessTabCallback(result);
								}else{
									$.modal.alertSuccess(result.msg);
								}
							} else if (result.code == web_status.WARNING) {
								$.modal.alertWarning(result.msg)
							} else {
								$.modal.alertError(result.msg);
							}
						}
					});
				});
			}
		</script>
	</th:block>
</div>