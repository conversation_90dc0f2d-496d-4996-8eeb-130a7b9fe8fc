<!-- 
	自由流转

输入参数:
businessId 	业务ID 必填
taskId 		任务ID 必填

buttonName 	按钮名字 默认【自由流转】
addTaskType 流程类型 默认C:复制(不结束当前人任务), X:转派任务(结束当前人任务), R:传阅任务(对主流程无任何影响), R1:传阅任务(对主流程无任何影响,传阅后用户看不到待办，直到所有传阅任务被完成)

commentId  	对应表单中意见ID
selectType  是否可以流转多人 默认M 多选 单选传S

orgCode 	组织编码 只能选择该组织下用户
callback 	回调函数,返回选择的用户
-->
<div th:fragment="init">
	<th:block th:if="${taskId != null}" th:with="
				buttonName=${buttonName==null?'自由流转':buttonName}
			">
		<button type="button" class="btn btn-primary" th:onclick="doWorkFlowOther()"><i class="fa fa-users"></i>&nbsp;[[${buttonName}]]</button>
	</th:block>
	<script type="text/javascript" th:inline="javascript">
	function doWorkFlowOther(){
		var url = ctx + 'mpad/user/selectUserList';
		
        var selectType = [[${selectType}]];
        if(!selectType){
        	selectType = "M";
		}
        url += "?selectType=" + selectType;
        
        var orgCode = [[${orgCode}]];
        url += "&orgCode=" + orgCode;
        
        url += "&callback=doWorkFlowOtherCallback";
        $.modal.open("选择流转用户", url, '1000', '500');	
	}
	
    function doWorkFlowOtherCallback(userCode, userName) {
    	if(!userCode){
    		 $.modal.alertWarning("请选择流转人");
    		 return false;
    	}
    	var doOtherUrl = ctx+"mpwf/flowInfo/doOther";
		var jsonData = {};
		jsonData["businessId"] = [[${businessId}]];//业务Id
		jsonData["taskId"] = [[${taskId}]];//任务ID
		var addTaskType = [[${addTaskType}]];
		if(!addTaskType){
			addTaskType = "C";
		}
		jsonData["addTaskType"] = addTaskType;//流程类型
		var userLabelM = userCode;//流转人
		jsonData["userLabelM"] = userLabelM;
		var comment = "";//流转意见
		if([[${commentId}]]){
			comment = $("#"+[[${commentId}]]).val();
		}
		jsonData["comment"] = comment;//流转意见
		$.modal.confirm("确认流转吗？", function () {
			$.ajax({
	            url: doOtherUrl,
	            data: JSON.stringify(jsonData),
	            dataType:"json",
	            contentType: "application/json",
	            type: 'POST',
	            success: function (result) {
	                if (result.code == web_status.SUCCESS) {
	                	if('R1'==addTaskType){
	                		$.operate.alertSuccessTabCallback(result);//R1需要关闭当前tab
	                	}else{
	                		$.modal.alertSuccess(result.msg);
	                	}
	                	//回调
	               		var callback = [[${callback}]];
	               		if(callback){
	               			eval(callback+'("'+userCode+'","'+userName+'")');
	               		}
	                } else if (result.code == web_status.WARNING) {
	                    $.modal.alertWarning(result.msg)
	                } else {
	                    $.modal.alertError(result.msg);
	                }
	            }
	        });
		});
    }
	</script>
</div>