
<!--
name:input name 默认 fwb
id:fwb  每一个id为 id+选择value   默认 name 值
see:回显(true,false) 默认false
value: 回显值
height 富文本默认高度
isrequired:是否必填 默认false
-->


<div th:fragment="init">
    <th:block th:with="thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)},
    	            see=${see!=null&&see?true:false},
               	 	name=${name==null?'fwb':name},
               	 	id=${id==null?name:id},
               	 	height=${height==null?400:height}
">
        <th:block th:if="${!see}">
            <div th:utext="${value}" th:id="${thisId}">

            </div>
            <input th:id="${id}" th:name="${name}" th:value="${value}" type="hidden"  th:required="${isrequired!=null && isrequired}">


        </th:block>
        <th:block th:unless="${!see}" >
            <div th:utext="${value}" th:id="${thisId}"  class="form-control-static" >

            </div>
        </th:block>




        <script th:inline="javascript" th:if="${!see}">
            $(document).ready(function () {
                $("#"+[[${thisId}]]).summernote({
                    lang: 'zh-CN',
                    height: [[${height}]],
                    callbacks: {//回调函数，重写onImageUpload方法
                        onImageUpload: function (files) {
                            sendFile(this, files[0], this);
                        },
                        onPaste: function (ne) {
                            var bufferText = ((ne.originalEvent || ne).clipboardData || window.clipboardData).getData('Text/plain');
                            //    ne.preventDefault();
                            ne.preventDefault ? ne.preventDefault() : (ne.returnValue = false);
                            // Firefox fix
                            document.execCommand("insertText", false, bufferText);
                        }, onChange:function(contents,$editable){
                            $("#"+[[${id}]]).val(contents);
                        }
                    }
                });
            });



        </script>
    </th:block>

</div>




