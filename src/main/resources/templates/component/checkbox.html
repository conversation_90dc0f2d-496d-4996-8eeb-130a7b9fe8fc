<!--
name:checkbox name 默认 checkbox
id  每一个选项id为id+选项value   id默认为name值
callback： 点击时回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
dictCode businessType: 数据字典key
notShowValue 不显示值（选项）
data 自定义数据
dataKey 自定义数据value 的key
dataNameKey 自定义数据name 的key

dataName（see=true 需要设置 ） 回显名称 如果不设置需要传入data dataKey dataNameKey进行筛选查询名称

-->


<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'checkbox':name},
               	 	id=${id==null?name:id},
               	 	arrayValue=${value==null?null:#strings.arraySplit(value, ',')},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}">

        <th:block th:if="${!see}">

            <th:block th:if="${#lists.isEmpty(data)}">
                <label class="check-box"
                       th:each="dict : ${@SDictUtil.getDictList(businessType,dictCode)}"
                       th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict.dictValue)}">
                    <input th:attr="checked=${arrayValue!=null && #lists.contains(arrayValue,dict.dictValue)}" th:id="${id+dict.dictValue}" th:name="${name}"
                           th:text="${dict.dictName}"
                           th:value="${dict.dictValue}"
                           type="checkbox"></label>

            </th:block>
            <th:block th:unless="${#lists.isEmpty(data)}">
                <label class="check-box"
                       th:each="dict : ${data}"
                       th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict[dataKey])}">
                    <input th:attr="checked=${arrayValue!=null && #lists.contains(arrayValue,dict[dataKey])}" th:id="${id+dict[dataKey]}" th:name="${name}"
                           th:text="${dict[dataNameKey]}"
                           th:value="${dict[dataKey]}"
                           type="checkbox"></label>
            </th:block>


            <label class="error" th:for="${name}"></label>
        </th:block>
        <th:block th:unless="${!see}">

            <div class="form-control-static" th:if="${dataName!=null }" th:utext="${dataName}"></div>

            <div class="form-control-static" th:unless="${dataName!=null }"
                 th:utext="${businessType!=null && dictCode!=null ?@SDictUtil.getDictName(businessType,dictCode,value):@SDictUtil.getNameByData(data,dataKey,dataNameKey,value)}"></div>
        </th:block>


        <script th:if="${!see}" th:inline="javascript">
            if ([[${isrequired!=null && isrequired}]]) {
                $("input:checkbox[name='" + [[${name}]] + "']").attr("required", "");
            }

        </script>
        <script th:if="${callback!=null}" th:inline="javascript">

            $('input:checkbox[name="' + [[${name}]] + '"]').on('ifClicked', function (event) {
                var callback = [[${callback}]]
                eval(callback + '("' + $(this).val() + '",' + !$(this).is(":checked") + ')');
            });

        </script>
    </th:block>

</div>


