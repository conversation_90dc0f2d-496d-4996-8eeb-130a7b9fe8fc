
<!-- 附件上传
bizId
moduleCode
columns
isCkFS 默认false 详情页是否显示打分
isXsZC 默认true 是否显示操作列
psbgSfzk 默认false 是否展开组员页(意见)
isGlps 是否管理评审 默认是 且当前用户是组织评审人员可管理
showYj 是否显示评审意见 默认是 且当前用户是组织评审人员可管理

addPSColumn 动态添加专家列
  //专家评审信息
    function addPSColumn(columns) {
        columns.push({
            field: 'reviewFlag',
            title: '是否同意小批量试制',
            formatter: function (value, row, index) {
                return '同意';
            }
        })
    }

-->
<div th:fragment="init">
    <th:block  th:with="thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)},
    psData=${T(com.baosight.bsvkkj.utils.expert.ExperReviewUtil).getReviewToPage(bizId,moduleCode,null)},
    loginName=${T(com.baosight.iplat4j.core.web.threadlocal.UserSession).getLoginName()},
    isAdmin=${T(com.baosight.bsvkkj.utils.expert.ExperReviewUtil).getReviewReqGly(bizId,moduleCode,null)},
    isCkFS=${isCkFS==null?false:isCkFS},
    isXsZC=${isXsZC==null?true:isXsZC},
    isGlps=${isGlps==null?true:isGlps},
    showYj=${showYj==null?true:showYj}
">
        <th:block th:if="${!#lists.isEmpty(psData)}">
        <div class="panel-group" th:id="${thisId+'accordion'}" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           th:href="${'#'+thisId+'PSJBXX'}" aria-expanded="false" class="collapsed">[[${titleName==null?'专家评审信息':titleName}]]
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div th:id="${thisId+'PSJBXX'}" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="mnote-editor-box">
                            <table class="layui-table" th:id="${thisId+'PSTable'}">

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <script th:inline="javascript">

            $(function() {
                var options = {
                    data:[[${psData}]],
                    id:[[${thisId+'PSTable'}]],
                    uniqueId:'reviewreqId',
                    detailView: true,
                    pagination:false,
                    showSearch:false,
                    showRefresh:false,
                    showColumns:false,
                    showToggle:false,
                    onExpandRow : function(index, row, $detail) {
                        initChildTable(index, row, $detail);
                    },
                    columns: [


                        {
                            field: 'yqFinishTime',
                            title: '要求完成日期'
                        },
                        {
                            field: 'approveReq',
                            title: '评审要求'
                        },
                        {
                            field: 'groupLeaderName',
                            title: '评审组长'
                        },
                        {
                            field: 'groupMembersName',
                            title: '评审组员'
                        },
                        {
                            field: 'approveNum',
                            title: '评审轮次'
                        },
                        {
                            field: 'isEnd',
                            title: '是否结束',
                            formatter: function(value, row, index) {
                                if(value=='1'){
                                    return "是";
                                }
                                return "否";
                            }
                        }]
                };
                if([[${isAdmin && isGlps}]]){
                    options.columns.push({
                        title: '管理',
                        align: 'center',
                        width:'200',
                        formatter: function(value, row, index) {
                            if(row.createUserLabel==[[${loginName}]]){
                                var actions = [];
                                actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="glPs(\'' + row.reviewreqId + '\')"><i class="fa fa-plus"></i>管理专家评审</a> ');
                                return actions.join('');
                            }
                            return "";
                        }
                    })
                }

                $.table.init(options);

                initChildTable = function(index, row, $detail) {
                    $detail.html('<table style="table-layout:fixed;word-break: break-all;" id="table'+row.reviewreqId+'" ></table>').find('table');
                    options["detailView"]=false;
                    options["onExpandRow"]=null;
                    options["id"]="table"+row.reviewreqId;
                    options["data"]=row.reviewInfo;
                    var columns=[
                        {
                            field: 'reviewUserName',
                            title: '评审专家',
                            width:'100'

                        },
                        {
                            field: 'userTypeName',
                            title: '成员类型',
                            width:'100'
                        },
                        {
                            field: 'reviewDate',
                            title: '评审完成时间',
                            width:'100'
                        }
                    ]
                    if(isFunction("addPSColumn")){
                        addPSColumn(columns);
                    }
                    if([[${showYj}]]){
                        columns.push({
                            field: 'reviewComment',
                            title:'评审意见'
                        })
                    }
                    columns.push({
                        field: 'psStatus',
                            title: '当前状态',
                            width:'100',
                            formatter: function(value, row, index) {
                            if(value==null){
                                return  "已评审";
                            }
                            return value;
                        }
                    })
                    if([[${isXsZC}]]){
                        columns.push({
                            title: '操作',
                            align: 'center',
                            width:'200',
                            formatter: function(value, row, index) {
                                if(row.psStatus==null){
                                    var actions = [];
                                    actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="seePs(\'' + row.reviewId + '\',\'' + row.reviewreqId + '\',\'' + row.extra1 + '\',\'' + row.extra2 + '\')"><i class="fa fa-plus"></i>查看评审信息</a> ');
                                    return actions.join('');
                                }
                                return "";
                            }
                        })
                    }


                    options["columns"]=columns;
                    $.table.init(options);
                };
            });

            function isFunction(funcName){
                try {
                    if(typeof(eval(funcName))=="function"){
                        return true;
                    }
                 }catch(e){

                }
                return false;
            }
            function seePs(reviewId,reviewreqId,extra1,extra2){
                $.modal.openTab("查看评审信息",ctxMP+"web/MPPS05?businessGuid="+reviewreqId+"&processCode="+extra1+"&activityCode="+extra2+"&reviewId="+reviewId+"&isCkFS="+[[${isCkFS}]]);
            }
            function glPs(reviewreqId){
                $.modal.open("专家评审管理",ctxMP+"web/MPPS07?reviewreqId="+reviewreqId,$(window).width() - 50,$(window).height() - 100);
            }
        </script>

        </th:block>
        <th:block th:if="${psbgSfzk!=null && psbgSfzk}">

            <script th:inline="javascript">
                $(document).ready(function(){
                    $("#"+[[${thisId+'PSTable'}]]).bootstrapTable('expandAllRows');
                });

            </script>
        </th:block>

    </th:block>
</div>
