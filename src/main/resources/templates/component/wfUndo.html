<!--
	流程撤回
	不允许退回的不会出现按钮,能退回多个节点用户自行选择
输入参数:
	businessId 业务ID
-->
<div th:fragment="init">
	<th:block th:with="undoTaskS=${@SWorkFlowUtil.getUndoTask(businessId)}">
		<th:block th:if="${undoTaskS?.size()>0}">
			<div class="btn-group dropup">
				<button id="undoTaskButton" type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" th:onclick="openUndoWindow()" >
					&nbsp; <i class="fa fa-step-backward"></i>撤回
				</button>
			</div>
			<div id="undoWindow" style="display: none">
				<div class="returnbox">
					<h2>撤回节点</h2>
					<div class="returnlist">
						<ul>
							<li th:each="undoTask : ${undoTaskS}">
								<input type="radio" name="undoTaskId" th:value="${undoTask.taskId}">[[${undoTask.taskName}]]</input>
							</li>
						</ul>
					</div>
				</div>
				<div style="position: absolute; right: 10px; bottom: 10px;">
					<button type="button" class="btn btn-primary" onclick="chooseUndoNode()"> <i class="fa fa-check"></i> 确 定 </button>
					<button type="button" class="btn btn-danger" onclick="closeUndoWindow()"> <i class="fa fa-reply-all"></i> 关 闭 </button>
				</div>
			</div>
			<script type="text/javascript" th:inline="javascript">
				//打开的页面的index，关闭用
				let undoWindowIndex;
				//点击退回
				function openUndoWindow(){
					undoWindowIndex = layer.open({
						type: 1,
						area: ['520px', '250px'],//大小
						fix: true,
						//不固定
						maxmin: true,
						shade: false,
						closeBtn: false, //不显示关闭按钮
						title: false, //不显示标题
						content: $('#undoWindow'), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
						cancel: function(){
							//layer.msg('捕获就是从页面已经存在的元素上，包裹layer的结构', {time: -1, icon:6});
						}
					});
				}

				//确定
				function chooseUndoNode(){
					let undoTaskId = $("input[name='undoTaskId']:checked").val();
					if (!undoTaskId) {
						$.modal.alertWarning("请选择撤回节点！");
						return false;
					}
					var doUndoUrl = ctx+"mpwf/flowInfo/doUndo";
					var jsonData = {};
					jsonData["taskId"] = undoTaskId;//任务ID
					$.modal.confirm("确认撤回吗？", function () {
						$.modal.loading("正在处理中，请稍后...");
						$.ajax({
							url: doUndoUrl,
							data: JSON.stringify(jsonData),
							dataType:"json",
							contentType: "application/json",
							type: 'POST',
							success: function (result) {
								$.modal.closeLoading();
								if (result.code == web_status.SUCCESS) {
									$.modal.alertSuccess(result.msg);
									$("#undoTaskButton").hide();
									closeUndoWindow();
								} else if (result.code == web_status.WARNING) {
									$.modal.alertWarning(result.msg);
								} else {
									$.modal.alertError(result.msg);
								}
							}
						});
					});
				}

				//关闭
				function closeUndoWindow(){
					layer.close(undoWindowIndex);
				}
			</script>
		</th:block>
	</th:block>
</div>