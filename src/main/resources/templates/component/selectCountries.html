<!--
AF Afghanistan 阿富汗
AL Albania 阿尔巴尼亚
DZ Algeria 阿尔及利亚
AS American Samoa 美属萨摩亚
AD Andorra 安道尔
AO Angola 安哥拉
AI Anguilla 安圭拉
AG Antigua and Barbuda 安提瓜和巴布达
AR Argentina 阿根廷
AM Armenia 亚美尼亚
AW Aruba 阿鲁巴岛
AU Australia 澳大利亚
AT Austria 奥地利
AZ Azerbaijan 阿塞拜疆
BS Bahamas 巴哈马
BH Bahrain 巴林
BD Bangladesh 孟加拉国
BB Barbados 巴巴多斯
BY Belarus 白俄罗斯
BE Belgium 比利时
BZ Belize 伯利兹
BJ Benin 贝宁
BM Bermuda 百慕大
BT Bhutan 不丹
BO Bolivia, Plurinational State of 玻利维亚多民族国
BA Bosnia and Herzegovina 波斯尼亚和黑塞哥维那
BW Botswana 博茨瓦纳
BV Bouvet Island 布维岛
BR Brazil 巴西
IO British Indian Ocean Territory 英属印度洋领地
BN Brunei Darussalam 文莱达鲁萨兰国
BG Bulgaria 保加利亚
BF Burkina Faso 布基纳法索
BI Burundi 蒲隆地
KH Cambodia 柬埔寨
CM Cameroon 喀麦隆
CA Canada 加拿大
CV Cape Verde 佛得角
KY Cayman Islands 开曼群岛
CF Central African Republic 中非共和国
TD Chad 乍得
CL Chile 智利
CN China 中国
CO Colombia 哥伦比亚
KM Comoros 科摩罗
CG Congo 刚果
CD Congo, the Democratic Republic of the 刚果民主共和国
CK Cook Islands 库克群岛
CR Costa Rica 哥斯达黎加
CI Cte d'Ivoire 科特迪瓦
HR Croatia 克罗地亚
CU Cuba 古巴
CW Curaao 库拉奥
CY Cyprus 塞浦路斯
CZ Czech Republic 捷克共和国
DK Denmark 丹麦
DJ Djibouti 吉布提
DM Dominica 多米尼加
DO Dominican Republic 多米尼加共和国
EC Ecuador 厄瓜多尔
EG Egypt 埃及
SV El Salvador 萨尔瓦多
GQ Equatorial Guinea 赤道几内亚
ER Eritrea 厄立特里亚
EE Estonia 爱沙尼亚
ET Ethiopia 埃塞俄比亚
FK Falkland Islands (Malvinas) 福克兰群岛（马尔维纳斯）
FO Faroe Islands 法罗群岛
FJ Fiji 斐济
FI Finland 芬兰
FR France 法国
GF French Guiana 法属圭亚那
PF French Polynesia 法属波利尼西亚
TF French Southern Territories 法属南部领地
GA Gabon 加蓬
GM Gambia 冈比亚
GE Georgia 格鲁吉亚
DE Germany 德国
GH Ghana 加纳
GI Gibraltar 直布罗陀
GR Greece 希腊
GL Greenland 格陵兰
GD Grenada 格林纳达
GP Guadeloupe 瓜德罗普
GU Guam 关岛
GT Guatemala 危地马拉
GG Guernsey 格恩西岛
GN Guinea 几内亚
GW Guinea-Bissau 几内亚比绍
GY Guyana 圭亚那
HT Haiti 海地
HM Heard Island and McDonald Islands 赫德岛和麦克唐纳群岛
VA Holy See (Vatican City State) 罗马教廷（梵蒂冈城国）
HN Honduras 洪都拉斯
HK Hong Kong 香港
HU Hungary 匈牙利
IS Iceland 冰岛
IN India 印度
ID Indonesia 印度尼西亚语
IR Iran, Islamic Republic of 伊朗伊斯兰共和国
IQ Iraq 伊拉克
IE Ireland 爱尔兰
IM Isle of Man 马恩岛
IL Israel 以色列
IT Italy 意大利
JM Jamaica 牙买加
JP Japan 日本
JE Jersey 泽西
JO Jordan 约旦
KZ Kazakhstan 哈萨克斯坦
KE Kenya 肯尼亚
KI Kiribati 基里巴斯
KP Korea, Democratic People's Republic of 朝鲜民主主义人民共和国
KR Korea, Republic of 韩国
KW Kuwait 科威特
KG Kyrgyzstan 吉尔吉斯斯坦
LA Lao People's Democratic Republic 老挝人民民主共和国
LV Latvia 拉托维亚
LB Lebanon 黎巴嫩
LS Lesotho 莱索托
LR Liberia 利比里亚
LY Libya 利比亚
LI Liechtenstein 列支敦士登
LT Lithuania 立陶宛
LU Luxembourg 卢森堡
MO Macao 澳门
MK Macedonia, the former Yugoslav Republic of 马其顿，前南斯拉夫共和国
MG Madagascar 马达加斯加
MW Malawi 马拉维
MY Malaysia 马来西亚
MV Maldives 马尔代夫
ML Mali 马里
MT Malta 马耳他
MH Marshall Islands 马绍尔群岛
MQ Martinique 马提尼克
MR Mauritania 毛里塔尼亚
MU Mauritius 毛里求斯
YT Mayotte 马约特
MX Mexico 墨西哥
FM Micronesia, Federated States of 密克罗尼西亚联邦
MD Moldova, Republic of 摩尔多瓦共和国
MC Monaco 摩纳哥
MN Mongolia 蒙古
ME Montenegro 黑山
MS Montserrat 蒙特塞拉特
MA Morocco 摩洛哥
MZ Mozambique 莫桑比克
MM Myanmar 缅甸
NA Namibia 纳米比亚
NR Nauru 瑙鲁
NP Nepal 尼泊尔
NL Netherlands 荷兰
NC New Caledonia 新喀里多尼亚
NZ New Zealand 新西兰
NI Nicaragua 尼加拉瓜
NE Niger 尼日尔
NG Nigeria 尼日利亚
NU Niue 纽埃
NF Norfolk Island 诺福克岛
MP Northern Mariana Islands 北马里亚纳群岛
NO Norway 挪威
OM Oman 阿曼
PK Pakistan 巴基斯坦
PW Palau 宫殿
PS Palestinian Territory, Occupied 巴勒斯坦领土，被占领
PA Panama 巴拿马
PG Papua New Guinea 巴布亚新几内亚
PY Paraguay 巴拉圭
PE Peru 秘鲁
PH Philippines 菲律宾
PN Pitcairn 皮特凯恩
PL Poland 波兰
PT Portugal 葡萄牙
PR Puerto Rico 波多黎各
QA Qatar 卡塔尔
RE Runion 鲁尼汪岛
RO Romania 罗马尼亚
RU Russian Federation 俄罗斯联邦
RW Rwanda 卢旺达
SH Saint Helena, Ascension and Tristan da Cunha 圣赫勒拿、阿森松和特里斯坦-达库尼亚
KN Saint Kitts and Nevis 圣基茨和尼维斯
LC Saint Lucia 圣卢西亚
MF Saint Martin (French part) 圣马丁（法国部分）
PM Saint Pierre and Miquelon 圣皮埃尔和密克隆群岛
VC Saint Vincent and the Grenadines 圣文森特和格林纳丁斯
WS Samoa 萨摩亚
SM San Marino 圣马力诺
ST Sao Tome and Principe 圣多美和普林西比
SA Saudi Arabia 沙特阿拉伯
SN Senegal 塞内加尔
RS Serbia 塞尔维亚
SC Seychelles 塞舌尔
SL Sierra Leone 塞拉利昂
SG Singapore 新加坡
SX Sint Maarten (Dutch part) 圣马丁岛（荷兰部分）
SK Slovakia 斯洛伐克
SI Slovenia 斯洛文尼亚
SB Solomon Islands 所罗门群岛
SO Somalia 索马里
ZA South Africa 南非
GS South Georgia and the South Sandwich Islands 南乔治亚岛和南桑威奇群岛
SS South Sudan 南苏丹
ES Spain 西班牙
LK Sri Lanka 斯里兰卡
SD Sudan 苏丹
SR Suriname 苏里南
SZ Swaziland 斯威士兰
SE Sweden 瑞典
CH Switzerland 瑞士
SY Syrian Arab Republic 阿拉伯叙利亚共和国
TW Taiwan, Province of China 中国台湾省
TJ Tajikistan 塔吉克斯坦
TZ Tanzania, United Republic of 坦桑尼亚联合共和国
TH Thailand 泰国
TL Timor-Leste 东帝汶
TG Togo 多哥
TK Tokelau 托克劳
TO Tonga 汤加
TT Trinidad and Tobago 特立尼达和多巴哥
TN Tunisia 突尼斯
TR Turkey 土耳其
TM Turkmenistan 土库曼斯坦
TC Turks and Caicos Islands 特克斯和凯科斯群岛
TV Tuvalu 图瓦卢
UG Uganda 乌干达
UA Ukraine 乌克兰
AE United Arab Emirates 阿拉伯联合酋长国
GB United Kingdom 英国
US United States 美国
UM United States Minor Outlying Islands 美国本土外小岛屿
UY Uruguay 乌拉圭
UZ Uzbekistan 乌兹别克斯坦
VU Vanuatu 瓦努阿图
VE Venezuela, Bolivarian Republic of 委内瑞拉玻利瓦尔共和国
VN Viet Nam 越南
VG Virgin Islands, British 英属维尔京群岛
VI Virgin Islands, U.S. 美属维尔京群岛
WF Wallis and Futuna 瓦利斯和富图纳群岛
EH Western Sahara 西撒哈拉
YE Yemen 也门
ZM Zambia 赞比亚
ZW Zimbabwe 津巴布韦
-->
<!--
国家选择
countriesCodeId
countriesNameId
id:input id 默认 name值

see:回显(true,false) 默认false
value: 回显值
isfirst: 是否添加首选项
onChangeCallback
-->


<div th:fragment="init">
    <div th:include="include :: bootstrap-select-css"></div>
    <div th:include="include :: bootstrap-select-js"></div>
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	countriesNameId=${countriesNameId==null?'countriesNameId':countriesNameId},
               	 	countriesCodeId=${countriesCodeId==null?'countriesCodeId':countriesCodeId},
               	 	id=${id==null?countriesCodeId:id},
               	 	isfirst=${isfirst==null||isfirst=='false'?false:true}
               	 	">
        <th:block th:if="${!see}">
            <select class="selectpicker form-control" th:name="${countriesCodeId}" th:id="${id}"
                    onchange="countriesOnChange(this)">
            </select>
            <input th:name="${countriesNameId}" th:id="${countriesNameId}" type="hidden">
        </th:block>
        <th:block th:unless="${!see}">
            <label class="control-label" style="text-align: left;" th:id="${id}+'See'"></label>
        </th:block>
        <script th:inline="javascript">
            let selectCountries = {
                id: [[${id}]],
                countriesNameId: [[${countriesNameId}]],
                value: /*[[${value}]]*/"",
                isfirst: /*[[${isfirst}]]*/false,
                countries: {
                    AF: "阿富汗",
                    AL: "阿尔巴尼亚",
                    DZ: "阿尔及利亚",
                    AS: "美属萨摩亚",
                    AD: "安道尔",
                    AO: "安哥拉",
                    AI: "安圭拉",
                    AG: "安提瓜和巴布达",
                    AR: "阿根廷",
                    AM: "亚美尼亚",
                    AW: "阿鲁巴岛",
                    AU: "澳大利亚",
                    AT: "奥地利",
                    AZ: "阿塞拜疆",
                    BS: "巴哈马",
                    BH: "巴林",
                    BD: "孟加拉国",
                    BB: "巴巴多斯",
                    BY: "白俄罗斯",
                    BE: "比利时",
                    BZ: "伯利兹",
                    BJ: "贝宁",
                    BM: "百慕大",
                    BT: "不丹",
                    BO: "玻利维亚多民族国",
                    BA: "波斯尼亚和黑塞哥维那",
                    BW: "博茨瓦纳",
                    BV: "布维岛",
                    BR: "巴西",
                    IO: "英属印度洋领地",
                    BN: "文莱达鲁萨兰国",
                    BG: "保加利亚",
                    BF: "布基纳法索",
                    BI: "蒲隆地",
                    KH: "柬埔寨",
                    CM: "喀麦隆",
                    CA: "加拿大",
                    CV: "佛得角",
                    KY: "开曼群岛",
                    CF: "中非共和国",
                    TD: "乍得",
                    CL: "智利",
                    CN: "中国",
                    CO: "哥伦比亚",
                    KM: "科摩罗",
                    CG: "刚果",
                    CD: "刚果民主共和国",
                    CK: "库克群岛",
                    CR: "哥斯达黎加",
                    CI: "科特迪瓦",
                    HR: "克罗地亚",
                    CU: "古巴",
                    CW: "库拉奥",
                    CY: "塞浦路斯",
                    CZ: "捷克共和国",
                    DK: "丹麦",
                    DJ: "吉布提",
                    DM: "多米尼加",
                    DO: "多米尼加共和国",
                    EC: "厄瓜多尔",
                    EG: "埃及",
                    SV: "萨尔瓦多",
                    GQ: "赤道几内亚",
                    ER: "厄立特里亚",
                    EE: "爱沙尼亚",
                    ET: "埃塞俄比亚",
                    FK: "福克兰群岛（马尔维纳斯）",
                    FO: "法罗群岛",
                    FJ: "斐济",
                    FI: "芬兰",
                    FR: "法国",
                    GF: "法属圭亚那",
                    PF: "法属波利尼西亚",
                    TF: "法属南部领地",
                    GA: "加蓬",
                    GM: "冈比亚",
                    GE: "格鲁吉亚",
                    DE: "德国",
                    GH: "加纳",
                    GI: "直布罗陀",
                    GR: "希腊",
                    GL: "格陵兰",
                    GD: "格林纳达",
                    GP: "瓜德罗普",
                    GU: "关岛",
                    GT: "危地马拉",
                    GG: "格恩西岛",
                    GN: "几内亚",
                    GW: "几内亚比绍",
                    GY: "圭亚那",
                    HT: "海地",
                    HM: "赫德岛和麦克唐纳群岛",
                    VA: "罗马教廷（梵蒂冈城国）",
                    HN: "洪都拉斯",
                    HK: "香港",
                    HU: "匈牙利",
                    IS: "冰岛",
                    IN: "印度",
                    ID: "印度尼西亚语",
                    IR: "伊朗伊斯兰共和国",
                    IQ: "伊拉克",
                    IE: "爱尔兰",
                    IM: "马恩岛",
                    IL: "以色列",
                    IT: "意大利",
                    JM: "牙买加",
                    JP: "日本",
                    JE: "泽西",
                    JO: "约旦",
                    KZ: "哈萨克斯坦",
                    KE: "肯尼亚",
                    KI: "基里巴斯",
                    KP: "朝鲜民主主义人民共和国",
                    KR: "韩国",
                    KW: "科威特",
                    KG: "吉尔吉斯斯坦",
                    LA: "老挝人民民主共和国",
                    LV: "拉托维亚",
                    LB: "黎巴嫩",
                    LS: "莱索托",
                    LR: "利比里亚",
                    LY: "利比亚",
                    LI: "列支敦士登",
                    LT: "立陶宛",
                    LU: "卢森堡",
                    MO: "澳门",
                    MK: "马其顿，前南斯拉夫共和国",
                    MG: "马达加斯加",
                    MW: "马拉维",
                    MY: "马来西亚",
                    MV: "马尔代夫",
                    ML: "马里",
                    MT: "马耳他",
                    MH: "马绍尔群岛",
                    MQ: "马提尼克",
                    MR: "毛里塔尼亚",
                    MU: "毛里求斯",
                    YT: "马约特",
                    MX: "墨西哥",
                    FM: "密克罗尼西亚联邦",
                    MD: "摩尔多瓦共和国",
                    MC: "摩纳哥",
                    MN: "蒙古",
                    ME: "黑山",
                    MS: "蒙特塞拉特",
                    MA: "摩洛哥",
                    MZ: "莫桑比克",
                    MM: "缅甸",
                    NA: "纳米比亚",
                    NR: "瑙鲁",
                    NP: "尼泊尔",
                    NL: "荷兰",
                    NC: "新喀里多尼亚",
                    NZ: "新西兰",
                    NI: "尼加拉瓜",
                    NE: "尼日尔",
                    NG: "尼日利亚",
                    NU: "纽埃",
                    NF: "诺福克岛",
                    MP: "北马里亚纳群岛",
                    NO: "挪威",
                    OM: "阿曼",
                    PK: "巴基斯坦",
                    PW: "宫殿",
                    PS: "巴勒斯坦领土，被占领",
                    PA: "巴拿马",
                    PG: "巴布亚新几内亚",
                    PY: "巴拉圭",
                    PE: "秘鲁",
                    PH: "菲律宾",
                    PN: "皮特凯恩",
                    PL: "波兰",
                    PT: "葡萄牙",
                    PR: "波多黎各",
                    QA: "卡塔尔",
                    RE: "鲁尼汪岛",
                    RO: "罗马尼亚",
                    RU: "俄罗斯联邦",
                    RW: "卢旺达",
                    SH: "圣赫勒拿、阿森松和特里斯坦-达库尼亚",
                    KN: "圣基茨和尼维斯",
                    LC: "圣卢西亚",
                    MF: "圣马丁（法国部分）",
                    PM: "圣皮埃尔和密克隆群岛",
                    VC: "圣文森特和格林纳丁斯",
                    WS: "萨摩亚",
                    SM: "圣马力诺",
                    ST: "圣多美和普林西比",
                    SA: "沙特阿拉伯",
                    SN: "塞内加尔",
                    RS: "塞尔维亚",
                    SC: "塞舌尔",
                    SL: "塞拉利昂",
                    SG: "新加坡",
                    SX: "圣马丁岛（荷兰部分）",
                    SK: "斯洛伐克",
                    SI: "斯洛文尼亚",
                    SB: "所罗门群岛",
                    SO: "索马里",
                    ZA: "南非",
                    GS: "南乔治亚岛和南桑威奇群岛",
                    SS: "南苏丹",
                    ES: "西班牙",
                    LK: "斯里兰卡",
                    SD: "苏丹",
                    SR: "苏里南",
                    SZ: "斯威士兰",
                    SE: "瑞典",
                    CH: "瑞士",
                    SY: "阿拉伯叙利亚共和国",
                    TW: "中国台湾省",
                    TJ: "塔吉克斯坦",
                    TZ: "坦桑尼亚联合共和国",
                    TH: "泰国",
                    TL: "东帝汶",
                    TG: "多哥",
                    TK: "托克劳",
                    TO: "汤加",
                    TT: "特立尼达和多巴哥",
                    TN: "突尼斯",
                    TR: "土耳其",
                    TM: "土库曼斯坦",
                    TC: "特克斯和凯科斯群岛",
                    TV: "图瓦卢",
                    UG: "乌干达",
                    UA: "乌克兰",
                    AE: "阿拉伯联合酋长国",
                    GB: "英国",
                    US: "美国",
                    UM: "美国本土外小岛屿",
                    UY: "乌拉圭",
                    UZ: "乌兹别克斯坦",
                    VU: "瓦努阿图",
                    VE: "委内瑞拉玻利瓦尔共和国",
                    VN: "越南",
                    VG: "英属维尔京群岛",
                    VI: "美属维尔京群岛",
                    WF: "瓦利斯和富图纳群岛",
                    EH: "西撒哈拉",
                    YE: "也门",
                    ZM: "赞比亚",
                    ZW: "津巴布韦",
                },
            }


            function initOption() {
                let documentFragment = document.createDocumentFragment();
                for (let key in selectCountries.countries) {
                    let htmlOptionElement = document.createElement("option");
                    htmlOptionElement.value = key
                    htmlOptionElement.textContent = selectCountries.countries[key]
                    htmlOptionElement.selected = (selectCountries.value === key)
                    documentFragment.appendChild(htmlOptionElement)
                }
                if (selectCountries.isfirst) {
                    $("#" + selectCountries.id).append('<option value="">请选择</option>')
                }
                $("#" + selectCountries.id).append(documentFragment)
                $("#" + selectCountries.id + 'See').text(selectCountries.countries[selectCountries.value])
                $("#" + selectCountries.countriesNameId).val(selectCountries.countries[selectCountries.value])
            }

            initOption();

            function countriesOnChange(obj) {
                let countrieCode = obj.options[obj.selectedIndex].value
                let countrieName = selectCountries.countries[countrieCode]
                $("#" + selectCountries.countriesNameId).val(countrieName)

                let onChangeCallback = [[${onChangeCallback}]]
                if (onChangeCallback != null && onChangeCallback != '') {
                    window.eval(onChangeCallback + '("' + countrieCode + '","' + countrieName + '")');
                }
            }

            //监听表单重置
            $("#" + selectCountries.id).parents("form")[0].addEventListener("reset", () => {
                $("#" + selectCountries.id).val("").trigger("change")
                $("#" + selectCountries.id).selectpicker('refresh');//使extraSelect1刷新，相当于重置，会默认选中第一个
            });
        </script>

    </th:block>

</div>




