<!-- 选择组织封装

labelName:label的text 默认附件
orgCodeId:orgCode name id
orgNameId:orgName name id
selectType:S 单选 M 多选 默认S
level:组织层级 -1只显示公司
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看
-->
<div th:fragment="init">
	<th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelName=${labelName==null?'选择组织：':labelName},
                    selectType=${selectType==null?'S':selectType}

               	 	">
		<th:block th:if="${!see}">
			<div th:class="'input-group'">
				<input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" />
				<input th:name="${orgNameId}" th:id="${orgNameId}"
					   th:onclick="choiceOrg2([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])"
					   th:value="${@OrgUtil.getOrgName(value)}"
					   class="form-control detailOrgOrUser" type="hidden"
					   th:required="${isrequired!=null && isrequired}" readonly>
				<div id="nameDiv" style="width: 100%;">
					<ul class="clearfix"></ul>
				</div>
				<span class="input-group-addon"  th:onclick="choiceOrg2([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])">
					<i class="fa fa-search "></i>
				</span>
			</div>

		</th:block>
		<th:block th:unless="${!see}">
			<input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" />
			<input th:name="${orgNameId}" th:id="${orgNameId}" th:value="${@OrgUtil.getOrgPathName(value)}" class="form-control" type="hidden" />
			<div id="nameDivSee" style="width: 100%;">
				<ul class="clearfix"></ul>
			</div>
		</th:block>
	</th:block>
	
	<script type="text/javascript" th:inline="javascript">
        let orgId2 = /*[[${orgCodeId}]]*/"orgId";
		let orgNameId2 = /*[[${orgNameId}]]*/"deptName";
        function choiceOrg2(orgCodeInputId, orgNameInputId, selectType, level, orgCode,showLevel,callback) {
            orgId2 = orgCodeInputId;
            orgNameId2 = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "mpad/org/selectOrgList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + orgId2).val();
            var options = {
                title: '选择组织',
                width: "380",
                height: '500',
                url: url,
                callBack: choiceOrgCallback2
            };
            $.modal.openOptions(options);
        }
        function choiceOrgCallback2(index, layero) {
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + orgId2).val(body.find('#treeId').val());

			let temp1 = body.find('#treeName').val()
			let temp2 = []
			if(temp1!=null){
				temp2 = temp1.split(",")
				for (let i = 0; i < temp2.length; i++) {
					var temp3 = temp2[i];
					if (temp3.indexOf("/")>=0){
						let split_ = temp3.split("/");
						temp3 = split_[split_.length-1]
					}
					temp2[i] = temp3
				}
			}
            $("#" + orgNameId2).val(temp2.join(","));
            layer.close(index);
			initNameSpan();
        }

		function initNameSpan(){
			var userCode=$("#"+orgId2).val();
			$("#nameDiv").find("ul").html("")
			if(userCode!=null && userCode!==''){
				var arr=userCode.split(",");
				var arr2=$("#"+orgNameId2).val().split(",");
				$("#nameDiv").find("ul").empty();
				var html="";
				for(let i=0;i<arr.length;i++){
					let value = arr2[i]
					if (value.indexOf("/")>=0){
						let split_ = value.split("/");
						value = split_[split_.length-1]
					}
					html+="<li id='"+arr[i]+"' ><span class='nameSpan' onclick='deleteNameSpan(&quot;" + arr[i] +"&quot;,&quot;" + value +"&quot;)'><i class='fa fa-close'></i>"+ value +"</span></li>";
				}
				$("#nameDiv").find("ul").append(html);

				$("#"+orgNameId2).attr("type","hidden");
				$("#nameDiv").show();
			}else{
				$("#"+orgNameId2).attr("type","text");
				$("#nameDiv").hide();
			}
		}
		initNameSpan();
		function deleteNameSpan(code,name){
			var userCodeS=$("#"+orgId2).val();
			var userNameS=$("#"+orgNameId2).val();
			var arrUserCode=userCodeS.split(",");
			var arrUserName=userNameS.split(",");
			arrUserCode.splice(arrUserCode.indexOf(code), 1)
			arrUserName.splice(arrUserName.indexOf(name), 1)
			$("#"+orgId2).val(arrUserCode.join(","));
			$("#"+orgNameId2).val(arrUserName.join(","));
			$("#nameDiv #"+code).remove();
			if(arrUserName.length === 0){
				$("#"+orgNameId2).attr("type","text");
				$("#nameDiv").hide();
			}
		}

		function seeInit(){
			let names = /*[[${@OrgUtil.getOrgPathName(value)}]]*/""
			if(names==null){
				return false;
			}
			let arr = names.split(",")
			let html="";
			for(let i=0;i<arr.length;i++){
				let value = arr[i]
				if (value.indexOf("/")>=0){
					let split_ = value.split("/");
					value = split_[split_.length-1]
				}
				html+="<li id='"+arr[i]+"' ><span class='nameSpan'>"+ value +"</span></li>";
			}
			$("#nameDivSee").find("ul").append(html);
		}
		seeInit()
    </script>
	<style>
		.clearfix:after {
			content: "";
			display: block;
			clear: both;
		}
		#nameDiv,#nameDivSee{
			display: inline-block;
			color: white;
			background: #f9f9f9;
			border: 1px solid #ddd;
		}
		#nameDivSee{
			border: none;
			background: none;
		}
		#nameDiv>ul>li,#nameDivSee>ul>li{
			float: left;
			display: inline;
			margin-right: 10px;
		}
		.nameSpan{
			border-radius: 4px;
			padding: 7px;
			background-color: #2668d4;
			border-color: #2668d4;
			cursor:pointer;
			line-height: 33px;
			transition: all 0.5s;
		}
		.nameSpan:hover{
			background-color: #204d74;
			border-color: #122b40;
		}
	</style>
</div>