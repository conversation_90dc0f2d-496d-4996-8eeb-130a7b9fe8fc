<!--
流程提交 用于单任务
	多个转移会让用户选择那个transitionKey,提交给退回节点transitionKey为jumpReturnNode@@-[节点编码]
输入参数:
taskId 任务ID
callback 回调函数,第一个参数固定为用户选择的提交节点
-->
<div th:fragment="init">
	<button type="button" class="btn btn-primary" th:onclick="doWorkFlowSubmit()" title="提交" data-toggle="tooltip">
		<i class="fa fa-share"></i>&nbsp;提交
	</button>
	<div id="submitWindow" style="display: none">
		<div class="returnbox-tit">提交设置</div>
		<div class="returnbox">
			<h2><span style="color: red">*</span>下一步操作</h2>
			<div class="returnlist">
				<ul id="submitNodes">
				</ul>
			</div>
			<div id="submitWindowChooseUser" style="display: none">
				<input type="hidden" id="submitActUserCode"/>
				<h2><span style="color: red">*</span>选择用户</h2>
				<div class="returninput" >
					<div class="input-group" onclick="nextActRadioChooseUser()">
						<input id="submitActUserName" type="text" class="form-control" placeholder="选择用户" readonly="readonly" required="required"/>
						<span class="input-group-addon "><i class="fa fa-search "></i></span>
					</div>
				</div>
			</div>
		</div>
		<div style="position: absolute; right: 10px; bottom: 10px;">
			<button type="button" class="btn btn-primary" onclick="chooseSubmitNode()"> <i class="fa fa-check"></i> 确 定 </button>
			<button type="button" class="btn btn-danger" onclick="closeSubmitWindow()"> <i class="fa fa-reply-all"></i> 关 闭 </button>
		</div>
	</div>
	<script type="text/javascript" th:inline="javascript">
		let submitWindowAct;
		let old_userLabelM;
		function doWorkFlowSubmit(){
			old_userLabelM = $('#workFlow_userLabelM').val();
			if($.validate.form()) {
				let validateUrl = [[${validateUrl}]] || "mpwf/flowInfo/getNextSubmitWF";
				let formId = [[${formId}]];
				$.ajax({
					url: ctx + validateUrl,
					data: $('#'+formId).serialize(),
					dataType:"json",
					type: 'POST',
					beforeSend: function () {
						$('#submitNodes').html('');
						$.modal.loading("正在处理中，请稍后...");
					},
					success: function (result) {
						if (result.code == web_status.SUCCESS) {
							if (!result.data || !result.data.nextActivitys|| result.data.nextActivitys.length==0) {
								let callback = [[${callback}]];
								if (callback) {
									eval(callback+'("")');
								}
							} else {
								$.each(result.data.nextActivitys, function (index, act) {
									let _strAct = JSON.stringify(act).replace(/"/g,"'");
									let li = `<li><input type="radio" onclick="showChooseNext(`+ _strAct +`)"`;
									if (index==0) {
										li += `checked="checked" `;
										showChooseNext(act);
									}
									let transitionKey = act.transitionKey||'orderTaskTransition';
									li += `name="submitActivityKey" value="` + transitionKey + `">`;
									if (transitionKey.indexOf('jumpReturnNode@@')>-1) {
										li += '直达退回节点-';
									}
									li += act.activityName;
									if (act.operator != '-' && act.flowConfig?.chooseUser != '1') {//配置了用户
										li += `(` + act.operator + `)`;
									}
									li += `</input></div></li>`;
									$('#submitNodes').append(li);
								});
								openSubmitWindow();
							}
						} else if (result.code == web_status.WARNING) {
							$.modal.alertWarning(result.msg)
						} else {
							$.modal.alertError(result.msg);
						};
						$.modal.closeLoading();
					}
				});
			}
		}

		function chooseSubmitNode(){
			let transitionKey = $("input[name='submitActivityKey']:checked").val();
			if (!transitionKey) {
				$.modal.alertWarning("请选择提交节点！");
				return false;
			}

			let callback = [[${callback}]];
			if (callback) {
				if ('orderTaskTransition' === transitionKey) {
					transitionKey = '';
				}
				$("#workFlow_transitionKey").val(transitionKey);
				if (!$("#submitWindowChooseUser").is(":hidden")) {
					if ($("#submitActUserCode").val()) {
						$("#workFlow_userLabelM").val($("#submitActUserCode").val());
					}
				}
				eval(callback+'("'+transitionKey+'")');
			}
		}

		let submitWindowIndex;
		function openSubmitWindow(){
			submitWindowIndex = layer.open({
				type: 1,
				area: ['520px', '350px'],//大小
				fix: true,
				//不固定
				maxmin: true,
				shade: false,
				closeBtn: false, //不显示关闭按钮
				title: false, //不显示标题
				content: $('#submitWindow'), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
				cancel: function(){
					//layer.msg('捕获就是从页面已经存在的元素上，包裹layer的结构', {time: -1, icon:6});
					$('#workFlow_userLabelM').val(old_userLabelM);
				}
			});
		}

		function closeSubmitWindow(){
			$('#workFlow_userLabelM').val(old_userLabelM);
			layer.close(submitWindowIndex);
		}

		function showChooseNext(act) {
			$('#SubProcessParameter').html('');//每次点击都清空子流程参数相关数据
			submitWindowAct = act;
			debugger;
			if (act.activityType == null || 'Manual' === act.activityType) {//人工活动节点
				if (submitWindowAct.operator === '-') {//没配置用户
					$('#submitWindowChooseUser').show();
				} else {
					let flowConfig = submitWindowAct?.flowConfig;
					if (flowConfig?.chooseUser === '1') {
						$('#submitWindowChooseUser').show();
					} else {
						$('#submitWindowChooseUser').hide();
					}
				}
			} else if ('SubProcess' === act.activityType){//子流程节点
				$('#submitWindowChooseUser').hide();
			} else if ('End' === act.activityType){//结束节点
				$('#submitWindowChooseUser').hide();
			}
		}

		//选择用户
		function nextActRadioChooseUser() {
			//workFlow_userLabelM 下一步提交的用户 隐藏在wfWorkFlow2中
			let flowConfig = submitWindowAct?.flowConfig;
			if (flowConfig) {
				// 从流程配置中选人
				if (flowConfig?.chooseUser === '1') {
					choiceConfigUser(submitWindowAct?.operator, flowConfig.otherParam);
				} else {
					$('#submitWindowChooseUser').show();
					if (flowConfig.code === 'TaskAssignWithChooseUserByRole') {//角色选人
						let rolCode = flowConfig.roleValue || '';
						if (rolCode==='') {
							$.modal.alertWarning("角色编码未配置，请联系管理员！");
						}
						choiceRoleUser('submitActUserCode', 'submitActUserName', flowConfig.otherParam, rolCode);
					} else if (flowConfig.code === 'TaskAssignWithChooseUserByDept') {//部门选人
						choiceDeptUser('submitActUserCode', 'submitActUserName', flowConfig.otherParam);
					} else if (flowConfig.code === 'TaskAssignWithChooseUserByTenant') {//租户选人
						choiceTenantUser('submitActUserCode', 'submitActUserName', flowConfig.otherParam, $('#tenantId').val());
					} else {//全组织选人
						choiceUser('submitActUserCode', 'submitActUserName', flowConfig.otherParam);
					}
				}
			} else {
				choiceUser('workFlow_userLabelM', 'submitActUserName', 'M');//默认 组织多选用户
			}
		}

		function choiceConfigUser(users, selectType, callback){
			var url = ctx + 'mpwf/flowInfo/configUser?users='+users;
			if (selectType === undefined || selectType == null || selectType == '') {
				selectType = "S";
			}
			url += "&selectType=" + selectType;
			var values = $("#submitActUserCode").val();
			if(!(values=== undefined)  && values!=null){
				url+="&values="+values;
			}
			if (!(callback === undefined) && callback != null) {
				url += "&callback=" + callback;
			}
			url += "&userId=" + $("#submitActUserCode").val() + "&userName=" + $("#submitActUserName").val();
			$.modal.open("选择用户", url, '800', '450');
		}

		function choiceConfigUserCallback(userCode, userName) {
			$("#submitActUserCode").val(userCode);
			$("#submitActUserName").val(userName);
			try{
				$("#submitActUserName").valid();//触发下校验
			} catch (e) {
			}
		}
	</script>
</div>