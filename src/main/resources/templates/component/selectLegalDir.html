<!-- 选择组织封装

labelName:label的text 默认附件
orgCodeId:orgCode name id
orgNameId:orgName name id
selectType:S 单选 M 多选 默认S
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看
isAuth: 是否需要权限控制 默认false
-->
<div th:fragment="init">
	<th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelName=${labelName==null?'选择目录：':labelName},
                    selectType=${selectType==null?'S':selectType}

               	 	">
		<th:block th:if="${!see}">
			<div th:class="'input-group'" th:onclick="choiceOrg([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])">
				<input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" />
				<input th:name="${orgNameId}" th:id="${orgNameId}" th:value="${@DirUtil2.getOrgPathName(value)}" class="form-control detailOrgOrUser" type="text" th:required="${isrequired!=null && isrequired}" readonly>
				<span class="input-group-addon">
					<i class="fa fa-search "></i>
				</span>
			</div>
		</th:block>
		<th:block th:unless="${!see}">
			<div class="form-control-static" th:utext="${@DirUtil2.getOrgPathName(value)}"></div>
			<input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" />
			<input th:name="${orgNameId}" th:id="${orgNameId}" th:value="${@DirUtil2.getOrgPathName(value)}" class="form-control" type="hidden" />
		</th:block>
	</th:block>

	<script type="text/javascript" th:inline="javascript">
		var orgId = "orgId";
		var orgNameId = "deptName";
		let isAuth = /*[[${isAuth}]]*/"false";//默认false
		function choiceOrg(orgCodeInputId, orgNameInputId, selectType, level, orgCode,showLevel,callback) {
			orgId = orgCodeInputId;
			orgNameId = orgNameInputId;
			if (selectType === undefined || selectType == null || selectType === '') {
				selectType = "S";
			}
			var url = ctx + "fwsc/legalCatalog/selectDirList?selectType=" + selectType;
			if (!(level === undefined) && level != null) {
				url += "&level=" + level;
			}
			if (!(orgCode === undefined) && orgCode != null) {
				url += "&orgCode=" + orgCode;
			}
			if (!(showLevel === undefined) && showLevel != null) {
				url += "&showLevel=" + showLevel;
			}
			if (!(callback === undefined) && callback != null) {
				url += "&callback=" + callback;
			}
			url += "&values=" + $("#" + orgId).val() + "&isAuth=" + isAuth;
			var options = {
				title: '选择目录',
				width: "380",
				height: '500',
				url: url,
				callBack: choiceOrgCallback
			};
			$.modal.openOptions(options);
		}

		function choiceOrgCallback(index, layero) {
			var tree = layero.find("iframe")[0].contentWindow.$._tree;
			var body = layer.getChildFrame('body', index);
			layero.find("iframe")[0].contentWindow.saveCheck();
			$("#" + orgId).val(body.find('#treeId').val());
			$("#" + orgNameId).val(body.find('#treeName').val());
			layer.close(index);
		}
	</script>
</div>