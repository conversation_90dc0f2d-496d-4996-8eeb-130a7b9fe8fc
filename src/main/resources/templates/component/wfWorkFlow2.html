<!--
	流程相关参数
输入参数:
	workFlow, 通过 SWorkFlowUtil.getWorkFlowByTaskId(String taskId) 获取
-->
<div th:fragment="init">
	<th:block th:with="needComment=${needComment==null ? true:needComment},
						chooseUserName=${workFlow?.extraParameters?.get('CHOOSE_USER_NAME')==null ? '下一步操作' : workFlow?.extraParameters?.get('CHOOSE_USER_NAME')},
			  			commentReuired=${workFlow?.processParameters?.get('COMMENT_REQUIRED')=='1'||workFlow?.extraParameters?.get('COMMENT_REQUIRED')=='1' ? true:false}
				">
		<input id="workFlow_taskId" name="workFlow.taskId" type="hidden" th:value="${workFlow?.taskId}">
		<input id="workFlow_taskType" name="workFlow.taskType" type="hidden" th:value="${workFlow?.taskType}">
		<input id="workFlow_operator" name="workFlow.operator" type="hidden" th:value="${workFlow?.operator}">
		<input id="workFlow_businessId" name="workFlow.businessId" type="hidden" th:value="${workFlow?.businessId}">
		<input id="workFlow_businessName" name="workFlow.businessName" type="hidden" th:value="${workFlow?.businessName}">
		<input id="workFlow_businessType" name="workFlow.businessType" type="hidden" th:value="${workFlow?.businessType}">
		<input id="workFlow_processCode" name="workFlow.processCode" type="hidden" th:value="${workFlow?.processCode}">
		<input id="workFlow_processInstanceId" name="workFlow.processInstanceId" type="hidden" th:value="${workFlow?.processInstanceId}">
		<input id="workFlow_currentActivity" name="workFlow.currentActivity" type="hidden" th:value="${workFlow?.currentActivity}">
		<input id="workFlow_currentActivityName" name="workFlow.currentActivityName" type="hidden" th:value="${workFlow?.currentActivityName}">
		<input id="workFlow_currentOperator" name="workFlow.currentOperator" type="hidden" th:value="${workFlow?.currentOperator}">
		<input id="workFlow_currentForm" name="workFlow.currentForm" type="hidden" th:value="${workFlow?.currentForm}">
		<input id="workFlow_flowState" name="workFlow.flowState" type="hidden" th:value="${workFlow?.flowState}">
		<input id="workFlow_transitionKey" name="workFlow.transitionKey" type="hidden" >
		<input id="workFlow_returnActivityKey" name="workFlow.returnActivityKey" type="hidden" >
		<input id="workFlow_jumpActivityKey" name="workFlow.jumpActivityKey" type="hidden" >
		<input id="workFlow_addTaskType" name="workFlow.addTaskType" type="hidden" >
		<input id="workFlow_variable_canJumpReturn" type="hidden" >

		<th:block th:if="${workFlow?.extraParameters?.get('CHOOSE_USER')!='1'}">
			<input id="workFlow_userLabelM" name="workFlow.userLabelM" type="hidden" >
		</th:block>
		<div th:if="${workFlow?.extraParameters?.get('CHOOSE_USER')=='1'}" class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#nextCz" aria-expanded="false" class="collapsed">
						下一步操作
						<span class="pull-right">
							<i class="fa fa-chevron-down" aria-hidden="true"></i>
						</span>
					</a>
				</h4>
			</div>
			<div id="nextCz" class="panel-collapse collapse in" aria-expanded="false">
				<div class="panel-body">
					<div class="form-group">
						<label class="col-sm-2 control-label is-required">[[${chooseUserName}]]：</label>
						<div th:if="${workFlow.extraParameters?.get('CHOOSE_USER_TYPE')=='ROLE'}" class="col-sm-4">
							<th:block th:with="roleCode=${workFlow.extraParameters?.get('ROLE_CODE')==null ? '' : workFlow.extraParameters?.get('ROLE_CODE')}"
									  th:include="component/selectRoleUser::init(roleCode=${roleCode}, userCodeId='workFlow.userLabelM', userNameId='workFlow.userNameM', selectType='M', isrequired=true)"></th:block>
						</div>
						<div th:unless="${workFlow.extraParameters?.get('CHOOSE_USER_TYPE')=='ROLE'}" class="col-sm-4">
							<th:block th:include="component/selectUser::init(userCodeId='workFlow.userLabelM', userNameId='workFlow.userNameM', selectType='M', isrequired=true)"></th:block>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div th:if="${needComment}" class="panel-group" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
							<span th:if="${commentReuired}" style="color: red">*</span>意见
							<span class="pull-right">
								<i class="fa fa-chevron-down" aria-hidden="true"></i>
							</span>
						</a>
						<ul style="display: flex;align-items: center;flex-wrap: nowrap; width:400px; margin-left:50px; margin-top: -24px">
							<li>常用批示语:</li>
							<li>
								<select id="common_comment" style="border: 1px solid #ddd;border-radius: 4px;background-color:#FFF;outline: 0;height: 30px;width: 200px;">
									<option value="">请选择</option>
								</select>
							</li>
							<li>
								&nbsp;&nbsp;<a href="javascript:editMyComment()" style="text-decoration-line: underline">自定义</a>
							</li>
						</ul >
					</h4>
				</div>
				<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group">
							<div class="col-sm-12">
								<textarea class="form-control" id="workFlow_comment" maxlength="1000" name="workFlow.comment" style="height: 200px; width: 100%;"
										  th:required="${commentReuired}"
										  th:utext="${workFlow?.comment}"></textarea>
								<div class="tip-comment">
									<span id="workFlow_comment_textCount">0</span>/<span>1000</span>
								</div>
								<script th:inline="javascript" type="text/javascript">
									$("#workFlow_comment").keyup(function () {
										$("#workFlow_comment_textCount").html(this.value.length);//中文占两个
									});
									$("#workFlow_comment").keyup();
								</script>
							</div>
						</div>
					</div>
				</div>
			</div>
			<script type="text/javascript" th:inline="javascript">
				$(function() {
					reloadMyComment();
				})

				//绑定回调
				$('#common_comment').on('change', function(event){
					var old = $('#workFlow_comment').val()
					var cur = $('#common_comment').val();
					$('#workFlow_comment').val(old+cur);
				});

				function editMyComment(){
					$.modal.open("我的常用审批语", ctxMP + "web/MPTYCO01");
				}

				function reloadMyComment(){
					$.ajax({
						url: ctx + "mpwf/flowInfo/getMyComment",
						dataType:"json",
						type: 'GET',
						success: function (result) {
							if (result.code == web_status.SUCCESS) {
								$('#common_comment').html('');
								$('#common_comment').append("<option >请选择</option>");
								$.each(result.data, function (index, comment) {
									$('#common_comment').append("<option >"+comment+"</option>");
								});
							} else if (result.code == web_status.WARNING) {
								$.modal.alertWarning(result.msg)
							} else {
								$.modal.alertError(result.msg);
							};
						}
					});
				}
			</script>
		</div>
	</th:block>
</div>