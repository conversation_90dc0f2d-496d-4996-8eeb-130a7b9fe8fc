<!-- 附件上传

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
name:附件input name 默认att
id:附件input id 默认att
isrequired:附件是否必须上传 默认false

see:是否查看
回显数据：
	sourceId 业务id,
	sourceModule 模块
	sourceLabel1 标签
	sourceLabel2 标签
	sourceLabel3

上传属性：
maxFileCount:最大限制上传附件 默认是10
-->
<div th:fragment="init">
    <th:block
            th:with="attachmentMap=${T(com.baosight.bsvkkj.mp.ty.utils.SAttachmentUtil).getAttachmentBySourceIdAndSourceLabelToMap(sourceId, sourceModule,sourceLabel1, sourceLabel2, sourceLabel3)},
                        thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)}">


        <input th:name="${name==null?'att':name}" th:id="${id==null?'att':id}" type="hidden"
               th:value="${attachmentMap.attachmentIds!=null?attachmentMap.attachmentIds:''}" class="form-control">
        <div class="layui-upload">
            <div th:id="${'div'+thisId}">
                <button th:if="${see==null  || (see!=null && !see)}" type="button"
                        class="layui-btn layui-btn-normal">选择多文件
                </button>
                <!--拖拽-->
                <div class="drag-box" th:if="${see==null  || (see!=null && !see)}" style="border: 1px  dashed  #ddd;width: 200px; height: 33px;background-color:  #fff; display:  inline-block;
			    font-size:  12px;
			    color:  #666;
			    line-height: 33px;
			    cursor:  pointer;">
                    <img th:src="@{/img/line-drag.png}" width="24" height="24"/>可将附件拖拽到框中
                </div>
                <!--拖拽-->
            </div>
            <div class="layui-upload-list" style="max-width: 1000px;">
                <table class="layui-table">
                    <colgroup>
                        <col style="width: 50%">
                        <col style="width: 10%">
                        <col style="width: 10%">
                        <col style="width: 30%">
                    </colgroup>
                    <thead>
                    <tr>
                        <th>文件名</th>
                        <th>大小</th>
                        <th>上传进度</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody th:id="${'list'+thisId}">
                    <th:block th:if="${attachmentMap!=null && not #lists.isEmpty(attachmentMap.attachmentList)}">
                        <tr th:each="attachment: ${attachmentMap.attachmentList} " class="upload"
                            th:id="${'upload-'+thisId+'-'+attachmentStat.index}">
                            <td th:utext="${attachment.attachmentName}"></td>
                            <td th:utext="${attachment.attachmentSize+'kb'}"></td>
                            <td>已上传</td>
                            <td><input class="key" type="hidden" th:id="${'attachment-id-'+attachmentStat.index}"
                                       th:value="${attachment.attachmentId}">
                                <button type="button"
                                        class="btn btn-sm btn-primary"
                                        th:onclick='window.open(ctx + "attachment/preview/" + [[${attachment.attachmentId}]])'>
                                    预览
                                </button>
                                <button type="button"
                                        class="btn btn-sm btn-primary"
                                        th:onclick="download([[${attachment.attachmentId}]]);">下载
                                </button>
                                <button type="button" th:if="${see==null  || (see!=null && !see)}"
                                        class="btn btn-sm btn-primary"
                                        th:onclick="deleteFile([[${attachment.attachmentId}]],[[${attachmentStat.index}]],[[${thisId}]],[[${id}]]);">
<!--                                        th:onclick="deleteByAttachmentId([[${attachment.attachmentId}]],[[${attachmentStat.index}]],[[${thisId}]],[[${id}]], [[${sourceId}]], [[${sourceModule}]], [[${sourceLabel1}]], [[${sourceLabel2}]], [[${sourceLabel3}]])">-->
                                    删除
                                </button>
                            </td>
                        </tr>
                    </th:block>
                    </tbody>
                </table>
            </div>
        </div>


        <script type="text/javascript" th:inline="javascript" th:if="${see==null  || (see!=null && !see)}">
            //初始化页面信息
            var prefix = ctx + "pmpty/attachmentMa";
            var isrequired = [[${isrequired}]];
            var id = [[${id==null?'att':id}]]
            if (isrequired != null && isrequired) {
                $("#" + [[${'fjLabel'+thisId}]]).addClass("is-required")
                $("#" + id).attr("required", "");
            }
            layui.use(['upload', 'element', 'layer'], function () {
                var $ = layui.jquery
                    , upload = layui.upload
                    , element = layui.element
                    , layer = layui.layer;
                var uploadListIns = upload.render({
                    elem: '#' + [[${'div'+thisId}]]
                    , elemList: $('#' + [[${'list'+thisId}]]) //列表元素对象
                    , url: prefix + '/upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                    , accept: 'file'
                    , data: {
                        sourceId: [[${sourceId}]],
                        sourceModule: [[${sourceModule}]],
                        sourceLabel1: [[${sourceLabel1==null?'':sourceLabel1}]],
                        sourceLabel2: [[${sourceLabel2==null?'':sourceLabel2}]],
                        sourceLabel3: [[${sourceLabel3==null?'':sourceLabel3}]]
                    }
                    , multiple: true
                    , number: [[${maxFileCount==null?30:maxFileCount}]]
                    , auto: true
                    , choose: function (obj) {
                        if (0 != [[${maxFileCount==null?30:maxFileCount}]]) {//需要验证附件总数
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            if (attachmentIds) {
                                var allFileTotal = attachmentIds.split(",").length;
                                $("#check").val(0);
                                if (allFileTotal >= [[${maxFileCount==null?30:maxFileCount}]]) {
                                    layer.msg('附件总数不能超过' + [[${maxFileCount==null?30:maxFileCount}]]);
                                    return false;
                                }
                                ;
                            }
                            ;
                        }
                        ;
                        var that = this;
                        var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">'
                                , '<td>' + file.name + '</td>'
                                , '<td>' + (file.size / 1014).toFixed(1) + 'kb</td>'
                                , '<td><div class="layui-progress" lay-filter="progress-demo-' + index + '"><div class="layui-progress-bar" lay-percent=""></div></div></td>'
                                , '<td> <input class="key" id="attachment-id-' + index + '"  type="hidden">'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-reload layui-hide">重传</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-download">下载</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-delete">删除</button>'
                                , '</td>'
                                , '</tr>'].join(''));

                            //单个重传
                            tr.find('.demo-reload').on('click', function () {
                                obj.upload(index, file);
                            });

                            //删除
                            tr.find('.demo-delete').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                                if (attachmentIds != null && attachmentIds != '') {
                                    var arr = attachmentIds.split(",");
                                    console.log(arr);
                                    if (arr.includes(key)) {
                                        arr.splice(arr.indexOf(key), 1);
                                        $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                                    }
                                }
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                            });
                            //下载
                            tr.find('.demo-download').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/download/" + key);
                            });

                            that.elemList.append(tr);
                            element.render('progress'); //渲染新加的进度条组件
                        });
                    }
                    , done: function (res, index, upload) { //成功的回调
                        var that = this;
                        if (res.status == 1) { //上传成功
                            var that = this;
                            var tr = that.elemList.find('tr#upload-' + index);
                            tr.find("#attachment-id-" + index).val(res.fileid);
                            console.log(index)
                            console.log(this.files)
                            delete this.files[index]; //删除文件队列已经上传成功的文件
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            var arr = attachmentIds.split(",");
                            if (attachmentIds == null || attachmentIds == '') {
                                arr = [];
                            }
                            arr.push(res.fileid);
                            $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                            tr.find("td").eq(2).html("已上传");
                            $("#check").val(1);
                            return;
                        }
                        this.error(index, upload);
                    }
                    , allDone: function (obj) { //多文件上传完毕后的状态回调
                        console.log(obj)
                    }
                    , error: function (index, upload) { //错误回调
                        var that = this;
                        var tr = that.elemList.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
                    }
                    , progress: function (n, elem, e, index) { //注意：index 参数为 layui 2.6.6 新增
                        console.log(index)
                        element.progress('progress-demo-' + index, n + '%'); //执行进度条。n 即为返回的进度百分比
                    }
                });

            });

            function deleteByAttachmentId(attachmentId, index, listId, id, sourceId, sourceModule, sourceLabel1, sourceLabel2, sourceLabel3) {
                let data = {};
                let that = this;
                data['attachmentId'] = attachmentId;
                data['sourceId'] = sourceId;
                data['sourceModule'] = sourceModule;
                data['sourceLabel1'] = sourceLabel1;
                data['sourceLabel2'] = sourceLabel2;
                data['sourceLabel3'] = sourceLabel3;
                postJsonData(prefix + "/deleteById", data, function (result) {
                    if (result.success) {
                        $("#upload-" + listId + "-" + index).remove();
                        var attachmentIds = $("#" + id).val();
                        console.log(attachmentIds);
                        if (attachmentIds != null && attachmentIds != '') {
                            var arr = attachmentIds.split(",");
                            if (arr.includes(attachmentId)) {
                                arr.splice(arr.indexOf(attachmentId), 1);
                                console.log(arr);
                                $("#" + id).val(arr.join(","));
                            }
                        }
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            }

            function postJsonData(url, data, callback) {
                submitContentType(url, 'application/json;charset=UTF-8', 'post', 'json', JSON.stringify(data), callback);
            }

            function submitContentType(url, contentType, type, dataType, data, callback) {
                console.log("data: " + data);
                var config = {
                    url: url,
                    type: type,
                    dataType: dataType,
                    data: data,
                    contentType: contentType,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        if (typeof callback == "function") {
                            callback(result);
                        }
                        $.modal.closeLoading();
                    }
                };
                $.ajax(config)
            }
        </script>
    </th:block>
</div>
