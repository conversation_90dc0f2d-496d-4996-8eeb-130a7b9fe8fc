<!-- 选择组织封装

labelName:label的text 默认附件
orgCodeId:orgCode name id
orgNameId:orgName name id
selectType:S 单选 M 多选 默认S
level:组织层级 -1只显示公司
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看
-->
<div th:fragment="init">
	<th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelName=${labelName==null?'选择组织：':labelName},
                    selectType=${selectType==null?'S':selectType}

               	 	">
		<th:block th:if="${!see}">
			<div th:class="'input-group'">
				<input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" />
				<input th:name="${orgNameId}" th:id="${orgNameId}"
					   th:onclick="choiceOrg3([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])"
					   th:value="${@OrgUtil.getOrgName(value)}"
					   class="form-control detailOrgOrUser" type="hidden"
					   th:required="${isrequired!=null && isrequired}" readonly>
				<div id="nameDiv3" style="width: 100%;">
					<ul class="clearfix"></ul>
				</div>
				<span class="input-group-addon"  th:onclick="choiceOrg3([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])">
					<i class="fa fa-search "></i>
				</span>
			</div>

		</th:block>
		<th:block th:unless="${!see}">
			<input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" />
			<input th:name="${orgNameId}" th:id="${orgNameId}" th:value="${@OrgUtil.getOrgPathName(value)}" class="form-control" type="hidden" />
			<div id="nameDivSee3" style="width: 100%;">
				<ul class="clearfix"></ul>
			</div>
		</th:block>
	</th:block>
	
	<script type="text/javascript" th:inline="javascript">
        let orgId3 = /*[[${orgCodeId}]]*/"orgId";
		let orgNameId3 = /*[[${orgNameId}]]*/"deptName";
        function choiceOrg3(orgCodeInputId, orgNameInputId, selectType, level, orgCode,showLevel,callback) {
            orgId3 = orgCodeInputId;
            orgNameId3 = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "mpad/org/selectOrgList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + orgId3).val();
            var options = {
                title: '选择组织',
                width: "380",
                height: '500',
                url: url,
                callBack: choiceOrgCallback3
            };
            $.modal.openOptions(options);
        }
        function choiceOrgCallback3(index, layero) {
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + orgId3).val(body.find('#treeId').val());

			let temp1 = body.find('#treeName').val()
			let temp2 = []
			if(temp1!=null){
				temp2 = temp1.split(",")
				for (let i = 0; i < temp2.length; i++) {
					var temp3 = temp2[i];
					if (temp3.indexOf("/")>=0){
						let split_ = temp3.split("/");
						temp3 = split_[split_.length-1]
					}
					temp2[i] = temp3
				}
			}
            $("#" + orgNameId3).val(temp2.join(","));
            layer.close(index);
			initNameSpan3();
        }

		function initNameSpan3(){
			var userCode=$("#"+orgId3).val();
			$("#nameDiv3").find("ul").html("")
			if(userCode!=null && userCode!==''){
				var arr=userCode.split(",");
				var arr2=$("#"+orgNameId3).val().split(",");
				$("#nameDiv3").find("ul").empty();
				var html="";
				for(let i=0;i<arr.length;i++){
					let value = arr2[i]
					if (value.indexOf("/")>=0){
						let split_ = value.split("/");
						value = split_[split_.length-1]
					}
					html+="<li id='"+arr[i]+"' ><span class='nameSpan' onclick='deleteNameSpan3(&quot;" + arr[i] +"&quot;,&quot;" + value +"&quot;)'><i class='fa fa-close'></i>"+ value +"</span></li>";
				}
				$("#nameDiv3").find("ul").append(html);

				$("#"+orgNameId3).attr("type","hidden");
				$("#nameDiv3").show();
			}else{
				$("#"+orgNameId3).attr("type","text");
				$("#nameDiv3").hide();
			}
		}
		initNameSpan3();
		function deleteNameSpan3(code,name){
			var userCodeS=$("#"+orgId3).val();
			var userNameS=$("#"+orgNameId3).val();
			var arrUserCode=userCodeS.split(",");
			var arrUserName=userNameS.split(",");
			arrUserCode.splice(arrUserCode.indexOf(code), 1)
			arrUserName.splice(arrUserName.indexOf(name), 1)
			$("#"+orgId3).val(arrUserCode.join(","));
			$("#"+orgNameId3).val(arrUserName.join(","));
			$("#nameDiv3 #"+code).remove();
			if(arrUserName.length === 0){
				$("#"+orgNameId3).attr("type","text");
				$("#nameDiv3").hide();
			}
		}

		function seeInit3(){
			let names = /*[[${@OrgUtil.getOrgPathName(value)}]]*/""
			if(names==null){
				return false;
			}
			let arr = names.split(",")
			let html="";
			for(let i=0;i<arr.length;i++){
				let value = arr[i]
				if (value.indexOf("/")>=0){
					let split_ = value.split("/");
					value = split_[split_.length-1]
				}
				html+="<li id='"+arr[i]+"' ><span class='nameSpan'>"+ value +"</span></li>";
			}
			$("#nameDivSee3").find("ul").append(html);
		}
		seeInit3()
    </script>
	<style  th:inline="javascript">
		.clearfix:after {
			content: "";
			display: block;
			clear: both;
		}
		#nameDiv3,#nameDivSee3{
			display: inline-block;
			color: white;
			background: #f9f9f9;
			border: 1px solid #ddd;
		}
		#nameDivSee3{
			border: none;
			background: none;
		}
		#nameDiv3>ul>li,#nameDivSee3>ul>li{
			float: left;
			display: inline;
			margin-right: 10px;
		}
		.nameSpan{
			border-radius: 4px;
			padding: 7px;
			background-color: #2668d4;
			border-color: #2668d4;
			cursor:pointer;
			line-height: 33px;
			transition: all 0.5s;
		}
		.nameSpan:hover{
			background-color: #204d74;
			border-color: #122b40;
		}
	</style>
</div>