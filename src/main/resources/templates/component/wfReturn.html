<!-- 
流程退回 用于单任务
	不允许退回的不会出现按钮,能退回多个节点用户自行选择
输入参数:
taskId 任务ID
callback 回调函数,第一个参数固定为用户选择的退回节点
-->
<div th:fragment="init">
	<th:block th:with="returnActS=${@SWorkFlowUtil.getReturnsActivities(taskId)}">
		<th:block th:if="${returnActS?.size()>0}">
			<div class="btn-group dropup">
				<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
					&nbsp;退回
					<span class="caret"></span>
				</button>
				<ul class="dropdown-menu dropdown-menu-right">
					<li th:each="returnAct : ${returnActS}">
						<a href="javascript:void(0);" th:onclick="doWorkFlowReturn([[${returnAct.nodeKey}]],[[${callback}]])" th:text="${returnAct.nodeName}"></a>
					</li>				
				</ul>
			</div>
		</th:block>
	</th:block>
	<script type="text/javascript" th:inline="javascript">
	function doWorkFlowReturn(nodeKey,callback){
		$("#workFlow_returnActivityKey").val(nodeKey);
		eval(callback+'("'+nodeKey+'")');
	}
	</script>
</div>