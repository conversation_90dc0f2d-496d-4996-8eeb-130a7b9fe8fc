
<!-- 附件上传

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
name:附件input name 默认att
id:附件input id 默认att
isrequired:附件是否必须上传 默认false
新增参数配置，增强附件组件
uploadSuccessCallback:上传成功回调函数
clickDelFileCallback:点击删除按钮回调函数
isDelSource: 是否删除附件源数据 默认true  （由于SAttachmentUtil.copyAttachment()方法复制的是附件关系；[HGDC]文档中心存有附件关系，所以不能直接删除源文件）
isDelRela: 是否删除附件关系 默认false
filedbCallback:文件对比回调函数
ckjgfileCallback:文件对比结果查看
editButtonClass:文件写操作按钮控制(可通过class控制通知上传文件按钮和删除文件按钮)

see:是否查看
回显数据：
	sourceId 业务id,
	sourceModule 模块
	sourceLabel1 标签
	sourceLabel2 标签
	sourceLabel3 

上传属性：
maxFileCount:最大限制上传附件 默认是10
maxUploadSize:文件最大限制 默认是50MB
type:文件类型 如pdf
-->
<div th:fragment="init">
    <th:block
            th:with="attachmentMap=${@SAttachmentUtil.getAttachmentBySourceIdAndSourceLabelToMap(sourceId, sourceModule,sourceLabel1, sourceLabel2, sourceLabel3)},
                        thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)}">

        <th:bolck th:if="${see==null  || (see!=null && !see)}">
            <input th:name="${name==null?'att':name}" th:id="${id==null?'att':id}" type="hidden"
                   th:value="${attachmentMap.attachmentIds!=null?attachmentMap.attachmentIds:''}" class="form-control">
            <div class="layui-upload">
                <div>
                    <button th:id="${'div'+thisId}" th:if="${see==null  || (see!=null && !see)}" type="button"
                            th:class="${'btn btn-primary '+editButtonClass}">选择文件
                    </button>
                    <button  th:onclick="dborgfile([[${id}]])" th:if="${see==null  || (see!=null && !see)}" type="button"
                            class="btn btn-warning">对比原始文件
                    </button>
                    <button  th:onclick="ckjgfile([[${id}]])" th:if="${see==null  || (see!=null && !see)}" type="button"
                             class="btn btn-warning">查看处理结果
                    </button>
                    <!--拖拽-->
                    <!--<div class="drag-box" th:if="${see==null  || (see!=null && !see)}" style="border: 1px  dashed  #ddd;width: 200px; height: 33px;background-color:  #fff; display:  inline-block;
                 font-size:  12px;
                 color:  #666;
                 line-height: 33px;
                 cursor:  pointer;">
                         <img th:src="@{/img/line-drag.png}" width="24" height="24"/>可将附件拖拽到框中
                     </div> -->
                    <!--拖拽-->
                </div>
                <div class="layui-upload-list" style="max-width: 1000px;">
                    <table class="layui-table">
                        <colgroup>
                            <col style="width: 50%">
                            <col style="width: 10%">
                            <col style="width: 10%">
                            <col style="width: 30%">
                        </colgroup>
                        <thead>
                        <tr>
                            <th >文件名</th>
                            <th >大小</th>
                            <th>上传进度</th>
                            <th >操作</th>
                        </tr>
                        </thead>
                        <tbody th:id="${'list'+thisId}">
                        <th:block th:if="${attachmentMap!=null && not #lists.isEmpty(attachmentMap.attachmentList)}">
                            <tr th:each="attachment: ${attachmentMap.attachmentList} " class="upload"
                                th:id="${'upload-'+thisId+'-'+attachmentStat.index}">
                                <td th:utext="${attachment.attachmentName}"></td>
                                <td th:utext="${attachment.attachmentSize+'kb'}"></td>
                                <td>已上传</td>
                                <td><input class="key" type="hidden" th:id="${'attachment-id-'+attachmentStat.index}"
                                           th:value="${attachment.attachmentId}">
                                    <button  type="button"
                                             class="btn btn-sm btn-primary"
                                             th:onclick='window.open(ctx + "attachment/download/" + [[${attachment.attachmentId}]]);'>下载
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary"
                                            th:onclick='window.open(ctx + "attachment/preview/" + [[${attachment.attachmentId}]]);'>预览
                                    </button>
                                    <button  type="button"  th:if="${see==null  || (see!=null && !see)}"
                                             th:class="${'btn btn-sm btn-primary '+editButtonClass}"
                                             th:onclick="deleteFile([[${attachment.attachmentId}]],[[${attachmentStat.index}]],[[${thisId}]],[[${id}]]);">
                                        删除
                                    </button>
                                </td>
                            </tr>
                        </th:block>
                        </tbody>
                    </table>
                </div>
            </div>
        </th:bolck>

        <th:bolck th:unless="${see==null  || (see!=null && !see)}">

            <th:block th:if="${#lists.isEmpty(attachmentMap.attachmentList)}">
                <div class="upload-list fn-clear" >
                    <p class="fn-left ">无附件 </p>
                </div>
            </th:block>
            <th:block th:unless="${#lists.isEmpty(attachmentMap.attachmentList)}">
                <div class="upload-list fn-clear" th:each="attachment: ${attachmentMap.attachmentList}">
                    <p class="fn-left ">
                        <a style="color: #816c00" target="_blank" th:onclick='window.open(ctx + "attachment/preview/" + [[${attachment.attachmentId}]])'>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="12" fill="currentColor" class="bi bi-eye-fill" viewBox="0 0 16 16">
                                <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/>
                                <path d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8zm8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>
                            </svg>
                            预览
                        </a>
                        &nbsp;&nbsp;&nbsp;
                        <a target="_blank" th:href="@{/attachment/download/}+${attachment.attachmentId}">
                            <i class="fa fa-paperclip"></i>
                            [[${attachment.attachmentName}]]
                        </a>
                    </p>
                </div>
            </th:block>


            <input th:name="${name==null?'att':name}" th:id="${id==null?'att':id}" type="hidden"
                   th:value="${attachmentMap.attachmentIds!=null?attachmentMap.attachmentIds:''}" class="form-control">
        </th:bolck>
        <script type="text/javascript" th:inline="javascript" th:if="${see==null  || (see!=null && !see)}">
            //初始化页面信息
            var isrequired = [[${isrequired}]];
            var id = [[${id==null?'att':id}]]
            if (isrequired != null && isrequired) {
                $("#" + [[${'fjLabel'+thisId}]]).addClass("is-required")
                $("#" + id).attr("required", "");
            }
            layui.use(['upload', 'element', 'layer'], function () {
                var $ = layui.jquery
                    , upload = layui.upload
                    , element = layui.element
                    , layer = layui.layer;
                var uploadListIns = upload.render({
                    elem: '#' + [[${'div'+thisId}]]
                    , elemList: $('#' + [[${'list'+thisId}]]) //列表元素对象
                    , url: ctx + 'attachment/upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                    , accept: 'file'
                    , exts: [[${type==null?'':type}]]
                    , multiple: true
                    ,size: [[${maxUploadSize==null?500:maxUploadSize}]]*1024
                    , number: [[${maxFileCount==null?30:maxFileCount}]]
                    , auto: true
                    , choose: function (obj) {
                        if(0!=[[${maxFileCount==null?30:maxFileCount}]]){//需要验证附件总数
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            if(attachmentIds){
                                var allFileTotal = attachmentIds.split(",").length;
                                if(allFileTotal>=[[${maxFileCount==null?30:maxFileCount}]]){
                                    layer.msg('附件总数不能超过'+[[${maxFileCount==null?30:maxFileCount}]]);
                                    return false;
                                };
                            };
                        };
                        var that = this;
                        var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">'
                                , '<td>' + file.name + '</td>'
                                , '<td>' + (file.size / 1014).toFixed(1) + 'kb</td>'
                                , '<td><div class="layui-progress" lay-filter="progress-demo-' + index + '"><div class="layui-progress-bar" lay-percent=""></div></div></td>'
                                , '<td> <input class="key" id="attachment-id-' + index + '"  type="hidden">'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-reload layui-hide">重传</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-download">下载</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-preview">预览</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-delete">删除</button>'
                                , '</td>'
                                , '</tr>'].join(''));

                            //单个重传
                            tr.find('.demo-reload').on('click', function () {
                                obj.upload(index, file);
                            });

                            //删除
                            tr.find('.demo-delete').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                                if (attachmentIds != null && attachmentIds != '') {
                                    var arr = attachmentIds.split(",");
                                    if (arr.includes(key)) {
                                        arr.splice(arr.indexOf(key), 1);
                                        $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                                    }
                                }
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                            });
                            //下载
                            tr.find('.demo-download').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/download/" + key);
                            });
                            //预览
                            tr.find('.demo-preview').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/preview/" + key);
                            });

                            that.elemList.append(tr);
                            element.render('progress'); //渲染新加的进度条组件
                        });
                    }
                    , done: function (res, index, upload) { //成功的回调
                        var that = this;
                        if (res.status == 1) { //上传成功
                            var that = this;
                            var tr = that.elemList.find('tr#upload-' + index);
                            tr.find("#attachment-id-" + index).val(res.fileid);
                            //console.log(index)
                            //console.log(this.files)
                            delete this.files[index]; //删除文件队列已经上传成功的文件
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            var arr = attachmentIds.split(",");
                            if (attachmentIds == null || attachmentIds == '') {
                                arr = [];
                            }
                            arr.push(res.fileid);
                            $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                            tr.find("td").eq(2).html("已上传");
                            layer.msg("上传成功")

                            let uploadSuccessCallback= [[${uploadSuccessCallback}]]
                            if(uploadSuccessCallback!=null){
                                window.eval(uploadSuccessCallback+'('+ JSON.stringify(res) + ')');
                            }
                            return;
                        }else{
                            //上传失败后的提示
                            layer.alert("上传失败:"+res.msg);
                        }
                        this.error(index, upload);
                    }
                    , allDone: function (obj) { //多文件上传完毕后的状态回调
                        //console.log(obj)
                    }
                    , error: function (index, upload) { //错误回调
                        var that = this;
                        var tr = that.elemList.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
                    }
                    , progress: function (n, elem, e, index) { //注意：index 参数为 layui 2.6.6 新增
                        //console.log(index)
                        element.progress('progress-demo-' + index, n + '%'); //执行进度条。n 即为返回的进度百分比
                    }
                });

            });
            /**
             * 删除文件
             * @param attachmentId
             * @param index
             */
            var deleteFile =  function (attachmentId, index, id, zjId) {
                console.log(zjId)
                let clickDelFileCallback= [[${clickDelFileCallback}]]
                if(clickDelFileCallback!=null){
                    window.eval(clickDelFileCallback + '("' + attachmentId + '","' + index + '","' + id + '")');
                }
                let isDelSource = /*[[${isDelSource}]]*/"true";
                let isDelRela = /*[[${isDelRela}]]*/"false";
                if("true" === isDelRela || true === isDelRela){
                    //删除指定附件关系
                    let url = ctx + "attachmentPlus/deleteAttachmentMapByAttachmentIdsAndSourceId" + "?attachmentIds=" + attachmentId + "&sourceId=" + [[${sourceId}]] + "&sourceModule=" + [[${sourceModule}]]
                    $.ajax({
                        url: url,
                        type: "get",
                        success: (res)=>{
                            if(res.success){
                                var demoListView = $("#upload-" + id + '-' + index );
                                var attachmentIds = $("#" + zjId).val();
                                debugger
                                if (attachmentIds != null && attachmentIds != '') {
                                    var arr = attachmentIds.split(",");
                                    if (arr.includes(attachmentId)) {
                                        arr.splice(arr.indexOf(attachmentId), 1);
                                        $("#" + zjId).val(arr.join(","));
                                    }
                                }
                                demoListView.remove();
                                layer.msg('删除成功');
                            }else{
                                $.modal.alertError(res.msg);
                            }
                        }
                    });
                }else if (isDelSource === null || isDelSource === "" || "true" === isDelSource || true === isDelSource) {
                    //删除源数据
                    $.ajax({
                        url: ctx + "attachment/delete/" + attachmentId,
                        methods: "get",
                        success: function (data) {
                            if (data.code == 500) {
                                layer.msg(data.msg);
                            } else {
                                var demoListView = $("#upload-" + id + '-' + index );
                                var attachmentIds = $("#" + zjId).val();
                                if (attachmentIds != null && attachmentIds != '') {
                                    var arr = attachmentIds.split(",");
                                    if (arr.includes(attachmentId)) {
                                        arr.splice(arr.indexOf(attachmentId), 1);
                                        $("#" + zjId).val(arr.join(","));
                                    }
                                }
                                demoListView.remove();
                                layer.msg('删除成功');
                            }

                            //console.log(data);
                        },
                        error: function (data) {
                            //console.log(data)
                            layer.msg('错误', data.msg);
                        }
                    });
                }else{
                    var demoListView = $("#upload-" + id + '-' + index );
                    var attachmentIds = $("#" + zjId).val();
                    if (attachmentIds != null && attachmentIds != '') {
                        var arr = attachmentIds.split(",");
                        if (arr.includes(attachmentId)) {
                            arr.splice(arr.indexOf(attachmentId), 1);
                            $("#" + zjId).val(arr.join(","));
                        }
                    }
                    demoListView.remove();
                    layer.msg('删除成功');
                }
            }

            //文件对比功能
            var dborgfile =  function (id) {
                var arrIds = $('#'+id).val()
                console.log(arrIds)
                if (!arrIds){
                    //附件id为空
                    layer.msg("对比文件不能为空");
                    return;
                }
                let filedbCallback= [[${filedbCallback}]]
                if(filedbCallback!=null){
                    window.eval(filedbCallback+'('+ JSON.stringify(arrIds) + ','+JSON.stringify(id)+')');
                }
            }

            var ckjgfile =  function (id) {
                console.log(id)
                let ckjgfileCallback= [[${ckjgfileCallback}]]
                if(ckjgfileCallback!=null){
                    window.eval(ckjgfileCallback+'('+ JSON.stringify(id) + ')');
                }
            }
        </script>
    </th:block>
</div>
