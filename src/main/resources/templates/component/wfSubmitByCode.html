<!-- 
流程提交 用于单任务
	不允许退回的不会出现按钮,能退回多个节点用户自行选择
输入参数:
processCode 流程编码
currentActivity 节点编码
callback 回调函数,第一个参数固定为用户选择的提交节点
-->
<div th:fragment="init">
	<th:block th:with="transitionS=${@SWorkFlowUtil.getNextTransition(processCode, currentActivity, null)}">
		<th:block th:if="${transitionS?.size()==0}">
			<button type="button" class="btn btn-primary" th:onclick="doWorkFlowSubmit('',[[${callback}]])" title="提交下一步" data-toggle="tooltip">
				<i class="fa fa-share"></i>&nbsp;提交
			</button>
		</th:block>
		<th:block th:if="${transitionS?.size()==1}">
			<button type="button" class="btn btn-primary" th:onclick="doWorkFlowSubmit([[${transitionS[0].transitionKey}]],[[${callback}]])" th:attr="title='提交'+${transitionS[0].nodeName}" data-toggle="tooltip">
				<i class="fa fa-share"></i>&nbsp;提交
			</button>
		</th:block>
		<th:block th:if="${transitionS?.size()>1}">
			<th:block th:each="transition : ${transitionS}" >
				<button type="button" class="btn btn-primary" th:onclick="doWorkFlowSubmit([[${transition.transitionKey}]],[[${callback}]])" th:attr="title='提交'+${transition.nodeName}" data-toggle="tooltip">
					<i class="fa fa-share"></i>&nbsp;[[${transition.transitionName}]]
				</button>
			</th:block>
		</th:block>
	</th:block>
	<script type="text/javascript" th:inline="javascript">
	$(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });
	function doWorkFlowSubmit(transitionKey,callback){
		$("#workFlow_transitionKey").val(transitionKey);
		eval(callback+'("'+transitionKey+'")');
	}
	</script>
</div>