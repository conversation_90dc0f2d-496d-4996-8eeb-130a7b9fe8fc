<!--
	会签操作
	从流程节点扩展属性(extraParameters)里获取相关信息
	HQ_PROCESS_CODE 会签流程编码
-->
<div th:fragment="init" xmlns:th="http://www.w3.org/1999/html">
	<th:block th:if="${taskId != null && extraParameters != null}">
		<th:block th:if="${extraParameters.get('HQ_PROCESS_CODE') != null}">
			<button type="button" class="btn btn-primary" th:onclick="doHqUnit()">
				<i class="fa fa-list"></i><span>会签</span>
			</button>
			<script type="text/javascript" th:inline="javascript">
				function doHqUnit(){
					let extraParameters = [[${extraParameters}]];//流程扩展属性
					let url = ctx + 'mpad/user/selectUserList';
					let hqUnitKind = extraParameters.HQ_UNIT_KIND;//选用户类型
					if (hqUnitKind==='DEPT') {
						url = ctx + 'mpad/user/selectDeptUser';
					} else if (hqUnitKind==='TENANT') {
						url = ctx + 'mpad/user/selectTenantUser';
					}
					url += "?selectType=M";
					if (hqUnitKind==='TENANT') {
						url += "&orgCode=" + $('#tenantId').val();
					}
					url += "&callback=doHq";
					$.modal.open("选择会签用户", url, 1000, 450);
				}

				function doHq(code, name) {
					let extraParameters = [[${extraParameters}]];//流程扩展属性
					let processCode = extraParameters.HQ_PROCESS_CODE;//选用户类型
					let jsonData = {};
					let url = ctx+"mpwf/flowInfo/doHq";
					jsonData["businessId"] = [[${businessId}]];//业务Id
					jsonData["taskId"] = [[${taskId}]];//任务ID
					jsonData["processCode"] = processCode;//意见
					jsonData["comment"] = $("#workFlow_comment").val();//意见
					let subProcessParamS = [];
					let codeS = code.split(',');
					for (let i = 0; i < codeS.length; i++) {
						if (codeS[i].trim() != '') {
							let unitData = {};
							unitData["departmentNo"] = codeS[i].trim();
							unitData["users"] = codeS[i].trim();
							subProcessParamS.push(unitData);
						}
					};
					jsonData["subProcessParamS"] = subProcessParamS;
					$.modal.confirm("确认会签吗？", function () {
						$.modal.loading("正在处理中，请稍后...");
						$.ajax({
							url: url,
							data: JSON.stringify(jsonData),
							dataType:"json",
							contentType: "application/json",
							type: 'POST',
							success: function (result) {
								$.modal.closeLoading();
								if (result.code == web_status.SUCCESS) {
									$.modal.alertSuccess(result.msg);
								} else if (result.code == web_status.WARNING) {
									$.modal.alertWarning(result.msg)
								} else {
									$.modal.alertError(result.msg);
								}
							}
						});
					});
				}
			</script>
		</th:block>
	</th:block>
</div>