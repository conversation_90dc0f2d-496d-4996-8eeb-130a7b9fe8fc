<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('选择用户')" />
</head>
<body class="gray-bg">
<div class="ui-layout-center">
	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 select-table">
				<input type="hidden" id="chooseConfigUser" th:value="${values}"/>
				<table id="bootstrap-table-selectConfigUser"></table>
			</div>
		</div>
	</div>
</div>
<th:block th:include="include :: baseJs" />
<script th:inline="javascript">
	$(function() {
		queryUserList();
	});

	function queryUserList(){
		var singleSelect=false;
		if([[${selectType=='S'}]]){
			singleSelect=true;
		}
		var options = {
			id: "bootstrap-table-selectConfigUser",
			data: [[${userData}]]||[],
			uniqueId: "userCode",
			modalName: "用户",
			showSearch: false,
			showRefresh: false,
			showToggle: false,
			singleSelect: singleSelect,
			showColumns: false,
			clickToSelect: true,
			rememberSelected: true,
			pagination: false,
			sidePagination: 'client',
			onDblClickRow: function (row, $element) {
				dbClick(row);
			},
			columns: [
				{
					field: 'state',
					checkbox: true,
					formatter: function(value, row, index) {
						if($("#chooseConfigUser").val().indexOf(row.userCode)>-1){
							return true;
						}
						return false;
					}
				},
				{
					field: 'userCode',
					title: '用户工号',
					sortable: true
				},
				{
					field: 'userName',
					title: '用户名称'
				}]
		};
		$.table.init(options);
	}

	/* 添加用户-选择用户-提交  */
	function submitHandler() {
		let codes = $.table.selectColumns('userCode');
		let names = $.table.selectColumns('userName');
		parent.choiceConfigUserCallback(codes, names);
		var callback= [[${callback}]]
		if(callback!=null && callback!=''){
			parent.eval(callback+'("'+codes+'","'+names+'")');
		}
		var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
		parent.layer.close(index);
	}

	function resetData(){
		$.form.reset();
	}

	//双击事件
	function dbClick(row){
		let codes = row.userCode;
		let names = row.userName;
		parent.choiceConfigUserCallback(codes, names);
		var callback= [[${callback}]]
		if(callback!=null && callback!=''){
			parent.eval(callback+'("'+codes+'","'+names+'")');
		}
		var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
		parent.layer.close(index);
	}
</script>
</body>
</html>