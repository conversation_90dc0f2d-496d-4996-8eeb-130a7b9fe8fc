<!-- 选择角色用户封装
roleCode 角色编码 必填
userCodeId:userCode name id
userNameId:userName name id
selectType:S 单选 M 多选 默认S
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->
<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}
               	 	">
        <th:block th:if="${!see}">
            <div th:class="'input-group'" th:onclick="choiceRoleUser([[${#strings.replace(userCodeId,'.','_')}]],[[${#strings.replace(userNameId,'.','_')}]],[[${selectType}]],[[${roleCode}]],[[${callback}]])">
                <input th:name="${userCodeId}" th:id="${#strings.replace(userCodeId,'.','_')}" type="hidden" th:value="${value}" />
                <input th:name="${userNameId}" th:id="${#strings.replace(userNameId,'.','_')}" th:value="${@SUserUtil.getUserName(value)}" class="form-control detailOrgOrUser" type="text"
                    th:required="${isrequired!=null && isrequired}" readonly >
                <span class="input-group-addon " ><i class="fa fa-search "></i></span>
            </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div class="form-control-static" th:utext="${@SUserUtil.getUserName(value)}"></div>
            <input th:name="${userCodeId}" th:id="${#strings.replace(userCodeId,'.','_')}" type="hidden" th:value="${value}" /> <input
                th:name="${userNameId}" th:id="${#strings.replace(userNameId,'.','_')}" type="hidden" th:value="${@SUserUtil.getUserName(value)}" class="form-control" />
        </th:block>
    </th:block>
</div>