
<!--
name:input name 默认 radio
id:radio  每一个id为 id+选择value   默认 name 值
callback： 点击时回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
dictCode  businessType: 数据字典key
notShowValue 不显示值（选项）

data 自定义数据
dataKey 自定义数据value 的key
dataNameKey 自定义数据name 的key

dataName（see=true 需要设置 ） 回显名称 如果不设置需要传入data dataKey dataNameKey进行筛选查询名称
-->


<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'radio':name},
               	 	id=${id==null?name:id},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}">

        <th:block th:if="${!see}">

            <th:block th:if="${#lists.isEmpty(data)}">
                <div class="radio-box"
                     th:each="dict : ${@SDictUtil.getDictList(businessType,dictCode)}" th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict.dictValue)}">
                    <input   type="radio" th:id="${id+dict.dictValue}"  th:name="${name}"
                           th:value="${dict.dictValue}"   th:attr="checked=${value!=null && value==dict.dictValue?true:false}" > <label
                        th:for="${'type_'+dictCode}" th:text="${dict.dictName}" ></label>
                </div>
            </th:block>
            <th:block th:unless="${#lists.isEmpty(data)}">
                <div class="radio-box" th:each="dict : ${data}" th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict[dataKey])}">
                <input   type="radio" th:id="${id+dict[dataKey]}"  th:name="${name}"
                         th:value="${dict[dataKey]}"   th:attr="checked=${dict[dataKey]==value?true:false}" > <label
                    th:for="${'type_'+dict[dataKey]}" th:text="${dict[dataNameKey]}" ></label>
                </div>
            </th:block>


        </th:block>

        <th:block th:unless="${!see}">

            <div th:if="${dataName!=null }"  th:id="${id}" class="form-control-static" th:utext="${dataName}"></div>

            <div th:unless="${dataName!=null }" th:id="${id}"  class="form-control-static" th:utext="${businessType!=null && dictCode!=null ?@SDictUtil.getDictName(businessType,dictCode,value):@SDictUtil.getNameByData(data,dataKey,dataNameKey,value)}"></div>
            <input th:id="${id}" th:name="${name}" th:value="${value}" type="hidden"/>
        </th:block>

        <script th:inline="javascript" th:if="${!see}">
            if ([[${isrequired!=null && isrequired}]]) {
                $("input:radio[name='"+[[${name}]]+"']").attr("required", "");
            }

        </script>
        <script th:inline="javascript" th:if="${callback!=null}">

            $('input:radio[name="'+[[${name}]]+'"]').on('ifClicked', function(event){
                var callback= [[${callback}]]

                eval(callback+'("'+$(this).val()+'")');
            });

        </script>
    </th:block>

</div>




