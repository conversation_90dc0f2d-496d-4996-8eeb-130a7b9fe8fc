<!--
	注意 该组件必须配合 组件 wfWorkFlow2 使用
	流程退回 用于单任务
	不允许退回的不会出现按钮,能退回多个节点用户自行选择
输入参数:
taskId 任务ID
callback 回调函数,第一个参数固定为用户选择的退回节点
-->
<div th:fragment="init">
	<th:block th:with="returnActS=${@SWorkFlowUtil.getReturnsActivities(taskId)}">
		<th:block th:if="${returnActS?.size()>0}">
			<div class="btn-group dropup">
				<button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" th:onclick="openReturnWindow()" >
					&nbsp; <i class="fa fa-step-backward"></i>退回
				</button>
			</div>
			<div id="returnWindow" style="display: none">
				<div class="returnbox-tit">退回设置</div>
				<div class="returnbox">
					<h2>请选择退回节点</h2>
					<div class="returnlist">
						<ul>
							<li th:each="returnAct : ${returnActS}">
								<input type="radio" name="returnActivityKey" th:value="${returnAct.nodeKey}">[[${returnAct.nodeName}]]</input>
							</li>
						</ul>
					</div>
					<h2>退回后再提交的处理方式</h2>
					<div class="returninput" >
						<div class="returnlist">
							<ul>
								<li>
									<span>请选择：</span>
								<th:block th:each="returnOption : ${@WorkFlowUtil.getReturnOption()}">
									<span><input type="radio" th:value="${returnOption.value}" name="canJumpReturn" th:checked="${returnOption.get('checked')}">[[${returnOption.key}]]</input></span>
								</th:block>
								</li>
							</ul>
						</div>
					</div>
				</div>
				<div style="position: absolute; right: 10px; bottom: 10px;">
					<button type="button" class="btn btn-primary" onclick="chooseReturnNode()"> <i class="fa fa-check"></i> 确 定 </button>
					<button type="button" class="btn btn-danger" onclick="closeReturnWindow()"> <i class="fa fa-reply-all"></i> 关 闭 </button>
				</div>
			</div>
			<script type="text/javascript" th:inline="javascript">
				//打开的页面的index，关闭用
				let returnWindowIndex;
				//点击退回
				function openReturnWindow(){
					returnWindowIndex = layer.open({
						type: 1,
						area: ['520px', '350px'],//大小
						fix: true,
						//不固定
						maxmin: true,
						shade: false,
						closeBtn: false, //不显示关闭按钮
						title: false, //不显示标题
						content: $('#returnWindow'), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
						cancel: function(){
							//layer.msg('捕获就是从页面已经存在的元素上，包裹layer的结构', {time: -1, icon:6});
						}
					});
				}

				//确定
				function chooseReturnNode(){
					let returnActivityKey = $("input[name='returnActivityKey']:checked").val();
					if (!returnActivityKey) {
						$.modal.alertWarning("请选择退回节点！");
						return false;
					}
					let canJumpReturn = $("input[name='canJumpReturn']:checked").val();
					if (!canJumpReturn) {
						$.modal.alertWarning("请选择退回后再提交的处理方式！");
						return false;
					}
					$("#workFlow_returnActivityKey").val(returnActivityKey);

					$('#workFlow_variable_canJumpReturn').attr("name", "workFlow.variable['canJumpReturn_"+returnActivityKey+"']");
					$('#workFlow_variable_canJumpReturn').val(canJumpReturn);

					let callback = [[${callback}]];
					if (callback) {
						eval(callback+'("'+returnActivityKey+'")');
					}
				}

				//关闭
				function closeReturnWindow(){
					layer.close(returnWindowIndex);
				}
			</script>
		</th:block>
	</th:block>
</div>