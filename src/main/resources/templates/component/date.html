
<!--
name:input name 默认 dateInput
id:input id 默认 name值
class：input class


see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值（Date型）
strValue: 回显值（String型）,
format : 时间格式 默认'yyyy-mm-dd',
minView: 时间视图  默认'2'
startDate: false  开始时间
endDate : false   结束时间
-->
<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},

               	 	name=${name==null?'dateInput':name},
               	 	id=${id==null?name:id},
               	 	class=${class==null?name:class},
               	 	format=${format==null?'yyyy-mm-dd':format},
               	 	minView=${minView==null?2:minView},

               	 	value=${value==null?null:#dates.format(value,'yyyy-MM-dd')},
               	 	strValue=${strValue!=null?strValue:value},
               	 	minDate=${minDate!=null?minDate:false},
               	 	maxDate=${maxDate!=null?maxDate:false},

                    startDate=${startDate!=null?startDate:false},
                    endDate=${endDate!=null?endDate:false}
               	 	">
        <th:block th:if="${!see}">
                <div class="input-group date" >
                    <input th:name="${name}" th:id="${id}" th:class="${class} + ' form-control'"
                           th:placeholder="${format}" type="text" th:value="${strValue}"  readonly>
                    <span class="input-group-addon" ><i class="fa fa-calendar"></i></span>
                </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div   th:class="form-control-static" th:utext="${strValue}"></div>

            <input th:name="${name}" th:id="${id}" class="form-control"
                   th:placeholder="${format}" type="hidden" th:value="${strValue}"  >
        </th:block>

        <script th:inline="javascript" th:if="${!see}">
            if ([[${isrequired!=null && isrequired}]]) {
                $("#" + [[${id}]]).attr("required", "");
            }
            $("input[name='"+[[${name}]]+"']").datetimepicker({
                format: [[${format}]],
                startView: [[${minView}]],
                minView: [[${minView}]],
                autoclose: true,
                scrollInput:false,
                startDate:[[${startDate}]],
                endDate:[[${endDate}]],
                todayBtn:true,
                forceParse:false
            });
            $("input[name='"+[[${name}]]+"']").parent().find("span").bind('click', function () {
            	$("input[name='"+[[${name}]]+"']").datetimepicker("show");
            });
        </script>
    </th:block>
</div>



