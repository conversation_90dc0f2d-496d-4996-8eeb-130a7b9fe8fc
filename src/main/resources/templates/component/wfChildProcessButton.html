<!--
	会签子流程
	输入参数:
	processInstanceId 主流程实例ID
-->
<div th:fragment="init">
	<th:block th:with="childProcess=${@SWorkFlowUtil.getHQChildFlowInfoByBusinessId(businessId)}">
		<th:block th:if="${childProcess?.size()>0}">
			<button type="button" class="btn btn-primary" th:onclick="childProcess([[${businessId}]])"> <i class="fa fa-users"></i>会签子流程</button>
			<script type="text/javascript" th:inline="javascript">
				function childProcess(businessId) {
					var url = ctx + "mpwf/flowInfo/childProcess/" + businessId;
					$.modal.openTab("会签子流程", url, true);
				}
			</script>
		</th:block>
	</th:block>
</div>