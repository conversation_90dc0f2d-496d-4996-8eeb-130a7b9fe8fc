<!--

name:input name 默认 selectbox
id:input id 默认 name值
textName 文本值 默认 name值+Text

isrequired:是否必填 默认false
value: 回显值
businessType,dictCode  : 数据字典key
isfirst: 是否添加首选项
firstName 首选项名称 默认 请选择
firstValue 首选项名称 默认 ''
-->

<div th:fragment="init">
    <style>
        .selectM {
            border-radius: 4px;
            border: 1px solid #e5e6e7;
            color: inherit;
            display: block;
            padding: 3px 6px 4px;
            -webkit-transition: border-color .15s ease-in-out 0s, box-shadow .15s ease-in-out 0s;
            transition: border-color .15s ease-in-out 0s, box-shadow .15s ease-in-out 0s;
            width: 100%;
            height: 34px;
        }
    </style>
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'selectbox':name},
               	 	id=${id==null?name:id},
               	 	textName=${textName==null?name+'Text':textName},
               	 	firstName=${firstName==null?'请选择':firstName},
               	 	firstValue=${firstValue==null?'':firstValue},
               	 	arrayValue=${value==null?null:#strings.arraySplit(value, ',')},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}
               	 	">
        <th:block th:if="${!see}">
            <select readonly th:id="${id}" th:name="${name}" multip class="selectM" tip="">
            </select>
            <input type="hidden" th:name="${textName}" th:id="${textName}">
        </th:block>
        <th:block th:unless="${!see}">
            <div th:if="${dataName!=null }" class="form-control-static" th:utext="${dataName}"></div>
            <div th:unless="${dataName!=null }" class="form-control-static" th:utext="${businessType!=null && dictCode!=null ?@SDictUtil.getDictName(businessType,dictCode,value):@SDictUtil.getNameByData(data,dataKey,dataNameKey,value)}"></div>
            <input th:id="${id}" th:name="${name}" th:value="${value}" type="hidden"/>
        </th:block>
        <script>
            (function () {
                selectMultip = {
                    register: function (id) {
                        //大致思路是：为下拉选创建一个隐藏的子选项，每次单选之后将单选的值追加到隐藏的子选项中，并将子选项选中显示即可
                        //全局查找所有标记multip的select
                        var eList = document.querySelectorAll("[multip]");
                        for (var i = 0; i < eList.length; i++) {
                            render(eList[i]);
                        }
                    },
                    reload: function (id, data, setData) {
                        var htm = "";
                        var optinoStyle = "";
                        for (var i = 0; i < data.length; i++) {
                            if (data[i].choose) {
                                htm += '<option value="' + data[i].value + '" choose>' + data[i].text + '</option>'
                            } else {
                                htm += '<option value="' + data[i].value + '">' + data[i].text + '</option>'
                            }
                        }
                        var e = document.getElementById(id);
                        e.innerHTML = htm;
                        render(e);
                        this.setVal(id, setData);
                    },
                    setVal: function (id, str) {
                        var type = Object.prototype.toString.call(str);
                        switch (type) {
                            case "[object String]":
                                document.getElementById(id).val = str;
                                break;
                            case "[object Array]":
                                document.getElementById(id).val = str.toString();
                                break;
                            default:
                                break;
                        }
                    },
                    getVal: function (id) {
                        return document.getElementById(id).val;
                    },

                }

                function render(e) {
                    e.param = {
                        arr: [],
                        valarr: [],
                        opts: []
                    };
                    var choosevalue = "", op;

                    for (var i = 0; i < e.length; i++) {
                        op = e.item(i);
                        e.param.opts.push(op);
                        if (op.hasAttribute("choose")) {
                            if (choosevalue == "") {
                                choosevalue = op.value
                            } else {
                                choosevalue += "," + op.value;
                            }
                        }
                    }

                    //创建一个隐藏的option标签用来存储多选的值，其中的值为一个数组
                    var option = document.createElement("option");
                    option.hidden = true;
                    e.appendChild(option);
                    e.removeEventListener("change", selchange);
                    e.addEventListener("change", selchange);

                    //重新定义标签基础属性的get和set方法，实现取值和赋值的功能
                    Object.defineProperty(e, "val", {
                        get: function () {
                            return this.querySelector("[hidden]").value;
                        },
                        set: function (value) {
                            e.param.valarr = [];
                            var valrealarr = value == "" ? [] : value.split(",");
                            e.param.arr = [];
                            e.param.opts.filter(function (o) {
                                o.style = "";
                            });
                            if (valrealarr.toString()) {
                                for (var i = 0; i < valrealarr.length; i++) {
                                    e.param.opts.filter(function (o) {
                                        if (o.value == valrealarr[i]) {
                                            o.style = "background-color: #8c8989;";
                                            e.param.arr.push(o.text);
                                            e.param.valarr.push(o.value)
                                        }
                                    });
                                }
                                this.options[e.length - 1].text = e.param.arr.toString();
                                this.options[e.length - 1].value = e.param.valarr.toString();
                                this.options[e.length - 1].selected = true;
                            } else {
                                this.options[0].selected = true;
                            }

                        },
                        configurable: true
                    })
                    Object.defineProperty(e, "textName", {
                        get: function () {
                            return this.querySelector("[hidden]").innerText;
                        },
                        configurable: true
                    })
                    //添加属性choose 此属性添加到option中用来指定默认值
                    e.val = choosevalue;
                    //添加属性tip 此属性添加到select标签上
                    if (e.hasAttribute("tip") && !e.tiped) {
                        e.tiped = true;
                    }
                }

                function selchange() {
                    var text = this.options[this.selectedIndex].text;
                    var value = this.options[this.selectedIndex].value;
                    this.options[this.selectedIndex].style = "background-color: #8c8989;";
                    var ind = this.param.arr.indexOf(text);
                    if (ind > -1) {
                        this.param.arr.splice(ind, 1);
                        this.param.valarr.splice(ind, 1);
                        this.param.opts.filter(function (o) {
                            if (o.value == value) {
                                o.style = "";
                            }
                        });
                    } else {
                        this.param.arr.push(text);
                        this.param.valarr.push(value);
                    }
                    this.options[this.length - 1].text = this.param.arr.toString();
                    this.options[this.length - 1].value = this.param.valarr.toString();
                    if (this.param.arr.length > 0) {
                        this.options[this.length - 1].selected = true;
                    } else {
                        this.options[0].selected = true;
                    }
                    document.getElementById("[[${textName}]]").value = document.getElementById("[[${id}]]").textName;
                }
            })();
        </script>
        <script th:inline="javascript">
            var data = []
            if ([[${isfirst}]]) {
                data.push({
                    value: [[${firstValue}]],
                    text: [[${firstName}]]
                })
            }
            var dictValue = /*[[${@SDictUtil.getDictList(businessType,dictCode)}]]*/[]
            var showValue = [[${value}]]
            showValue = showValue === null ? "" : showValue;
            let strings = showValue.split(",");
            for (let item of dictValue) {
                let d = {value: item.dictValue, text: item.dictName}
                for (let i = 0; i < strings.length; i++) {
                    if (strings[i] === item.dictValue) {
                        d.choose = true
                        break;
                    }
                }
                data.push(d)
            }
            selectMultip.register();
            //动态渲染
            selectMultip.reload([[${id}]], data);

            $("#" + [[${id}]]).parents("form")[0].addEventListener("reset", () => {
                selectMultip.setVal([[${id}]], '');
            });
            if ([[${isrequired!=null && isrequired}]]) {
                $("#" + [[${id}]]).attr("required", "");
            }
        </script>
    </th:block>
</div>




