<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('待办列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-1 control-label">名称:</label>
                <div class="col-sm-3">
                    <input type="text" class="form-control" id="businessNameLike" name="businessNameLike"
                           placeholder="支持模糊查询"/>
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="col-sm-12 select-table">
        <!--        <div th:if="${extraParameters!=null && extraParameters.get('BATCH_SUBMIT')=='1'}" style="color: red">以下合同可点击“处理”按钮逐个审批，也可全选或部分选中后批量"流转"审批</div>-->
        <table id="bootstrap-table"></table>
    </div>
    <!--    &lt;!&ndash; 批量提交 &ndash;&gt;-->
    <!--    <form id="SubmitBatchForm">-->
    <!--        <div th:if="${extraParameters!=null && extraParameters.get('BATCH_SUBMIT')=='1'}" class="col-sm-12 select-table">-->
    <!--            <th:block th:include="component/wfSubmitBatch:: init(processCode=${processCode}, currentActivity=${activityCode}, formId='SubmitBatchForm', callback=doSubmitBatch)"/>-->
    <!--        </div>-->
    <!--    </form>-->
</div>

<script th:inline="javascript">
    var prefix = ctx + "jsmm/workFlow";
    $(function () {
        var options = {
            url: prefix + "/pageDB",
            queryParams: queryParams,
            modalName: "待办列表",
            field: 'taskId',
            title: '任务ID',
            visible: false,
            columns: [{
                checkbox: true
            },
                {
                    field: 'businessName',
                    title: '名称'
                },
                {
                    field: 'currentActivityName',
                    title: '当前节点'
                },
                {
                    field: 'lastOperatorName',
                    title: '上一步处理人'
                },
                {
                    field: 'lastTime',
                    title: '上一步处理时间',
                    formatter: function (value, row, index) {
                        var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                        return timeData;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        var param = "'" + row.processCode + "','" + row.pageNo + "','"
                            + row.businessId + "','" + row.taskId + "','"
                            + row.currentActivity + "','" + row.processInstanceId + "','"
                            + row.businessType + "','" + row.businessName + "','"
                            + row.businessLabel + "'";
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryDBDetail(' + param + ')"><i class="fa fa-edit"></i>处理</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    })

    function queryParams(params) {
        var search = $.table.queryParams(params);
        search.businessNameLike = $("#businessNameLike").val();
        search.processCode = [[${processCode}]];
        search.flowCode = [[${processCode}]];
        search.currentActivity = [[${activityCode}]];
        search.businessType = 'JSMM';
        search.state = 'open';
        return search;
    }

    function queryDBDetail(processCode, pageNo, businessId, taskId, activityCode, processInstanceId, businessType, businessName, businessLabel) {
        var url;
        if (processCode === "JSMMRD01") {//秘密认定
            if (!pageNo || !pageNo.trim()) {
                pageNo = "technologySecret";
            }
            url = ctx + "jsmm/technologySecret/queryDBDetail/" + pageNo + "/" + businessId + "/" + taskId;
            $.modal.openTab(businessName + "-待办详细", url, true);
        }
        if (processCode === "JSMMBG01") {//秘密变更
            if (!pageNo || !pageNo.trim()) {
                pageNo = "changeApplication";
            }
            url = ctx + "jsmm/changeApplication/queryDBDetail/" + pageNo + "/" + businessId + "/" + taskId;
            $.modal.openTab(businessName + "-待办详细", url, true);
        }
        if (processCode === "MPPS_members_review") {//专家评审
            url = ctxMP + "web/MPPS03?processCode=" + processCode + "&activityCode=" + activityCode + "&pageNo=" + pageNo
                + "&businessGuid=" + businessId + "&processInstanceId=" + processInstanceId + "&taskId=" + taskId;
            //$.modal.openTab(businessName + "-待办详细", url, true);
            window.open(url);
        }


    }
</script>
</body>
</html>