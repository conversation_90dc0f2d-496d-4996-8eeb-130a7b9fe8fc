<!-- 
	按钮相关
输入参数:
	canDoOther 是否能自由流转 默认 false
-->
<div th:fragment="init">
    <th:block th:with="
               	 	canDoOther=${workFlow?.extraParameters?.get('DO_OTHER')==null ? false : true}
               	 	">
        <!-- 审批履历 -->
		<th:block th:include="component/wfCommentList4 :: init(businessId=${workFlow?.businessId},variable=${workFlow?.variable})" />
		<!-- 流程跟踪 -->
		<th:block th:include="component/wfCommentList :: init(businessId=${workFlow?.businessId})" />
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button th:if="${readOnly ne true or detailType eq 'adminEdit'}" type="button" class="btn btn-primary" onclick="save()">
				<i class="fa fa-check"></i>
				暂存
			</button>
			<th:block th:if="${workFlow ne null and readOnly ne true}">
				<!-- 提交 -->
				<th:block th:unless="${workFlow.currentActivity eq 'Manual4' and psStatus ne 2}" th:include="component/wfSubmit2:: init(taskId=${workFlow.taskId}, callback=doSubmit, formId=${formId}, validateUrl=${validateUrl})"/>
				<!-- 退回 -->
				<th:block th:unless="${workFlow.taskType eq 'collaborator' or (psStatus eq 1 and workFlow.currentActivity eq 'Manual4')}" th:include="component/wfReturn2 :: init(taskId=${workFlow.taskId},callback=doReturn)"/>
				<!-- 专家评审 -->
				<button th:unless="${workFlow.currentActivity ne 'Manual4' or psStatus ne 0}" type="button" class="btn btn-primary" onclick="startPS()">
					<i class="fa fa-comments"></i>&nbsp;组织专家评审
				</button>
				<button th:unless="${workFlow.currentActivity ne 'Manual4' or psStatus ne 1}" type="button" class="btn btn-primary" disabled>
					<i class="fa fa-share"></i>&nbsp;专家评审进行中
				</button>
				<!-- 自由流 -->
				<th:block th:include="component/wfDoOther2 :: init(businessId=${workFlow.businessId}, taskId=${workFlow.taskId}, extraParameters=${workFlow.extraParameters} )"/>
				<!-- 按钮操作 -->
				<th:block th:if="${workFlow.taskType ne 'collaborator'}" th:include="component/wfCommonButton :: init(businessId=${workFlow.businessId}, taskId=${workFlow.taskId}, extraParameters=${workFlow.extraParameters} )"/>
				<!-- 会签 -->
				<th:block th:if="${workFlow.taskType ne 'collaborator'}" th:include="component/wfHq :: init(businessId=${workFlow.businessId}, taskId=${workFlow.taskId}, extraParameters=${workFlow.extraParameters} )"/>
			</th:block>
			<button type="button" class="btn btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
	</th:block>
</div>