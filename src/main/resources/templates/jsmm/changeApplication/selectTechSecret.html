<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('科技秘密列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-2 control-label">科技秘密名称:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="technologyNameLike" placeholder="支持模糊查询"/>
                </div>
                <label class="col-sm-2 control-label">科技秘密编号:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="technologyId"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "jsmm/technologySecret";

    $(function () {
        var options = {
            url: prefix + "/list?type=selectTechSecret",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "科技秘密",
            columns: [{
                radio: true
            },
                {
                    field: 'technologyId',
                    title: '科技秘密ID',
                    visible: false
                },
                {
                    field: 'confirmNum',
                    title: '认定号',
                    formatter: function (value, row, index) {
                        if (value == null)
                            return "认定中";
                        else
                            return value;
                    }
                },
                {
                    field: 'technologyName',
                    title: '科技秘密名称'
                },
                {
                    field: 'secretLevel',
                    title: '类型',
                    formatter: function (value, row, index) {
                        if (value == 'core')
                            return '核心科技秘密';
                        else if (value == 'ordinary')
                            return '普通科技秘密';
                        else
                            return '无';
                    }
                },
                {
                    field: 'contactPersonName',
                    title: '提出人'
                },
                {
                    field: 'firstDeptName',
                    title: '第一申报单位'
                },
                {
                    field: 'status',
                    title: '当前状态',
                    formatter: function(value, row, index) {
                        if ($.common.isEmpty(value)) {
                            return '';
                        }
                        if (value == 'draft') {
                            return '<span class="label label-success">草稿</span>';
                        }
                        else if (value == 'ended') {
                            return '<span class="label label-primary">已认定</span>';
                        }
                        else if (value == 'active') {
                            return '<span class="label label-warning">认定中</span>';
                        } else {
                            return '-';
                        }
                    }
                }]
        };
        $.table.init(options);
    });

    function submitHandler(){
        var row=$("#bootstrap-table").bootstrapTable('getSelections');
        parent.selectTechSecretCallback(row[0]);
        $.modal.close();
    }
</script>
</body>
</html>