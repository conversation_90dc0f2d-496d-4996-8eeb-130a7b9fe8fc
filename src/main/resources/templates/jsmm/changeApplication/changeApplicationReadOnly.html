<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('变更申请信息')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-changeApplication-edit" th:object="${changeApplicationVO}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false"
                           class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--保留字段-->
                        <input name="changeId" th:field="*{changeId}" type="hidden">
                        <input name="technologyId" th:field="*{technologyId}" type="hidden">

                        <div class="row ">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">科技秘密原文：</label>
                                    <div class="col-sm-8">
                                        <input id="techSecretOrigin" class="form-control" name="techSecretOrigin" required
                                               th:value="*{techSecret.technologyName}" type="text" readonly>
                                    </div>
                                    <div class="col-sm-2">
                                        <button class="btn btn-primary" onclick="techSecretDetail()" type="button">
                                            查看科技秘密原文
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <br/><br/>

                        <div id="secretDetail">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">科技秘密名称：</label>
                                        <div class="col-sm-10">
                                            <label id="technologyName" class="control-label"> [[*{techSecret.technologyName}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">认定号：</label>
                                        <div class="col-sm-10">
                                            <label id="confirmNum" class="control-label"> [[*{techSecret.confirmNum}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">认定名称：</label>
                                        <div class="col-sm-10">
                                            <label id="confirmName" class="control-label"> [[*{techSecret.technologyName}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">提出人：</label>
                                        <div class="col-sm-8">
                                            <label id="contactPersonName" class="control-label"> [[*{techSecret.contactPersonName}]]</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">联系人电话：</label>
                                        <div class="col-sm-8">
                                            <label id="contactPersonTel" class="control-label"> [[*{techSecret.contactPersonTel}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">提出人部门名称：</label>
                                        <div class="col-sm-10">
                                            <label id="firstDeptName" class="control-label"> [[*{techSecret.firstDeptName}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">原保密期限：</label>
                                        <div class="col-sm-10">
                                            <label id="confidentialityPeriod" class="control-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <br/><br/>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">变更后保密期限：</label>
                                    <div class="col-sm-10">
                                        <label class="control-label" id="changedConfidentialityPeriod"> </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">变更原因：</label>
                                    <div class="col-sm-10">
                                        <textarea name="changeReason" class="form-control" required
                                                  style="width: 100%; height: 150px;"
                                                  th:utext="*{changeReason}" readonly></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">相关材料：</label>
                                    <div class="col-sm-10">
                                        <th:block
                                                th:include="/component/attachment::init(display='none' ,sourceId=*{changeId},sourceModule='JSMM',sourceLabel1='BgFile',id='bgFile',name='attachmentS[bgFile]',labelClass='control-label',see=true)"></th:block>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- 流程相关信息 -->
        <th:block th:include="component/wfWorkFlow2 :: init(workFlow=*{workFlow}, needComment=${needComment eq true})"/>
        <!-- 流程相关信息end -->
    </form>
    <!--按钮区-->
    <th:block th:include="jsmm/wfDetailButton :: init(workFlow=${changeApplicationVO.workFlow},readOnly=${readOnly},formId='form-changeApplication-edit',validateUrl='jsmm/changeApplication/getNextSubmitWF')"/>
    <!--按钮区end-->
</div>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "jsmm/changeApplication";
    var prefixTechSecret = ctx + "jsmm/technologySecret";

    var confidentialityPeriod = [[${changeApplicationVO.techSecret.confidentialityPeriod}]];
    var changedConfidentialityPeriod = [[${changeApplicationVO.changedConfidentialityPeriod}]];
    var index = [[${changeApplicationVO.changedConfidentialityPeriod}]];

    $("#form-changeApplication-edit").validate({
        focusCleanup: true
    });

    $(document).ready(function () {
        if($.common.isEmpty($("#technologyId").val())){
            $("#techSecretOrigin").val(null)
            $("#secretDetail").hide();
        }
        displayConfidentialityPeriod(confidentialityPeriod,"confidentialityPeriod");
        displayConfidentialityPeriod(changedConfidentialityPeriod,"changedConfidentialityPeriod");
    });

    function displayConfidentialityPeriod(value,id){
        if (!$.common.isEmpty(value)){
            switch (value){
                case '1':
                    document.getElementById(id).innerText = "一年";
                    break;
                case '2':
                    document.getElementById(id).innerText = "两年";
                    break;
                case '3':
                    document.getElementById(id).innerText = "三年";
                    break;
                default:
                    break;
            }
        }
    }

    //暂存
    function save(){
        $.operate.saveTab(prefix + "/save", $('#form-changeApplication-edit').serialize());
    }

    function selectTechSecret() {
        var url = prefix + "/selectTechSecret";
        $.modal.open('选择科技秘密',url,'1152','550');
    }

    function selectTechSecretCallback(data){
        $("#secretDetail").show();
        $("#technologyId").val(data.technologyId);
        $("#techSecretOrigin").val(data.technologyName);
        document.getElementById("technologyName").innerText = data.technologyName;
        document.getElementById("confirmNum").innerText = data.confirmNum;
        document.getElementById("confirmName").innerText = data.technologyName;
        document.getElementById("contactPersonName").innerText = data.contactPersonName;
        document.getElementById("contactPersonTel").innerText = data.contactPersonTel;
        document.getElementById("firstDeptName").innerText = data.firstDeptName;
        displayConfidentialityPeriod(data.confidentialityPeriod);
    }

    function techSecretDetail(){
        if($.common.isEmpty($("#technologyId").val())){
            $.modal.msgWarning("请选择科技秘密！");
        }else {
            var url = prefixTechSecret + '/detail/' + $("#technologyId").val();
            $.modal.openTab("科技秘密原文",url);
        }
    }

    function doSubmit(transitionKey) {
        if ($.validate.form()) {
            let tooltipMsg = "确认流转吗？";

            $.modal.confirm(tooltipMsg, function () {
                $.operate.saveTabAlert(prefix + "/doSubmit", $('#form-changeApplication-edit').serialize());
            });
        }
    }

</script>
</body>
</html>