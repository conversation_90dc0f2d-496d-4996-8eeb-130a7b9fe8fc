<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('我的变更申请')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-2 control-label">科技秘密名称:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="technologyNameLike" placeholder="支持模糊查询"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "jsmm/changeApplication";

    $(function () {
        var options = {
            url: prefix + "/list?type=myApplication",
            detailUrl: prefix + "/detail/{id}",
            modalName: "变更申请",
            columns: [{
                checkbox: true
            },
                {
                    field: 'changeId',
                    title: '变更申请编号',
                    visible: false
                },
                {
                    field: 'confirmNum',
                    title: '认定号'
                },
                {
                    field: 'technologyName',
                    title: '名称'
                },
                {
                    field: 'lastTime',
                    title: '最后处理时间',
                    formatter: function (value, row, index) {
                        var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                        return timeData;
                    }
                },
                {
                    field: 'status',
                    title: '当前状态',
                    formatter: function (value, row, index) {
                        if ($.common.isEmpty(value)) {
                            return '';
                        }
                        if (value == 'draft') {
                            return '<span class="label label-success">草稿</span>';
                        } else if (value == 'ended') {
                            return '<span class="label label-primary">已完成</span>';
                        } else if (value == 'active') {
                            return '<span class="label label-warning">处理中</span>';
                        } else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'currentOperator',
                    title: '当前任务人',
                    formatter: function (value, row, index) {
                        if (row.status == 'ended') {
                            return '-';
                        } else {
                            return value;
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.changeId + '\')"><i class="fa fa-info-circle"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>