<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('来源项目列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-2 control-label">项目名称:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="projectName"/>
                </div>
                <label class="col-sm-2 control-label">项目编号:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="projectCode"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "jsmm/technologySecret";

    $(function () {
        var options = {
            url: prefix + "/sourceList",
            modalName: "来源项目",
            pagination: false,
            columns: [{
                radio: true
            },
                {
                    field: 'projectId',
                    title: '科研项目ID',
                    visible: false
                },
                {
                    field: 'projectCode',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'fzrName',
                    title: '负责人'
                },
                {
                    field: 'fzDeptName',
                    title: '负责部门'
                }]
        };
        $.table.init(options);
    });

    function submitHandler(){
        var row=$("#bootstrap-table").bootstrapTable('getSelections');
        parent.selectSourceCallback(row[0]);
        $.modal.close();
    }
</script>
</body>
</html>