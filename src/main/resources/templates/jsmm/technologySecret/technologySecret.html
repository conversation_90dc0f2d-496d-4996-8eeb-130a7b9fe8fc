<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include :: header('申请科技秘密')" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: baseJs" />
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <th:block
            th:with="readOnly=${readOnly!=null&&readOnly?true:false},detailType=${detailType==null?'detail':detailType}" />
        <form class="form-horizontal m" id="form-technologySecret-edit" th:object="${technologySecretVO}">
            <!--保留字段-->
            <input id="contactPersonCode" name="contactPersonCode" th:value="*{contactPersonCode}" type="hidden">
            <!--基本信息-->
            <div aria-multiselectable="true" class="panel-group" id="accordion1" role="tablist">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                                href="#1">基本信息<span class="pull-right"><i aria-hidden="true"
                                        class="fa fa-chevron-down"></i></span></a>
                        </h5>
                    </div>
                    <input id="technologyId" name="technologyId" th:value="*{technologyId}" type="hidden">
                    <!--折叠区域-->
                    <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                        <div class="panel-body">

                            <div class="row ">
                                <div class="form-group col-sm-12">
                                    <div class="col-sm-1"></div>
                                    <div class="col-sm-10">
                                        <p>
                                            <b>关于《签订保密协议》的说明：</b><br>
                                            &emsp;&emsp;为了将科技秘密保密工作从源头抓起，科技秘密管理系统新增了保密措施，即对申报材料进行初步审查的管理员、评审专家如果未与公司签订《保密协议》，应及时补签；在进行科技秘密申报时，提出人必须明确告知其是否已签订过《保密协议》，如没有签订过，提出人将申报材料提交给管理员，由管理员将所申报的科技秘密内容请有关评审专家评审，评审后认定为科技秘密的，应向部门有关领导进行说明，与该提出人签订《保密协议》，如评审专家认为所申报内容属于公知公用的现有技术，可以不与提出人签订《保密协议》。
                                            <br>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <br />
                            <div class="row ">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">科技秘密名称：</label>
                                        <div class="col-sm-10">
                                            <input class="form-control" name="technologyName" required
                                                th:value="*{technologyName}" type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row ">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">类型：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                th:include="/component/radio :: init(id='secretLevel', name='secretLevel',businessType='JSMM', value=*{secretLevel},dictCode='JSMM_SECRET_LEVEL' ,isrequired=true,callback='secretCallback')">
                                            </th:block>
                                            <br><label id="secretLabel" style="color: red;display: none"
                                                hidden="hidden">提示：泄露会使公司的经济利益遭受严重损害的确定为核心科技秘密，一般指在国际上首发的产品制造技术、在国际上具有领先优势的技术。提出人不得将比较核心的技术方案写在核心科技秘密的摘要中。认定为核心科技秘密则全文将被隐藏。</label>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row" th:if="*{not #strings.isEmpty(confirmNum)}">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label ">认定号：</label>
                                        <div class="col-sm-8">
                                            <label class="control-label"> [[*{confirmNum}]]</label>
                                            <input class="form-control" name="confirmNum" th:value="*{confirmNum}"
                                                type="hidden">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label ">认定日期：</label>
                                        <div class="col-sm-8">
                                            <label class="control-label"> [[*{confirmDate}]]</label>
                                            <input class="form-control" name="confirmDate" th:value="*{confirmDate}"
                                                type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row ">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">联系人：</label>
                                        <div class="col-sm-8">
                                            <label class="control-label"> [[*{contactPersonName}]]</label>
                                            <input class="form-control" name="contactPersonName" required
                                                th:value="*{contactPersonName}" type="hidden">
                                            <input class="form-control" name="contactPersonGh" required
                                                th:value="*{contactPersonCode}" type="hidden">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">联系人Email：</label>
                                        <div class="col-sm-8">
                                            <input class="form-control" name="contactPersonEmail"
                                                th:value="*{contactPersonEmail}" type="text" required>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row ">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">联系人手机：</label>
                                        <div class="col-sm-8">
                                            <input class="form-control" name="contactPersonPhone"
                                                th:value="*{contactPersonPhone}" type="text" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">联系人电话：</label>
                                        <div class="col-sm-8">
                                            <input class="form-control" name="contactPersonTel"
                                                th:value="*{contactPersonTel}" type="text" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row ">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">第一申报单位：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                th:include="/component/selectOrg::init(orgCodeId='firstDeptCode',orgNameId='firstDeptName' ,value=*{firstDeptCode} ,selectType='S')" />
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row ">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">权属：</label>
                                        <div class="col-sm-8">
                                            <label class="control-label"
                                                id="ownershipNumLabel">[[*{ownershipNum}]]</label>
                                            <input class="form-control" id="ownershipNum" name="ownershipNum"
                                                th:value="*{ownershipNum}" type="hidden">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">提出日期：</label>
                                        <div class="col-sm-8">
                                            <label class="control-label"> [[*{authDate}]]</label>
                                            <input class="form-control" id="authDate" name="authDate"
                                                th:value="*{authDate}" type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row ">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">来源：</label>
                                        <div class="col-sm-10"
                                            th:include="/component/radio :: init(id='authSource', name='authSource',businessType='JSMM', dictCode='JSMM_SOURCE_TYPE',multimultiple=false ,value=*{authSource},callback='sourceCallback')">
                                        </div>
                                        <input class="form-control" id="authSourceValue" name="authSourceValue" required
                                            th:value="*{authSource}" type="hidden">
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="source">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">来源项目：</label>
                                        <div class="col-sm-10">
                                            <input class="form-control" id="sourceCode" name="sourceCode" required
                                                th:value="*{sourceCode}" type="hidden">
                                            <input name="sourceName" id="sourceName" class="form-control"
                                                th:value="*{sourceName}" placeholder="点击选择来源项目" onclick="selectSource()"
                                                required readonly />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="sameSourcePatent">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">同来源专利：</label>
                                        <div class="col-sm-10">
                                            <div th:insert="/jsmm/techSecretComponent/sameSourcePatentList :: init">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="sameSourceSecret">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">同来源科技秘密：</label>
                                        <div class="col-sm-10">
                                            <div th:insert="/jsmm/techSecretComponent/sameSourceSecretList :: init">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row" id="otherSource">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">其他来源情况说明：</label>
                                        <div class="col-sm-10">
                                            <input class="form-control" name="otherSource" th:value="*{otherSource}"
                                                type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">应用方式：</label>
                                        <div class="col-sm-10"
                                            th:include="/component/radio :: init(id='applicationWay', name='applicationWay',businessType='JSMM',value=*{applicationWay},dictCode='JSMM_APPLICATION_WAY',callback='applicationWayCallback' ,isrequired=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="applicationDept">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">应用单位：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                th:include="/component/selectOrg::init(orgCodeId='applicationDeptCode', orgNameId='applicationDeptName', selectType='M',value=*{applicationDeptCode})" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="isAvailable">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">初始应用日：</label>
                                        <div class="col-sm-10"
                                            th:include="/component/date :: init(id='useDate',name='useDate',strValue=*{useDate})">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="notAvailable">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">未应用原因：</label>
                                        <div class="col-sm-10"
                                            th:include="/component/radio :: init(id='unusedReason',value=*{unusedReason}, name='unusedReason',businessType='JSMM', dictCode='JSMM_UNUSED_REASON')">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">标签：</label>
                                        <div class="col-sm-10"
                                            th:include="/component/checkbox :: init(id='techLabel', name='techLabel',businessType='JSMM',value=*{techLabel},  dictCode='JSMM_TECH_LABEL',multimultiple=false,isrequired=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">技术领域：</label>
                                        <input type="hidden" id="technologyFieldId" name="technologyFieldId"
                                            th:value="*{technologyFieldId}" />
                                        <div class="col-sm-10">
                                            <input name="technologyFieldName" id="technologyFieldName"
                                                class="form-control" th:value="*{technologyFieldName}"
                                                placeholder="点击选择技术领域" onclick="choiceTechArea()" required readonly />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">预计效果：</label>
                                        <div class="col-sm-10"
                                            th:include="/component/checkbox :: init(id='expectedResult', name='expectedResult',businessType='JSMM',value=*{expectedResult}, dictCode='JSMM_EXPECTED_RESULT',isrequired=true )">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">保密期限：</label>
                                        <div class="col-sm-10">
                                            <select id="confidentialityPeriod" name="confidentialityPeriod"
                                                class="form-control" required>
                                                <option value="">请选择</option>
                                                <option value="1" th:selected="*{confidentialityPeriod eq '1'}">一年
                                                </option>
                                                <option value="2" th:selected="*{confidentialityPeriod eq '2'}">两年
                                                </option>
                                                <option value="3" th:selected="*{confidentialityPeriod eq '3'}">三年
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!--权利人单位-->
            <div aria-multiselectable="true" class="panel-group" id="accordion2" role="tablist">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                                href="#2">科技秘密权利人单位<span class="pull-right"><i aria-hidden="true"
                                        class="fa fa-chevron-down"></i></span></a>
                        </h5>
                    </div>

                    <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                        <div class="panel-body">
                            <div
                                th:insert="/jsmm/techSecretComponent/techOwnerList :: initOwner(technologyId=*{technologyId})">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--提出人信息-->
            <div aria-multiselectable="true" class="panel-group" id="accordion3" role="tablist">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                                href="#4">提出人信息<span class="pull-right"><i aria-hidden="true"
                                        class="fa fa-chevron-down"></i></span></a>
                        </h5>
                    </div>

                    <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                        <div class="panel-body">
                            <div
                                th:insert="/jsmm/techSecretComponent/techMemberList :: initMember(technologyId=*{technologyId})">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div aria-multiselectable="true" class="panel-group" id="accordion4" role="tablist">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                                id="attAndSummary" name="attAndSummary" href="#5">科技秘密摘要及附件<span class="pull-right"><i
                                        aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
                        </h5>
                    </div>
                    <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                        <div class="panel-body">

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">摘要：</label>
                                        <div class="col-sm-10">
                                            <textarea name="abstractContent" class="form-control" required
                                                style="width: 100%; height: 150px;"
                                                th:utext="*{abstractContent}"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">相关附件：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='JSMM',sourceLabel1='RdFile',id='rdFile',name='attachmentS[rdFile]',labelClass='control-label',isrequired=true)">
                                            </th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label"
                                            id="propertyRightsNotRequired">知识产权协议附件：</label>
                                        <label class="col-sm-2 control-label is-required"
                                            id="propertyRightsRequired">知识产权协议附件：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='JSMM',sourceLabel1='PropertyRights',id='propertyRights',name='attachmentS[propertyRights]',labelClass='control-label')">
                                            </th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">声明：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                th:include="/component/checkbox :: init(id='declare', name='declare', value=1,businessType='JSMM', dictCode='JSMM_CHECKED',isrequired=true )">
                                            </th:block>
                                            <br>
                                            <span style="color: red;">其他提出人已确认第一提出人填写的申报内容及贡献系数分配方案</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--管理员评审表-->
            <div aria-multiselectable="true" class="panel-group" id="accordion5" role="tablist"
                th:if="${adminReview eq true}">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                                href="#4">管理员评审表<span class="pull-right"><i aria-hidden="true"
                                        class="fa fa-chevron-down"></i></span></a>
                        </h5>
                    </div>

                    <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                        <div class="panel-body">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">评审结果：</label>
                                    <div class="col-sm-10"
                                        th:include="/component/radio :: init(id='adminReviewResult', name='adminReviewResult',businessType='JSMM',value=*{adminReviewResult},dictCode='JSMM_ADMIN_REVIEW',isrequired=true)">
                                    </div>
                                    <label class="col-sm-2 control-label is-required">意见：</label>
                                    <div class="col-sm-10">
                                        <textarea name="reviewOpinion" class="form-control" required
                                            style="width: 100%; height: 150px;" th:utext="*{reviewOpinion}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 专家评审信息 -->
            <th:block th:unless="${technologySecretVO.workFlow.currentActivity ne 'Manual4' or psStatus ne 2}"
                th:include="/component/expertReview :: init(bizId=*{technologyId},moduleCode='jsmm_rdps')"></th:block>
            <!-- 专家评审信息end -->

            <!--管理员评审结果-->
            <div aria-multiselectable="true" class="panel-group" id="accordion6" role="tablist"
                th:if="${technologySecretVO.workFlow.currentActivity eq 'Manual4' and psStatus eq 2}">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                                href="#4">评审结果<span class="pull-right"><i aria-hidden="true"
                                        class="fa fa-chevron-down"></i></span></a>
                        </h5>
                    </div>

                    <div aria-expanded="false" class="panel-collapse collapse in" id="7">
                        <div class="panel-body">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">评审结果：</label>
                                    <div class="col-sm-10"
                                        th:include="/component/radio :: init(id='adminReviewDecision', name='adminReviewDecision',businessType='JSMM',dictCode='JSMM_ADMIN_REVIEW_DECISION',isrequired=true)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 流程相关信息 -->
            <th:block
                th:include="component/wfWorkFlow2 :: init(workFlow=*{workFlow}, needComment=${needComment eq true})" />
            <!-- 流程相关信息end -->
        </form>

        <!--按钮区-->
        <th:block
            th:include="jsmm/wfDetailButton2 :: init(workFlow=${technologySecretVO.workFlow},readOnly=${readOnly},psStatus=${psStatus},formId='form-technologySecret-edit',validateUrl='jsmm/technologySecret/getNextSubmitWF')" />
        <!--按钮区-->
    </div>

    <script th:inline="javascript">
        var prefixTech = ctx + "jsmm/technologySecret";

        $("#form-technologySecret-edit").validate({
            focusCleanup: true
        });

        var technologyId = [[${ technologySecretVO.technologyId }]];
        var index = [[${ technologySecretVO.confidentialityPeriod }]];

        //初始化
        $(document).ready(function () {
            $("#isAvailable").hide();
            $("#notAvailable").hide();
            $("#otherSource").hide();
            $("#source").hide();
            $("#sameSourcePatent").hide();
            $("#sameSourceSecret").hide();
            $("#applicationDept").hide();
            $("#secretLabel").hide();

            applicationWayCallback([[${ technologySecretVO.applicationWay }]]);
            sourceCallback([[${ technologySecretVO.authSource }]]);
            secretCallback([[${ technologySecretVO.secretLevel }]]);
            attachmentRequired([[${ technologySecretVO.ownershipNum }]]);

            // 修改流转按钮文字为提交
            setTimeout(function () {
                $('button[title="流转"]').each(function () {
                    $(this).html('<i class="fa fa-share"></i>&nbsp;提交');
                    $(this).attr('title', '提交');
                });
            }, 100);
        });

        function secretCallback(a) {
            if ($.common.isEmpty(a)) {
                return;
            }
            if (a == "core") {
                $("#secretLabel").show();
            } else {
                $("#secretLabel").hide();
            }
        }

        function applicationWayCallback(type) {
            if ($.common.isEmpty(type)) {
                return;
            }
            if (type == 'notApplied') {
                $("#isAvailable").hide();
                $("#notAvailable").show();
                $("#applicationDept").hide();
            } else if (type == 'internalApplied') {
                $("#notAvailable").hide();
                $("#isAvailable").show();
                $("#applicationDept").show();
            }
        }

        function sourceCallback(type) {
            if ($.common.isEmpty(type)) {
                return;
            }
            if (type == 'noSource') {
                $("#source").hide();
                $("#sameSourcePatent").hide();
                $("#sameSourceSecret").hide();
                $("#otherSource").hide();
                // 取消来源项目必传要求
                $("#sourceName").removeAttr("required");
                $("#sourceCode").removeAttr("required");
            } else if (type == 'otherSource') {
                $("#source").hide();
                $("#sameSourcePatent").hide();
                $("#sameSourceSecret").hide();
                $("#otherSource").show();
                // 取消来源项目必传要求
                $("#sourceName").removeAttr("required");
                $("#sourceCode").removeAttr("required");
            } else {
                if (type != $("input[name='authSource']:checked").val()) {
                    $("#sourceCode").val("");
                    $("#sourceName").val("");
                }
                $("#otherSource").hide();
                $("#source").show();
                // 恢复来源项目必传要求
                $("#sourceName").attr("required", "required");
                $("#sourceCode").attr("required", "required");
                getSource();
            }
            $("#authSourceValue").val(type);
        }

        //选择来源项目
        function selectSource() {
            var url = prefixTech + '/selectSource';
            $.modal.open('选择来源项目', url, '1152', '550');
        }

        function selectSourceCallback(data) {
            $("#sourceCode").val(data.projectCode);
            $("#sourceName").val(data.projectName);
            getSource();
        }

        function getSource() {
            var sourceCode = $("#sourceCode").val();
            var config = {
                url: prefixTech + '/getSource?sourceCode=' + sourceCode + '&technologyId=' + technologyId,
                type: "get",
                dataType: "json",
                success: function (result) {
                    var secretList = result.data.secretList;
                    var patentList = result.data.patentList;

                    if (secretList != null && secretList.length != 0) {
                        $('#sameSourceSecretTable').bootstrapTable('load', secretList);
                        $('#sameSourceSecret').show();
                    } else {
                        $('#sameSourceSecret').hide();
                    }

                    if (patentList != null && secretList.length != 0) {
                        $('#sameSourcePatentTable').bootstrapTable('load', patentList);
                        $('#sameSourcePatent').show();
                    } else {
                        $('#sameSourcePatent').hide();
                    }
                }
            };
            $.ajax(config);
        }

        //技术领域
        function choiceTechArea() {
            var url = prefixTech + "/selectTechAreaTree?selectType=M";
            url += "&values=" + $("#technologyFieldId").val();
            var options = {
                title: '选择',
                width: "380",
                height: '550',
                url: url,
                callBack: choiceTechAreaCallback
            };
            $.modal.openOptions(options);
        }

        function choiceTechAreaCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#technologyFieldId").val(body.find('#treeId').val());
            $("#technologyFieldName").val(body.find('#treeName').val());
            layer.close(index);
        }

        // 暂存
        function save() {
            var config = {
                url: prefixTech + "/save",
                type: "post",
                dataType: "json",
                data: $('#form-technologySecret-edit').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        saveOwner(technologyId);
                        saveMember(technologyId);
                        $.modal.alertSuccess(result.msg)
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                }
            };
            $.ajax(config)
        }

        function doSubmit(transitionKey) {
            if ($.validate.form()) {
                let tooltipMsg = "确认流转吗？";
                let wfTooltipMsg = [[${ contractVO?.workFlow?.extraParameters?.get('TOOLTIP_SUBMIT') }]];
                if (wfTooltipMsg) {
                    tooltipMsg = wfTooltipMsg;
                }

                $.modal.confirm(tooltipMsg, function () {
                    if ($("#ownershipNum").val() > 1 && $("#propertyRights").val() == "") {
                        $.modal.alertWarning("权属大于2时必须上传知识产权附件！");
                    } else {
                        saveOwner(technologyId);
                        saveMember(technologyId);
                        $.operate.saveTabAlert(prefixTech + "/doSubmit", $('#form-technologySecret-edit').serialize());
                    }
                });
            }
        }

        function doReturn(returnActivityKey) {
            let tooltipMsg = "确认退回吗？";
            let wfTooltipMsg = [[${ contractVO?.workFlow?.extraParameters?.get('TOOLTIP_RETURN') }]];
            if (wfTooltipMsg) {
                tooltipMsg = wfTooltipMsg;
            }
            $.modal.confirm(tooltipMsg, function () {
                $('#workFlow_userLabelM').val('');
                $.operate.saveTabAlert(prefixTech + "/doReturn", $('#form-technologySecret-edit').serialize());
            });
        }

        //组织专家评审
        function startPS() {
            if ($.validate.form()) {
                $.modal.confirm("确定启动评审吗？", function () {
                    save();
                    $.post(prefixTech + "/getPSInfo", {
                        "technologyId": technologyId,
                        "moduleCode": "jsmm_rdps"
                    }, function (data) {
                        var url = ctxMP + "web/MPPS01?bizGuid=" + technologyId + "&moduleCode=jsmm_rdps&approveKind=MPPS_members_review&jbxx=" + data;
                        $.modal.openTab("启动专家评审", url);
                    });
                });
            }
        }

        //权属显示
        function setOwnerShipNum(value) {
            $("#ownershipNum").val(value);
            document.getElementById("ownershipNumLabel").innerText = value;
            attachmentRequired(value);
        }

        //权属决定附件是否必须上传
        function attachmentRequired(ownerShipNum) {
            if (ownerShipNum > 1) {
                $("#propertyRightsRequired").show();
                $("#propertyRightsNotRequired").hide();
            } else {
                $("#propertyRightsRequired").hide();
                $("#propertyRightsNotRequired").show();
            }
        }

    </script>
</body>

</html>