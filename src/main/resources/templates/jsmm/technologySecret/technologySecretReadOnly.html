<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('申请科技秘密')"/>
    <th:block th:include="include :: datetimepicker-js"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <th:block th:with="readOnly=${readOnly!=null&&readOnly?true:false},detailType=${detailType==null?'detail':detailType}" />
    <form class="form-horizontal m" id="form-technologySecret-edit" th:object="${technologySecretVO}">
        <!--基本信息-->
        <div aria-multiselectable="true" class="panel-group" id="accordion1" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" href="#1">基本信息<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>
                <input id="technologyId" name="technologyId" th:value="*{technologyId}" type="hidden">
                <input id="sourceCode" name="sourceCode" th:value="*{sourceCode}" type="hidden">
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <div class="col-sm-1"></div>
                                <div class="col-sm-10">
                                    <p>
                                        <b>关于《签订保密协议》的说明：</b><br>
                                        &emsp;&emsp;为了将科技秘密保密工作从源头抓起，科技秘密管理系统新增了保密措施，即对申报材料进行初步审查的管理员、评审专家如果未与公司签订《保密协议》，应及时补签；在进行科技秘密申报时，提出人必须明确告知其是否已签订过《保密协议》，如没有签订过，提出人将申报材料提交给管理员，由管理员将所申报的科技秘密内容请有关评审专家评审，评审后认定为科技秘密的，应向部门有关领导进行说明，与该提出人签订《保密协议》，如评审专家认为所申报内容属于公知公用的现有技术，可以不与提出人签订《保密协议》。
                                        <br>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <hr>
                        <br/>

                        <div class="row ">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">科技秘密名称：</label>
                                    <div class="col-sm-10">
                                        <label class="control-label"> [[*{technologyName}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">类型：</label>
                                    <div class="col-sm-10">
                                        <th:block
                                                th:include="/component/radio :: init(id='secretLevel', name='secretLevel',businessType='JSMM', value=*{secretLevel},dictCode='JSMM_SECRET_LEVEL' ,isrequired=true,callback='secretCallback',see=true)"></th:block>
                                        <label id="secretLabel" style="color: red;display: none">提示：泄露会使公司的经济利益遭受严重损害的确定为核心科技秘密，一般指在国际上首发的产品制造技术、在国际上具有领先优势的技术。提出人不得将比较核心的技术方案写在核心科技秘密的摘要中。认定为核心科技秘密则全文将被隐藏。</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row" th:if="*{not #strings.isEmpty(confirmNum)}">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label ">认定号：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{confirmNum}]]</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label ">认定日期：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{confirmDate}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系人：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{contactPersonName}]]</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系人Email：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{contactPersonEmail}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系人手机：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{contactPersonPhone}]]</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系人电话：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{contactPersonTel}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">第一申报单位：</label>
                                    <div class="col-sm-10">
                                        <th:block
                                                th:include="/component/selectOrg::init(orgCodeId='firstDeptCode',orgNameId='firstDeptName' ,value=*{firstDeptCode} ,selectType='S',see=true)"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">权属：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label" id="ownershipNumLabel"
                                               th:utext="*{ownershipNum}"></label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">提出日期：</label>
                                    <div class="col-sm-8">
                                        <label class="control-label"> [[*{authDate}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row " th:if="*{authSource ne 'noSource'}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">来源：</label>
                                    <div class="col-sm-10"
                                         th:include="/component/radio :: init(id='authSource', name='authSource',businessType='JSMM', dictCode='JSMM_SOURCE_TYPE',multimultiple=false ,value=*{authSource},callback='sourceCallback',see=true)"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row " th:if="${technologySecretVO.authSource eq 'noSource' and detailType eq 'adminEdit'}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">来源：</label>
                                    <div class="col-sm-10"
                                         th:include="/component/radio :: init(id='authSource', name='authSource',businessType='JSMM', dictCode='JSMM_SOURCE_TYPE',multimultiple=false ,value=*{authSource},callback='sourceCallback')"></div>
                                </div>
                            </div>
                        </div>

                        <div th:if="*{authSource ne 'noSource'}">
                            <div class="row" id="source">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">来源项目：</label>
                                        <div class="col-sm-10">
                                            <label class="control-label"> [[*{sourceName}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="otherSource">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">其他来源情况说明：</label>
                                        <div class="col-sm-10">
                                            <label class="control-label"> [[*{otherSource}]]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div th:if="${technologySecretVO.authSource eq 'noSource' and detailType eq 'adminEdit'}">
                            <div class="row" id="source">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">来源项目：</label>
                                        <div class="col-sm-10">
                                            <input name="sourceName" id="sourceName" class="form-control"
                                                   th:value="*{sourceName}" placeholder="点击选择来源项目"
                                                   onclick="selectSource()" required readonly/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="otherSource">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">其他来源情况说明：</label>
                                        <div class="col-sm-10">
                                            <input class="form-control" name="otherSource" th:value="*{otherSource}"
                                                   type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="sameSourcePatent">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label ">同来源专利：</label>
                                    <div class="col-sm-10">
                                        <div th:insert="/jsmm/techSecretComponent/sameSourcePatentList :: init"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="sameSourceSecret">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label ">同来源科技秘密：</label>
                                    <div class="col-sm-10">
                                        <div th:insert="/jsmm/techSecretComponent/sameSourceSecretList :: init"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">应用方式：</label>
                                    <div class="col-sm-10"
                                         th:include="/component/radio :: init(id='applicationWay', name='applicationWay',businessType='JSMM',value=*{applicationWay},dictCode='JSMM_APPLICATION_WAY',callback='applicationWayCallback' ,isrequired=true,see=true)"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="applicationDept">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">应用单位：</label>
                                    <div class="col-sm-10">
                                        <th:block
                                                th:include="/component/selectOrg::init(orgCodeId='applicationDeptCode', orgNameId='applicationDeptName', selectType='M',value=*{applicationDeptCode},see=true)"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="isAvailable">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">初始应用日：</label>
                                    <div class="col-sm-10"
                                         th:include="/component/date :: init(id='useDate',name='useDate',strValue=*{useDate},see=true)"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="notAvailable">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">未应用原因：</label>
                                    <div class="col-sm-10"
                                         th:include="/component/radio :: init(id='unusedReason',value=*{unusedReason}, name='unusedReason',businessType='JSMM', dictCode='JSMM_UNUSED_REASON',see=true)"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">标签：</label>
                                    <div class="col-sm-10">
                                        <label id="techLabel" class="control-label"> [[*{techLabel}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">技术领域：</label>
                                    <div class="col-sm-10">
                                        <label class="control-label"> [[*{technologyFieldName}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">预计效果：</label>
                                    <div class="col-sm-10">
                                        <label id="expectedResult" class="control-label"> [[*{expectedResult}]]</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">保密期限：</label>
                                    <div class="col-sm-10">
                                        <label class="control-label" id="confidentialityPeriod"> </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!--权利人单位-->
        <div aria-multiselectable="true" class="panel-group" id="accordion2" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" href="#2">科技秘密权利人单位<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>

                <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                    <div class="panel-body">
                        <div th:insert="/jsmm/techSecretComponent/techOwnerListReadOnly :: initOwner(technologyId=*{technologyId})"></div>
                    </div>
                </div>
            </div>
        </div>

        <!--提出人信息-->
        <div aria-multiselectable="true" class="panel-group" id="accordion3" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" href="#4">提出人信息<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>

                <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                    <div class="panel-body">
                        <div th:insert="/jsmm/techSecretComponent/techMemberListReadOnly :: initMember(technologyId=*{technologyId})"></div>
                    </div>
                </div>
            </div>
        </div>

        <div aria-multiselectable="true" class="panel-group" id="accordion4" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" id="attAndSummary"
                           name="attAndSummary" href="#5">科技秘密摘要及附件<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>
                <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                    <div class="panel-body">

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">摘要：</label>
                                    <div class="col-sm-10">
                                    <textarea name="abstractContent" class="form-control" required
                                              style="width: 100%; height: 150px;"
                                              th:utext="*{abstractContent}" readonly></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div th:if="${detailType ne 'adminEdit'}">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">相关附件：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                    th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='JSMM',sourceLabel1='RdFile',id='rdFile',name='attachmentS[rdFile]',labelClass='control-label',isrequired='true',see=true)"></th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">知识产权协议附件：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                    th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='JSMM',sourceLabel1='PropertyRights',id='propertyRights',name='attachmentS[propertyRights]',labelClass='control-label',see=true)"></th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div th:if="${detailType eq 'adminEdit'}">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">相关附件：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                    th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='JSMM',sourceLabel1='RdFile',id='rdFile',name='attachmentS[rdFile]',labelClass='control-label',isrequired=true)"></th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">知识产权协议附件：</label>
                                        <div class="col-sm-10">
                                            <th:block
                                                    th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='JSMM',sourceLabel1='PropertyRights',id='propertyRights',name='attachmentS[propertyRights]',labelClass='control-label')"></th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流程相关信息 -->
        <th:block th:include="component/wfWorkFlow2 :: init(workFlow=*{workFlow}, needComment=${needComment eq true})"/>
        <!-- 流程相关信息end -->

    </form>

    <!--按钮区-->
    <th:block
            th:include="jsmm/wfDetailButton2 :: init(workFlow=${technologySecretVO.workFlow},readOnly=${readOnly},detailType=${detailType},formId='form-technologySecret-edit',validateUrl='jsmm/technologySecret/getNextSubmitWF')"/>
    <!--按钮区-->
</div>

<script th:inline="javascript">
    var prefixTech = ctx + "jsmm/technologySecret";

    $("#form-technologySecret-edit").validate({
        focusCleanup: true
    });

    var technologyId = [[${technologySecretVO.technologyId}]];
    var index = [[${technologySecretVO.confidentialityPeriod}]];
    var techLabel = [[${technologySecretVO.techLabel}]];
    var expectedResult = [[${technologySecretVO.expectedResult}]];

    //初始化
    $(document).ready(function () {
        $("#isAvailable").hide();
        $("#notAvailable").hide();
        $("#otherSource").hide();
        $("#source").hide();
        $("#applicationDept").hide();
        $("#secretLabel").hide();
        document.getElementById("techLabel").innerText = techLabel.replace(',','，');
        document.getElementById("expectedResult").innerText = expectedResult.replace(',','，');

        applicationWayCallback([[${technologySecretVO.applicationWay}]]);
        sourceCallback([[${technologySecretVO.authSource}]]);
        secretCallback([[${technologySecretVO.secretLevel}]]);
        displayConfidentialityPeriod(index);
    });

    function displayConfidentialityPeriod(value) {
        if (!$.common.isEmpty(value)) {
            switch (value) {
                case '1':
                    document.getElementById("confidentialityPeriod").innerText = "一年";
                    break;
                case '2':
                    document.getElementById("confidentialityPeriod").innerText = "两年";
                    break;
                case '3':
                    document.getElementById("confidentialityPeriod").innerText = "三年";
                    break;
                default:
                    break;
            }
        }
    }

    function sourceCallback(type) {
        if ($.common.isEmpty(type)) {
            return;
        }
        if (type == 'noSource') {
            $("#source").hide();
            $("#sameSourcePatent").hide();
            $("#sameSourceSecret").hide();
            $("#otherSource").hide();
        } else if (type == 'otherSource') {
            $("#source").hide();
            $("#sameSourcePatent").hide();
            $("#sameSourceSecret").hide();
            $("#otherSource").show();
        } else {
            if (type != $("input[name='authSource']:checked").val()) {
                $("#sourceCode").val("");
                $("#sourceName").val("");
            }
            $("#otherSource").hide();
            $("#source").show();
            getSource();
        }
        $("#authSourceValue").val(type);
    }

    //选择来源项目
    function selectSource() {
        var url = prefixTech + '/selectSource';
        $.modal.open('选择来源项目',url,'1152','550');
    }

    function selectSourceCallback(data){
        $("#sourceCode").val(data.projectCode);
        $("#sourceName").val(data.projectName);
        getSource();
    }

    function getSource(){
        var sourceCode = [[${technologySecretVO.sourceCode}]];
        var config = {
            url: prefixTech + '/getSource?sourceCode=' + sourceCode + '&technologyId=' + technologyId,
            type: "get",
            dataType: "json",
            success: function (result){
                var secretList = result.data.secretList;
                var patentList = result.data.patentList;

                if(secretList != null && secretList.length != 0){
                    $('#sameSourceSecretTable').bootstrapTable('load', secretList);
                    $('#sameSourceSecret').show();
                }else{
                    $('#sameSourceSecret').hide();
                }

                if(patentList != null && secretList.length != 0){
                    $('#sameSourcePatentTable').bootstrapTable('load', patentList);
                    $('#sameSourcePatent').show();
                }else{
                    $('#sameSourcePatent').hide();
                }
            }
        };
        $.ajax(config);
    }

    function secretCallback(a) {
        if ($.common.isEmpty(a)) {
            return;
        }
        if (a == "core") {
            $("#secretLabel").show();
        } else {
            $("#secretLabel").hide();
        }
    }

    function applicationWayCallback(type) {
        if ($.common.isEmpty(type)) {
            return;
        }
        if (type == 'notApplied') {
            $("#isAvailable").hide();
            $("#notAvailable").show();
            $("#applicationDept").hide();
        } else if (type == 'internalApplied') {
            $("#notAvailable").hide();
            $("#isAvailable").show();
            $("#applicationDept").show();
        }
    }

    function doSubmit(transitionKey) {
        if ($.validate.form()) {
            let tooltipMsg = "确认流转吗？";
            let wfTooltipMsg = [[${contractVO?.workFlow?.extraParameters?.get('TOOLTIP_SUBMIT')}]];
            if (wfTooltipMsg) {
                tooltipMsg = wfTooltipMsg;
            }

            $.modal.confirm(tooltipMsg, function () {
                $.operate.saveTabAlert(prefixTech + "/doSubmit", $('#form-technologySecret-edit').serialize());
            });
        }
    }

    // 暂存
    function save() {
        var config = {
            url: prefixTech + "/save",
            type: "post",
            dataType: "json",
            data: $('#form-technologySecret-edit').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $.modal.alertSuccess(result.msg)
                }else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
                $.modal.enable();
            }
        };
        $.ajax(config)
    }
</script>
</body>
</html>