<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('科技秘密列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-2 control-label">科技秘密名称:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="technologyNameLike" placeholder="支持模糊查询"/>
                </div>
                <label class="col-sm-2 control-label">类型:</label>
                <div class="col-sm-4">
                    <select id="secretLevel" name="secretLevel" class="form-control">
                        <option value="">请选择</option>
                        <option value="core">核心科技秘密</option>
                        <option value="ordinary">普通科技秘密</option>
                        <option value="none">无</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">认定号:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="confirmNum"/>
                </div>
                <label class="col-sm-2 control-label">认定日期:</label>
                <div class="col-sm-4">
                    <div class="input-daterange input-group">
                        <th:block th:include="/component/date::init(name='confirmDateFrom',id='confirmDateFrom',class='input-sm')"/>
                        <span class="input-group-addon">到</span>
                        <th:block th:include="/component/date::init(name='confirmDateTo',id='confirmDateTo',class='input-sm')"/>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">联系人:</label>
                <div class="col-sm-4">
                    <th:block th:include="/component/selectUser :: init(userCodeId='contactPersonCode',userNameId='contactPersonName')"></th:block>
                </div>
                <label class="col-sm-2 control-label">第一申报单位:</label>
                <div class="col-sm-4">
                    <th:block th:include="/component/selectOrg::init(orgCodeId='firstDeptCode', orgNameId='firstDeptName')"/>

                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">来源类别:</label>
                <div class="col-sm-4">
                    <th:block th:include="component/selectDict :: init(id='authSource',name='authSource',businessType='JSMM',dictCode='JSMM_SOURCE_TYPE',isfirst=true)"></th:block>
                </div>
                <label class="col-sm-2 control-label">来源编号:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="sourceCode"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">状态:</label>
                <div class="col-sm-4">
                    <select id="status" name="status" class="form-control">
                        <option value="">请选择</option>
                        <option value="draft">草稿</option>
                        <option value="active">认定中</option>
                        <option value="ended">已认定</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label">技术领域:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="technologyFieldId" placeholder="支持模糊查询"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">标签:</label>
                <div class="col-sm-4">
                    <th:block th:include="component/selectDict :: init(id='techLabelLike',name='techLabelLike',businessType='JSMM',dictCode='JSMM_TECH_LABEL',multimultiple=true)"></th:block>
                </div>
                <label class="col-sm-2 control-label">预计效果:</label>
                <div class="col-sm-4">
                    <th:block th:include="component/selectDict :: init(id='expectedResultLike',name='expectedResultLike',businessType='JSMM',dictCode='JSMM_EXPECTED_RESULT',multimultiple=true)"></th:block>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "jsmm/technologySecret";

    var sourceDict = [[${@SDictUtil.getDictList("JSMM","JSMM_SOURCE_TYPE")}]];
    var isZHAdmin = [[${@JSMMRoleUtil.isZHAdmin()}]];

    $(function() {
        var options = {
            url: prefix + "/list?type=inquiry",
            detailUrl: prefix + "/detail/{id}",
            modalName: "科技秘密",
            columns: [{
                checkbox: true
            },
                {
                    field: 'technologyId',
                    title: '科技秘密ID',
                    visible: false
                },
                {
                    field: 'technologyName',
                    title: '科技秘密名称'
                },
                {
                    field: 'secretLevel',
                    title: '类型',
                    formatter: function (value, row, index){
                        if(value == 'core')
                            return '核心科技秘密';
                        else if(value == 'ordinary')
                            return '普通科技秘密';
                        else
                            return '无';
                    }
                },
                {
                    field: 'confirmNum',
                    title: '认定号'
                },
                {
                    field: 'confirmDate',
                    title: '认定日期'
                },
                {
                    field: 'contactPersonCode',
                    title: '联系人工号',
                    visible: false
                },
                {
                    field: 'contactPersonName',
                    title: '联系人姓名'
                },
                {
                    field: 'firstDeptCode',
                    title: '第一申报单位',
                    visible: false
                },
                {
                    field: 'firstDeptName',
                    title: '第一申报单位名称'
                },
                {
                    field: 'authSource',
                    title: '来源',
                    formatter: function (value, row, index){
                        return $.table.selectDictLabel(sourceDict,value);
                    }
                },
                {
                    field: 'sourceCode',
                    title: '来源编号',
                    visible: false
                },
                {
                    field: 'sourceName',
                    title: '来源名称',
                    visible: false
                },
                {
                    field: 'techLabel',
                    title: '标签',
                    visible: false,
                    formatter: function (value, row, index){
                        return value.replace(',','，');
                    }
                },
                {
                    field: 'technologyFieldId',
                    title: '技术领域ID',
                    visible: false
                },
                {
                    field: 'technologyFieldName',
                    title: '技术领域',
                    formatter: function (value, row, index){
                        return value.replace(',','，');
                    }
                },
                {
                    field: 'expectedResult',
                    title: '预计效果',
                    visible: false,
                    formatter: function (value, row, index){
                        return value.replace(',','，');
                    }
                },
                {
                    field: 'status',
                    title: '当前状态',
                    formatter: function(value, row, index) {
                        if ($.common.isEmpty(value)) {
                            return '';
                        }
                        if (value == 'draft') {
                            return '<span class="label label-success">草稿</span>';
                        }else if (value == 'ended') {
                            return '<span class="label label-primary">已认定</span>';
                        }else if (value == 'active') {
                            return '<span class="label label-warning">认定中</span>';
                        }else if (value == 'termination'){
                            return '<span class="label label-danger">已否定</span>';
                        }else {
                            return '-';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (isZHAdmin && row.status == 'ended'){
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="adminEdit(\'' + row.technologyId + '\')"><i class="fa fa-edit"></i>维护</a> ');
                        }
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.technologyId + '\')"><i class="fa fa-info-circle"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function reset(){
        $("#secretLevel").val('').select2();
        $("#authSource").val('').select2();
        $("#techLabelLike").val('').select2();
        $("#expectedResultLike").val('').select2();
        $("#contactPersonCode").val('');
        $("#firstDeptCode").val('');
        $.form.reset();
    }

    function adminEdit(technologyId){
        var url = prefix + "/detail/" + technologyId + "?detailType=adminEdit";
        $.modal.openTab("科技秘密维护", url);
    }
</script>
</body>
</html>