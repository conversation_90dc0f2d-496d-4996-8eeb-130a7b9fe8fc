<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('科技秘密申请列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="search-collapse">
        <form id="formId" class="form-horizontal" style="width: 99%;">
            <div class="form-group">
                <label class="col-sm-2 control-label">科技秘密名称:</label>
                <div class="col-sm-4">
                    <input type="text" class="form-control" name="technologyNameLike" placeholder="支持模糊查询"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                </div>
                <label class="col-sm-1 control-label"></label>
                <div class="col-sm-3">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                        <i class="fa fa-search"></i>
                        &nbsp;搜索
                    </a>
                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                        <i class="fa fa-refresh"></i>
                        &nbsp;重置
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="btn-group-sm" id="toolbar" role="group">
		<a class="btn btn-success" onclick="$.operate.addTab()" >
			<i class="fa fa-plus"></i> 添加
		</a>
        <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
            <i class="fa fa-remove"></i> 删除
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="bootstrap-table"></table>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "jsmm/technologySecret";

    $(function () {
        var options = {
            url: prefix + "/list?type=draftApplication",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "科技秘密",
            columns: [{
                checkbox: true
            },
                {
                    field: 'technologyId',
                    title: '科技秘密ID',
                    visible: false
                },
                {
                    field: 'technologyName',
                    title: '科技秘密名称'
                },
                {
                    field: 'secretLevel',
                    title: '类型',
					formatter: function (value, row, index){
						if(value == 'core')
							return '核心科技秘密';
						else if(value == 'ordinary')
							return '普通科技秘密';
						else
							return '无';
					}
                },
				{
					field: 'firstDeptName',
					title: '第一申报单位'
				},
				{
					field: 'authDate',
					title: '提出日期'
				},
                {
                    field: 'status',
                    title: '当前状态',
                    formatter: function(value, row, index) {
                        if ($.common.isEmpty(value)) {
                            return '';
                        }
                        if (value == 'draft') {
                            return '<span class="label label-success">草稿</span>';
                        }
                        else if (value == 'ended') {
                            return '<span class="label label-primary">已认定</span>';
                        }
                        else if (value == 'active') {
                            return '<span class="label label-warning">认定中</span>';
                        } else {
                            return '-';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.technologyId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.technologyId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>