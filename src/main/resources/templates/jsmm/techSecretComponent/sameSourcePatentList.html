<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="init">
    <div class="col-sm-12 select-table table-striped">
        <table id="sameSourcePatentTable"></table>
    </div>

    <script th:inline="javascript">
        $(function () {
            var options = {
                id: 'sameSourcePatentTable',
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                pagination: false,
                detailUrl: ctx + 'kizl/applyBaseinfo/queryDT?businessId={id}',
                uniqueId: "applyId",
                modalName: "同来源专利",
                columns: [
                    {
                        field: 'applyId',
                        title: '专利ID',
                        visible: false
                    },
                    {
                        field: 'applyName',
                        title: '专利名称'
                    },
                    {
                        field: 'flzt',
                        title: '最新状态',
                        formatter: function(value, row, index) {
                            if ($.common.isEmpty(value)) {
                                return '';
                            }
                            //法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止
                            if (value == '01') {
                                return '<span class="label label-info">申请中</span>';
                            } else if (value == '02') {
                                return '<span class="label label-primary">已受理</span>';
                            } else if (value == '03') {
                                return '<span class="label label-warning">主动放弃</span>';
                            } else if (value == '04') {
                                return '<span class="label label-danger">视为撤回</span>';
                            } else if (value == '05') {
                                return '<span class="label label-default">主动撤回</span>';
                            } else if (value == '06') {
                                return '<span class="label label-danger">驳回</span>';
                            } else if (value == '07') {
                                return '<span class="label label-default">到期</span>';
                            } else if (value == '08') {
                                return '<span class="label label-success">转让</span>';
                            } else if (value == '09') {
                                return '<span class="label label-danger">终止</span>';
                            } else {
                                return value;
                            }
                        }
                    },
                    {
                        field: 'jsbh',
                        title: '接收编号'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.applyId + '\')"><i class="fa fa-info-circle"></i>详情</a>');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

</html>