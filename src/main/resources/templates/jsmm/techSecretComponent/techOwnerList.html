<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="initOwner">
    <div class="btn-group-sm" id="ownerToolbar" role="group">
        <a class="btn btn-success" onclick="addOwner()">
            <i class="fa fa-plus"></i> 添加
        </a>
        <a class="btn btn-danger" onclick="removeAllOwner()">
            <i class="fa fa-remove"></i> 删除所有
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="techOwnerTable"></table>
    </div>

    <script th:inline="javascript">
        var prefixOwner = ctx + "jsmm/technologyOwner";

        var curIndex = 0;
        $(function () {
            var options = {
                id: 'techOwnerTable',
                toolbar: 'ownerToolbar',
                url: prefixOwner + "/list?technologyId=" + [[${technologyId}]],
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                pagination: false,
                uniqueId: "techOwnerId",
                modalName: "权利人单位",
                columns: [
                    {
                        field: 'techOwnerId',
                        title: '权利人单位ID',
                        visible: false
                    },
                    {
                        field: 'technologyId',
                        title: '科技秘密ID',
                        visible: false
                    },
                    {
                        field: 'techOwnerCode',
                        title: '权利人单位编码',
                        visible: false
                    },
                    {
                        field: 'order',
                        title: '序号',
                        align: 'center',
                        width: '50px',
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'techOwnerType',
                        title: '单位类型',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            if (value == 0)
                                actions.push('<input type="radio" id="within" name="techOwnerType' + index + '" value="0" onchange="typeChangeCallback(' + index + ')" checked="checked">集团内 <input type="radio" id="outside" name="techOwnerType' + index + '" value="1" onchange="typeChangeCallback(' + index + ')">集团外');
                            else if (value == 1)
                                actions.push('<input type="radio" id="within" name="techOwnerType' + index + '" value="0" onchange="typeChangeCallback(' + index + ')">集团内 <input type="radio" id="outside" name="techOwnerType' + index + '" value="1" onchange="typeChangeCallback(' + index + ')" checked="checked">集团外');
                            else
                                actions.push('<input type="radio" id="within" name="techOwnerType' + index + '" value="0" onchange="typeChangeCallback(' + index + ')">集团内 <input type="radio" id="outside" name="techOwnerType' + index + '" value="1" onchange="typeChangeCallback(' + index + ')">集团外');
                            return actions.join('');
                        }
                    },
                    {
                        field: 'techOwnerName',
                        title: '单位名称',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            var type = row.techOwnerType;
                            if ($.common.isEmpty(value))
                                value = '';
                            if (type == 0) {
                                actions.push('<input class="form-control" id="techOwnerName' + index + '" value="' + value + '" name="techOwnerName' + index + '" type="text" readonly onclick="selectOwner(' + index + ')">')
                            } else {
                                actions.push('<input class="form-control" id="techOwnerName' + index + '" value="' + value + '" name="techOwnerName' + index + '" type="text" onchange="saveText(' + index + ')">')
                            }
                            return actions.join('');
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="removeOwner(\'' + row.techOwnerId + '\')"><i class="fa fa-remove"></i>删除</a>');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });

        function addOwner() {
            var newRow = {
                techOwnerId: Date.now().toString(36)
            };
            $('#techOwnerTable').bootstrapTable('append', newRow);
            var value = $('#techOwnerTable').bootstrapTable('getData').length;
            setOwnerShipNum(value);
        }

        function removeOwner(id) {
            $('#techOwnerTable').bootstrapTable('removeByUniqueId', id);
            var value = $('#techOwnerTable').bootstrapTable('getData').length;
            setOwnerShipNum(value);
        }

        function removeAllOwner() {
            $('#techOwnerTable').bootstrapTable('removeAll');
            setOwnerShipNum(0);
        }

        function typeChangeCallback(index) {
            var selected = $("input[name='techOwnerType" + index + "']:checked");
            var rows = {
                index: index,
                field: "techOwnerType",
                value: selected.val()
            }
            $('#techOwnerTable').bootstrapTable("updateCell", rows);
        }

        function selectOwner(index) {
            var url = ctx + "mpad/org/selectOrgList?selectType='S'";
            curIndex = index;
            var options = {
                title: '选择权利人单位',
                width: "380",
                height: '500',
                url: url,
                callBack: choiceCallback
            };
            $.modal.openOptions(options);
        }

        function choiceCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            var rowCode = {
                index: curIndex,
                field: "techOwnerCode",
                value: body.find('#treeId').val()
            }
            var rowName = {
                index: curIndex,
                field: "techOwnerName",
                value: body.find('#treeName').val()
            }
            $('#techOwnerTable').bootstrapTable("updateCell", rowCode);
            $('#techOwnerTable').bootstrapTable("updateCell", rowName);

            layer.close(index);
        }

        function saveText(index){
            var idSelector = '#techOwnerName' + index;
            var rowName = {
                index: index,
                field: "techOwnerName",
                value: $(idSelector).val()
            }
            $('#techOwnerTable').bootstrapTable("updateCell", rowName);
        }

        function saveOwner(technologyId){
            var url = prefixOwner + "/save/" + technologyId;
            var info = $('#techOwnerTable').bootstrapTable("getData");
            var config = {
                url: url,
                type: 'post',
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(info)
            };
            $.ajax(config)
        }
    </script>
</div>

</html>