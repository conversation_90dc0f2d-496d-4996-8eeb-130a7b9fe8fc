<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="init">
    <div class="col-sm-12 select-table table-striped">
        <table id="sameSourceSecretTable"></table>
    </div>

    <script th:inline="javascript">
        $(function () {
            var options = {
                id: 'sameSourceSecretTable',
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                pagination: false,
                detailUrl: ctx + "jsmm/technologySecret/detail/{id}",
                uniqueId: "technologyId",
                modalName: "同来源科技秘密",
                columns: [
                    {
                        field: 'technologyId',
                        title: '科技秘密ID',
                        visible: false
                    },
                    {
                        field: 'technologyName',
                        title: '科技秘密名称'
                    },
                    {
                        field: 'status',
                        title: '最新状态',
                        formatter: function(value, row, index) {
                            if ($.common.isEmpty(value)) {
                                return '';
                            }
                            if (value == 'draft') {
                                return '<span class="label label-success">草稿</span>';
                            }else if (value == 'ended') {
                                return '<span class="label label-primary">已认定</span>';
                            }else if (value == 'active') {
                                return '<span class="label label-warning">认定中</span>';
                            }else if (value == 'termination'){
                                return '<span class="label label-danger">已否定</span>';
                            }else {
                                return '-';
                            }
                        }
                    },
                    {
                        field: 'confirmNum',
                        title: '认定号'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.technologyId + '\')"><i class="fa fa-info-circle"></i>详情</a>');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

</html>