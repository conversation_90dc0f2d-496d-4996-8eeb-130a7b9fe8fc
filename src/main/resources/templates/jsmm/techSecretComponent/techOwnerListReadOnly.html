<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="initOwner">
    <div class="col-sm-12 select-table table-striped">
        <table id="techOwnerTable"></table>
    </div>

    <script th:inline="javascript">
        var prefixOwner = ctx + "jsmm/technologyOwner";

        var curIndex = 0;
        $(function () {
            var options = {
                id: 'techOwnerTable',
                toolbar: 'ownerToolbar',
                url: prefixOwner + "/list?technologyId=" + [[${technologyId}]],
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                pagination: false,
                uniqueId: "techOwnerId",
                modalName: "权利人单位",
                columns: [
                    {
                        field: 'techOwnerId',
                        title: '权利人单位ID',
                        visible: false
                    },
                    {
                        field: 'technologyId',
                        title: '科技秘密ID',
                        visible: false
                    },
                    {
                        field: 'techOwnerCode',
                        title: '权利人单位编码',
                        visible: false
                    },
                    {
                        field: 'order',
                        title: '序号',
                        align: 'center',
                        width: '50px',
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'techOwnerType',
                        title: '单位类型',
                        align: 'center',
                        formatter: function (value, row, index) {
                            if (value == 0)
                                return '集团内';
                            else if (value == 1)
                                return '集团外';
                            else
                                return '-';
                        }
                    },
                    {
                        field: 'techOwnerName',
                        title: '单位名称',
                        align: 'center'
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

</html>