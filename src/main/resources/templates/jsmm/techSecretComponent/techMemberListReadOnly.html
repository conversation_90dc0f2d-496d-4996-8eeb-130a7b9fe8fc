<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="initMember">
    <div class="col-sm-12 select-table table-striped">
        <table id="techMemberTable"></table>
    </div>

    <script th:inline="javascript">
        var prefixMember = ctx + "jsmm/technologyMember";

        var jobTitleDict = [[${@SDictUtil.getDictList("JSMM","JSMM_JOB_TITLE")}]];
        var jobDict = [[${@SDictUtil.getDictList("JSMM","JSMM_JOB")}]];

        $(function () {
            var options = {
                id: 'techMemberTable',
                toolbar: 'memberToolbar',
                url: prefixMember + "/list?technologyId=" + [[${technologyId}]],
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                pagination: false,
                uniqueId: "techMemberId",
                modalName: "提出人信息",
                columns: [
                    {
                        field: 'techMemberId',
                        title: '提出人记录ID',
                        visible: false
                    },
                    {
                        field: 'technologyId',
                        title: '科技秘密ID',
                        visible: false
                    },
                    {
                        field: 'memberType',
                        title: '提出人类型',
                        visible: false
                    },
                    {
                        field: 'order',
                        title: '序号',
                        align: 'center',
                        width: '50px',
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'memberName',
                        title: '姓名'
                    },
                    {
                        field: 'memberCode',
                        title: '工号'
                    },
                    {
                        field: 'memberDeptCode',
                        title: '提出人组织代码',
                        visible: false
                    },
                    {
                        field: 'memberDeptName',
                        title: '单位'
                    },
                    {
                        field: 'postTitle',
                        title: '职称',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(jobTitleDict,value);
                        }
                    },
                    {
                        field: 'position',
                        title: '岗位',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(jobDict,value);
                        }
                    },
                    {
                        field: 'signStatus',
                        title: '是否签过保密协议',
                        formatter: function (value, row, index) {
                            if (value == 0)
                                return '否';
                            else if (value == 1)
                                return '是';
                            else
                                return '-';
                        }
                    },
                    {
                        field: 'remark',
                        title: '备注',
                        visible: false
                    }]
            };
            $.table.init(options);
        });
    </script>
</div>

</html>