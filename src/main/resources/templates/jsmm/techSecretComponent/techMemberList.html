<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<div th:fragment="initMember">
    <div class="btn-group-sm" id="memberToolbar" role="group">
        <a class="btn btn-success" onclick="addMember()">
            <i class="fa fa-plus"></i> 添加
        </a>
        <a class="btn btn-danger" onclick="removeAllMember()">
            <i class="fa fa-remove"></i> 删除所有
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table id="techMemberTable"></table>
    </div>

    <script th:inline="javascript">
        var prefixMember = ctx + "jsmm/technologyMember";

        var jobTitleDict = [[${@SDictUtil.getDictList("JSMM","JSMM_JOB_TITLE")}]];
        var jobDict = [[${@SDictUtil.getDictList("JSMM","JSMM_JOB")}]];

        $(function () {
            var options = {
                id: 'techMemberTable',
                toolbar: 'memberToolbar',
                url: prefixMember + "/list?technologyId=" + [[${technologyId}]],
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                pagination: false,
                uniqueId: "techMemberId",
                modalName: "提出人信息",
                columns: [
                    {
                        field: 'techMemberId',
                        title: '提出人记录ID',
                        visible: false
                    },
                    {
                        field: 'technologyId',
                        title: '科技秘密ID',
                        visible: false
                    },
                    {
                        field: 'memberType',
                        title: '提出人类型',
                        visible: false
                    },
                    {
                        field: 'order',
                        title: '序号',
                        align: 'center',
                        width: '50px',
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'memberName',
                        title: '姓名'
                    },
                    {
                        field: 'memberCode',
                        title: '工号'
                    },
                    {
                        field: 'memberDeptCode',
                        title: '提出人组织代码',
                        visible: false
                    },
                    {
                        field: 'memberDeptName',
                        title: '单位'
                    },
                    {
                        field: 'postTitle',
                        title: '职称',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(jobTitleDict, value);
                        }
                    },
                    {
                        field: 'position',
                        title: '岗位',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(jobDict, value);
                        }
                    },
                    {
                        field: 'signStatus',
                        title: '是否签过保密协议',
                        formatter: function (value, row, index) {
                            if (value == 0)
                                return '否';
                            else if (value == 1)
                                return '是';
                            else
                                return '-';
                        }
                    },
                    {
                        field: 'remark',
                        title: '备注',
                        visible: false
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="editMember(\'' + row.techMemberId + '\')"><i class="fa fa-edit"></i>编辑</a>');
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="removeMember(\'' + row.techMemberId + '\')"><i class="fa fa-remove"></i>删除</a>');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });

        function addMember() {
            var url = prefixMember + "/add";
            $.modal.open('添加提出人', url, '1152', '550');
        }

        function removeMember(id) {
            $('#techMemberTable').bootstrapTable('removeByUniqueId', id);
        }

        function removeAllMember() {
            $('#techMemberTable').bootstrapTable('removeAll');
        }

        function addMemberCallback(formData) {
            var rowData = {
                techMemberId: Date.now().toString(36),
            }
            for (let [key, value] of formData.entries()) {
                if (!$.common.isEmpty(value)) {
                    rowData[key] = value;
                }
            }
            $('#techMemberTable').bootstrapTable('append', rowData);
        }

        function editMember(id) {
            var row = $('#techMemberTable').bootstrapTable('getRowByUniqueId', id);
            var url = prefixMember + "/editTable?editType=E&id=" + id;
            for (let key in row) {
                if (row[key] != null)
                    url = url + '&' + key + "=" + row[key];
            }
            $.modal.open('修改提出人', url, '1152', '550');
        }

        function editMemberCallback(formData, id) {
            var rowData = {
                id: id,
                replace: true,
                row: {
                    techMemberId: Date.now().toString(36),
                }
            }
            for (let [key, value] of formData.entries()) {
                if (!$.common.isEmpty(value)) {
                    rowData.row[key] = value;
                }
            }
            $('#techMemberTable').bootstrapTable('updateByUniqueId', rowData);
        }

        function saveMember(technologyId) {
            var url = prefixMember + "/save/" + technologyId;
            var info = $('#techMemberTable').bootstrapTable("getData");
            var config = {
                url: url,
                type: 'post',
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(info)
            };
            $.ajax(config)
        }
    </script>
</div>

</html>