<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('提出人信息')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-technologyMember-edit">
        <div class="panel-body">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">类型：</label>
                <div class="col-sm-10"
                     th:include="/component/radio :: init(id='memberType', name='memberType', value=${memberType} ,businessType='JSMM', dictCode='JSMM_MEMBER_TYPE', callback='onTypeChange',isrequired=true)">
                </div>
            </div>
            <input id="memberCode" name="memberCode" th:value="${memberCode}" type="hidden">
            <input id="memberName" name="memberName" th:value="${memberName}" type="hidden">
            <input id="memberDeptCode" name="memberDeptCode" th:value="${memberDeptCode}" type="hidden">
            <input id="memberDeptName" name="memberDeptName" th:value="${memberDeptName}" type="hidden">

            <!--集团内-->
            <div id="within" hidden="hidden">
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">工号：</label>
                    <div class="col-sm-8">
                        <input id="memberCodeInside" th:value="${memberCode}" class="form-control" type="text"
                               placeholder="请选择人员" readonly required>
                    </div>
                    <div class="col-sm-1">
                        <span class="btn btn-primary" type="button" onclick="chooseMember()">选择人员</span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">姓名：</label>
                    <div class="col-sm-8">
                        <input id="memberNameInside" th:value="${memberName}" class="form-control" type="text"
                               placeholder="请选择人员" readonly required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">人员组织：</label>
                    <div class="col-sm-8">
                        <input id="memberDeptNameInside" th:value="${memberDeptName}" class="form-control"
                               placeholder="请选择人员" type="text" readonly required>
                    </div>
                </div>
            </div>

            <!--集团外-->
            <div id="outside" hidden="hidden">
                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">姓名：</label>
                    <div class="col-sm-10">
                        <input id="memberNameOutside" th:value="${memberName}" class="form-control" type="text" required>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label is-required">人员组织：</label>
                    <div class="col-sm-10">
                        <input id="memberDeptNameOutside" th:value="${memberDeptName}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label is-required">职称：</label>
                <div class="col-sm-10">
                    <th:block th:include="component/selectDict :: init(id='postTitle',name='postTitle',businessType='JSMM',dictCode='JSMM_JOB_TITLE',value=${postTitle},isfirst=true,isrequired=true)"></th:block>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">岗位：</label>
                <div class="col-sm-10">
                    <th:block th:include="component/selectDict :: init(id='position',name='position',businessType='JSMM',dictCode='JSMM_JOB',value=${position},isfirst=true,isrequired=true)"></th:block>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">是否签过保密协议：</label>
                <div class="col-sm-10"
                     th:include="/component/radio :: init(id='signStatus', name='signStatus', value=${signStatus} ,businessType='JSMM', dictCode='JSMM_SIGN_STATUS',isrequired=true)">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea th:utext="${remark}" name="remark" class="form-control"
                              style="width: 100%; height: 100px;"></textarea>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "jsmm/technologyMember";
    var memberType = [[${memberType}]];
    var editType = [[${editType}]];
    var id = [[${id}]];

    $("#form-technologyMember-edit").validate({
        focusCleanup: true
    });

    //初始化
    $(document).ready(function () {
        onTypeChange(memberType);
    });

    function submitHandler() {
        if (memberType == 0) {
            $("#memberCode").val($("#memberCodeInside").val());
            $("#memberName").val($("#memberNameInside").val());
            $("#memberDeptName").val($("#memberDeptNameInside").val());
        } else {
            $("#memberCode").val('');
            $("#memberName").val($("#memberNameOutside").val());
            $("#memberDeptCode").val('');
            $("#memberDeptName").val($("#memberDeptNameOutside").val());
        }
        if ($.validate.form()) {
            var formData = new FormData(document.getElementById('form-technologyMember-edit'));
            if (editType == 'E')
                parent.editMemberCallback(formData, id);
            else
                parent.addMemberCallback(formData);
            $.modal.close();
        }
    }

    function onTypeChange(val) {
        if (val == 0) {
            $("#within").show();
            document.getElementById("memberCodeInside").disabled = false;
            document.getElementById("memberNameInside").disabled = false;
            document.getElementById("memberDeptNameInside").disabled = false;
            $("#outside").hide();
            document.getElementById("memberNameOutside").disabled = true;
            document.getElementById("memberDeptNameOutside").disabled = true;
        } else {
            $("#within").hide();
            document.getElementById("memberCodeInside").disabled = true;
            document.getElementById("memberNameInside").disabled = true;
            document.getElementById("memberDeptNameInside").disabled = true;
            $("#outside").show();
            document.getElementById("memberNameOutside").disabled = false;
            document.getElementById("memberDeptNameOutside").disabled = false;
        }
        memberType = val;
    }

    function chooseMember() {
        choiceUser('memberCodeInside', 'memberNameInside', 'S', null, 'getOrg');
    }

    function getOrg(userCode, userName) {
        var url = prefix + "/getOrg?userCode=" + userCode;
        var object;
        $.ajax({
            url: url,
            type: "get",
            async: false,
            dataType: "json",
            contentType: 'application/json;charset=UTF-8',
            success: function (result) {
                object = result.data;
                if (!$.common.isEmpty(object)) {
                    $("#memberDeptCode").val(object.orgPathCode);
                    $("#memberDeptNameInside").val(object.orgPathName);
                } else {
                    $.modal.alertError('未找到该人员的组织！');
                    $("#memberDeptCode").val('');
                    $("#memberDeptNameInside").val('');
                }
            },

        });
    }
</script>
</body>
</html>