<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('提出人信息列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">输入框:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="" placeholder="支持模糊查询"/>
				    </div>
				    <label class="col-sm-1 control-label">下拉框:</label>
					<div class="col-sm-3">
						<select id="state" name="" class="form-control m-b">
							<option value="">请选择</option>
							<option value="0">状态0</option>
							<option value="1">状态1</option>
						</select>
				    </div>
				    <label class="col-sm-1 control-label">日期:</label>
					<div class="col-sm-3">
						<th:block th:include="/component/date::init(name='',id='')"/>
				    </div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcelBit()" >
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary" onclick="$.table.importTemplateBit()" >
                <i class="fa fa-download"></i> 导出模板
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "jsmm/technologyMember";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "提出人信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'techMemberId',
                    title: '提出人记录ID',
                    visible: false
                },
                {
                    field: 'technologyId',
                    title: '技术秘密ID'
                },
                {
                    field: 'memberType',
                    title: '提出人类型'
                },
                {
                    field: 'memberCode',
                    title: '提出人工号'
                },
                {
                    field: 'memberName',
                    title: '提出人姓名'
                },
                {
                    field: 'memberDeptCode',
                    title: '提出人组织代码'
                },
                {
                    field: 'memberDeptName',
                    title: '提出人组织名称'
                },
                {
                    field: 'postTitle',
                    title: '职称'
                },
                {
                    field: 'position',
                    title: '岗位'
                },
                {
                    field: 'contribution',
                    title: '贡献系数'
                },
                {
                    field: 'signStatus',
                    title: '是否签过保密协议'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'createDate',
                    title: '记录创建日期'
                },
                {
                    field: 'updateDate',
                    title: '记录修改日期'
                },
                {
                    field: 'deleteDate',
                    title: '记录删除日期'
                },
                {
                    field: 'createUserLabel',
                    title: '记录创建人'
                },
                {
                    field: 'updateUserLabel',
                    title: '记录修改人'
                },
                {
                    field: 'deleteUserLabel',
                    title: '记录删除人'
                },
                {
                    field: 'recordVersion',
                    title: '记录版本号'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.techMemberId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.techMemberId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>