<!--  弹框通用CSS -->
<head th:fragment=header(title)>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="" name="keywords">
    <meta content="" name="description">
    <meta name="referrer" content="never">
    <title th:text="${title}"></title>
    <link href="favicon.ico" rel="shortcut icon">
    <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/font-awesome.min.css}"/>
    <!-- bootstrap-table 表格插件样式 -->
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css?v=20210602}"/>
    <link rel="stylesheet" th:href="@{/css/animate.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/style.min.css?v=20200903}"/>

    <link rel="stylesheet" th:href="@{/ruoyi/css/ry-ui.css?v=4.6.1}"/>
    <!--时间-->
    <th:block th:include="include :: datetimepicker-css"/>

    <th:block th:include="include :: lay-ui-css"/>
    <th:block th:include="include :: select2-css"/>


    <link rel="stylesheet" th:href="@{/css/mystyle.css}"/>
    <!--引导图-->
    <link rel="stylesheet" th:href="@{/css/liulist.css}"/>

    <!--模块待办-->
    <link rel="stylesheet" th:href="@{/css/wfdb.css}"/>
    <link th:href="@{/css/attachment.css}" rel="stylesheet"/>

    <link rel="stylesheet" th:href="@{/ky/css/module.css}"/>

</head>
<!-- 弹框通用js  -->
<div th:fragment="baseJs">
    <script th:inline="javascript">
        var ctx = [[@{/}]];
        var ctxMP = [[${@AppContextProp.ctxGGMK}]];
        var ctxKY = [[${@AppContextProp.ctxKY}]];
        //选择后默认回调 id为隐藏code的id, name为显示名称的id, codes选择返回的code值, names选择返回的显示名称值
        function selectDefaultCallback(id, name, codes, names) {
            $("#" + id).val(codes);
            $("#" + name).val(names);
            try{
                $("#" + name).valid();//触发下校验
            } catch (e) {
            }
        }
    </script>

    <a class="btn btn-sm display" href="#" id="scroll-up"><i class="fa fa-angle-double-up"></i></a>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <!-- bootstrap-table 表格插件 -->
    <script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/locale/bootstrap-table-zh-CN.min.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.js?v=20210602}"></script>
    <!-- jquery-validate 表单验证插件 -->
    <script th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
    <script th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
    <!-- jquery-validate 表单树插件 -->
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/tree/bootstrap-table-tree.min.js?v=20210602}"></script>
    <!-- 遮罩层 -->
    <script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
    <script th:src="@{/ajax/libs/iCheck/icheck.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js?v=20210516}"></script>
    <script th:src="@{/ajax/libs/layui/layui.js?v=20210516}"></script>
    <script th:src="@{/ruoyi/js/common.js?v=4.6.1}"></script>
    <script th:src="@{/ruoyi/js/ry-ui.js?v=4.6.1}"></script>

    <!--附件-->
    <th:block th:include="include :: lay-upload-js"/>
    <!--时间-->
    <th:block th:include="include :: datetimepicker-js"/>

    <th:block th:include="include :: select2-js"/>

    <!-- 	&lt;!&ndash;折叠图标&ndash;&gt;-->
    <!--	<script th:src="@{/js/cc.js}"></script>-->

    <!--选择组织-->
    <th:block th:include="include :: selectOrg" />
    <!--从组织树选择用户-->
    <th:block th:include="include :: selectUser" />
    <!--指定组织下选择用户-->
    <th:block th:include="include :: selectOrgUser" />
    <!--根据角色编码选择用户-->
    <th:block th:include="include :: selectRoleUser" />
    <!--本部门选择用户-->
    <th:block th:include="include :: selectDeptUser" />
    <!--本租户选择用户-->
    <th:block th:include="include :: selectTenantUser" />

    <!--富文本-->
    <th:block th:include="/component/richText :: js" />
    <script th:inline="javascript">

        $(document).on('click', 'body .btn-rounded',function(){
            $('.btn-rounded').attr("disabled", true).css("pointer-events", "none");
            setTimeout(function () {
                $('.btn-rounded').attr("disabled", false).css("pointer-events", "auto");
            }, 3000);
        });

        var sbxhsjTime
        $(document).on('mouseenter', '.input-group .detailOrgOrUser',function(){
            var input =$(this).val();
            sbxhsjTime=setTimeout(function(){
                if (input!=null && input.length > 40) {
                    layer.alert(input, {
                        title : "信息内容",
                        shadeClose : true,
                        btn : [ '确认' ],
                        btnclass : [ 'btn btn-primary' ],
                    });
                }
            },2000)
        });
        $(document).on('mouseleave', '.input-group .detailOrgOrUser',function(){
            if(sbxhsjTime){
                clearInterval(sbxhsjTime);
            }
        });
        $(document).on('mouseenter', '.attachmentShow',function(){
            var input =$(this).find("p").text();
            if(input){
                sbxhsjTime=setTimeout(function(){
                    if (input!=null && input.length > 40) {
                        layer.alert(input, {
                            title : "信息内容",
                            shadeClose : true,
                            btn : [ '确认' ],
                            btnclass : [ 'btn btn-primary' ],
                        });
                    }
                },2000)
            }

        });
        $(document).on('mouseleave', '.attachmentShow',function(){
            if(sbxhsjTime){
                clearInterval(sbxhsjTime);
            }
        });

        $(document).ready(function(){
            //右边浮动导航
            var aDiv = $('.wrapper-content .panel-group:visible');//楼层
            if(aDiv&&aDiv.length>=3){//大于等于3块显示
                initNavigator(aDiv);
                titleSq();
            }
        });

        function initNavigator(aDiv) {
            //动态新增浮动导航
            $("#LoutiNav").remove();
            var rightNavHtml = "<ul id=\"LoutiNav\">";
            rightNavHtml+='<div id="sqDiv" class="pack-up-button"><a onclick="titleSq()"><i class="fa fa-chevron-circle-right">收起</i> </a></div>';
            aDiv.each(function(index,element) {
                if(index<10){//最多显示10个
                    var title = $(element).find(".panel-title").find("a").first().text();
                    var title = title.replace(/[^\u4e00-\u9fa5a-zA-Z]/gi, "");//获取汉字英文
                    if (title) {
                        var show = title;
                        if(title&&title.length>5){
                            title = title.substring(0,5);//截取前5个汉字
                        }
                        if(index==0){
                            rightNavHtml += "<li class=\"active\">";
                        }else{
                            rightNavHtml += "<li>";
                        }
                        rightNavHtml += "<i>";
                        rightNavHtml += "<div class=\"fa fa-caret-right\" aria-hidden=\"true\"></div>";
                        rightNavHtml += title;
                        rightNavHtml += "</i>";
                        rightNavHtml += "<span title='"+show+"'>";
                        rightNavHtml += "<div class=\"fa fa-caret-right\" aria-hidden=\"true\"></div>";
                        rightNavHtml += title;
                        rightNavHtml += "</span>";
                        rightNavHtml += "</li>";
                    }
                }
            })
            rightNavHtml += "</ul>"
            $("body").prepend(rightNavHtml);

            var oNav = $('#LoutiNav');//导航壳
            var aNav = oNav.find('li');//导航
            var oTop = $('#goTop');
            //回到顶部
            $(window).scroll(function() {
                var winH = $(window).height();//可视窗口高度
                var iTop = $(window).scrollTop();//鼠标滚动的距离
                if (iTop >= $('.wrapperheader').height()) {
                    oNav.fadeIn();
                    oTop.fadeIn();
                    //鼠标滑动式改变
                    aDiv.each(function() {
                        if (iTop + 40 - $(this).offset().top > 0) {//2022-04-15 韩学闯修改（右导航定位不准确问题）panel-group同层级不要添加其他元素
                            // if (winH + iTop - $(this).offset().top > winH) {
                            aNav.removeClass('active');
                            aNav.eq($(this).index()).addClass('active');
                        }
                    })
                } else {
                    oNav.fadeOut();
                    oTop.fadeOut();
                }
            })
            //点击top回到顶部
            oTop.click(function() {
                $('body,html').animate({
                    "scrollTop" : 0
                }, 500)
            })
            //点击回到当前楼层
            aNav.click(function() {
                var t = aDiv.eq($(this).index()-1).offset().top;
                $('body,html').animate({
                    "scrollTop" : t
                }, 500);
                $(this).addClass('active').siblings().removeClass('active');
            });
        }

        function titleSq(){
            var data= $("#LoutiNav").attr("data");
            if(data==null || "1"==data){
                $("#LoutiNav").find("li").css("right","-83px");
                $("#sqDiv").css("margin-left","25px");
                $("#LoutiNav").css("right","-30px");
                $("#LoutiNav").attr("data","2");
                $("#sqDiv .fa").removeClass("fa-chevron-circle-right");
                $("#sqDiv .fa").addClass("fa-chevron-circle-left");
            }else {
                $("#sqDiv").css("margin-left","0px");
                $("#LoutiNav").css("right","0px");
                $("#LoutiNav").find("li").css("right","0px");
                $("#LoutiNav").find("li").css("visibility","inherit");
                $("#LoutiNav").attr("data","1");
                $("#sqDiv .fa").removeClass("fa-chevron-circle-left");
                $("#sqDiv .fa").addClass("fa-chevron-circle-right");
            }
        }




        function Base64() {

            // private property
            _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

            // public method for encoding
            this.encode = function (input) {
                var output = "";
                var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
                var i = 0;
                input = _utf8_encode(input);
                while (i < input.length) {
                    chr1 = input.charCodeAt(i++);
                    chr2 = input.charCodeAt(i++);
                    chr3 = input.charCodeAt(i++);
                    enc1 = chr1 >> 2;
                    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
                    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
                    enc4 = chr3 & 63;
                    if (isNaN(chr2)) {
                        enc3 = enc4 = 64;
                    } else if (isNaN(chr3)) {
                        enc4 = 64;
                    }
                    output = output +
                        _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
                        _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
                }
                return output;
            }

            // public method for decoding
            this.decode = function (input) {
                var output = "";
                var chr1, chr2, chr3;
                var enc1, enc2, enc3, enc4;
                var i = 0;
                input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
                while (i < input.length) {
                    enc1 = _keyStr.indexOf(input.charAt(i++));
                    enc2 = _keyStr.indexOf(input.charAt(i++));
                    enc3 = _keyStr.indexOf(input.charAt(i++));
                    enc4 = _keyStr.indexOf(input.charAt(i++));
                    chr1 = (enc1 << 2) | (enc2 >> 4);
                    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
                    chr3 = ((enc3 & 3) << 6) | enc4;
                    output = output + String.fromCharCode(chr1);
                    if (enc3 != 64) {
                        output = output + String.fromCharCode(chr2);
                    }
                    if (enc4 != 64) {
                        output = output + String.fromCharCode(chr3);
                    }
                }
                output = _utf8_decode(output);
                return output;
            }

            // private method for UTF-8 encoding
            _utf8_encode = function (string) {
                string = string.replace(/\r\n/g,"\n");
                var utftext = "";
                for (var n = 0; n < string.length; n++) {
                    var c = string.charCodeAt(n);
                    if (c < 128) {
                        utftext += String.fromCharCode(c);
                    } else if((c > 127) && (c < 2048)) {
                        utftext += String.fromCharCode((c >> 6) | 192);
                        utftext += String.fromCharCode((c & 63) | 128);
                    } else {
                        utftext += String.fromCharCode((c >> 12) | 224);
                        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }

                }
                return utftext;
            }

            // private method for UTF-8 decoding
            _utf8_decode = function (utftext) {
                var string = "";
                var i = 0;
                var c = c1 = c2 = 0;
                while ( i < utftext.length ) {
                    c = utftext.charCodeAt(i);
                    if (c < 128) {
                        string += String.fromCharCode(c);
                        i++;
                    } else if((c > 191) && (c < 224)) {
                        c2 = utftext.charCodeAt(i+1);
                        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                        i += 2;
                    } else {
                        c2 = utftext.charCodeAt(i+1);
                        c3 = utftext.charCodeAt(i+2);
                        string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                        i += 3;
                    }
                }
                return string;
            }
        }
        //放大表格列
        function fdTableL(){
            var bgl=$(".dropdown-menu").eq(0);
            var checkedArr=[];
            var dataFieldArr=[];
            var dataFieldNameArr=[];

            bgl.find("li").each(function(data){
                var lAttr= $(this).find("input");
                console.log();
                if($(lAttr).prop('checked')){
                    checkedArr.push('1');
                }else{
                    checkedArr.push('0');
                }
                dataFieldArr.push($(lAttr).attr("data-field"));
                dataFieldNameArr.push($(this).find("label").text());
            });
            layer.open({
                type: 2,
                area: ['800px', '600px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: '显示隐藏列',
                content: ctx +"common/fdl?checkedArr="+checkedArr+"&dataFieldArr="+dataFieldArr+"&dataFieldNameArr="+dataFieldNameArr,
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true
            });
        }
    </script>

</div>

<!-- excel pdf 导出使用js  -->
<div th:fragment="exportkkJs">
    <script th:inline="javascript">
        var ctx = [[@{/}]];
    </script>

    <script th:src="@{/js/plugins/eportPlugins/FileSaver.min.js}" type="text/javascript"></script>
    <script th:src="@{/js/plugins/eportPlugins/jspdf.umd.min.js}" type="text/javascript"></script>
    <script th:src="@{/js/plugins/eportPlugins/xlsx.core.min.js}" type="text/javascript"></script>
    <script th:src="@{/js/plugins/eportPlugins/tableExport.min.js}" type="text/javascript"></script>
    <script th:src="@{/js/plugins/eportPlugins/vfs_fonts.js}" type="text/javascript"></script>
    <script th:src="@{/js/plugins/eportPlugins/pdfmake.min.js}" type="text/javascript"></script>
</div>

<!-- ztree树插件 -->
<div th:fragment="ztree-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}"/>
</div>
<div th:fragment="ztree-js">
    <script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
</div>
<div th:fragment="core-js">
    <script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.core-3.5.js}"></script>
</div>


<!-- select2下拉框插件 -->
<div th:fragment="select2-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/select2/select2.min.css}"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/select2/select2-bootstrap.css}"/>
</div>
<div th:fragment="select2-js">
    <script th:src="@{/ajax/libs/select2/select2.min.js}"></script>
</div>

<!-- bootstrap-select下拉框插件 -->
<div th:fragment="bootstrap-select-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-select/bootstrap-select.css}"/>
</div>
<div th:fragment="bootstrap-select-js">
    <script th:src="@{/ajax/libs/bootstrap-select/bootstrap-select.js}"></script>
</div>

<!-- datetimepicker日期和时间插件 -->
<div th:fragment="datetimepicker-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.css}"/>
</div>
<div th:fragment="datetimepicker-js">
    <script th:src="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.js}"></script>
</div>

<!-- ui布局插件 -->
<div th:fragment="layout-latest-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-layout/jquery.layout-latest.css}"/>
</div>
<div th:fragment="layout-latest-js">
    <script th:src="@{/ajax/libs/jquery-layout/jquery.layout-latest.js}"></script>
</div>

<!-- layui插件 -->
<div th:fragment="lay-ui-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/layui/css/layui.css}"/>
</div>

<!-- summernote富文本编辑器插件 -->
<div th:fragment="summernote-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/summernote/summernote.css}"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/summernote/summernote-bs3.css}"/>
</div>
<div th:fragment="summernote-js">
    <script th:src="@{/ajax/libs/summernote/summernote.min.js}"></script>
    <script th:src="@{/ajax/libs/summernote/summernote-zh-CN.js}"></script>
</div>
<div th:fragment="lay-upload-js">

    <script th:inline="javascript" type="text/javascript">
        function download(id) {
            window.open(ctx + "attachment/download/" + id);
        }
        function downloadHtFile(id) {
            var tenantId = $("#tenantId").val();
            window.open(ctx + "attachment/downloadHtFile/" + id+"/"+tenantId);
        }
        function preview(id) {
            window.open(ctx + "attachment/preview/" + id);
        }


        function deleteFile(attId, index, listId, id) {
            $("#upload-" + listId + "-" + index).remove();
            var attachmentIds = $("#" + id).val();
            if (attachmentIds != null && attachmentIds != '') {
                var arr = attachmentIds.split(",");
                if (arr.includes(attId)) {
                    arr.splice(arr.indexOf(attId), 1);
                    $("#" + id).val(arr.join(","));
                }
            }
        }
    </script>

</div>

<!-- cropper图像裁剪插件 -->
<div th:fragment="cropper-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/cropper/cropper.min.css}"/>
</div>
<div th:fragment="cropper-js">
    <script th:src="@{/ajax/libs/cropper/cropper.min.js}"></script>
</div>

<!-- jasny功能扩展插件 -->
<div th:fragment="jasny-bootstrap-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jasny/jasny-bootstrap.min.css}"/>
</div>
<div th:fragment="jasny-bootstrap-js">
    <script th:src="@{/ajax/libs/jasny/jasny-bootstrap.min.js}"></script>
</div>

<!-- fileinput文件上传插件 -->
<div th:fragment="bootstrap-fileinput-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput/fileinput.min.css?v=20201202}"/>
</div>
<div th:fragment="bootstrap-fileinput-js">
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js?v=20201202}"></script>
</div>

<!-- duallistbox双列表框插件 -->
<div th:fragment="bootstrap-duallistbox-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.css}"/>
</div>
<div th:fragment="bootstrap-duallistbox-js">
    <script th:src="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.js}"></script>
</div>

<!-- suggest搜索自动补全 -->
<div th:fragment="bootstrap-suggest-js">
    <script th:src="@{/ajax/libs/suggest/bootstrap-suggest.min.js}"></script>
</div>

<!-- typeahead搜索自动补全 -->
<div th:fragment="bootstrap-typeahead-js">
    <script th:src="@{/ajax/libs/typeahead/bootstrap3-typeahead.min.js}"></script>
</div>

<!-- 多级联动下拉 -->
<div th:fragment="jquery-cxselect-js">
    <script th:src="@{/ajax/libs/cxselect/jquery.cxselect.min.js}"></script>
</div>

<!-- jsonview格式化和语法高亮JSON格式数据查看插件 -->
<div th:fragment="jsonview-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jsonview/jquery.jsonview.css}"/>
</div>
<div th:fragment="jsonview-js">
    <script th:src="@{/ajax/libs/jsonview/jquery.jsonview.js}"></script>
</div>

<!-- jquery.smartwizard表单向导插件 -->
<div th:fragment="jquery-smartwizard-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/smartwizard/smart_wizard_all.min.css}"/>
</div>
<div th:fragment="jquery-smartwizard-js">
    <script th:src="@{/ajax/libs/smartwizard/jquery.smartWizard.min.js}"></script>
</div>

<!-- ECharts百度统计图表插件 -->
<div th:fragment="echarts-js">
    <script th:src="@{/ajax/libs/report/echarts/echarts-all.min.js}"></script>
</div>

<!-- peity图表组合插件 -->
<div th:fragment="peity-js">
    <script th:src="@{/ajax/libs/report/peity/jquery.peity.min.js}"></script>
</div>

<!-- sparkline线状图插件 -->
<div th:fragment="sparkline-js">
    <script th:src="@{/ajax/libs/report/sparkline/jquery.sparkline.min.js}"></script>
</div>

<!-- 表格行拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-rows-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-rows/bootstrap-table-reorder-rows.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-rows/jquery.tablednd.js}"></script>
</div>

<!-- 表格列拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-columns-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/jquery.dragtable.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/bootstrap-table-reorder-columns.js?v=20210602}"></script>
</div>

<!-- 表格列宽拖动插件 -->
<div th:fragment="bootstrap-table-resizable-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/jquery.resizableColumns.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/bootstrap-table-resizable.js?v=20210602}"></script>
</div>

<!-- 表格行内编辑插件 -->
<div th:fragment="bootstrap-editable-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.css}"/>
</div>
<div th:fragment="bootstrap-table-editable-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-table-editable.js?v=20210602}"></script>
</div>

<!-- 表格导出插件 -->
<div th:fragment="bootstrap-table-export-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.min.js}"></script>
</div>

<!-- 表格冻结列插件 -->
<div th:fragment="bootstrap-table-fixed-columns-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/columns/bootstrap-table-fixed-columns.js?v=20210602}"></script>
</div>

<!-- 表格自动刷新插件 -->
<div th:fragment="bootstrap-table-auto-refresh-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/auto-refresh/bootstrap-table-auto-refresh.js?v=20210602}"></script>
</div>

<!-- 表格打印插件 -->
<div th:fragment="bootstrap-table-print-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/print/bootstrap-table-print.js?v=20210602}"></script>
</div>

<!-- 表格视图分页插件 -->
<div th:fragment="bootstrap-table-custom-view-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/custom-view/bootstrap-table-custom-view.js?v=20210602}"></script>
</div>


<!--组织
orgCodeInputId 组织 input  ID 必填
orgNameInputId:组织名称 input  ID 必填
selectType 单选S 多选M 必填
level 最高层级 空是查询所有
orgCode 根组织  空是集团
-->
<div th:fragment="selectOrg">
    <script type="text/javascript" th:inline="javascript">
        var orgId = "orgId";
        var orgNameId = "deptName";
        function choiceOrg(orgCodeInputId, orgNameInputId, selectType, level, orgCode,showLevel,callback) {
            orgId = orgCodeInputId;
            orgNameId = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "mpad/org/selectOrgList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + orgId).val();
            var options = {
                title: '选择组织',
                width: "450",
                height: '450',
                url: url,
                callBack: choiceOrgCallback
            };
            $.modal.openOptions(options);
        }

        function choiceOrgCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + orgId).val(body.find('#treeId').val());
            $("#" + orgNameId).val(body.find('#treeName').val());
            layer.close(index);
        }
    </script>


    <!--组织下选择人员
    userCodeInputId:工号input id 必填
    userNameInputId:名称input id 必填
    selectType 单选S 多选M 必填
    orgCode  组织 默认是空 默认是集团所有组织
    -->
    <div th:fragment="selectUser">
        <script type="text/javascript" th:inline="javascript">
            var ctx = [[@{/}]];
            var userId = "userId";
            var userNameId = "userName";

            function choiceUser(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
                userId = userCodeInputId;
                userNameId = userNameInputId;
                var url = ctx + 'mpad/user/selectUserList';
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                url += "?selectType=" + selectType;
                if (!(orgCode === undefined) && orgCode != null) {
                    url += "&orgCode=" + orgCode;
                }
                var  values=$("#"+userCodeInputId).val();
                if(!(values=== undefined)  && values!=null){
                    url+="&values="+values;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&userId=" + $("#" + userId).val() + "&userName" + $("#" + userNameId).val();
                $.modal.open("选择用户", url, '1000', '450');
            }

            function choiceUserCallback(userCode, userName) {
                $("#" + userId).val(userCode);
                $("#" + userNameId).val(userName);
                try{
                    $("#" + userNameId).valid();//触发下校验
                } catch (e) {
                }
            }
        </script>
    </div>
    <div th:fragment="selectRoleUser">
        <script type="text/javascript" th:inline="javascript">
            var ctx = [[@{/}]];
            var userId = "userId";
            var userNameId = "userName";

            function choiceRoleUser(userCodeInputId, userNameInputId, selectType, roleCode, callback) {
                userId = userCodeInputId;
                userNameId = userNameInputId;
                var url = ctx + 'mpad/user/selectRoleUserList/'+roleCode;
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                url += "?selectType=" + selectType;
                var values = $("#"+userCodeInputId).val();
                if(!(values=== undefined)  && values!=null){
                    url+="&values="+values;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&userId=" + $("#" + userId).val() + "&userName=" + $("#" + userNameId).val();
                $.modal.open("选择角色用户", url, '1000', '450');
            }

            function choiceRoleUserCallback(userCode, userName) {
                $("#" + userId).val(userCode);
                $("#" + userNameId).val(userName);
                try{
                    $("#" + userNameId).valid();//触发下校验
                } catch (e) {
                }
            }
        </script>
    </div>

    <!--本部门下选择人员
    userCodeInputId:工号input id 必填
    userNameInputId:名称input id 必填
    selectType 单选S 多选M 必填
    orgCode  组织 默认是空 默认是集团所有组织
    -->
    <div th:fragment="selectDeptUser">
        <script type="text/javascript" th:inline="javascript">
            var ctx = [[@{/}]];
            var userId = "userId";
            var userNameId = "userName";

            function choiceDeptUser(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
                userId = userCodeInputId;
                userNameId = userNameInputId;
                var url = ctx + 'mpad/user/selectDeptUser';
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                url += "?selectType=" + selectType;
                var values = $("#"+userCodeInputId).val();
                if(!(values=== undefined)  && values!=null){
                    url+="&values="+values;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&userId=" + $("#" + userId).val() + "&userName=" + $("#" + userNameId).val();
                if (orgCode) {
                    url += "&orgCode="+orgCode;
                }
                $.modal.open("选择同部门用户", url, 1000, 450);
            }

            function choiceDeptUserCallback(userCode, userName) {
                $("#" + userId).val(userCode);
                $("#" + userNameId).val(userName);
                try{
                    $("#" + userNameId).valid();//触发下校验
                } catch (e) {
                }
            }
        </script>
    </div>

    <!--本租户下选择人员
    userCodeInputId:工号input id 必填
    userNameInputId:名称input id 必填
    selectType 单选S 多选M 必填
    orgCode  组织 默认是空 默认是集团所有组织
    -->
    <div th:fragment="selectTenantUser">
        <script type="text/javascript" th:inline="javascript">
            var ctx = [[@{/}]];
            var userId = "userId";
            var userNameId = "userName";

            function choiceTenantUser(userCodeInputId, userNameInputId, selectType, tenantId, callback) {
                userId = userCodeInputId;
                userNameId = userNameInputId;
                var url = ctx + 'mpad/user/selectTenantUser';
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                url += "?selectType=" + selectType;
                if (!(tenantId === undefined) && tenantId != null) {
                    url += "&orgCode=" + tenantId;
                }
                var  values=$("#"+userCodeInputId).val();
                if(!(values=== undefined)  && values!=null){
                    url+="&values="+values;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&userId=" + $("#" + userId).val() + "&userName" + $("#" + userNameId).val();
                $.modal.open("选择用户", url, 1000, 450);
            }

            function choiceTenantUserCallback(userCode, userName) {
                $("#" + userId).val(userCode);
                $("#" + userNameId).val(userName);
                try{
                    $("#" + userNameId).valid();//触发下校验
                } catch (e) {
                }
            }
        </script>
    </div>

    <div th:fragment="selectOrgUser">
        <script type="text/javascript" th:inline="javascript">
            var ctx = [[@{/}]];
            var userId = "userId";
            var userNameId = "userName";

            function choiceOrgUser(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
                userId = userCodeInputId;
                userNameId = userNameInputId;
                var url = ctx + 'mpad/user/selectOrgUserList';
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                url += "?selectType=" + selectType;
                var values = $("#"+userCodeInputId).val();
                if(!(values=== undefined)  && values!=null){
                    url+="&values="+values;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&userId=" + $("#" + userId).val() + "&userName=" + $("#" + userNameId).val();
                if (orgCode) {
                    url += "&orgCode="+orgCode;
                }
                $.modal.open("选择组织下用户", url, 1000, 450);
            }

            function choiceOrgUserCallback(userCode, userName) {
                $("#" + userId).val(userCode);
                $("#" + userNameId).val(userName);
            }
        </script>
    </div>

    <div th:fragment="selectService">
        <script th:inline="javascript" type="text/javascript">
            var orgId = "orgId";
            var orgNameId = "deptName";

            function choiceService(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
                orgId = orgCodeInputId;
                orgNameId = orgNameInputId;
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                var url = ctx + "htsb/tradeService/selectServiceList?selectType=" + selectType;
                if (!(level === undefined) && level != null) {
                    url += "&level=" + level;
                }
                if (!(showLevel === undefined) && showLevel != null) {
                    url += "&showLevel=" + showLevel;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&values=" + $("#" + orgId).val();

                //debugger
                var options = {
                    title: '选择服务',
                    width: "580",
                    height: '450',
                    url: url,
                    callBack: choiceServiceCallback
                };
                $.modal.openOptions(options);
            }

            function choiceServiceCallback(index, layero) {
                var tree = layero.find("iframe")[0].contentWindow.$._tree;
                var body = layer.getChildFrame('body', index);
                layero.find("iframe")[0].contentWindow.saveCheck();
                $("#" + orgId).val(body.find('#treeId').val());
                $("#" + orgNameId).val(body.find('#treeName').val());
                layer.close(index);
            }
        </script>
    </div>
    <div th:fragment="selectCountry">
        <script type="text/javascript" th:inline="javascript">
            let userIdCountry ;
            let userNameIdCountry ;
            function choiceCountry(userCode, userName, selectType, callback) {
                callback="choiceCountryCallback";
                userIdCountry = userCode;
                userNameIdCountry = userName;
                var url = ctx + 'htsb/tradeRegist/selectCountry'
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                url += "?selectType=" + selectType;
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                var userCode = $("#" + userCode).val();
                if (!(userCode === undefined) && userCode != null) {
                    url += "&userCode=" + userCode;
                }
                var userName = $("#" + userName).val();
                if (!(userName === undefined) && userName != null) {
                    url += "&userName=" + userName;
                }
                url += "&userId=" + $("#" + userIdCountry).val();
                $.modal.open("选择国家", url, '1000', '450');
            }

            function choiceCountryCallback(userCode, userName) {
                $("#" + userIdCountry).val(userCode);
                $("#" + userNameIdCountry).val(userName);
            }
        </script>
    </div>
    <div th:fragment="selectTechnical">
        <script th:inline="javascript" type="text/javascript">
            var orgId = "orgId";
            var orgNameId = "deptName";
            function choiceTechnical(orgCodeInputId, orgNameInputId, selectType, level, showLevel, callback,type) {
                orgId = orgCodeInputId;
                orgNameId = orgNameInputId;
                if (selectType === undefined || selectType == null || selectType == '') {
                    selectType = "S";
                }
                var url = ctx + "htsb/tradeTechnical/selectTecList?selectType=" + selectType;
                if (!(level === undefined) && level != null) {
                    url += "&level=" + level;
                }
                if (!(showLevel === undefined) && showLevel != null) {
                    url += "&showLevel=" + showLevel;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                if (!(type === undefined) && type != null) {
                    url += "&type=" + type;
                }
                url += "&values=" + $("#" + orgId).val();

                //debugger
                var options = {
                    title: '选择技术分类',
                    width: "580",
                    height: '450',
                    url: url,
                    callBack: choiceTechnicalCallback
                };
                $.modal.openOptions(options);
            }

            function choiceTechnicalCallback(index, layero) {
                var tree = layero.find("iframe")[0].contentWindow.$._tree;
                var body = layer.getChildFrame('body', index);
                layero.find("iframe")[0].contentWindow.saveCheck();

                $("#" + orgId).val(body.find('#treeId').val());
                $("#" + orgNameId).val(body.find('#treeName').val());
                layer.close(index);
            }
        </script>
    </div>
</div>