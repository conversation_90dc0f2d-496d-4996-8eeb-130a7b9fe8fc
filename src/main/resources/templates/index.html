<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="renderer" content="webkit">
<title th:text="${projectCname}"></title>
<!-- 避免IE使用兼容模式 -->
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<link th:href="@{/css/bootstrap.min.css}" rel="stylesheet" />
<link th:href="@{/css/metisMenu.css}" rel="stylesheet" />
<link th:href="@{/css/jquery.contextMenu.min.css}" rel="stylesheet" />
<link th:href="@{/css/font-awesome.min.css}" rel="stylesheet" />
<link th:href="@{/css/animate.min.css}" rel="stylesheet" />
<link th:href="@{/css/style.min.css}" rel="stylesheet" />
<link th:href="@{/css/skins.css}" rel="stylesheet" />
<link th:href="@{/ruoyi/css/ry-ui.css?v=4.6.1}" rel="stylesheet" />
<link rel="stylesheet" th:href="@{/ajax/libs/layer/theme/default/layer.css}" id="layuicss-layer">
	<style type="text/css">
		.slimScrollDiv {
			width: 200px;
			height: 200px;
		}

		.slimScrollDiv {
			position: relative;
			overflow: hidden;
		}

		#side-menu {
			position: absolute;
			left: 0;
			overflow-x: hidden;
			overflow-y: scroll;
		}

		#side-menu::-webkit-scrollbar {
			display: none;
		}
		.nav>li{
			width: 201px;
		}
		.xtfc{
			color: #fff;
			padding: 2px 12px;
			margin: 11px 5px 5px 15px;
			font-size: 17px;
			float: left;
			/*font-family: 黑体;*/
			font-family: "PingFang SC", "Lantinghei SC", "Microsoft YaHei", "HanHei SC", "Helvetica Neue", "Open Sans", "Hiragino Sans GB", 微软雅黑, STHeiti, "WenQuanYi Micro Hei", Arial, SimSun, sans-serif;
		}
	</style>
</head>
<body class="fixed-sidebar full-height-layout gray-bg theme-blue skin-blue" style="overflow: hidden;">
<div id="wrapper">
	<!--左侧导航开始-->
	<nav class="navbar-default navbar-static-side" role="navigation">
		<div class="nav-close">
			<i class="fa fa-times-circle"></i>
		</div>
		<a href="#">
			<li class="logo hidden-xs" style="width: 200px;">
					<span class="logo-lg">
						<img th:src="@{/img/logo0001.png}" style="width: 60%;">
					</span>
			</li>
		</a>
		<div class="slimScrollDiv" style="position: relative; width: auto; height: 96%;">
			<div class="sidebar-collapse tab-content" id="side-menu" style="width: auto; height: 96%;">
				<!-- 左侧菜单 -->
				<div class="tab-pane fade height-full active in" id="menu_1" >
					<ul class="nav in" style="height: auto;">
						<li>
							<a class="menuItem" th:href="@{/}+'iPlatV6-index.jsp'">
								<i class="fa fa-home"></i>
								<span class="nav-label">首页</span>
							</a>
						</li>
						<li th:each="menu : ${@indexMenu.getMyChildMenu('ZZZC')}">
							<a href="#" th:onclick="getSubMenu([[${menu.label}]], 0)" aria-expanded="true">
								<i class="iconIndex"></i>
								<span class="nav-label" th:text="${menu.text}">一级菜单</span>
								<span class="fa arrow"></span>
							</a>
							<ul th:class="'menuLabel_'+${menu.label} +' nav nav-second-level'"></ul>
						</li>
					</ul>
				</div>
			</div>
			<div class="slimScrollBar" style="background: rgb(0, 0, 0); width: 2px; position: absolute; top: 0px; opacity: 0.4; display: none; border-radius: 7px; z-index: 99; right: 1px; height: 826px;"></div>
			<div class="slimScrollRail" style="width: 2px; height: 100%; position: absolute; top: 0px; display: none; border-radius: 7px; background: rgb(51, 51, 51); opacity: 0.9; z-index: 90; right: 1px;"></div>
		</div>
	</nav>
	<!--左侧导航结束-->

	<!--右侧部分开始-->
	<div id="page-wrapper" class="gray-bg dashbard-1">
		<div class="row border-bottom">
			<nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
				<div class="navbar-header">
					<a class="navbar-minimalize minimalize-styl-2" style="color: #ffffff;" href="#" title="收起菜单">
						<i class="fa fa-outdent"></i>
					</a>
				</div>
				<div class="navbar-header">
					<span class="xtfc" th:text="${projectCname}"></span>
				</div>
				<ul class="nav navbar-top-links navbar-right welcome-message">
					<li class="dropdown user-menu" style="width: 161px;">
						<a href="javascript:void(0)" class="dropdown-toggle" data-hover="dropdown" style="text-align: center;">
							<i class="fa fa-lock"></i>
							<span class="hidden-xs" th:utext="${loginCName}"></span>
						</a>
						<ul class="dropdown-menu">
							<li class="mt5">
								<a th:href="${@EPlatUserUtil.getLogoutUrl()}">
									<i class="fa fa-sign-out"></i>
									退出登录
								</a>
							</li>
						</ul>
					</li>
				</ul>
				<!-- 顶部栏 -->
				<div id="navMenu">
					<ul class="nav navbar-toolbar nav-tabs navbar-left hidden-xs">
						<li class="dropdown hidden" role="presentation" style="opacity: 1;">
							<a class="dropdown-toggle" data-toggle="dropdown" href="#" aria-expanded="false">
								<i class="glyphicon glyphicon-align-justify"></i>
								<b class="caret"></b>
							</a>
							<ul class="dropdown-menu" role="menu"></ul>
						</li>
					</ul>
				</div>
			</nav>
		</div>
		<div class="row content-tabs">
			<button class="roll-nav roll-left tabLeft">
				<i class="fa fa-backward"></i>
			</button>
			<nav class="page-tabs menuTabs">
				<div class="page-tabs-content" style="margin-left: 0px;">
					<a href="javascript:;" class="menuTab active" th:data-id="@{/}+'iPlatV6-index.jsp'">首页</a>
				</div>
			</nav>
			<button class="roll-nav roll-right tabRight">
				<i class="fa fa-forward"></i>
			</button>
			<a href="javascript:void(0);" class="roll-nav roll-right tabReload">
				<i class="fa fa-refresh"></i>
				刷新
			</a>
		</div>

		<a id="ax_close_max" class="ax_close_max" href="#" title="关闭全屏"> <i class="fa fa-times-circle-o">关闭全屏</i> </a>

		<div class="row mainContent" id="content-main">
			<iframe class="RuoYi_iframe" name="iframe0" id="indexIframe" width="100%" height="100%" th:data-id="@{/}+'iPlatV6-index.jsp'" th:src="@{/}+'iPlatV6-index.jsp'" frameborder="0" seamless=""></iframe>
		</div>

		<div class="footer">
			<div class="pull-right">版权所有：鄂钢集团|科技管理系统</div>
		</div>
	</div>
	<!--右侧部分结束-->
</div>
<!-- 全局js -->
<script th:src="@{/js/jquery.min.js}"></script>
<script th:src="@{/js/bootstrap.min.js}"></script>
<script th:src="@{/js/plugins/metisMenu/3.0.6/metisMenu.js}"></script>
<script th:src="@{/js/plugins/slimscroll/jquery.slimscroll.min.js}"></script>
<script th:src="@{/js/jquery.contextMenu.min.js}"></script>
<script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script th:src="@{/ruoyi/js/ry-ui.js}"></script>
<script th:src="@{/ruoyi/index.js}"></script>
<script th:src="@{/ruoyi/js/common.js}"></script>
<script th:src="@{/ajax/libs/fullscreen/jquery.fullscreen.js}"></script>
<script th:src="@{/js/resize-tabs.js}"></script>
<script>
	window.history.forward(1);
	var ctx = "[[@{/}]]";
	var lockscreen = null;
	if (lockscreen) {
		window.top.location = ctx + "lockscreen";
	}
	// 皮肤缓存
	var skin = storage.get("skin");
	// history（表示去掉地址的#）否则地址以"#"形式展示
	var mode = "history";
	// 历史访问路径缓存
	var historyPath = storage.get("historyPath");
	// 是否页签与菜单联动
	var isLinkage = true;

	// 本地主题优先，未设置取系统配置
	if ($.common.isNotEmpty(skin)) {
		$("body").addClass(skin.split('|')[0]);
		$("body").addClass(skin.split('|')[1]);
	} else {
		$("body").addClass("theme-blue");
		$("body").addClass("skin-blue");
	}

	function getSubMenu(menuLabel, level) {
		if ($(".menuLabel_" + menuLabel).html().length == 0) {
			menuLabel = menuLabel.trim();
			var url = ctx + 'index/getMyChildMenu/' + menuLabel;
			$(".menuLabel_" + menuLabel).html("数据加载中...请稍后!");//length 12
			$.ajax({
				type : "get",
				url : url,
				cache : false,
				success : function(result) {
					var subMenu = '';
					result.forEach(function(item,index,array) {
						var nodeUrl = item.nodeUrl;
						var sLabel = item.label.trim();
						var newLevel = level+1;
						var sLeaf = item.leaf;
						if (sLeaf == 1) {//叶子节点
							if(""==nodeUrl.trim()){
								nodeUrl = "web/"+sLabel;
							}else{
								if (nodeUrl.indexOf("/")==0) {
									nodeUrl = nodeUrl.substring(1);
								}
							}
							subMenu += '<li class="mm-active">';
							if (nodeUrl.indexOf("http://")==-1) {
								subMenu += '<a href="' + ctx + nodeUrl + '" class="menuItem" aria-expanded="true" aria-disabled="true" data-refresh="true" title="' + item.text + '">' + item.text + '</a>';
							} else {
								subMenu += '<a href="'+nodeUrl+'" target="_blank" aria-expanded="true" aria-disabled="true" data-refresh="true" title="' + item.text + '">' + item.text + '</a>';
							}
							subMenu += '</li>';
						} else if(level==0) {//1级节点
							subMenu += '<li>';
							subMenu += $.common.sprintf("<a href='#'\"' onclick=\"getSubMenu('%s',%s)\" aria-expanded=\"false\">", sLabel, newLevel);
							//subMenu += "<a href="#" onclick=\"getSubMenu(\''+ sLabel + '\',' + newLevel + ')\" aria-expanded="false">';
							subMenu += '<span class="nav-label">' + item.text + '</span>';
							subMenu += '<span class="fa arrow"></span>';
							subMenu += '</a>';
							subMenu += '<ul class="menuLabel_'+sLabel+' nav nav-third-level"></ul>';
							subMenu += '</li>';
						} else {
							subMenu += '<li>';
							subMenu += $.common.sprintf("<a href='#'\"' onclick=\"getSubMenu('%s',%s)\" aria-expanded=\"false\">", sLabel, newLevel);
							subMenu += '<span class="nav-label">' + item.text + '</span>';
							subMenu += '<span class="fa arrow"></span>';
							subMenu += '</a>';
							let paddingLeft = level*20;
							subMenu += '<ul class="menuLabel_'+sLabel+' nav" style="padding-left: '+paddingLeft+'px"></ul>';
							subMenu += '</li>';
						}
					});
					$(".menuLabel_" + menuLabel).html(subMenu);
					//target iframe
					$(".menuLabel_" + menuLabel + " .menuItem").on('click', menuItem);
					$(".menuLabel_" + menuLabel).parent().metisMenu();//渲染生成的新菜单
				}
			});
		}
	}

	function menuItem() {
		$('.RuoYi_iframe').each(function() {
			if($(this).css("display")!='none'){
				var scrollTop= $(this).contents().scrollTop();
				$(this).attr("data-scrollTop",scrollTop);
			}
		});
		// 获取标识数据
		var dataUrl = $(this).attr('href'), dataIndex = $(this).data('index'),
				menuName = $.trim($(this).text()), isRefresh = $(this).data("refresh"), flag = true;

		var $dataObj = $('a[href$="' + decodeURI(dataUrl) + '"]');
		if (!$dataObj.hasClass("noactive")) {
			$('.tab-pane li').removeClass("active");
			$('.nav ul').removeClass("in");
			$dataObj.parents("ul").addClass("in")
			$dataObj.parents("li").addClass("active").siblings().removeClass(
					"active").find('li').removeClass("active");
			$dataObj.parents("ul").css('height', 'auto').height();
			$(".nav ul li, .nav li").removeClass("selected");
			$(this).parent("li").addClass("selected");
		}
		setIframeUrl(dataUrl);
		if (dataUrl == undefined || $.trim(dataUrl).length == 0)
			return false;

		// 选项卡菜单已存在
		$('.menuTab').each(function() {
			if ($(this).data('id') == dataUrl) {
				if (!$(this).hasClass('active')) {
					$(this).addClass('active').siblings('.menuTab')
							.removeClass('active');
					scrollToTab(this);
					// 显示tab对应的内容区
					$('.mainContent .RuoYi_iframe').each(
							function() {
								if ($(this).data('id') == dataUrl) {
									$(this).show().siblings(
											'.RuoYi_iframe').hide();
									return false;
								}
							});
				}
				if (isRefresh) {
					refreshTab();
				}
				flag = false;
				return false;
			}
		});
		// 选项卡菜单不存在
		if (flag) {
			var str = '<a href="javascript:;" class="active menuTab" data-id="' + dataUrl + '">'
					+ menuName + ' <i class="fa fa-times-circle"></i></a>';
			$('.menuTab').removeClass('active');

			// 添加选项卡对应的iframe
			var str1 = '<iframe class="RuoYi_iframe" name="iframe' + dataIndex
					+ '" width="100%" height="100%" src="' + dataUrl
					+ '" frameborder="0" data-id="' + dataUrl
					+ '" seamless></iframe>';
			$('.mainContent').find('iframe.RuoYi_iframe').hide().parents(
					'.mainContent').append(str1);

			$.modal.loading("数据加载中，请稍后...");

			$('.mainContent iframe:visible').load(function() {
				$.modal.closeLoading();
			});

			// 添加选项卡
			$('.menuTabs .page-tabs-content').append(str);
			scrollToTab($('.menuTab.active'));
		}
		return false;
	}

	/* 用户管理-重置密码 */
	function resetPwd() {
		var url = ctx + 'system/user/profile/resetPwd';
		$.modal.open("重置密码", url, '770', '380');
	}
	/* 切换主题 */
	function switchSkin() {
		layer.open({
			type : 2,
			shadeClose : true,
			title : "切换主题",
			area : [ "530px", "386px" ],
			content : [ ctx + "system/switchSkin", 'no' ]
		})
	}

	/* 切换菜单 */
	function toggleMenu() {
		$.modal.confirm("确认要切换成左侧菜单吗？", function() {
			$.get(ctx + 'system/menuStyle/default', function(result) {
				window.location.reload();
			});
		})
	}
	/** 刷新时访问路径页签 */
	function applyPath(url) {
		var $dataObj = $('a[href$="' + decodeURI(url) + '"]');
		$dataObj.click();
		if (!$dataObj.hasClass("noactive")) {
			$dataObj.parent("li").addClass("selected").parents("li").addClass(
					"active").end().parents("ul").addClass("in");
		}
		// 顶部菜单同步处理
		var tabStr = $dataObj.parents(".tab-pane").attr("id");
		if ($.common.isNotEmpty(tabStr)) {
			var sepIndex = tabStr.lastIndexOf('_');
			var menuId = tabStr.substring(sepIndex + 1, tabStr.length);
			$("#tab_" + menuId + " a").click();
		}
	}

	$(function() {
		var lockPath = storage.get('lockPath');
		if ($.common.equals("history", mode)
				&& window.performance.navigation.type == 1) {
			var url = storage.get('publicPath');
			if ($.common.isNotEmpty(url)) {
				applyPath(url);
			} else {
				$(".navbar-toolbar li a").eq(0).click();
			}
		} else if ($.common.isNotEmpty(lockPath)) {
			applyPath(lockPath);
			storage.remove('lockPath');
		} else {
			var hash = location.hash;
			if ($.common.isNotEmpty(hash)) {
				var url = hash.substring(1, hash.length);
				applyPath(url);
			} else {
				if ($.common.equals("history", mode)) {
					storage.set('publicPath', "");
				}
				$(".navbar-toolbar li a").eq(0).click();
			}
		}

		/* 初始密码提示 */
		if (false) {
			layer.confirm("您的密码还是初始密码，请修改密码！", {
				icon : 0,
				title : "安全提示",
				btn : [ '确认', '取消' ],
				offset : [ '30%' ]
			}, function(index) {
				resetPwd();
				layer.close(index);
			});
		}

		/* 过期密码提示 */
		if (false) {
			layer.confirm("您的密码已过期，请尽快修改密码！", {
				icon : 0,
				title : "安全提示",
				btn : [ '确认', '取消' ],
				offset : [ '30%' ]
			}, function(index) {
				resetPwd();
				layer.close(index);
			});
		}
	});


	function initIcon(){

		$.getJSON("[[@{/icon/icon.json}]]", function(data) {
			// var length=data.length;
			var dataIcon=["fa fa-folder","fa fa-file-text","fa fa-book","fa fa-pie-chart","fa fa-cube"];
			var i=0;
			$(".iconIndex").each(function(index,value){
				// var i =Math.round(Math.random()*length);
				$(this).removeClass();
				$(this).addClass(dataIcon[i++])
				$(this).addClass("iconIndex")
			})
		});
	}
	initIcon();
</script>
<ul class="context-menu-list context-menu-root" style="display: none;">
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-close">
		<span>关闭当前</span>
	</li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-window-close-o">
		<span>关闭其他</span>
	</li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-reply">
		<span>关闭左侧</span>
	</li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-share">
		<span>关闭右侧</span>
	</li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-window-close">
		<span>全部关闭</span>
	</li>
	<li class="context-menu-item context-menu-separator context-menu-not-selectable"></li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-arrows-alt">
		<span>全屏显示</span>
	</li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-refresh">
		<span>刷新页面</span>
	</li>
	<li class="context-menu-item context-menu-icon context-menu-icon--fa fa fa-link">
		<span>新窗口打开</span>
	</li>
</ul>
</body>
</html>