.tabs-container .nav-tabs > li.active > a, .tabs-container .nav-tabs > li.active > a:hover, .tabs-container .nav-tabs > li.active > a:focus {
	border-top: #1c6ac7 solid 4px;
}
.btnwarp {
	width: 40px;
	margin-left: auto;
	margin-right: auto;
	margin-top: 140px;
}
.btnwarp li {
	margin-bottom: 20px;
}
.btnwarp li .fa-angle-double-right {
	font-size: 25px;
	margin-top: 0px;
	margin-left: 1px;
}
.btnwarp li .fa-sign-in {
	margin-top: 2px;
	margin-left: -1px;
	font-size: 23px;
}
.btnwarp li .fa-chevron-right {
	font-size: 20px;
	margin-top: 3px;
	margin-left: 2px;
}
.btnwarp li .fa-angle-double-left {
	font-size: 25px;
	margin-top: 0px;
	margin-left: -3px;
}
.btnwarp li .fa-chevron-left {
	font-size: 20px;
	margin-right: 4px;
	margin-top: 3px;
}
.add-content {
	height: 444px;
	margin-top: 45px;
}
.btn-circle {
	width: 40px;
	height: 40px;
	padding: 6px 0;
	border-radius: 50%;
	text-align: center;
	font-size: 12px;
	line-height: 1.428571429;
}
.add-content1 {
	height: 444px;
}
.form-group {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	border: 0px solid #e5e6e7;
}
.form-search .form-control {
	border-left: 0px solid #dadada;
	border-bottom: 1px solid #dadada;
	border-right: 0px solid #dadada;
	border-radius: 0;
	border-top: 0px solid #dadada;
	margin: 0px;
}
.form-search .btn {
	border-radius: 0px;
	margin: 0px;
	border: 1px solid #2668d4;
}
.form-search .btn-success {
	border-radius: 0;
}
.add-content .treebox {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
}
.form-search {
	width: 100%;
	display: -webkit-flex;
	display: flex;
}
.jsc-container-div .col-md-7 .tabs-container .panel-body {
	background: #fff;
	border-radius: 2px;
	padding-top: 0px;
	padding-right: 15px;
	padding-left: 15px;
	position: relative;
}
.treebox {
	margin-left: 0px;
	margin-right: 0px;
}
.panel-border::before {
	content: "";
	border: 1px solid #e7eaec;
	top: 58px;
	position: absolute;
	width: 97%;
}

.nav-tabs>li>a {
	font-size: 14px;
}

.ztree li span.button.user_ico_docu{background-image: url(../../img/user.png);}

/***新加***/
.returnbox {
	margin-top: 10px;
	margin-left: 10px;
	margin-right: 10px;
	text-align:left ;
}
.returnbox h2 {
	font-size: 14px;
	color: #000;
	margin-bottom: 10px;
	border-bottom: #eee 1px solid;
	height: 40px;
	line-height: 40px;
	font-weight: bold;
}
.returnbox .table-striped table thead {
	background-color: #e5efff;
	color: #000;
	font-size: 14px;
}
.returnbox .table-striped .table>thead>tr>th, .returnbox .table-striped .table>tbody>tr>th {
	border-bottom: 1px solid #ccc !important;
	border-top: 0 !important;
	font-weight: normal;
	font-size: 14px;
}
.returnbox .table-striped table>tbody>tr>td {
	color: #333;
}
.returninput {
	font-size: 14px;
	color: #000;
	margin-top:0px;
	margin-bottom:10px;
}
.returninput input[type=radio] {
	margin: 0px 0 0;
	margin-top: 1px \9;
	line-height: normal;
	vertical-align: middle;
}
.returnlist{ margin-top:0px; margin-bottom:10px; margin-left:15px;}
.returnlist ul {
	margin: 0px;
	padding: 0px;
	color:#000;
}
.returnlist ul input[type=checkbox],.returnlist ul input[type=radio] {
	vertical-align: middle;
	margin: 0px 0 0;
}
.returnbox-tit {
	font-size: 14px;
	background-color: #F9F9F9;
	height: 36px;
	line-height: 36px;
	color: #000;
	padding-left: 10px;
	font-weight: bold;
	text-align:left ;
}
.returnlist span{ margin-right:5px;}
.returnlist ul li{ line-height:28px; height:28px;}