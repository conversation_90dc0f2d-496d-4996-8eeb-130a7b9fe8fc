@charset "UTF-8";
.layui-elem-quote{
    margin: 10px 0;
    padding: 15px 0;
    border: 1px solid #ebebeb;
    border-radius: 6px;
    position: relative;
    line-height: inherit;
}
/*左箭头*/
.leftIcon{
    position: absolute;
    width: 2.5%;
    text-align: center;
    left: 0;
    top: 0;
    background: #fff;
    z-index: 10;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
}
.leftIcon i{
    cursor: pointer;
    font-size: 20px;
    line-height: 152px;
}
/*右箭头*/
.rightIcon{
    position: absolute;
    width: 2.5%;
    text-align: center;
    right: 0;
    top: 0;
    background: #fff;
    z-index: 10;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
}
.rightIcon i{
    cursor: pointer;
    font-size: 20px;
    line-height: 152px;
}
/*图片*/
.layui-upload-list{
    width: fit-content;
    margin: auto;
    /*height: 122px;*/
    overflow: hidden;
    display: flex;
}
.layui-upload-list img{
    width: 122px;
    height: 122px;
    margin: 0 6px;
    border: 1px solid #dcdfe6;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
}
.layui-upload-list img:hover{
    border: 1px solid #c0ccda;
}
.layui-upload-list img:after{
    border: 1px solid red;
}
.clearfix{ *zoom:1;}
.clearfix:after{ clear: both; display: block; content: ""; height: 0; overflow: hidden;}
a{ text-decoration: none; color: #333;}
a:hover{ color: #f00;}
.center{ text-align: center; margin-top: 20px; font-family: "Microsoft Yahei";}
#box{ width: 100%;position: relative; overflow: hidden;}
.content{ margin-left: 3%; width: 100%; height: 122px; overflow: hidden; position: relative;}
#wrap{ position: absolute; left: 0; width: 1500px;}
#wrap ul{ padding: 10px 0;}
#wrap li{ float: left; width: 120px; height: 100px; background: purple; margin-left: 10px;}

.layui-upload-list{
    margin: 0 !important;
}
.layui-elem-quote{

    overflow-x: auto;
    white-space: nowrap;
}
.layui-upload-img{
    width: 122px;
    height: 122px;
    margin: 0 6px;
}
.delete-btn{
    position: relative;
    rigth:10px;
    background: #f4f4f4;
    border-radius: 50%;

}


.layui-table td, .layui-table th{
    width: 150px !important;
    text-align: center;
}
.layui-table td, .layui-table th:nth-child(1){
    width: 200px !important;
    text-align: center;
}
.list-size{
    height: 122px !important;
}
/*.list-border{*/
    /*border: 1px solid #f4f4f4 !important;*/
    /*padding: 10px;*/
    /*width: 100%;*/
/*}*/

.id-input{
    height: 30px;
    margin-bottom: 20px;
    border: 1px solid #ebebeb !important;
}

.table-con{
    width: 80%;
}
.img-box{
    position: relative;
}
.delete-pos{
    color: #ff0000;
    position: absolute;
    top: 1px;
    right: 10px;
    background: #f4f4f4;
    border-radius: 50%;
    padding: 2px;
}
.layui-btn{
    margin-left: 0px !important;
}
.layui-btn:last-child{
    margin-left: 10px !important;
}


