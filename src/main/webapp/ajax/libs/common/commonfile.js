layui.define('upload', function (exports) {
    //var picPrefix = ctx + "tool/file";
	var bizType = $('#businessType').val();
    var upload = layui.upload;
    var obj = {

        tableInit: function () {
            var options = {
                url: ctx+"attachment/list/bizId/bizType",
                method: 'get',
                cache: false,
                height: undefined,
                striped: false,
                toolbar: "#toolbar",
                modalName: "文件",
                columns: [{
                    field: 'selectItem',
                    radio: true
                },
                    {
                        field: "attachmentId",
                        title: "文件id",
                        visible: false
                    },
                    {
                        title: '文件名称',
                        field: 'attachmentName',
                        width: '80%'
                    }
                ]
            };
            $('#file-table').bootstrapTable(options);
        },
        /**
         * 根据不同的前端id渲染上传图片组件
         * @param id
         */
        initImgUpload: function (id) {
            var that = this;
            var previewView = $("#picturePreview-" + id),
                attachmentIdsView = $("#attachmentIds_" + id);
            upload.render({
                elem: '#' + id
                , url: ctx+'attachment/upload'
                , multiple: true
                , choose: function (obj) {
                    var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                    obj.preview(function (index, file, result) {
                        var fileObj = file; //上传文件的对象
                        var reader = new FileReader();
                        reader.readAsDataURL(fileObj);
                        reader.onload = function (e) {
                            var image = new Image();    //新建一个img标签（不嵌入DOM节点，仅做canvas操作)
                            image.src = e.target.result;    //让该标签加载base64格式的原图
                            image.onload = function () {    //图片加载完毕后再通过canvas压缩图片，否则图片还没加载完就压缩，结果图片是全黑的
                                var canvas = document.createElement('canvas'), //创建一个canvas元素
                                    context = canvas.getContext('2d'),    //context相当于画笔，里面有各种可以进行绘图的API
                                    imageWidth = image.width / 2,    //压缩后图片的宽度，这里设置为缩小一半
                                    imageHeight = image.height / 2,    //压缩后图片的高度，这里设置为缩小一半
                                    data = '';    //存储压缩后的图片
                                canvas.width = imageWidth;    //设置绘图的宽度
                                canvas.height = imageHeight;    //设置绘图的高度

                                //使用drawImage重新设置img标签中的图片大小，实现压缩。drawImage方法的参数可以自行查阅W3C
                                context.drawImage(image, 0, 0, imageWidth, imageHeight);
                                //使用toDataURL将canvas上的图片转换为base64格式
                                result = canvas.toDataURL('image/jpeg');
                            }
                        }
                        obj.resetFile(index, result, file.name);
                    })
                }
                , before: function (obj) {
                    obj.preview(function (index, file, result) {
                        var htm1 = "<div class='img-box' id='upload-'>" +
                            "<img data-original='" + result + "'  src='" + result + "'   height=\"300px\" class=\"layui-upload-img\">" +
                            "<i  id='delete-' class=\"fa fa-close delete-pos\"></i></div>";
                        previewView.append(htm1)

                        // $('#picturePreview').append('<img id="upload-'+ index +'" src="' + result + '" onclick="previewImg(\'' + result + '\')"   alt="' + file.name + '" height="300px" class="layui-upload-img">');
                    })
                }
                , done: function (res, index, upload) {
                    console.log(index);
                    var attachmentIds = attachmentIdsView.val();
                    if (attachmentIds === "") {
                        attachmentIds = attachmentIds + res.attachmentId;
                    } else {
                        attachmentIds = attachmentIds + "," + res.attachmentId;
                    }
                    attachmentIdsView.val(attachmentIds);
                    $("#upload-").attr('id', res.attachmentId);
                    $("#delete-").attr('id', 'delete-' + res.attachmentId);
                    var preImgView = $("#" + res.attachmentId + ' ' + 'img');
                    var srcView = preImgView.attr('src');
                    preImgView.click(function () {
                        that.previewImg(this.src,id);
                    });

                    var deleteView = $("#delete-" + res.attachmentId);
                    deleteView.click(function () {
                        console.log(res.attachmentId);
                        that.deleteImg(this.id.substring(7), id);
                    });
                    return delete this.files[index];
                }
            });
        },
        /***
         *  自定义多个按钮公用一个预览框
         * @param id
         */
        userDefinedInitImgUpload: function (id) {
            var that = this;
            var previewView = $("#picturePreview"),
                attachmentIdsView = $("#attachmentIds");
            upload.render({
                elem: '#' + id
                , url: ctx+'attachment/upload'
                , multiple: true
                , choose: function (obj) {
                    var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                    obj.preview(function (index, file, result) {
                        var fileObj = file; //上传文件的对象
                        var reader = new FileReader();
                        reader.readAsDataURL(fileObj);
                        reader.onload = function (e) {
                            var image = new Image();    //新建一个img标签（不嵌入DOM节点，仅做canvas操作)
                            image.src = e.target.result;    //让该标签加载base64格式的原图
                            image.onload = function () {    //图片加载完毕后再通过canvas压缩图片，否则图片还没加载完就压缩，结果图片是全黑的
                                var canvas = document.createElement('canvas'), //创建一个canvas元素
                                    context = canvas.getContext('2d'),    //context相当于画笔，里面有各种可以进行绘图的API
                                    imageWidth = image.width / 2,    //压缩后图片的宽度，这里设置为缩小一半
                                    imageHeight = image.height / 2,    //压缩后图片的高度，这里设置为缩小一半
                                    data = '';    //存储压缩后的图片
                                canvas.width = imageWidth;    //设置绘图的宽度
                                canvas.height = imageHeight;    //设置绘图的高度

                                //使用drawImage重新设置img标签中的图片大小，实现压缩。drawImage方法的参数可以自行查阅W3C
                                context.drawImage(image, 0, 0, imageWidth, imageHeight);
                                //使用toDataURL将canvas上的图片转换为base64格式
                                result = canvas.toDataURL('image/jpeg');
                            }
                        }
                        obj.resetFile(index, result, file.name);
                    })
                }
                , before: function (obj) {
                    obj.preview(function (index, file, result) {
                        var htm1 = "<div class='img-box' id='upload-'>" +
                            "<img data-original='" + result + "' src='" + result + "'   height=\"300px\" class=\"layui-upload-img\">" +
                            "<i  id='delete-' class=\"fa fa-close delete-pos\"></i></div>";
                        previewView.append(htm1)

                        // $('#picturePreview').append('<img id="upload-'+ index +'" src="' + result + '" onclick="previewImg(\'' + result + '\')"   alt="' + file.name + '" height="300px" class="layui-upload-img">');
                    })
                }
                , done: function (res, index, upload) {
                    console.log(index);
                    var attachmentIds = attachmentIdsView.val();
                    if (attachmentIds === "") {
                        attachmentIds = attachmentIds + res.attachmentId;
                    } else {
                        attachmentIds = attachmentIds + "," + res.attachmentId;
                    }
                    attachmentIdsView.val(attachmentIds);
                    $("#upload-").attr('id', res.attachmentId);
                    $("#delete-").attr('id', 'delete-' + res.attachmentId);
                    var preImgView = $("#" + res.attachmentId + ' ' + 'img');
                    var srcView = preImgView.attr('src');
                    preImgView.click(function () {
                        that.previewImg(this.src,'');
                    });

                    var deleteView = $("#delete-" + res.attachmentId);
                    deleteView.click(function () {
                        console.log(res.attachmentId);
                        that.userDefinedDeleteImg(this.id.substring(7));
                    });
                    return delete this.files[index];
                }
            });
        },

        /**
         * 根据不同的id渲染上传文件组件
         * @type {jQuery|HTMLElement}
         */
        initFileUpload: function (id) {
            var demoListView = $('#demoList-' + id),
                attachmentIdsView = $('#attachmentIds_' + id);
            var that = this;
            var id = id;
            var uploadListIns = upload.render({
                elem: '#' + id
                , url: ctx+'attachment/upload'
                , accept: 'file'
                , multiple: true
                , auto: false
                , bindAction: '#action-' + id
                , choose: function (obj) {
                    var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                    //读取本地文件
                    obj.preview(function (index, file, result) {
                        console.log(file)
                        var tr = $(['<tr id="upload-' + index + '">'
                            , '<td>' + file.name + '</td>'
                            , '<td>' + (file.size / 1024).toFixed(1) + 'kb</td>'
                            , '<td>等待上传</td>'
                            , '<td>'
                            , '<button type="button" class="layui-btn layui-btn-xs layui-btn-blue demo-reload layui-hide"><i class="fa fa-history"></i>&nbsp;重传</button>'
                            , '<button type="button" class="layui-btn layui-btn-xs layui-btn-blue demo-delete layui-hide"><i class="fa fa-remove"></i>&nbsp;删除</button>'
                            , '</td>'
                            , '</tr>'].join(''));

                        //单个重传
                        tr.find('.demo-reload').on('click', function () {
                            obj.upload(index, file);
                        });

                        //删除
                        tr.find('.demo-delete').on('click', function () {
                            delete files[index]; //删除对应的文件
                            tr.remove();
                            uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                        });

                        demoListView.append(tr);
                    });
                }
                , done: function (res, index, upload) {
                    console.log(res);
                    if (res.success) { //上传成功
                        var attachmentFileIds = attachmentIdsView.val();
                        if (attachmentFileIds === "") {
                            attachmentFileIds = attachmentFileIds + res.attachmentId;
                        } else {
                            attachmentFileIds = attachmentFileIds + "," + res.attachmentId;
                        }
                        attachmentIdsView.val(attachmentFileIds);
                        console.log('文件id');
                        $('#attachmentFileIds').val(attachmentFileIds);
                        console.log('文件id'+$('#attachmentFileIds').val());
                        var tr = demoListView.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                        var txt1 = '<button type="button" shiro:hasPermission="tool:file:download" class="layui-btn layui-btn-xs layui-btn-blue" id="download-' + res.attachmentId + '" ><i class="fa fa-arrow-down"></i>&nbsp;下载</button> ';
                        var txt2 = '<button type="button" shiro:hasPermission="tool:file:delete" class="layui-btn layui-btn-xs layui-btn-blue" id="delete-' + res.attachmentId + '"><i class="fa fa-remove"></i>&nbsp;删除</button>';
                        var txt3 = '<button type="button" shiro:hasPermission="tool:file:preview" class="layui-btn layui-btn-xs layui-btn-blue" id="preview-' + res.attachmentId + '"><i class="fa fa-file-text"></i>&nbsp;预览</button>';
                        tds.eq(3).append(txt2, txt1, txt3);
                        $('#delete-' + res.fileid).click(function () {
                            that.deleteFile(this.id.substring(7), index,id);
                        });
                        $('#download-' + res.fileid).click(function () {
                            that.downloadFile(this.id.substring(9));
                        });
                        $('#preview-' + res.fileid).click(function () {
                            that.previewFile(this.id.substring(8));
                        });
                        return delete this.files[index]; //删除文件队列已经上传成功的文件
                    }
                    this.error(index, upload);
                }
                , error: function (index, upload) {
                    var tr = demoListView.find('tr#upload-' + index)
                        , tds = tr.children();
                    tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                    tds.eq(3).find('.demo-delete').removeClass('layui-hide'); //显示删除
                    tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
                }
            });

        },

        /**
         * 初始化文件预览
         * @param id 组件id
         * @param bizId 欲展示的id
         */
        fileTableInit: function (id, bizId) {
        	if(bizId==""){
        		bizId="undefined";
        	}
            var url = ctx+"attachment/list/" + bizId + "/";
            var attachmentIds = "";
            var attachmentIdsView = $('#attachmentIds_' + id);
            var that = this;
            $.ajax({
                url: url,
                methods: "get",
                success: function (res) {
                    for (i = 0, len = res.length; i < len; i++) {
                        var attachmentId = res[i].fileid;
                        if (attachmentIds === "") {
                            attachmentIds = attachmentIds + attachmentId;
                        } else {
                            attachmentIds = attachmentIds + "," + attachmentId;
                        }
                        attachmentIdsView.val(attachmentIds);
                        var tr = $(['<tr id="upload-' + attachmentId + '" >'
                            , '<td>' + res[i].attachmentName + '</td>'
                            , '<td>' + (res[i].fileSize) + '</td>'
                            , '<td>已上传</td>'
                            , '<td>'
                            , '<button type="button" shiro:hasPermission="tool:file:delete" class="layui-btn layui-btn-xs layui-btn-blue" id="delete-' + attachmentId + '"><i class="fa fa-remove"></i>&nbsp;删除</button>'
                            , '<button type="button" shiro:hasPermission="tool:file:download" class="layui-btn layui-btn-xs layui-btn-blue" id="download-' + attachmentId + '" ><i class="fa fa-arrow-down"></i>&nbsp;下载</button> '
                            , '<button type="button" shiro:hasPermission="tool:file:preview" class="layui-btn layui-btn-xs layui-btn-blue" id="preview-' + attachmentId + '" ><i class="fa fa-file-text"></i>&nbsp;预览</button> '
                            , '</td>'
                            , '</tr>'].join(''));
                        $('#demoList-' + id).append(tr);
                        $('#delete-' + attachmentId).click(function () {
                            that.deleteFile(this.id.substring(7), this.id.substring(7),id);
                        });
                        $('#download-' + attachmentId).click(function () {
                            that.downloadFile(this.id.substring(9));
                        });
                        $('#preview-' + attachmentId).click(function () {
                            that.previewFile(this.id.substring(8));
                        });
                    }

                }
            });
        },

        /**
         * 自定义多个按钮公用一个预览框
         * @param id
         * @param 欲加载的bizId
         */
        userDefinedImageInit: function (bizId) {
        	if(bizId==""){
        		bizId="undefined";
        	}
            var url = ctx+"attachment/list/" + bizId + "/";
            var attachmentIdsView = $('#attachmentIds');
            var attachmentIds = "";
            var that = this;
            $.ajax({
                url: url,
                methods: "get",
                success: function (res) {
                    console.log(res);
                    var len = res.length;
                    for (var i = 0; i < len; i++) {
                        var attachmentId = res[i].fileid;
                        console.log('图片'+attachmentId);
                        if (attachmentIds === "") {
                            attachmentIds = attachmentIds + attachmentId;
                        } else {
                            attachmentIds = attachmentIds + "," + attachmentId;
                        }
                        attachmentIdsView.val(attachmentIds);
                        var imgUrl = ctx+"attachment/download/" + attachmentId;
                        var htm1 = "<div class='img-box' id='upload-'>" +
                            "<img data-original='" + imgUrl + "' src='" + imgUrl + "'   height=\"300px\" class=\"layui-upload-img\">" +
                            "<i  id='delete-" + attachmentId + "' class=\"fa fa-close delete-pos\"></i></div>";
                        $('#picturePreview').append(htm1);
                        // $("#delete-").attr('id', 'delete-' + attachmentId);
                        $("#upload-").attr('id', attachmentId);
                        var preImgView = $("#" + attachmentId + ' ' + 'img');
                        preImgView.click(function () {
                            console.log(this.src)
                            that.previewImg(this.src,'');
                        });
                        var deleteView = $("#delete-" + attachmentId);
                        deleteView.on('click',function () {
                            console.log(this.id.substring(7))
                            that.userDefinedDeleteImg(this.id.substring(7));
                        })
                    }
                },
            })
        },

        /***
         * 初始化图片预览
         * @param id
         */
        imageInit: function (id, bizId) {
        	if(bizId==""){
        		bizId="undefined";
        	}
            var url = ctx+"attachment/list/" + bizId + "/";
            var attachmentIdsView = $('#attachmentIds_' + id);
            var attachmentIds = "";
            var that = this;
            $.ajax({
                url: url,
                methods: "get",
                success: function (res) {
                    console.log(res);
                    var len = res.length;
                    for (var i = 0; i < len; i++) {
                        var attachmentId = res[i].fileid;
                        console.log('图片'+attachmentId);
                        if (attachmentIds === "") {
                            attachmentIds = attachmentIds + attachmentId;
                        } else {
                            attachmentIds = attachmentIds + "," + attachmentId;
                        }
                        attachmentIdsView.val(attachmentIds);
                        var imgUrl = ctx+"attachment/download/" + attachmentId;
                        var htm1 = "<div class='img-box' id='upload-'>" +
                            "<img data-original='" + imgUrl + "' src='" + imgUrl + "'   height=\"300px\" class=\"layui-upload-img\">" +
                            "<i  id='delete-" + attachmentId + "' class=\"fa fa-close delete-pos\"></i></div>";
                        $('#picturePreview-' + id).append(htm1);
                        // $("#delete-").attr('id', 'delete-' + attachmentId);
                        $("#upload-").attr('id', attachmentId);
                        var preImgView = $("#" + attachmentId + ' ' + 'img');
                        preImgView.click(function () {
                            console.log(this.src)
                            that.previewImg(this.src,id);
                        });
                        var deleteView = $("#delete-" + attachmentId);
                        deleteView.on('click',function () {
                            console.log(this.id.substring(7))
                            that.deleteImg(this.id.substring(7), id);
                        })
                    }
                },
            })
        },
        /**
         * 根据不同的前端id渲染上传图片组件
         * @param id
         */
        initImgUploadByType: function (id) {
            var that = this;
            var previewView = $("#picturePreview-" + id),
                attachmentIdsView = $("#attachmentIds_" + id);
            var id = id;
            upload.render({
                elem: '#' + id
                , url: ctx+'attachment/upload'
                , multiple: true
                , choose: function (obj) {
                    var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                    obj.preview(function (index, file, result) {
                        var fileObj = file; //上传文件的对象
                        var reader = new FileReader();
                        reader.readAsDataURL(fileObj);
                        reader.onload = function (e) {
                            var image = new Image();    //新建一个img标签（不嵌入DOM节点，仅做canvas操作)
                            image.src = e.target.result;    //让该标签加载base64格式的原图
                            image.onload = function () {    //图片加载完毕后再通过canvas压缩图片，否则图片还没加载完就压缩，结果图片是全黑的
                                var canvas = document.createElement('canvas'), //创建一个canvas元素
                                    context = canvas.getContext('2d'),    //context相当于画笔，里面有各种可以进行绘图的API
                                    imageWidth = image.width / 2,    //压缩后图片的宽度，这里设置为缩小一半
                                    imageHeight = image.height / 2,    //压缩后图片的高度，这里设置为缩小一半
                                    data = '';    //存储压缩后的图片
                                canvas.width = imageWidth;    //设置绘图的宽度
                                canvas.height = imageHeight;    //设置绘图的高度

                                //使用drawImage重新设置img标签中的图片大小，实现压缩。drawImage方法的参数可以自行查阅W3C
                                context.drawImage(image, 0, 0, imageWidth, imageHeight);
                                //使用toDataURL将canvas上的图片转换为base64格式
                                result = canvas.toDataURL('image/jpeg');
                            }
                        }
                        obj.resetFile(index, result, file.name);
                    })
                }
                , before: function (obj) {
                    obj.preview(function (index, file, result) {
                        var htm1 = "<div class='img-box' id='upload-'>" +
                            "<img data-original='" + result + "' src='" + result + "'   height=\"300px\" class=\"layui-upload-img\">" +
                            "<i  id='delete-' class=\"fa fa-close delete-pos\"></i></div>";
                        previewView.append(htm1);

                        // $('#picturePreview').append('<img id="upload-'+ index +'" src="' + result + '" onclick="previewImg(\'' + result + '\')"   alt="' + file.name + '" height="300px" class="layui-upload-img">');
                    })
                }
                , done: function (res, index, upload) {
                    console.log(res);
                    var attachmentIds = attachmentIdsView.val();
                    if (attachmentIds === "") {
                        attachmentIds = attachmentIds + res.attachmentId;
                    } else {
                        attachmentIds = attachmentIds + "," + res.attachmentId;
                    }
                    attachmentIdsView.val(attachmentIds);
                    $("#upload-").attr('id', res.attachmentId);
                    $("#delete-").attr('id', 'delete-' + res.attachmentId);
                    var preImgView = $("#" + res.attachmentId + ' ' + 'img');
                    var srcView = preImgView.attr('src');
                    preImgView.click(function () {
                        that.previewImg(this.src,id);
                    });


                    var deleteView = $("#delete-" + res.attachmentId);
                    deleteView.click(function () {
                        console.log(res.attachmentId);
                        that.deleteImg(this.id.substring(7), id);
                    });
                    return delete this.files[index];
                }
            });
        },

        /**
         * 根据不同的id渲染上传文件组件
         * @type {jQuery|HTMLElement}
         */
        initFileUploadByType: function (id) {
            var demoListView = $('#demoList-' + id),
                attachmentIdsView = $('#attachmentIds_' + id);
            var that = this;
            var id = id;
            var uploadListIns = upload.render({
                elem: '#' + id
                , url: ctx+'attachment/upload'
                , accept: 'file'
                , multiple: true
                , auto: false
                , bindAction: '#action-' + id
                , choose: function (obj) {
                    var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                    //读取本地文件
                    obj.preview(function (index, file, result) {
                        console.log(file)
                        var tr = $(['<tr id="upload-' + index + '">'
                            , '<td class="name-width">' + file.name + '</td>'
                            , '<td>' + (file.size / 1024).toFixed(1) + 'kb</td>'
                            , '<td>等待上传</td>'
                            , '<td>'
                            , '<button type="button" class="layui-btn layui-btn-xs layui-btn-blue demo-reload layui-hide"><i class="fa fa-history"></i>&nbsp;重传</button>'
                            , '<button type="button" class="layui-btn layui-btn-xs layui-btn-blue demo-delete layui-hide"><i class="fa fa-remove"></i>&nbsp;删除</button>'
                            , '</td>'
                            , '</tr>'].join(''));

                        //单个重传
                        tr.find('.demo-reload').on('click', function () {
                            obj.upload(index, file);
                        });

                        //删除
                        tr.find('.demo-delete').on('click', function () {
                            delete files[index]; //删除对应的文件
                            tr.remove();
                            uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                        });

                        demoListView.append(tr);
                    });
                }
                , done: function (res, index, upload) {
                    if (res.success) { //上传成功
                        var attachmentFileIds = attachmentIdsView.val();
                        if (attachmentFileIds === "") {
                            attachmentFileIds = attachmentFileIds + res.fileid;
                        } else {
                            attachmentFileIds = attachmentFileIds + "," + res.fileid;
                        }
                        attachmentIdsView.val(attachmentFileIds);
                        var tr = demoListView.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                        var txt1 = '<button type="button" shiro:hasPermission="tool:file:download" class="layui-btn layui-btn-xs layui-btn-blue" id="download-' + res.fileid + '" ><i class="fa fa-arrow-down"></i>&nbsp;下载</button> ';
                        var txt2 = '<button type="button" shiro:hasPermission="tool:file:delete" class="layui-btn layui-btn-xs layui-btn-blue" id="delete-' + res.fileid + '"><i class="fa fa-remove"></i>&nbsp;删除</button>';
                        var txt3 = '<button type="button" shiro:hasPermission="tool:file:preview" class="layui-btn layui-btn-xs layui-btn-blue" id="preview-' + res.fileid + '"><i class="fa fa-file-text"></i>&nbsp;预览</button>';
                        tds.eq(3).append(txt2, txt1, txt3);
                        $('#delete-' + res.fileid).click(function () {
                            that.deleteFile(this.id.substring(7), index, id);
                        });
                        $('#download-' + res.fileid).click(function () {
                            that.downloadFile(this.id.substring(9));
                        });
                        $('#preview-' + res.fileid).click(function () {
                            that.previewFile(this.id.substring(8));
                        });
                        return delete this.files[index]; //删除文件队列已经上传成功的文件
                    }
                    this.error(index, upload);
                }
                , error: function (index, upload) {
                    var tr = demoListView.find('tr#upload-' + index)
                        , tds = tr.children();
                    tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                    tds.eq(3).find('.demo-delete').removeClass('layui-hide'); //显示删除
                    tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
                }
            });

        },
        /**
         * 初始化文件预览
         * @param id 组件id
         * @param bizId 欲展示的id
         */
        fileTableInitByType: function (id, bizId, bizType) {
        	if(bizId==""){
        		bizId="undefined";
        	}
            var url = ctx+"attachment/list/" + bizId + "/" + bizType;
            var attachmentIds = "";
            var attachmentIdsView = $('#attachmentIds_' + id);
            var that = this;
            $.ajax({
                url: url,
                methods: "get",
                success: function (res) {
                    for (i = 0, len = res.length; i < len; i++) {
                        var attachmentId = res[i].fileid;
                        if (attachmentIds == "") {
                            attachmentIds = attachmentIds + attachmentId;
                        } else {
                            attachmentIds = attachmentIds + "," + attachmentId;
                        }
                        attachmentIdsView.val(attachmentIds);
                        var tr = $(['<tr class="table-item" id="upload-' + attachmentId + '" >'
                            , '<td class="name-labelwidth">' + res[i].attachmentName + '</td>'
                            , '<td>' + (res[i].fileSize) + '</td>'
                            , '<td>已上传</td>'
                            , '<td>'
                            , '<button type="button" shiro:hasPermission="tool:file:delete" class="layui-btn layui-btn-xs layui-btn-blue" id="delete-' + attachmentId + '"><i class="fa fa-remove"></i>&nbsp;删除</button>'
                            , '<button type="button" shiro:hasPermission="tool:file:download" class="layui-btn layui-btn-xs layui-btn-blue" id="download-' + attachmentId + '" ><i class="fa fa-arrow-down"></i>&nbsp;下载</button> '
                            , '<button type="button" shiro:hasPermission="tool:file:preview" class="layui-btn layui-btn-xs layui-btn-blue" id="preview-' + attachmentId + '" ><i class="fa fa-file-text"></i>&nbsp;预览</button> '
                            , '</td>'
                            , '</tr>'].join(''));
                        $('#demoList-' + id).append(tr);
                        $('#delete-' + attachmentId).click(function () {
                            console.log(this.id.substring(7))
                            that.deleteFile(this.id.substring(7), this.id.substring(7),id);
                        });
                        $('#download-' + attachmentId).click(function () {
                            that.downloadFile(this.id.substring(9));
                        });
                        $('#preview-' + attachmentId).click(function () {
                            that.previewFile(this.id.substring(8));
                        });
                    }

                }
            });
        },

        /**
         * 初始化图片预览
         * @param id
         * @param 欲加载的bizId
         */
        imageInitByType: function (id, bizId, bizType) {
        	if(bizId==""){
        		bizId="undefined";
        	}
            var url = ctx+"attachment/list/" + bizId + "/" + bizType;
            var attachmentIdsView = $('#attachmentIds_' + id);
            var attachmentIds = "";
            var that = this;
            $.ajax({
                url: url,
                methods: "get",
                success: function (res) {
                    for (i = 0, len = res.length; i < len; i++) {
                        var attachmentId = res[i].fileid;
                        if (attachmentIds === "") {
                            attachmentIds = attachmentIds + attachmentId;
                        } else {
                            attachmentIds = attachmentIds + "," + attachmentId;
                        }
                        attachmentIdsView.val(attachmentIds);
                        var imgUrl = ctx+"attachment/download/" + attachmentId;
                        var htm1 = "<div class='img-box' id='upload-'>" +
                            "<img data-original='" + imgUrl + "' src='" + imgUrl + "'   height=\"300px\" class=\"layui-upload-img\">" +
                            "<i  id='delete-' class=\"fa fa-close delete-pos\"></i></div>";
                        $('#picturePreview-' + id).append(htm1);
                        $("#delete-").attr('id', 'delete-' + attachmentId);
                        $("#upload-").attr('id', attachmentId);
                        var preImgView = $("#" + attachmentId + ' ' + 'img');
                        preImgView.click(function () {
                            console.log(this.src)
                            that.previewImg(this.src,id);
                        });
                        var deleteView = $("#delete-" + attachmentId);
                        deleteView.click(function () {
                            that.deleteImg(this.id.substring(7), id);
                        })
                    }
                },
            })
        },

        /**
         * 下载图片
         * @param attachmentId 图片id
         */
        downloadFile: function (attachmentId) {
            //console.log(attachmentId);
            window.open(ctx+"attachment/download/" + attachmentId);
        },

        /**
         * 预览
         * @param attachmentId 图片id
         */
        previewFile: function (attachmentId) {
			window.open(ctx+"attachment/preview/"+attachmentId);
		},

        /**
         * 显示大图
         * @param src
         */
        previewImg: function (src,id) {
            if(!id || id==''){
                var viewer = new Viewer(document.getElementById("picturePreview"),{
                    url: 'data-original',
                    show: function(){
                        viewer.update();
                    }
                });
            }else{
                var viewer = new Viewer(document.getElementById("picturePreview-" + id),{
                    url: 'data-original',
                    show: function(){
                        viewer.update();
                    }
                });

            }

            // var picturePreview = document.getElementById('picturePreview');
            // var img = new Image();
            // img.src = src;
            // var imgHtml = "<img height='400px' width='600px'  src='" + src + "' />";
            // //弹出层
            // layer.open({
            //     type: 1,
            //     shade: 0.8,
            //     offset: 'auto',
            //     area: ['auto', 'auto'],
            //     shadeClose: true,//点击外围关闭弹窗
            //     scrollbar: true,//不现实滚动条
            //     title: "图片预览", //不显示标题
            //     content: imgHtml, //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
            //     cancel: function () {
            //         //layer.msg('捕获就是从页面已经存在的元素上，包裹layer的结构', { time: 5000, icon: 6 });
            //     }
            // });
        },

        /**
         * 删除图片
         * @param attachmentId
         */
        userDefinedDeleteImg: function (attachmentId) {
            console.log(attachmentId)
            var that = this;
            $.ajax({
                url: ctx+"attachment/delete/" + attachmentId,
                methods: "get",
                success: function (data) {
                    var attachmentIdsView = $('#attachmentIds');
                    var imgView = $("#" + attachmentId);
                    var attachmentIds = new Array();
                    var str = attachmentIdsView.val();
                    attachmentIds = str.split(",");
                    attachmentIds = that.delArrVal(attachmentIds, attachmentId);
                    attachmentIdsView.val(attachmentIds.toString());
                    imgView.remove();
                    layer.msg('删除成功');
                    console.log(data);
                },
                error: function (data) {
                    console.log(data)
                    layer.msg('错误', data.msg);
                }
            });
        },
        /**
         * 删除图片
         * @param attachmentId
         */
        deleteImg: function (attachmentId, id) {
            console.log(attachmentId)
            var that = this;
            $.ajax({
                url: ctx+"attachment/delete/" + attachmentId,
                methods: "get",
                success: function (data) {
                    var attachmentIdsView = $('#attachmentIds_' + id);
                    var imgView = $("#" + attachmentId);
                    var attachmentIds = new Array();
                    var str = attachmentIdsView.val();
                    attachmentIds = str.split(",");
                    attachmentIds = that.delArrVal(attachmentIds, attachmentId);
                    attachmentIdsView.val(attachmentIds.toString());
                    imgView.remove();
                    layer.msg('删除成功');
                    console.log(data);
                },
                error: function (data) {
                    console.log(data)
                    layer.msg('错误', data.msg);
                }
            });
        },

        /**
         * 删除文件
         * @param attachmentId
         * @param index
         */
        deleteFile: function (attachmentId, index,id) {
            var that = this;
            $.ajax({
                url: ctx+"attachment/delete/" + attachmentId,
                methods: "get",
                success: function (data) {
                    if(data.code == 500){
                        layer.msg(data.msg);
                    }else{
                        var demoListView = $("#upload-" + index);
                        var attachmentIdsView = $('#attachmentIds_' + id);
                        // var attachmentIdsView = $('#attachmentIds_testList');
                        var attachmentIds = new Array();
                        var str = attachmentIdsView.val();
                        console.log(str);
                        attachmentIds = str.split(",");
                        attachmentIds = that.delArrVal(attachmentIds, attachmentId);
                        attachmentIdsView.val(attachmentIds.toString());
                        demoListView.remove();
                        layer.msg('删除成功');
                    }

                    console.log(data);
                },
                error: function (data) {
                    console.log(data)
                    layer.msg('错误', data.msg);
                }
            });
        },
        searchFile: function () {
            console.log("search is asked");
            var bizId = $("#bizId").val();
            var bizType = $('#bizType').val();
            if(bizId==""){
        		bizId="undefined";
        	}
            var options = {
                url: ctx+"attachment/list/" + bizId + "/" + bizType,
                method: 'get'
            };
            $('#file-table').bootstrapTable('refresh', options);
        },
        getAttachmentIds: function (id) {
            var attachmentIdsView = $('#attachmentIds_' + id);
            if (id == 'imageupload') {
                var imgViewBox = $('.img-box');
                var ids = new Array();
                $('.img-box').each(function (index) {
                    ids[index] = $(this).attr('id')
                });
                attachmentIdsView.val(ids);
            }
            console.log(attachmentIdsView.val());
        },
        delArrVal: function (arr, val) {    //查找数组中的某个值并全部删除    第一个参数是查找的数组 第二个参数是删除的值
            for (var i = 0; i < arr.length; i++) {
                if (arr[i] == val) {
                    arr.splice(i, 1);
                    i--;
                }
            }
            return arr;
        }
    }


    exports('commonfile', obj);
})

/*文件下载*/
function toDownloadAttFile(attachmentId){
	attachmentId=attachmentId.substring(9);
	window.open(ctx+"attachment/download/" + attachmentId);
}

/*文件预览*/
function toPreviewAttFile(attachmentId){
	attachmentId=attachmentId.substring(8);
	window.open(ctx+"attachment/preview/"+attachmentId);
}

/*文件删除*/
function toDeleteAttFile(id,attachmentId){
	var that = this;
	id=id.substring(7);
	$.ajax({
        url: ctx+"attachment/delete/" + id,
        methods: "get",
        success: function (data) {
            if(data.code == 500){
                layer.msg(data.msg);
            }else{
                var demoListView = $("#upload-" + id);
			    var attachmentIdsView = $('#attachmentIds_' + attachmentId);
			    // var attachmentIdsView = $('#attachmentIds_testList');
			    var attachmentIds = new Array();
			    var str = attachmentIdsView.val();
			    attachmentIds = str.split(",");
			    attachmentIds = that.delArrVal(attachmentIds, id);
			    attachmentIdsView.val(attachmentIds.toString());
			    demoListView.remove();
			    layer.msg('删除成功');
            }
        },
        error: function (data) {
            console.log(data)
            layer.msg('错误', data.msg);
        }
    });
}
function delArrVal(arr, val) {    //查找数组中的某个值并全部删除    第一个参数是查找的数组 第二个参数是删除的值
	for (var i = 0; i < arr.length; i++) {
		if (arr[i] == val) {
			arr.splice(i, 1);
			i--;
		}
	}
	return arr;
}