/**
 * <AUTHOR> wen <<EMAIL>>
 * version: 1.18.3
 * https://github.com/wenzhixin/bootstrap-table/
 */
function getRememberRowIds(a,b){return $.isArray(a)?props=$.map(a,function(c){return c[b]}):props=[a[b]],props}function addRememberRow(b,c){var a=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId,d=getRememberRowIds(b,a);-1==$.inArray(c[a],d)&&(b[b.length]=c)}function removeRememberRow(b,c){var a=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId,f=getRememberRowIds(b,a),d=$.inArray(c[a],f);-1!=d&&b.splice(d,1)}!function(a,b){"object"==typeof exports&&"undefined"!=typeof module?module.exports=b(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],b):(a="undefined"!=typeof globalThis?globalThis:a||self,a.BootstrapTable=b(a.jQuery))}(this,function(fl){function fK(a){return a&&"object"==typeof a&&"default" in a?a:{"default":a}}function fD(a){return(fD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(b){return typeof b}:function(b){return b&&"function"==typeof Symbol&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b})(a)}function fw(a,b){if(!(a instanceof b)){throw new TypeError("Cannot call a class as a function")}}function fu(b,c){for(var a=0;a<c.length;a++){var d=c[a];d.enumerable=d.enumerable||!1,d.configurable=!0,"value" in d&&(d.writable=!0),Object.defineProperty(b,d.key,d)}}function fQ(b,c,a){return c&&fu(b.prototype,c),a&&fu(b,a),b}function fm(a,b){return fM(a)||fh(a,b)||fL(a,b)||fG()}function fp(a){return fz(a)||fF(a)||fL(a)||fr()}function fz(a){return Array.isArray(a)?fI(a):void 0}function fM(a){return Array.isArray(a)?a:void 0}function fF(a){return"undefined"!=typeof Symbol&&Symbol.iterator in Object(a)?Array.from(a):void 0}function fh(k,h){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(k)){var g=[],d=!0,c=!1,j=void 0;try{for(var m,b=k[Symbol.iterator]();!(d=(m=b.next()).done)&&(g.push(m.value),!h||g.length!==h);d=!0){}}catch(f){c=!0,j=f}finally{try{d||null==b["return"]||b["return"]()}finally{if(c){throw j}}}return g}}function fL(b,c){if(b){if("string"==typeof b){return fI(b,c)}var a=Object.prototype.toString.call(b).slice(8,-1);return"Object"===a&&b.constructor&&(a=b.constructor.name),"Map"===a||"Set"===a?Array.from(b):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?fI(b,c):void 0}}function fI(b,c){(null==c||c>b.length)&&(c=b.length);for(var a=0,d=Array(c);c>a;a++){d[a]=b[a]}return d}function fr(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fG(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fg(d,h){var c;if("undefined"==typeof Symbol||null==d[Symbol.iterator]){if(Array.isArray(d)||(c=fL(d))||h&&d&&"number"==typeof d.length){c&&(d=c);var k=0,j=function(){};return{s:j,n:function(){return k>=d.length?{done:!0}:{done:!1,value:d[k++]}},e:function(a){throw a},f:j}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var b,f=!0,g=!1;return{s:function(){c=d[Symbol.iterator]()},n:function(){var a=c.next();return f=a.done,a},e:function(a){g=!0,b=a},f:function(){try{f||null==c["return"]||c["return"]()}finally{if(g){throw b}}}}}function fO(a,b){return b={exports:{}},a(b,b.exports),b.exports}function fx(a,b){return RegExp(a,b)}var fc=fK(fl),ff="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},f2=function(a){return a&&a.Math==Math&&a},fd=f2("object"==typeof globalThis&&globalThis)||f2("object"==typeof window&&window)||f2("object"==typeof self&&self)||f2("object"==typeof ff&&ff)||function(){return this}()||Function("return this")(),fA=function(a){try{return !!a()}catch(b){return !0}},f8=!fA(function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}),f1={}.propertyIsEnumerable,gw=Object.getOwnPropertyDescriptor,f6=gw&&!f1.call({1:2},1),gk=f6?function(a){var b=gw(this,a);return !!b&&b.enumerable}:f1,gz={f:gk},gS=function(a,b){return{enumerable:!(1&a),configurable:!(2&a),writable:!(4&a),value:b}},f3={}.toString,gs=function(a){return f3.call(a).slice(8,-1)},fB="".split,fR=fA(function(){return !Object("z").propertyIsEnumerable(0)})?function(a){return"String"==gs(a)?fB.call(a,""):Object(a)}:Object,f9=function(a){if(void 0==a){throw TypeError("Can't call method on "+a)}return a},gr=function(a){return fR(f9(a))},gu=function(a){return"object"==typeof a?null!==a:"function"==typeof a},fY=function(b,c){if(!gu(b)){return b}var a,d;if(c&&"function"==typeof(a=b.toString)&&!gu(d=a.call(b))){return d}if("function"==typeof(a=b.valueOf)&&!gu(d=a.call(b))){return d}if(!c&&"function"==typeof(a=b.toString)&&!gu(d=a.call(b))){return d}throw TypeError("Can't convert object to primitive value")},gy={}.hasOwnProperty,gd=function(a,b){return gy.call(a,b)},gl=fd.document,gc=gu(gl)&&gu(gl.createElement),f0=function(a){return gc?gl.createElement(a):{}},e9=!f8&&!fA(function(){return 7!=Object.defineProperty(f0("div"),"a",{get:function(){return 7}}).a}),fq=Object.getOwnPropertyDescriptor,fX=f8?fq:function(b,c){if(b=gr(b),c=fY(c,!0),e9){try{return fq(b,c)}catch(a){}}return gd(b,c)?gS(!gz.f.call(b,c),b[c]):void 0},gp={f:fX},gf=function(a){if(!gu(a)){throw TypeError(a+" is not an object")}return a},fV=Object.defineProperty,fW=f8?fV:function(b,c,a){if(gf(b),c=fY(c,!0),gf(a),e9){try{return fV(b,c,a)}catch(d){}}if("get" in a||"set" in a){throw TypeError("Accessors not supported")}return"value" in a&&(b[c]=a.value),b},gh={f:fW},f4=f8?function(b,c,a){return gh.f(b,c,gS(1,a))}:function(b,c,a){return b[c]=a,b},fU=function(b,c){try{f4(fd,b,c)}catch(a){fd[b]=c}return c},a8="__core-js_shared__",eM=fd[a8]||fU(a8,{}),dS=eM,cJ=Function.toString;"function"!=typeof dS.inspectSource&&(dS.inspectSource=function(a){return cJ.call(a)});var cr,gF,bq,bK=dS.inspectSource,dc=fd.WeakMap,fj="function"==typeof dc&&/native code/.test(bK(dc)),d4=fO(function(a){(a.exports=function(b,c){return dS[b]||(dS[b]=void 0!==c?c:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})}),aW=0,eZ=Math.random(),eA=function(a){return"Symbol("+((void 0===a?"":a)+"")+")_"+(++aW+eZ).toString(36)},cb=d4("keys"),ek=function(a){return cb[a]||(cb[a]=eA(a))},aK={},fZ=fd.WeakMap,cZ=function(a){return bq(a)?gF(a):cr(a,{})},gV=function(a){return function(c){var b;if(!gu(c)||(b=gF(c)).type!==a){throw TypeError("Incompatible receiver, "+a+" required")}return b}};if(fj){var ay=dS.state||(dS.state=new fZ),dh=ay.get,g7=ay.has,du=ay.set;cr=function(a,b){return b.facade=a,du.call(ay,a,b),b},gF=function(a){return dh.call(ay,a)||{}},bq=function(a){return g7.call(ay,a)}}else{var d8=ek("state");aK[d8]=!0,cr=function(a,b){return b.facade=a,f4(a,d8,b),b},gF=function(a){return gd(a,d8)?a[d8]:{}},bq=function(a){return gd(a,d8)}}var c2={set:cr,get:gF,has:bq,enforce:cZ,getterFor:gV},a2=fO(function(b){var c=c2.get,a=c2.enforce,d=(String+"").split("String");(b.exports=function(h,k,m,g){var i,j=g?!!g.unsafe:!1,f=g?!!g.enumerable:!1,n=g?!!g.noTargetGet:!1;return"function"==typeof m&&("string"!=typeof k||gd(m,"name")||f4(m,"name",k),i=a(m),i.source||(i.source=d.join("string"==typeof k?k:""))),h===fd?void (f?h[k]=m:fU(k,m)):(j?!n&&h[k]&&(f=!0):delete h[k],void (f?h[k]=m:f4(h,k,m)))})(Function.prototype,"toString",function(){return"function"==typeof this&&c(this).source||bK(this)})}),dV=fd,gb=function(a){return"function"==typeof a?a:void 0},bB=function(a,b){return arguments.length<2?gb(dV[a])||gb(fd[a]):dV[a]&&dV[a][b]||fd[a]&&fd[a][b]},cF=Math.ceil,dx=Math.floor,aE=function(a){return isNaN(a=+a)?0:(a>0?dx:cF)(a)},dG=Math.min,af=function(a){return a>0?dG(aE(a),9007199254740991):0},ep=Math.max,al=Math.min,aQ=function(b,c){var a=aE(b);return 0>a?ep(a+c,0):al(a,c)},cx=function(a){return function(g,c,j){var h,b=gr(g),d=af(b.length),f=aQ(j,d);if(a&&c!=c){for(;d>f;){if(h=b[f++],h!=h){return !0}}}else{for(;d>f;f++){if((a||f in b)&&b[f]===c){return a||f||0}}}return !a&&-1}},bh={includes:cx(!0),indexOf:cx(!1)},eQ=bh.indexOf,gL=function(d,f){var c,h=gr(d),g=0,b=[];for(c in h){!gd(aK,c)&&gd(h,c)&&b.push(c)}for(;f.length>g;){gd(h,c=f[g++])&&(~eQ(b,c)||b.push(c))}return b},eD=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],cP=eD.concat("length","prototype"),gB=Object.getOwnPropertyNames||function(a){return gL(a,cP)},bY={f:gB},cf=Object.getOwnPropertySymbols,g1={f:cf},e1=bB("Reflect","ownKeys")||function(b){var c=bY.f(gf(b)),a=g1.f;return a?c.concat(a(b)):c},bP=function(d,g){for(var c=e1(g),j=gh.f,h=gp.f,b=0;b<c.length;b++){var f=c[b];gd(d,f)||j(d,f,h(g,f))}},b1=/#|\.prototype\./,fy=function(b,c){var a=bw[dJ(b)];return a==eU?!0:a==bg?!1:"function"==typeof c?fA(c):!!c},dJ=fy.normalize=function(a){return(a+"").replace(b1,".").toLowerCase()},bw=fy.data={},bg=fy.NATIVE="N",eU=fy.POLYFILL="P",dZ=fy,cU=gp.f,cB=function(u,m){var j,f,d,q,v,b,g=u.target,p=u.global,k=u.stat;if(f=p?fd:k?fd[g]||fU(g,{}):(fd[g]||{}).prototype){for(d in m){if(v=m[d],u.noTargetGet?(b=cU(f,d),q=b&&b.value):q=f[d],j=dZ(p?d:g+(k?".":"#")+d,u.forced),!j&&void 0!==q){if(typeof v==typeof q){continue}bP(v,q)}(u.sham||q&&q.sham)&&f4(v,"sham",!0),a2(f,d,v,u)}}},gQ="	\n\x0B\f\r                　\u2028\u2029\ufeff",bA="["+gQ+"]",bT=RegExp("^"+bA+bA+"*"),dm=RegExp(bA+bA+"*$"),fJ=function(a){return function(c){var b=f9(c)+"";return 1&a&&(b=b.replace(bT,"")),2&a&&(b=b.replace(dm,"")),b}},ed={start:fJ(1),end:fJ(2),trim:fJ(3)},a1="᠎",e5=function(a){return fA(function(){return !!gQ[a]()||a1[a]()!=a1||gQ[a].name!==a})},eH=ed.trim;cB({target:"String",proto:!0,forced:e5("trim")},{trim:function(){return eH(this)}});var ck=function(b,c){var a=[][b];return !!a&&fA(function(){a.call(null,c||function(){throw 1},1)})},ev=[].join,aP=fR!=Object,gq=ck("join",",");cB({target:"Array",proto:!0,forced:aP||!gq},{join:function(a){return ev.call(gr(this),void 0===a?",":a)}});var c6=function(){var a=gf(this),b="";return a.global&&(b+="g"),a.ignoreCase&&(b+="i"),a.multiline&&(b+="m"),a.dotAll&&(b+="s"),a.unicode&&(b+="u"),a.sticky&&(b+="y"),b},g0=fA(function(){var a=fx("a","y");return a.lastIndex=2,null!=a.exec("abcd")}),aD=fA(function(){var a=fx("^r","gy");return a.lastIndex=2,null!=a.exec("str")}),dr={UNSUPPORTED_Y:g0,BROKEN_CARET:aD},ak=RegExp.prototype.exec,dB=String.prototype.replace,eh=ak,c9=function(){var a=/a/,b=/b*/g;return ak.call(a,"a"),ak.call(b,"a"),0!==a.lastIndex||0!==b.lastIndex}(),a7=dr.UNSUPPORTED_Y||dr.BROKEN_CARET,d2=void 0!==/()??/.exec("")[1],gA=c9||d2||a7;gA&&(eh=function(u){var m,j,f,d,q=this,v=a7&&q.sticky,b=c6.call(q),g=q.source,p=0,k=u;return v&&(b=b.replace("y",""),-1===b.indexOf("g")&&(b+="g"),k=(u+"").slice(q.lastIndex),q.lastIndex>0&&(!q.multiline||q.multiline&&"\n"!==u[q.lastIndex-1])&&(g="(?: "+g+")",k=" "+k,p++),j=RegExp("^(?:"+g+")",b)),d2&&(j=RegExp("^"+g+"$(?!\\s)",b)),c9&&(m=q.lastIndex),f=ak.call(v?j:q,k),v?f?(f.input=f.input.slice(p),f[0]=f[0].slice(p),f.index=q.lastIndex,q.lastIndex+=f[0].length):q.lastIndex=0:c9&&f&&(q.lastIndex=q.global?f.index+f[0].length:m),d2&&f&&f.length>1&&dB.call(f[0],j,function(){for(d=1;d<arguments.length-2;d++){void 0===arguments[d]&&(f[d]=void 0)}}),f});var bJ=eh;cB({target:"RegExp",proto:!0,forced:/./.exec!==bJ},{exec:bJ});var cO,dE,aJ="process"==gs(fd.process),dN=bB("navigator","userAgent")||"",au=fd.process,ey=au&&au.versions,ax=ey&&ey.v8;ax?(cO=ax.split("."),dE=cO[0]+cO[1]):dN&&(cO=dN.match(/Edge\/(\d+)/),(!cO||cO[1]>=74)&&(cO=dN.match(/Chrome\/(\d+)/),cO&&(dE=cO[1])));var aV=dE&&+dE,cE=!!Object.getOwnPropertySymbols&&!fA(function(){return !Symbol.sham&&(aJ?38===aV:aV>37&&41>aV)}),bp=cE&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,eX=d4("wks"),gU=fd.Symbol,eK=bp?gU:gU&&gU.withoutSetter||eA,cX=function(a){return(!gd(eX,a)||!cE&&"string"!=typeof eX[a])&&(cE&&gd(gU,a)?eX[a]=gU[a]:eX[a]=eK("Symbol."+a)),eX[a]},gK=cX("species"),b5=!fA(function(){var a=/./;return a.exec=function(){var b=[];return b.groups={a:"7"},b},"7"!=="".replace(a,"$<a>")}),cp=function(){return"$0"==="a".replace(/./,"$0")}(),g6=cX("replace"),e8=function(){return/./[g6]?""===/./[g6]("a","$0"):!1}(),bW=!fA(function(){var b=/(?:)/,c=b.exec;b.exec=function(){return c.apply(this,arguments)};var a="ab".split(b);return 2!==a.length||"a"!==a[0]||"b"!==a[1]}),b8=function(u,m,j,f){var d=cX(u),q=!fA(function(){var a={};return a[d]=function(){return 7},7!=""[u](a)}),v=q&&!fA(function(){var c=!1,a=/a/;return"split"===u&&(a={},a.constructor={},a.constructor[gK]=function(){return a},a.flags="",a[d]=/./[d]),a.exec=function(){return c=!0,null},a[d](""),!c});if(!q||!v||"replace"===u&&(!b5||!cp||e8)||"split"===u&&!bW){var b=/./[d],g=j(d,""[u],function(c,h,a,r,l){return h.exec===bJ?q&&!l?{done:!0,value:b.call(h,a,r)}:{done:!0,value:c.call(a,h,r)}:{done:!1}},{REPLACE_KEEPS_$0:cp,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:e8}),p=g[0],k=g[1];a2(String.prototype,u,p),a2(RegExp.prototype,d,2==m?function(a,c){return k.call(a,this,c)}:function(a){return k.call(a,this)})}f&&f4(RegExp.prototype[d],"sham",!0)},fS=cX("match"),dQ=function(a){var b;return gu(a)&&(void 0!==(b=a[fS])?!!b:"RegExp"==gs(a))},bG=function(a){if("function"!=typeof a){throw TypeError(a+" is not a function")}return a},bf=cX("species"),eR=function(b,c){var a,d=gf(b).constructor;return void 0===d||void 0==(a=gf(d)[bf])?c:bG(a)},dW=function(a){return function(g,c){var j,h,b=f9(g)+"",d=aE(c),f=b.length;return 0>d||d>=f?a?"":void 0:(j=b.charCodeAt(d),55296>j||j>56319||d+1===f||(h=b.charCodeAt(d+1))<56320||h>57343?a?b.charAt(d):j:a?b.slice(d,d+2):(j-55296<<10)+(h-56320)+65536)}},cQ={codeAt:dW(!1),charAt:dW(!0)},cy=cQ.charAt,gN=function(b,c,a){return c+(a?cy(b,c).length:1)},bx=function(b,c){var a=b.exec;if("function"==typeof a){var d=a.call(b,c);if("object"!=typeof d){throw TypeError("RegExp exec method returned something other than an Object or null")}return d}if("RegExp"!==gs(b)){throw TypeError("RegExp#exec called on incompatible receiver")}return bJ.call(b,c)},bQ=[].push,dj=Math.min,fC=4294967295,d9=!fA(function(){return !RegExp(fC,"y")});b8("split",2,function(b,c,a){var d;return d="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(w,k){var g=f9(this)+"",f=void 0===k?fC:k>>>0;if(0===f){return[]}if(void 0===w){return[g]}if(!dQ(w)){return c.call(g,w,f)}for(var q,x,e,j=[],p=(w.ignoreCase?"i":"")+(w.multiline?"m":"")+(w.unicode?"u":"")+(w.sticky?"y":""),m=0,v=RegExp(w.source,p+"g");(q=bJ.call(v,g))&&(x=v.lastIndex,!(x>m&&(j.push(g.slice(m,q.index)),q.length>1&&q.index<g.length&&bQ.apply(j,q.slice(1)),e=q[0].length,m=x,j.length>=f)));){v.lastIndex===q.index&&v.lastIndex++}return m===g.length?(e||!v.test(""))&&j.push(""):j.push(g.slice(m)),j.length>f?j.slice(0,f):j}:"0".split(void 0,0).length?function(f,e){return void 0===f&&0===e?[]:c.call(this,f,e)}:c,[function(h,g){var j=f9(this),f=void 0==h?void 0:h[b];return void 0!==f?f.call(h,j,g):d.call(j+"",h,g)},function(E,j){var B=a(d,E,this,j,d!==c);if(B.done){return B.value}var F=gf(E),e=this+"",n=eR(F,RegExp),z=F.unicode,q=(F.ignoreCase?"i":"")+(F.multiline?"m":"")+(F.unicode?"u":"")+(d9?"y":"g"),D=new n(d9?F:"^(?:"+F.source+")",q),y=void 0===j?fC:j>>>0;if(0===y){return[]}if(0===e.length){return null===bx(D,e)?[e]:[]}for(var x=0,i=0,w=[];i<e.length;){D.lastIndex=d9?i:0;var C,A=bx(D,d9?e:e.slice(i));if(null===A||(C=dj(af(D.lastIndex+(d9?0:i)),e.length))===x){i=gN(e,i,z)}else{if(w.push(e.slice(x,i)),w.length===y){return w}for(var k=1;k<=A.length-1;k++){if(w.push(A[k]),w.length===y){return w}}i=x=C}}return w.push(e.slice(x)),w}]},!d9);var a0,e2=Object.keys||function(a){return gL(a,eD)},eE=f8?Object.defineProperties:function(d,f){gf(d);for(var c,h=e2(f),g=h.length,b=0;g>b;){gh.f(d,c=h[b++],f[c])}return d},cg=bB("document","documentElement"),eq=">",aO="<",gg="prototype",c3="script",gZ=ek("IE_PROTO"),aC=function(){},dq=function(a){return aO+c3+eq+a+aO+"/"+c3+eq},ag=function(a){a.write(dq("")),a.close();var b=a.parentWindow.Object;return a=null,b},dy=function(){var b,c=f0("iframe"),a="java"+c3+":";return c.style.display="none",cg.appendChild(c),c.src=a+"",b=c.contentWindow.document,b.open(),b.write(dq("document.F=Object")),b.close(),b.F},eg=function(){try{a0=document.domain&&new ActiveXObject("htmlfile")}catch(a){}eg=a0?ag(a0):dy();for(var b=eD.length;b--;){delete eg[gg][eD[b]]}return eg()};aK[gZ]=!0;var c8=Object.create||function(b,c){var a;return null!==b?(aC[gg]=gf(b),a=new aC,aC[gg]=null,a[gZ]=b):a=eg(),void 0===c?a:eE(a,c)},a6=cX("unscopables"),d1=Array.prototype;void 0==d1[a6]&&gh.f(d1,a6,{configurable:!0,value:c8(null)});var gx=function(a){d1[a6][a]=!0},bI=bh.includes;cB({target:"Array",proto:!0},{includes:function(a){return bI(this,a,arguments.length>1?arguments[1]:void 0)}}),gx("includes");var cL=Array.isArray||function(a){return"Array"==gs(a)},dD=function(a){return Object(f9(a))},aI=function(b,c,a){var d=fY(c);d in b?gh.f(b,d,gS(0,a)):b[d]=a},dK=cX("species"),ap=function(b,c){var a;return cL(b)&&(a=b.constructor,"function"!=typeof a||a!==Array&&!cL(a.prototype)?gu(a)&&(a=a[dK],null===a&&(a=void 0)):a=void 0),new (void 0===a?Array:a)(0===c?0:c)},ex=cX("species"),aw=function(a){return aV>=51||!fA(function(){var c=[],b=c.constructor={};return b[ex]=function(){return{foo:1}},1!==c[a](Boolean).foo})},aU=cX("isConcatSpreadable"),cD=9007199254740991,bm="Maximum allowed index exceeded",eW=aV>=51||!fA(function(){var a=[];return a[aU]=!1,a.concat()[0]!==a}),gT=aw("concat"),eJ=function(a){if(!gu(a)){return !1}var b=a[aU];return void 0!==b?!!b:cL(a)},cW=!eW||!gT;cB({target:"Array",proto:!0,forced:cW},{concat:function(k){var h,g,d,c,j,m=dD(this),b=ap(m,0),f=0;for(h=-1,d=arguments.length;d>h;h++){if(j=-1===h?m:arguments[h],eJ(j)){if(c=af(j.length),f+c>cD){throw TypeError(bm)}for(g=0;c>g;g++,f++){g in j&&aI(b,f,j[g])}}else{if(f>=cD){throw TypeError(bm)}aI(b,f++,j)}}return b.length=f,b}});var gH=function(b,c,a){if(bG(b),void 0===c){return b}switch(a){case 0:return function(){return b.call(c)};case 1:return function(d){return b.call(c,d)};case 2:return function(d,e){return b.call(c,d,e)};case 3:return function(d,f,e){return b.call(c,d,f,e)}}return function(){return b.apply(c,arguments)}},b2=[].push,cm=function(d){var h=1==d,c=2==d,k=3==d,j=4==d,b=6==d,f=7==d,g=5==d||b;return function(i,s,n,B){for(var r,q,a=dD(i),o=fR(a),A=gH(s,n,3),x=af(o.length),e=0,t=B||ap,z=h?t(i,x):c||f?t(i,0):void 0;x>e;e++){if((g||e in o)&&(r=o[e],q=A(r,e,a),d)){if(h){z[e]=q}else{if(q){switch(d){case 3:return !0;case 5:return r;case 6:return e;case 2:b2.call(z,r)}}else{switch(d){case 4:return !1;case 7:b2.call(z,r)}}}}}return b?-1:k||j?j:z}},g5={forEach:cm(0),map:cm(1),filter:cm(2),some:cm(3),every:cm(4),find:cm(5),findIndex:cm(6),filterOut:cm(7)},e7=g5.find,bV="find",b7=!0;bV in []&&Array(1)[bV](function(){b7=!1}),cB({target:"Array",proto:!0,forced:b7},{find:function(a){return e7(this,a,arguments.length>1?arguments[1]:void 0)}}),gx(bV);var fP=function(a){if(dQ(a)){throw TypeError("The method doesn't accept regular expressions")}return a},dP=cX("match"),bD=function(b){var c=/./;try{"/./"[b](c)}catch(a){try{return c[dP]=!1,"/./"[b](c)}catch(d){}}return !1};cB({target:"String",proto:!0,forced:!bD("includes")},{includes:function(a){return !!~(f9(this)+"").indexOf(fP(a),arguments.length>1?arguments[1]:void 0)}});var bd={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},eP=g5.forEach,cN=ck("forEach"),cw=cN?[].forEach:function(a){return eP(this,a,arguments.length>1?arguments[1]:void 0)};for(var gJ in bd){var bv=fd[gJ],bO=bv&&bv.prototype;if(bO&&bO.forEach!==cw){try{f4(bO,"forEach",cw)}catch(dg){bO.forEach=cw}}}var fv=ed.trim,d7=fd.parseFloat,aZ=1/d7(gQ+"-0")!==-(1/0),e0=aZ?function(b){var c=fv(b+""),a=d7(c);return 0===a&&"-"==c.charAt(0)?-0:a}:d7;cB({global:!0,forced:parseFloat!=e0},{parseFloat:e0});var eC=gz.f,cd=function(a){return function(g){for(var c,j=gr(g),h=e2(j),b=h.length,d=0,f=[];b>d;){c=h[d++],(!f8||eC.call(j,c))&&f.push(a?[c,j[c]]:j[c])}return f}},em={entries:cd(!0),values:cd(!1)},aN=em.entries;cB({target:"Object",stat:!0},{entries:function(a){return aN(a)}});var f7=bh.indexOf,c1=[].indexOf,gY=!!c1&&1/[1].indexOf(1,-0)<0,aB=ck("indexOf");cB({target:"Array",proto:!0,forced:gY||!aB},{indexOf:function(a){return gY?c1.apply(this,arguments)||0:f7(this,a,arguments.length>1?arguments[1]:void 0)}});var dl=[],ad=dl.sort,dw=fA(function(){dl.sort(void 0)}),ec=fA(function(){dl.sort(null)}),c5=ck("sort"),a5=dw||!ec||!c5;cB({target:"Array",proto:!0,forced:a5},{sort:function(a){return void 0===a?ad.call(dD(this)):ad.call(dD(this),bG(a))}});var dY=Math.floor,gm="".replace,bF=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,cI=/\$([$&'`]|\d{1,2})/g,dA=function(k,h,g,d,c,j){var m=g+k.length,b=d.length,f=cI;return void 0!==c&&(c=dD(c),f=bF),gm.call(j,f,function(i,e){var p;switch(e.charAt(0)){case"$":return"$";case"&":return k;case"`":return h.slice(0,g);case"'":return h.slice(m);case"<":p=c[e.slice(1,-1)];break;default:var o=+e;if(0===o){return i}if(o>b){var n=dY(o/10);return 0===n?i:b>=n?void 0===d[n-1]?e.charAt(1):d[n-1]+e.charAt(1):i}p=d[o-1]}return void 0===p?"":p})},aH=Math.max,dI=Math.min,aj=function(a){return void 0===a?a:a+""};b8("replace",2,function(d,g,c,j){var h=j.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,b=j.REPLACE_KEEPS_$0,f=h?"$":"$0";return[function(k,m){var l=f9(this),e=void 0==k?void 0:k[d];return void 0!==e?e.call(k,l,m):g.call(l+"",k,m)},function(B,E){if(!h&&b||"string"==typeof E&&-1===E.indexOf(f)){var C=c(g,B,this,E);if(C.done){return C.value}}var G=gf(B),M=this+"",I="function"==typeof E;I||(E+="");var A=G.global;if(A){var L=G.unicode;G.lastIndex=0}for(var K=[];;){var D=bx(G,M);if(null===D){break}if(K.push(D),!A){break}var J=D[0]+"";""===J&&(G.lastIndex=gN(M,af(G.lastIndex),L))}for(var z="",N=0,F=0;F<K.length;F++){D=K[F];for(var o=D[0]+"",s=aH(dI(aE(D.index),M.length),0),e=[],q=1;q<D.length;q++){e.push(aj(D[q]))}var H=D.groups;if(I){var i=[o].concat(e,s,M);void 0!==H&&i.push(H);var a=E.apply(void 0,i)+""}else{a=dA(o,M,s,e,H,E)}s>=N&&(z+=M.slice(N,s)+a,N=s+o.length)}return z+M.slice(N)}]});var eu=Object.assign,ar=Object.defineProperty,aT=!eu||fA(function(){if(f8&&1!==eu({b:1},eu(ar({},"a",{enumerable:!0,get:function(){ar(this,"b",{value:3,enumerable:!1})}}),{b:2})).b){return !0}var b={},c={},a=Symbol(),d="abcdefghijklmnopqrst";return b[a]=7,d.split("").forEach(function(e){c[e]=e}),7!=eu({},b)[a]||e2(eu({},c)).join("")!=d})?function(w,m){for(var j=dD(w),f=arguments.length,d=1,q=g1.f,x=gz.f;f>d;){for(var b,g=fR(arguments[d++]),p=q?e2(g).concat(q(g)):e2(g),k=p.length,v=0;k>v;){b=p[v++],(!f8||x.call(g,b))&&(j[b]=g[b])}}return j}:eu;cB({target:"Object",stat:!0,forced:Object.assign!==aT},{assign:aT});var cA=g5.filter,bl=aw("filter");cB({target:"Array",proto:!0,forced:!bl},{filter:function(a){return cA(this,a,arguments.length>1?arguments[1]:void 0)}});var eT=Object.is||function(a,b){return a===b?0!==a||1/a===1/b:a!=a&&b!=b};b8("search",1,function(b,c,a){return[function(f){var d=f9(this),g=void 0==f?void 0:f[b];return void 0!==g?g.call(f,d):RegExp(f)[b](d+"")},function(e){var i=a(c,e,this);if(i.done){return i.value}var h=gf(e),d=this+"",f=h.lastIndex;eT(f,0)||(h.lastIndex=0);var g=bx(h,d);return eT(h.lastIndex,f)||(h.lastIndex=f),null===g?-1:g.index}]});var gP=ed.trim,eG=fd.parseInt,cT=/^[+-]?0[Xx]/,gE=8!==eG(gQ+"08")||22!==eG(gQ+"0x16"),b0=gE?function(b,c){var a=gP(b+"");return eG(a,c>>>0||(cT.test(a)?16:10))}:eG;cB({global:!0,forced:parseInt!=b0},{parseInt:b0});var cj=g5.map,g4=aw("map");cB({target:"Array",proto:!0,forced:!g4},{map:function(a){return cj(this,a,arguments.length>1?arguments[1]:void 0)}});var e4=g5.findIndex,bS="findIndex",b4=!0;bS in []&&Array(1)[bS](function(){b4=!1}),cB({target:"Array",proto:!0,forced:b4},{findIndex:function(a){return e4(this,a,arguments.length>1?arguments[1]:void 0)}}),gx(bS);var fH=function(a){if(!gu(a)&&null!==a){throw TypeError("Can't set "+(a+"")+" as a prototype")}return a},dM=Object.setPrototypeOf||("__proto__" in {}?function(){var b,c=!1,a={};try{b=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,b.call(a,[]),c=a instanceof Array}catch(d){}return function(e,f){return gf(e),fH(f),c?b.call(e,f):e.__proto__=f,e}}():void 0),bz=function(b,c,a){var f,d;return dM&&"function"==typeof(f=c.constructor)&&f!==a&&gu(d=f.prototype)&&d!==a.prototype&&dM(b,d),b},bc=cX("species"),eO=function(b){var c=bB(b),a=gh.f;f8&&c&&!c[bc]&&a(c,bc,{configurable:!0,get:function(){return this}})},dU=gh.f,cM=bY.f,cv=c2.set,gI=cX("match"),bu=fd.RegExp,bN=bu.prototype,df=/a/g,fs=/a/g,d6=new bu(df)!==df,aY=dr.UNSUPPORTED_Y,eB=f8&&dZ("RegExp",!d6||aY||fA(function(){return fs[gI]=!1,bu(df)!=df||bu(fs)==fs||"/a/i"!=bu(df,"i")}));if(eB){for(var cc=function(d,g){var c,j=this instanceof cc,h=dQ(d),b=void 0===g;if(!j&&h&&d.constructor===cc&&b){return d}d6?h&&!b&&(d=d.source):d instanceof cc&&(b&&(g=c6.call(d)),d=d.source),aY&&(c=!!g&&g.indexOf("y")>-1,c&&(g=g.replace(/y/g,"")));var f=bz(d6?new bu(d,g):bu(d,g),j?this:bN,cc);return aY&&c&&cv(f,{sticky:c}),f},el=(function(a){a in cc||dU(cc,a,{configurable:!0,get:function(){return bu[a]},set:function(b){bu[a]=b}})}),aM=cM(bu),f5=0;aM.length>f5;){el(aM[f5++])}bN.constructor=cc,cc.prototype=bN,a2(fd,"RegExp",cc)}eO("RegExp");var c0="toString",gX=RegExp.prototype,aA=gX[c0],dk=fA(function(){return"/a/b"!=aA.call({source:"a",flags:"b"})}),ac=aA.name!=c0;(dk||ac)&&a2(RegExp.prototype,c0,function(){var b=gf(this),c=b.source+"",a=b.flags,d=(void 0===a&&b instanceof RegExp&&!("flags" in gX)?c6.call(b):a)+"";return"/"+c+"/"+d},{unsafe:!0});var dv=cX("toStringTag"),eb={};eb[dv]="z";var c4=eb+""=="[object z]",a4=cX("toStringTag"),dX="Arguments"==gs(function(){return arguments}()),gj=function(b,c){try{return b[c]}catch(a){}},bE=c4?gs:function(b){var c,a,d;return void 0===b?"Undefined":null===b?"Null":"string"==typeof(a=gj(c=Object(b),a4))?a:dX?gs(c):"Object"==(d=gs(c))&&"function"==typeof c.callee?"Arguments":d},cH=c4?{}.toString:function(){return"[object "+bE(this)+"]"};c4||a2(Object.prototype,"toString",cH,{unsafe:!0});var dz=aw("slice"),aG=cX("species"),dH=[].slice,ah=Math.max;cB({target:"Array",proto:!0,forced:!dz},{slice:function(k,h){var g,d,c,j=gr(this),m=af(j.length),b=aQ(k,m),f=aQ(void 0===h?m:h,m);if(cL(j)&&(g=j.constructor,"function"!=typeof g||g!==Array&&!cL(g.prototype)?gu(g)&&(g=g[aG],null===g&&(g=void 0)):g=void 0,g===Array||void 0===g)){return dH.call(j,b,f)}for(d=new (void 0===g?Array:g)(ah(f-b,0)),c=0;f>b;b++,c++){b in j&&aI(d,c,j[b])}return d.length=c,d}});var er,aq,aS,cz=!fA(function(){function a(){}return a.prototype.constructor=null,Object.getPrototypeOf(new a)!==a.prototype}),bk=ek("IE_PROTO"),eS=Object.prototype,gO=cz?Object.getPrototypeOf:function(a){return a=dD(a),gd(a,bk)?a[bk]:"function"==typeof a.constructor&&a instanceof a.constructor?a.constructor.prototype:a instanceof Object?eS:null},eF=cX("iterator"),cS=!1,gD=function(){return this};[].keys&&(aS=[].keys(),"next" in aS?(aq=gO(gO(aS)),aq!==Object.prototype&&(er=aq)):cS=!0);var bZ=void 0==er||fA(function(){var a={};return er[eF].call(a)!==a});bZ&&(er={}),gd(er,eF)||f4(er,eF,gD);var ch={IteratorPrototype:er,BUGGY_SAFARI_ITERATORS:cS},g3=gh.f,e3=cX("toStringTag"),bR=function(b,c,a){b&&!gd(b=a?b:b.prototype,e3)&&g3(b,e3,{configurable:!0,value:c})},b3=ch.IteratorPrototype,fE=function(b,c,a){var d=c+" Iterator";return b.prototype=c8(b3,{next:gS(1,a)}),bR(b,d,!1),b},dL=ch.IteratorPrototype,by=ch.BUGGY_SAFARI_ITERATORS,bj=cX("iterator"),eV="keys",d0="values",cV="entries",cC=function(){return this},gR=function(G,A,w,m,k,D,H){fE(w,A,m);var b,q,C,x=function(a){if(a===k&&y){return y}if(!by&&a in z){return z[a]}switch(a){case eV:return function(){return new w(this,a)};case d0:return function(){return new w(this,a)};case cV:return function(){return new w(this,a)}}return function(){return new w(this)}},F=A+" Iterator",B=!1,z=G.prototype,j=z[bj]||z["@@iterator"]||k&&z[k],y=!by&&j||x(k),E="Array"==A?z.entries||j:j;if(E&&(b=gO(E.call(new G)),dL!==Object.prototype&&b.next&&(gO(b)!==dL&&(dM?dM(b,dL):"function"!=typeof b[bj]&&f4(b,bj,cC)),bR(b,F,!0))),k==d0&&j&&j.name!==d0&&(B=!0,y=function(){return j.call(this)}),z[bj]!==y&&f4(z,bj,y),k){if(q={values:x(d0),keys:D?y:x(eV),entries:x(cV)},H){for(C in q){!by&&!B&&C in z||a2(z,C,q[C])}}else{cB({target:A,proto:!0,forced:by||B},q)}}return q},bC="Array Iterator",bU=c2.set,dp=c2.getterFor(bC),fN=gR(Array,"Array",function(a,b){bU(this,{type:bC,target:gr(a),index:0,kind:b})},function(){var b=dp(this),c=b.target,a=b.kind,d=b.index++;return !c||d>=c.length?(b.target=void 0,{value:void 0,done:!0}):"keys"==a?{value:d,done:!1}:"values"==a?{value:c[d],done:!1}:{value:[d,c[d]],done:!1}},"values");gx("keys"),gx("values"),gx("entries");var ef=cX("iterator"),a3=cX("toStringTag"),e6=fN.values;for(var eI in bd){var cl=fd[eI],ew=cl&&cl.prototype;if(ew){if(ew[ef]!==e6){try{f4(ew,ef,e6)}catch(dg){ew[ef]=e6}}if(ew[a3]||f4(ew,a3,eI),bd[eI]){for(var aR in fN){if(ew[aR]!==fN[aR]){try{f4(ew,aR,fN[aR])}catch(dg){ew[aR]=fN[aR]}}}}}}var gv=aw("splice"),c7=Math.max,g2=Math.min,aF=9007199254740991,ds="Maximum allowed length exceeded";cB({target:"Array",proto:!0,forced:!gv},{splice:function(w,m){var j,f,d,q,x,b,g=dD(this),p=af(g.length),k=aQ(w,p),v=arguments.length;if(0===v?j=f=0:1===v?(j=0,f=p-k):(j=v-2,f=g2(c7(aE(m),0),p-k)),p+j-f>aF){throw TypeError(ds)}for(d=ap(g,f),q=0;f>q;q++){x=k+q,x in g&&aI(d,q,g[x])}if(d.length=f,f>j){for(q=k;p-f>q;q++){x=q+f,b=q+j,x in g?g[b]=g[x]:delete g[b]}for(q=p;q>p-f+j;q--){delete g[q-1]}}else{if(j>f){for(q=p-f;q>k;q--){x=q+f-1,b=q+j-1,x in g?g[b]=g[x]:delete g[b]}}}for(q=0;j>q;q++){g[q+k]=arguments[q+2]}return g.length=p-f+j,d}});var am=bY.f,dC=gp.f,ej=gh.f,db=ed.trim,bb="Number",d3=fd[bb],gC=d3.prototype,bM=gs(c8(gC))==bb,cR=function(p){var j,h,f,d,m,q,b,g,k=fY(p,!1);if("string"==typeof k&&k.length>2){if(k=db(k),j=k.charCodeAt(0),43===j||45===j){if(h=k.charCodeAt(2),88===h||120===h){return NaN}}else{if(48===j){switch(k.charCodeAt(1)){case 66:case 98:f=2,d=49;break;case 79:case 111:f=8,d=55;break;default:return +k}for(m=k.slice(2),q=m.length,b=0;q>b;b++){if(g=m.charCodeAt(b),48>g||g>d){return NaN}}return parseInt(m,f)}}}return +k};if(dZ(bb,!d3(" 0o1")||!d3("0b1")||d3("+0x1"))){for(var dF,aL=function(b){var c=arguments.length<1?0:b,a=this;return a instanceof aL&&(bM?fA(function(){gC.valueOf.call(a)}):gs(a)!=bb)?bz(new d3(cR(c)),a,aL):cR(c)},dO=f8?am(d3):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),av=0;dO.length>av;av++){gd(d3,dF=dO[av])&&!gd(aL,dF)&&ej(aL,dF,dC(d3,dF))}aL.prototype=gC,gC.constructor=aL,a2(fd,bb,aL)}var ez=[].reverse,az=[1,2];cB({target:"Array",proto:!0,forced:az+""==az.reverse()+""},{reverse:function(){return cL(this)&&(this.length=this.length),ez.call(this)}});var aX="1.18.3",cG=4;try{var bs=fc["default"].fn.dropdown.Constructor.VERSION;void 0!==bs&&(cG=parseInt(bs,10))}catch(eY){}try{var gW=bootstrap.Tooltip.VERSION;void 0!==gW&&(cG=parseInt(gW,10))}catch(eY){}var eL={3:{iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">   <a onclick="fdTableL()"><i class="fa fa-arrows-alt" style="\n' +
            '    float: right;\n' +
            '    margin-top: 5px;\n' +
            '    margin-right: 5px;\n' +
            '"></i></a>',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">  <a><i class="fa" style="\n' +
            '    float: right;\n' +
            '    margin-top: 5px;\n' +
            '    margin-right: 5px;\n' +
            '"></i></a>',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{iconsPrefix:"fa",icons:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{iconsPrefix:"fa",icons:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[cG],cY={id:void 0,firstLoad:!0,height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",striped:!1,headerStyle:function(a){return{}},rowStyle:function(a,b){return{}},rowAttributes:function(a,b){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(a){return a},queryParamsType:"limit",responseHandler:function(a){return a},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(a){return{}},searchAccentNeutralise:!1,showColumns:!1,showSearch:!1,showPageGo:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(a){var b=a.tagName;return["A","BUTTON"].includes(b)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(a,b){return""},detailFilter:function(a,b){return !0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["search","paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:eL.classes.buttonsPrefix,buttonsClass:eL.classes.buttons,icons:eL.icons,iconSize:void 0,iconsPrefix:eL.iconsPrefix,loadingFontSize:"auto",loadingTemplate:function(a){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(a,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(a,b){return !1},onClickCell:function(b,c,a,d){return !1},onDblClickCell:function(b,c,a,d){return !1},onClickRow:function(a,b){return !1},onDblClickRow:function(a,b){return !1},onSort:function(a,b){return !1},onCheck:function(a){return !1},onUncheck:function(a){return !1},onCheckAll:function(a){return !1},onUncheckAll:function(a){return !1},onCheckSome:function(a){return !1},onUncheckSome:function(a){return !1},onLoadSuccess:function(a){return !1},onLoadError:function(a){return !1},onColumnSwitch:function(a,b){return !1},onPageChange:function(a,b){return !1},onSearch:function(a){return !1},onShowSearch:function(){return !1},onToggle:function(a){return !1},onPreBody:function(a){return !1},onPostBody:function(){return !1},onPostHeader:function(){return !1},onPostFooter:function(){return !1},onExpandRow:function(b,c,a){return !1},onCollapseRow:function(a,b){return !1},onRefreshOptions:function(a){return !1},onRefresh:function(a){return !1},onResetView:function(){return !1},onScrollBody:function(){return !1}},gM={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(a){return"".concat(a," rows per page")},formatShowingRows:function(b,c,a,d){return void 0!==d&&d>0&&d>a?"Showing ".concat(b," to ").concat(c," of ").concat(a," rows (filtered from ").concat(d," total rows)"):"Showing ".concat(b," to ").concat(c," of ").concat(a," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(a){return"to page ".concat(a)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(a){return"Showing ".concat(a," rows")},formatSearch:function(){return"Search"},formatShowSearch:function(){return"Show Search"},formatPageGo:function(){return"Go"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},b6={field:void 0,title:void 0,titleTooltip:void 0,"class":void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,ignore:!1,switchable:!0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:!1,events:void 0},cq=["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],ab={"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody"};Object.assign(cY,gM);var fb={VERSION:aX,THEME:"bootstrap".concat(cG),CONSTANTS:eL,DEFAULTS:cY,COLUMN_DEFAULTS:b6,METHODS:cq,EVENTS:ab,LOCALES:{en:gM,"en-US":gM}},bX=fA(function(){e2(1)});cB({target:"Object",stat:!0,forced:bX},{keys:function(a){return e2(dD(a))}});var b9=gp.f,fT="".startsWith,dR=Math.min,bH=bD("startsWith"),a9=!bH&&!!function(){var a=b9(String.prototype,"startsWith");return a&&!a.writable}();cB({target:"String",proto:!0,forced:!a9&&!bH},{startsWith:function(b){var c=f9(this)+"";fP(b);var a=af(dR(arguments.length>1?arguments[1]:void 0,c.length)),d=b+"";return fT?fT.call(c,d,a):c.slice(a,a+d.length)===d}});var eN=gp.f,dT="".endsWith,cK=Math.min,cu=bD("endsWith"),gG=!cu&&!!function(){var a=eN(String.prototype,"endsWith");return a&&!a.writable}();cB({target:"String",proto:!0,forced:!gG&&!cu},{endsWith:function(d){var f=f9(this)+"";fP(d);var c=arguments.length>1?arguments[1]:void 0,h=af(f.length),g=void 0===c?h:cK(af(c),h),b=d+"";return dT?dT.call(f,b,g):f.slice(g-b.length,g)===b}});var br={getSearchInput:function(a){return"string"==typeof a.options.searchSelector?fc["default"](a.options.searchSelector):a.$toolbar.find(".search input")},sprintf:function(d){for(var g=arguments.length,c=Array(g>1?g-1:0),j=1;g>j;j++){c[j-1]=arguments[j]}var h=!0,b=0,f=d.replace(/%s/g,function(){var a=c[b++];return void 0===a?(h=!1,""):a});return h?f:""},isObject:function(a){return a instanceof Object&&!Array.isArray(a)},isEmptyObject:function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(a).length&&a.constructor===Object},isNumeric:function(a){return !isNaN(parseFloat(a))&&isFinite(a)},getFieldTitle:function(d,f){var c,h=fg(d);try{for(h.s();!(c=h.n()).done;){var g=c.value;if(g.field===f){return g.title}}}catch(b){h.e(b)}finally{h.f()}return""},setFieldIndex:function(k){var F,B=0,y=[],x=fg(k[0]);try{for(x.s();!(F=x.n()).done;){var J=F.value;B+=J.colspan||1}}catch(q){x.e(q)}finally{x.f()}for(var v=0;v<k.length;v++){y[v]=[];for(var A=0;B>A;A++){y[v][A]=!1}}for(var H=0;H<k.length;H++){var C,j=fg(k[H]);try{for(j.s();!(C=j.n()).done;){var G=C.value,E=G.rowspan||1,w=G.colspan||1,D=y[H].indexOf(!1);G.colspanIndex=D,1===w?(G.fieldIndex=D,void 0===G.field&&(G.field=D)):G.colspanGroup=G.colspan;for(var I=0;E>I;I++){for(var z=0;w>z;z++){y[H+I][D+z]=!0}}}}catch(q){j.e(q)}finally{j.f()}}},normalizeAccent:function(a){return"string"!=typeof a?a:a.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(y){var q,k,g=(q=[]).concat.apply(q,fp(y)),b=fg(y);try{for(b.s();!(k=b.n()).done;){var w,z=k.value,j=fg(z);try{for(j.s();!(w=j.n()).done;){var v=w.value;if(v.colspanGroup>1){for(var m=0,x=function(a){var c=g.find(function(d){return d.fieldIndex===a});c.visible&&m++},r=v.colspanIndex;r<v.colspanIndex+v.colspanGroup;r++){x(r)}v.colspan=m,v.visible=m>0}}}catch(p){j.e(p)}finally{j.f()}}}catch(p){b.e(p)}finally{b.f()}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var b=fc["default"]("<div/>").addClass("fixed-table-scroll-inner"),c=fc["default"]("<div/>").addClass("fixed-table-scroll-outer");c.append(b),fc["default"]("body").append(c);var a=b[0].offsetWidth;c.css("overflow","scroll");var d=b[0].offsetWidth;a===d&&(d=c[0].clientWidth),c.remove(),this.cachedWidth=a-d}return this.cachedWidth},calculateObjectValue:function(p,i,d,b){var k=i;if("string"==typeof i){var q=i.split(".");if(q.length>1){k=window;var f,j=fg(q);try{for(j.s();!(f=j.n()).done;){var g=f.value;k=k[g]}}catch(m){j.e(m)}finally{j.f()}}else{k=window[i]}}return null!==k&&"object"===fD(k)?k:"function"==typeof k?k.apply(p,d||[]):!k&&"string"==typeof i&&this.sprintf.apply(this,[i].concat(fp(d)))?this.sprintf.apply(this,[i].concat(fp(d))):b},compareObjects:function(d,h,c){var k=Object.keys(d),j=Object.keys(h);if(c&&k.length!==j.length){return !1}for(var b=0,f=k;b<f.length;b++){var g=f[b];if(j.includes(g)&&d[g]!==h[g]){return !1}}return !0},escapeHTML:function(a){return"string"==typeof a?a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):a},unescapeHTML:function(a){return"string"==typeof a?a.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#039;/g,"'").replace(/&#x60;/g,"`"):a},getRealDataAttr:function(d){for(var g=0,c=Object.entries(d);g<c.length;g++){var j=fm(c[g],2),h=j[0],b=j[1],f=h.split(/(?=[A-Z])/).join("-").toLowerCase();f!==h&&(d[f]=b,delete d[h])}return d},getItemField:function(k,h,g){var d=k;if("string"!=typeof h||k.hasOwnProperty(h)){return g?this.escapeHTML(k[h]):k[h]}var c,j=h.split("."),m=fg(j);try{for(m.s();!(c=m.n()).done;){var b=c.value;d=d&&d[b]}}catch(f){m.e(f)}finally{m.f()}return g?this.escapeHTML(d):d},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(d,f){var c,h=fg(d);try{for(h.s();!(c=h.n()).done;){var g=c.value;if(JSON.stringify(g)===JSON.stringify(f)){return d.indexOf(g)}}}catch(b){h.e(b)}finally{h.f()}return -1},trToData:function(b,c){var a=this,f=[],d=[];return c.each(function(j,g){var h=fc["default"](g),i={};i._id=h.attr("id"),i._class=h.attr("class"),i._data=a.getRealDataAttr(h.data()),i._style=h.attr("style"),h.find(">td,>th").each(function(e,r){for(var v=fc["default"](r),k=+v.attr("colspan")||1,q=+v.attr("rowspan")||1,m=e;d[j]&&d[j][m];m++){}for(var t=m;m+k>t;t++){for(var p=j;j+q>p;p++){d[p]||(d[p]=[]),d[p][t]=!0}}var o=b[m].field;i[o]=v.html().trim(),i["_".concat(o,"_id")]=v.attr("id"),i["_".concat(o,"_class")]=v.attr("class"),i["_".concat(o,"_rowspan")]=v.attr("rowspan"),i["_".concat(o,"_colspan")]=v.attr("colspan"),i["_".concat(o,"_title")]=v.attr("title"),i["_".concat(o,"_data")]=a.getRealDataAttr(v.data()),i["_".concat(o,"_style")]=v.attr("style")}),f.push(i)}),f},sort:function(d,f,c,h,g,b){return(void 0===d||null===d)&&(d=""),(void 0===f||null===f)&&(f=""),h&&d===f&&(d=g,f=b),this.isNumeric(d)&&this.isNumeric(f)?(d=parseFloat(d),f=parseFloat(f),f>d?-1*c:d>f?c:0):d===f?0:("string"!=typeof d&&(d=""+d),-1===d.localeCompare(f)?-1*c:c)},getEventName:function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return b=b||"".concat(+new Date).concat(~~(1000000*Math.random())),"".concat(a,"-").concat(b)},hasDetailViewIcon:function(a){return a.detailView&&a.detailViewIcon&&!a.cardView},getDetailViewIndexOffset:function(a){return this.hasDetailViewIcon(a)&&"right"!==a.detailViewAlign?1:0},checkAutoMergeCells:function(d){var h,c=fg(d);try{for(c.s();!(h=c.n()).done;){for(var k=h.value,j=0,b=Object.keys(k);j<b.length;j++){var f=b[j];if(f.startsWith("_")&&(f.endsWith("_rowspan")||f.endsWith("_colspan"))){return !0}}}}catch(g){c.e(g)}finally{c.f()}return !1},deepCopy:function(a){return void 0===a?a:fc["default"].extend(!0,Array.isArray(a)?[]:{},a)}},bL=50,dd=4,fk=function(){function a(c){var b=this;fw(this,a),this.rows=c.rows,this.scrollEl=c.scrollEl,this.contentEl=c.contentEl,this.callback=c.callback,this.itemHeight=c.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,c.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var d=function(){b.lastCluster!==(b.lastCluster=b.getNum())&&(b.initDOM(b.rows),b.callback())};this.scrollEl.addEventListener("scroll",d,!1),this.destroy=function(){b.contentEl.innerHtml="",b.scrollEl.removeEventListener("scroll",d,!1)}}return fQ(a,[{key:"initDOM",value:function(d,h){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=d[0]+d[0]+d[0],this.getRowsHeight(d));var c=this.initData(d,this.getNum(h)),k=c.rows.join(""),j=this.checkChanges("data",k),b=this.checkChanges("top",c.topOffset),f=this.checkChanges("bottom",c.bottomOffset),g=[];j&&b?(c.topOffset&&g.push(this.getExtra("top",c.topOffset)),g.push(k),c.bottomOffset&&g.push(this.getExtra("bottom",c.bottomOffset)),this.contentEl.innerHTML=g.join(""),h&&(this.contentEl.scrollTop=this.cache.scrollTop)):f&&(this.contentEl.lastChild.style.height="".concat(c.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var b=this.contentEl.children,c=b[Math.floor(b.length/2)];this.itemHeight=c.offsetHeight}this.blockHeight=this.itemHeight*bL,this.clusterRows=bL*dd,this.clusterHeight=this.blockHeight*dd}},{key:"getNum",value:function(b){return this.scrollTop=b?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(k,h){if(k.length<bL){return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:k}}var g=Math.max((this.clusterRows-bL)*h,0),d=g+this.clusterRows,c=Math.max(g*this.itemHeight,0),j=Math.max((k.length-d)*this.itemHeight,0),m=[],b=g;1>c&&b++;for(var f=g;d>f;f++){k[f]&&m.push(k[f])}return{topOffset:c,bottomOffset:j,rowsAbove:b,rows:m}}},{key:"checkChanges",value:function(c,d){var b=d!==this.cache[c];return this.cache[c]=d,b}},{key:"getExtra",value:function(c,d){var b=document.createElement("tr");return b.className="virtual-scroll-".concat(c),d&&(b.style.height="".concat(d,"px")),b.outerHTML}}]),a}(),d5=function(){function a(d,c){fw(this,a),this.options=c,this.$el=fc["default"](d),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return fQ(a,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var c=this.options;this.constants=fb.CONSTANTS,this.constants.theme=fc["default"].fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var d=c.buttonsPrefix?"".concat(c.buttonsPrefix,"-"):"";this.constants.buttonsClass=[c.buttonsPrefix,d+c.buttonsClass,br.sprintf("".concat(d,"%s"),c.iconSize)].join(" ").trim(),this.buttons=br.calculateObjectValue(this,c.buttons,[],{}),"object"!==fD(this.buttons)&&(this.buttons={}),"string"==typeof c.icons&&(c.icons=br.calculateObjectValue(null,c.icons))}},{key:"initLocale",value:function(){if(this.options.locale){var c=fc["default"].fn.bootstrapTable.locales,d=this.options.locale.split(/-|_/);d[0]=d[0].toLowerCase(),d[1]&&(d[1]=d[1].toUpperCase()),c[this.options.locale]?fc["default"].extend(this.options,c[this.options.locale]):c[d.join("-")]?fc["default"].extend(this.options,c[d.join("-")]):c[d[0]]&&fc["default"].extend(this.options,c[d[0]])}}},{key:"initContainer",value:function(){var d=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",f=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",c=br.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=fc["default"]('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(d,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(c,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(f,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=fc["default"]("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var d=this,c=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=fc["default"]('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each(function(g,i){var h=fc["default"](i),f=[];h.find("th").each(function(k,l){var j=fc["default"](l);void 0!==j.data("field")&&j.data("field","".concat(j.data("field"))),f.push(fc["default"].extend({},{title:j.html(),"class":j.attr("class"),titleTooltip:j.attr("title"),rowspan:j.attr("rowspan")?+j.attr("rowspan"):void 0,colspan:j.attr("colspan")?+j.attr("colspan"):void 0},j.data()))}),c.push(f),h.attr("class")&&d._headerTrClasses.push(h.attr("class")),h.attr("style")&&d._headerTrStyles.push(h.attr("style"))}),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=fc["default"].extend(!0,[],c,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],br.setFieldIndex(this.options.columns),this.options.columns.forEach(function(f,g){f.forEach(function(j,k){var h=fc["default"].extend({},a.COLUMN_DEFAULTS,j);void 0!==h.fieldIndex&&(d.columns[h.fieldIndex]=h,d.fieldsColumnsIndex[h.field]=h.fieldIndex),d.options.columns[g][k]=h})}),!this.options.data.length){var e=br.trToData(this.columns,this.$el.find(">tbody>tr"));e.length&&(this.options.data=e,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=br.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var d=this,f={},c=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},br.updateFieldGroup(this.options.columns),this.options.columns.forEach(function(k,j){var h=[];h.push("<tr".concat(br.sprintf(' class="%s"',d._headerTrClasses[j])," ").concat(br.sprintf(' style="%s"',d._headerTrStyles[j]),">"));var i="";if(0===j&&br.hasDetailViewIcon(d.options)){var e=d.options.columns.length>1?' rowspan="'.concat(d.options.columns.length,'"'):"";i='<th class="detail"'.concat(e,'>\n          <div class="fht-cell"></div>\n          </th>')}i&&"right"!==d.options.detailViewAlign&&h.push(i),k.forEach(function(G,D){var B=br.sprintf(' class="%s"',G["class"]),F=G.widthUnit,L=parseFloat(G.width),H=br.sprintf("text-align: %s; ",G.halign?G.halign:G.align),A=br.sprintf("text-align: %s; ",G.align),K=br.sprintf("vertical-align: %s; ",G.valign);if(K+=br.sprintf("width: %s; ",!G.checkbox&&!G.radio||L?L?L+F:void 0:G.showSelectTitle?void 0:"36px"),void 0!==G.fieldIndex||G.visible){var J=br.calculateObjectValue(null,d.options.headerStyle,[G]),C=[],I="";if(J&&J.css){for(var z=0,M=Object.entries(J.css);z<M.length;z++){var E=fm(M[z],2),q=E[0],t=E[1];C.push("".concat(q,": ").concat(t))}}if(J&&J.classes&&(I=br.sprintf(' class="%s"',G["class"]?[G["class"],J.classes].join(" "):J.classes)),void 0!==G.fieldIndex){if(d.header.fields[G.fieldIndex]=G.field,d.header.styles[G.fieldIndex]=A+K,d.header.classes[G.fieldIndex]=B,d.header.formatters[G.fieldIndex]=G.formatter,d.header.detailFormatters[G.fieldIndex]=G.detailFormatter,d.header.events[G.fieldIndex]=G.events,d.header.sorters[G.fieldIndex]=G.sorter,d.header.sortNames[G.fieldIndex]=G.sortName,d.header.cellStyles[G.fieldIndex]=G.cellStyle,d.header.searchables[G.fieldIndex]=G.searchable,!G.visible){return}if(d.options.cardView&&!G.cardVisible){return}f[G.field]=G}h.push("<th".concat(br.sprintf(' title="%s"',G.titleTooltip)),G.checkbox||G.radio?br.sprintf(' class="bs-checkbox %s"',G["class"]||""):I||B,br.sprintf(' style="%s"',H+K+C.join("; ")),br.sprintf(' rowspan="%s"',G.rowspan),br.sprintf(' colspan="%s"',G.colspan),br.sprintf(' data-field="%s"',G.field),0===D&&j>0?" data-not-first-th":"",">"),h.push(br.sprintf('<div class="th-inner %s">',d.options.sortable&&G.sortable?"sortable both":""));var o=d.options.escape?br.escapeHTML(G.title):G.title,s=o;G.checkbox&&(o="",!d.options.singleSelect&&d.options.checkboxHeader&&(o='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),d.header.stateField=G.field),G.radio&&(o="",d.header.stateField=G.field),!o&&G.showSelectTitle&&(o+=s),h.push(o),h.push("</div>"),h.push('<div class="fht-cell"></div>'),h.push("</div>"),h.push("</th>")}}),i&&"right"===d.options.detailViewAlign&&h.push(i),h.push("</tr>"),h.length>3&&c.push(h.join(""))}),this.$header.html(c.join("")),this.$header.find("th[data-field]").each(function(h,e){fc["default"](e).data(f[fc["default"](e).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(j){var h=fc["default"](j.currentTarget);return d.options.detailView&&!h.parent().hasClass("bs-checkbox")&&h.closest(".bootstrap-table")[0]!==d.$container[0]?!1:void (d.options.sortable&&h.parent().data().sortable&&d.onSort(j))}),this.$header.children().children().off("keypress").on("keypress",function(j){if(d.options.sortable&&fc["default"](j.currentTarget).data().sortable){var h=j.keyCode||j.which;13===h&&d.onSort(j)}});var g=br.getEventName("resize.bootstrap-table",this.$el.attr("id"));fc["default"](window).off(g),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),fc["default"](window).on(g,function(){return d.resetView()})),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(j){j.stopPropagation();var h=fc["default"](j.currentTarget).prop("checked");d[h?"checkAll":"uncheckAll"](),d.updateSelected()})}},{key:"initData",value:function(c,d){"append"===d?this.options.data=this.options.data.concat(c):"prepend"===d?this.options.data=[].concat(c).concat(this.options.data):(c=c||br.deepCopy(this.options.data),this.options.data=Array.isArray(c)?c:c[this.options.dataField]),this.data=fp(this.options.data),this.options.sortReset&&(this.unsortedData=fp(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var d=this,f=this.options.sortName,c="desc"===this.options.sortOrder?-1:1,h=this.header.fields.indexOf(this.options.sortName),g=0;-1!==h?(this.options.sortStable&&this.data.forEach(function(i,j){i.hasOwnProperty("_position")||(i._position=j)}),this.options.customSort?br.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort(function(m,i){d.header.sortNames[h]&&(f=d.header.sortNames[h]);var j=br.getItemField(m,f,d.options.escape),k=br.getItemField(i,f,d.options.escape),e=br.calculateObjectValue(d.header,d.header.sorters[h],[j,k,m,i]);return void 0!==e?d.options.sortStable&&0===e?c*(m._position-i._position):c*e:br.sort(j,k,c,d.options.sortStable,m._position,i._position)}),void 0!==this.options.sortClass&&(clearTimeout(g),g=setTimeout(function(){d.$el.removeClass(d.options.sortClass);var i=d.$header.find('[data-field="'.concat(d.options.sortName,'"]')).index();d.$el.find("tr td:nth-child(".concat(i+1,")")).addClass(d.options.sortClass)},250))):this.options.sortReset&&(this.data=fp(this.unsortedData))}},{key:"onSort",value:function(f){var g=f.type,d=f.currentTarget,j="keypress"===g?fc["default"](d):fc["default"](d).parent(),h=this.$header.find("th").eq(j.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===j.data("field")){var c=this.options.sortOrder;void 0===c?this.options.sortOrder="asc":"asc"===c?this.options.sortOrder="desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else{this.options.sortName=j.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===j.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[j.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[j.data("field")]].order}return this.trigger("sort",this.options.sortName,this.options.sortOrder),j.add(h).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination&&this.options.serverSort?(this.options.pageNumber=1,void this.initServer(this.options.silentSort)):(this.initSort(),void this.initBody())}},{key:"initToolbar",value:function(){var di,fn=this,ei=this.options,ee=[],ge=0,dn=0;this.$toolbar.find(".bs-bars").children().length&&fc["default"]("body").append(fc["default"](ei.toolbar)),this.$toolbar.html(""),("string"==typeof ei.toolbar||"object"===fD(ei.toolbar))&&fc["default"](br.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,ei.toolbarAlign)).appendTo(this.$toolbar).append(fc["default"](ei.toolbar)),ee=['<div class="'.concat(["columns","columns-".concat(ei.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(ei.buttonsAlign)].join(" "),'">')],"string"==typeof ei.buttonsOrder&&(ei.buttonsOrder=ei.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{search:{text:ei.formatSearch(),icon:ei.icons.search,render:!1,event:this.toggleShowSearch,attributes:{"aria-label":ei.formatShowSearch(),title:ei.formatShowSearch()}},paginationSwitch:{text:ei.pagination?ei.formatPaginationSwitchUp():ei.formatPaginationSwitchDown(),icon:ei.pagination?ei.icons.paginationSwitchDown:ei.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":ei.formatPaginationSwitch(),title:ei.formatPaginationSwitch()}},refresh:{text:ei.formatRefresh(),icon:ei.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":ei.formatRefresh(),title:ei.formatRefresh()}},toggle:{text:ei.formatToggle(),icon:ei.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":ei.formatToggleOn(),title:ei.formatToggleOn()}},fullscreen:{text:ei.formatFullscreen(),icon:ei.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":ei.formatFullscreen(),title:ei.formatFullscreen()}},columns:{render:!1,html:function s(){var e=[];if(e.push('<div class="keep-open '.concat(fn.constants.classes.buttonsDropdown,'" title="').concat(ei.formatColumns(),'">\n            <button class="').concat(fn.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(fn.constants.dataToggle,'="dropdown"\n            aria-label="Columns" title="').concat(ei.formatColumns(),'">\n            ').concat(ei.showButtonIcons?br.sprintf(fn.constants.html.icon,ei.iconsPrefix,ei.icons.columns):"","\n            ").concat(ei.showButtonText?ei.formatColumns():"","\n            ").concat(fn.constants.html.dropdownCaret,"\n            </button>\n            ").concat(fn.constants.html.toolbarDropdown[0])),ei.showColumnsSearch&&(e.push(br.sprintf(fn.constants.html.toolbarDropdownItem,br.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',fn.constants.classes.input,ei.formatSearch()))),e.push(fn.constants.html.toolbarDropdownSeparator)),ei.showColumnsToggleAll){var d=fn.getVisibleColumns().length===fn.columns.filter(function(f){return !fn.isSelectionColumn(f)}).length;e.push(br.sprintf(fn.constants.html.toolbarDropdownItem,br.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',d?'checked="checked"':"",ei.formatColumnsToggleAll()))),e.push(fn.constants.html.toolbarDropdownSeparator)}var c=0;return fn.columns.forEach(function(f){f.visible&&c++}),fn.columns.forEach(function(g,j){if(!fn.isSelectionColumn(g)&&(!ei.cardView||g.cardVisible)&&!g.ignore){var f=g.visible?' checked="checked"':"",h=c<=ei.minimumCountColumns&&f?' disabled="disabled"':"";g.switchable&&(e.push(br.sprintf(fn.constants.html.toolbarDropdownItem,br.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',g.field,j,f,h,g.title))),dn++)}}),e.push(fn.constants.html.toolbarDropdown[1],"</div>"),e.join("")}}});for(var eo={},ft=0,fa=Object.entries(this.buttons);ft<fa.length;ft++){var de=fm(fa[ft],2),fo=de[0],fi=de[1],ea=void 0;if(fi.hasOwnProperty("html")){"function"==typeof fi.html?ea=fi.html():"string"==typeof fi.html&&(ea=fi.html)}else{if(ea='<button class="'.concat(this.constants.buttonsClass,'" type="button" name="').concat(fo,'"'),fi.hasOwnProperty("attributes")){for(var fe=0,ga=Object.entries(fi.attributes);fe<ga.length;fe++){var en=fm(ga[fe],2),da=en[0],Z=en[1];ea+=" ".concat(da,'="').concat(Z,'"')}}ea+=">",ei.showButtonIcons&&fi.hasOwnProperty("icon")&&(ea+="".concat(br.sprintf(this.constants.html.icon,ei.iconsPrefix,fi.icon)," ")),ei.showButtonText&&fi.hasOwnProperty("text")&&(ea+=fi.text),ea+="</button>"}eo[fo]=ea;var ct="show".concat(fo.charAt(0).toUpperCase()).concat(fo.substring(1)),es=ei[ct];!(!fi.hasOwnProperty("render")||fi.hasOwnProperty("render")&&fi.render)||void 0!==es&&es!==!0||(ei[ct]=!0),ei.buttonsOrder.includes(fo)||ei.buttonsOrder.push(fo)}var ai,Q=fg(ei.buttonsOrder);try{for(Q.s();!(ai=Q.n()).done;){var ce=ai.value,ae=ei["show".concat(ce.charAt(0).toUpperCase()).concat(ce.substring(1))];ae&&ee.push(eo[ce])}}catch(be){Q.e(be)}finally{Q.f()}ee.push("</div>"),(this.showToolbar||ee.length>2)&&this.$toolbar.append(ee.join("")),ei.showSearch&&this.$toolbar.find('button[name="showSearch"]').off("click").on("click",function(){return fn.toggleShowSearch()});for(var cn=0,co=Object.entries(this.buttons);cn<co.length;cn++){var aa=fm(co[cn],2),bt=aa[0],et=aa[1];if(et.hasOwnProperty("event")){if("function"==typeof et.event||"string"==typeof et.event){var gi=function(){var c="string"==typeof et.event?window[et.event]:et.event;return fn.$toolbar.find('button[name="'.concat(bt,'"]')).off("click").on("click",function(){return c.call(fn)}),"continue"}();if("continue"===gi){continue}}for(var an=function(){var d=fm(ca[bo],2),c=d[0],f=d[1],e="string"==typeof f?window[f]:f;fn.$toolbar.find('button[name="'.concat(bt,'"]')).off(c).on(c,function(){return e.call(fn)})},bo=0,ca=Object.entries(et.event);bo<ca.length;bo++){an()}}}if(ei.showColumns){di=this.$toolbar.find(".keep-open");var y=di.find('input[type="checkbox"]:not(".toggle-all")'),ci=di.find('input[type="checkbox"].toggle-all');if(dn<=ei.minimumCountColumns&&di.find("input").prop("disabled",!0),di.find("li, label").off("click").on("click",function(c){c.stopImmediatePropagation()}),y.off("click").on("click",function(d){var c=d.currentTarget,e=fc["default"](c);fn._toggleColumn(e.val(),e.prop("checked"),!1),fn.trigger("column-switch",e.data("field"),e.prop("checked")),ci.prop("checked",y.filter(":checked").length===fn.columns.filter(function(f){return !fn.isSelectionColumn(f)}).length)}),ci.off("click").on("click",function(d){var c=d.currentTarget;fn._toggleAllColumns(fc["default"](c).prop("checked"))}),ei.showColumnsSearch){var at=di.find('[name="columnsSearch"]'),bi=di.find(".dropdown-item-marker");at.on("keyup paste change",function(d){var f=d.currentTarget,c=fc["default"](f),g=c.val().toLowerCase();bi.show(),y.each(function(k,l){var j=fc["default"](l),m=j.parents(".dropdown-item-marker"),h=m.text().toLowerCase();h.includes(g)||m.hide()})})}}var ao=function(d){var c="keyup drop blur mouseup";d.off(c).on(c,function(e){ei.searchOnEnterKey&&13!==e.keyCode||[37,38,39,40].includes(e.keyCode)||(clearTimeout(ge),ge=setTimeout(function(){fn.onSearch({currentTarget:e.currentTarget})},ei.searchTimeOut))})};if((ei.search||this.showSearchClearButton)&&"string"!=typeof ei.searchSelector){ee=[];var J=br.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,ei.formatSearch(),ei.showButtonIcons?br.sprintf(this.constants.html.icon,ei.iconsPrefix,ei.icons.search):"",ei.showButtonText?ei.formatSearch():""),cs=br.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,ei.formatClearSearch(),ei.showButtonIcons?br.sprintf(this.constants.html.icon,ei.iconsPrefix,ei.icons.clearSearch):"",ei.showButtonText?ei.formatClearSearch():""),dt='<input class="'.concat(this.constants.classes.input,"\n        ").concat(br.sprintf(" %s%s",this.constants.classes.inputPrefix,ei.iconSize),'\n        search-input" type="search" placeholder="').concat(ei.formatSearch(),'" autocomplete="off">'),v=dt;if(ei.showSearchButton||ei.showSearchClearButton){var bn=(ei.showSearchButton?J:"")+(ei.showSearchClearButton?cs:"");v=ei.search?br.sprintf(this.constants.html.inputGroup,dt,bn):bn}ee.push(br.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(ei.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),v)),this.$toolbar.append(ee.join(""));var ba=br.getSearchInput(this);ei.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",function(){clearTimeout(ge),ge=setTimeout(function(){fn.onSearch({currentTarget:ba})},ei.searchTimeOut)}),ei.searchOnEnterKey&&ao(ba)):ao(ba),ei.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click(function(){fn.resetSearch()})}else{if("string"==typeof ei.searchSelector){var i=br.getSearchInput(this);ao(i)}}}},{key:"onSearch",value:function(){var d=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},f=d.currentTarget,c=d.firedByInitSearchText,h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:!0;if(void 0!==f&&fc["default"](f).length&&h){var g=fc["default"](f).val().trim();if(this.options.trimOnSearch&&fc["default"](f).val()!==g&&fc["default"](f).val(g),this.searchText===g){return}(f===br.getSearchInput(this)[0]||fc["default"](f).hasClass("search-input"))&&(this.searchText=g,this.options.searchText=g)}c||(this.options.pageNumber=1),this.initSearch(),c?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var d=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch){return this.data=br.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),void (this.options.sortReset&&(this.unsortedData=fp(this.data)))}var f=this.searchText&&(this.fromHtml?br.escapeHTML(this.searchText):this.searchText).toLowerCase(),c=br.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(f=br.normalizeAccent(f)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter(function(h){return d.filterOptions.filterAlgorithm.apply(null,[h,c])}):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=c?this.options.data.filter(function(j){var l=d.filterOptions.filterAlgorithm;if("and"===l){for(var k in c){if(Array.isArray(c[k])&&!c[k].includes(j[k])||!Array.isArray(c[k])&&j[k]!==c[k]){return !1}}}else{if("or"===l){var h=!1;for(var i in c){(Array.isArray(c[i])&&c[i].includes(j[i])||!Array.isArray(c[i])&&j[i]===c[i])&&(h=!0)}return h}}return !0}):fp(this.options.data));var g=this.getVisibleFields();this.data=f?this.data.filter(function(n,k){for(var A=0;A<d.header.fields.length;A++){if(d.header.searchables[A]&&(!d.options.visibleSearch||-1!==g.indexOf(d.header.fields[A]))){var D=br.isNumeric(d.header.fields[A])?parseInt(d.header.fields[A],10):d.header.fields[A],e=d.columns[d.fieldsColumnsIndex[D]],m=void 0;if("string"==typeof D){m=n;for(var y=D.split("."),q=0;q<y.length;q++){null!==m[y[q]]&&(m=m[y[q]])}}else{m=n[D]}if(d.options.searchAccentNeutralise&&(m=br.normalizeAccent(m)),e&&e.searchFormatter&&(m=br.calculateObjectValue(e,d.header.formatters[A],[m,n,k,e.field],m)),"string"==typeof m||"number"==typeof m){if(d.options.strictSearch){if("".concat(m).toLowerCase()===f){return !0}}else{var C=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm,x=C.exec(d.searchText),w=!1;if(x){var j=x[1]||"".concat(x[5],"l"),t=x[2]||x[3],B=parseInt(m,10),z=parseInt(t,10);switch(j){case">":case"<l":w=B>z;break;case"<":case">l":w=z>B;break;case"<=":case"=<":case">=l":case"=>l":w=z>=B;break;case">=":case"=>":case"<=l":case"=<l":w=B>=z}}if(w||"".concat(m).toLowerCase().includes(f)){return !0}}}}}return !1}):this.data,this.options.sortReset&&(this.unsortedData=fp(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var O=this,K=this.options;if(!K.pagination){return void this.$pagination.hide()}this.$pagination.show();var G,F,T,C,D,I,Q,L=[],B=!1,P=this.getData({includeHiddenRows:!1}),N=K.pageList;if("string"==typeof N&&(N=N.replace(/\[|\]| /g,"").toLowerCase().split(",")),N=N.map(function(c){return"string"==typeof c?c.toLowerCase()===K.formatAllRows().toLowerCase()||["all","unlimited"].includes(c.toLowerCase())?K.formatAllRows():+c:c}),this.paginationParts=K.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==K.sidePagination&&(K.totalRows=P.length),this.totalPages=0,K.totalRows&&(K.pageSize===K.formatAllRows()&&(K.pageSize=K.totalRows,B=!0),this.totalPages=~~((K.totalRows-1)/K.pageSize)+1,K.totalPages=this.totalPages),this.totalPages>0&&K.pageNumber>this.totalPages&&(K.pageNumber=this.totalPages),this.pageFrom=(K.pageNumber-1)*K.pageSize+1,this.pageTo=K.pageNumber*K.pageSize,this.pageTo>K.totalRows&&(this.pageTo=K.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&L.push('<div class="'.concat(this.constants.classes.pull,"-").concat(K.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var E=this.paginationParts.includes("pageInfoShort")?K.formatDetailPagination(K.totalRows):K.formatShowingRows(this.pageFrom,this.pageTo,K.totalRows,K.totalNotFiltered);L.push('<span class="pagination-info">\n      '.concat(E,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){L.push('<div class="page-list">');var M=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(B?K.formatAllRows():K.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];N.forEach(function(c,e){if(!K.smartDisplay||0===e||N[e-1]<K.totalRows||c===K.formatAllRows()){var d;d=B?c===K.formatAllRows()?O.constants.classes.dropdownActive:"":c===K.pageSize?O.constants.classes.dropdownActive:"",M.push(br.sprintf(O.constants.html.pageDropdownItem,d,c))}}),M.push("".concat(this.constants.html.pageDropdown[1],"</div>")),L.push(K.formatRecordsPerPage(M.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&L.push("</div></div>"),this.paginationParts.includes("pageList")){L.push('<div class="'.concat(this.constants.classes.pull,"-").concat(K.paginationHAlign,' pagination">'),br.sprintf(this.constants.html.pagination[0],br.sprintf(" pagination-%s",K.iconSize)),br.sprintf(this.constants.html.paginationItem," page-pre",K.formatSRPaginationPreText(),K.paginationPreText)),this.totalPages<K.paginationSuccessivelySize?(F=1,T=this.totalPages):(F=K.pageNumber-K.paginationPagesBySide,T=F+2*K.paginationPagesBySide),K.pageNumber<K.paginationSuccessivelySize-1&&(T=K.paginationSuccessivelySize),K.paginationSuccessivelySize>this.totalPages-F&&(F=F-(K.paginationSuccessivelySize-(this.totalPages-F))+1),1>F&&(F=1),T>this.totalPages&&(T=this.totalPages);var A=Math.round(K.paginationPagesBySide/2),R=function(c){var d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return br.sprintf(O.constants.html.paginationItem,d+(c===K.pageNumber?" ".concat(O.constants.classes.paginationActive):""),K.formatSRPaginationPageText(c),c)};if(F>1){var H=K.paginationPagesBySide;for(H>=F&&(H=F-1),G=1;H>=G;G++){L.push(R(G))}F-1===H+1?(G=F-1,L.push(R(G))):F-1>H&&(F-2*K.paginationPagesBySide>K.paginationPagesBySide&&K.paginationUseIntermediate?(G=Math.round((F-A)/2+A),L.push(R(G," page-intermediate"))):L.push(br.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(G=F;T>=G;G++){L.push(R(G))}if(this.totalPages>T){var q=this.totalPages-(K.paginationPagesBySide-1);for(T>=q&&(q=T+1),T+1===q-1?(G=T+1,L.push(R(G))):q>T+1&&(this.totalPages-T>2*K.paginationPagesBySide&&K.paginationUseIntermediate?(G=Math.round((this.totalPages-A-T)/2+T),L.push(R(G," page-intermediate"))):L.push(br.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),G=q;G<=this.totalPages;G++){L.push(R(G))}}L.push(br.sprintf(this.constants.html.paginationItem," page-next",K.formatSRPaginationNextText(),K.paginationNextText)),L.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(L.join(""));var z=["bottom","both"].includes(K.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";if(this.$pagination.last().find(".page-list > div").addClass(z),!K.onlyInfoPagination&&(C=this.$pagination.find(".page-list a"),D=this.$pagination.find(".page-pre"),I=this.$pagination.find(".page-next"),Q=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),K.smartDisplay&&(N.length<2||K.totalRows<=N[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),K.paginationLoop||(1===K.pageNumber&&D.addClass("disabled"),K.pageNumber===this.totalPages&&I.addClass("disabled")),B&&(K.pageSize=K.formatAllRows()),C.off("click").on("click",function(c){return O.onPageListChange(c)}),D.off("click").on("click",function(c){return O.onPagePre(c)}),I.off("click").on("click",function(c){return O.onPageNext(c)}),Q.off("click").on("click",function(c){return O.onPageNumber(c)}),this.options.showPageGo)){var j=this,t=this.$pagination.find("ul.pagination"),J=t.find("li.pageGo");J.length||(J=fl('<li class="pageGo">'+br.sprintf('<input type="text" class="form-control" value="%s">',this.options.pageNumber)+('<button class="btn'+br.sprintf(" btn-%s",this.constants.buttonsClass)+br.sprintf(" btn-%s",K.iconSize)+'" title="'+K.formatPageGo()+'"  type="button">'+K.formatPageGo())+"</button></li>").appendTo(t),J.find("button").click(function(){var c=parseInt(J.find("input").val())||1;(1>c||c>j.options.totalPages)&&(c=1),j.selectPage(c)}))}}},{key:"updatePagination",value:function(c){c&&fc["default"](c.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(c){c.preventDefault();var d=fc["default"](c.currentTarget);return d.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=d.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+d.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(c),!1}},{key:"onPagePre",value:function(c){return c.preventDefault(),this.options.pageNumber-1===0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(c),!1}},{key:"onPageNext",value:function(c){return c.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(c),!1}},{key:"onPageNumber",value:function(c){return c.preventDefault(),this.options.pageNumber!==+fc["default"](c.currentTarget).text()?(this.options.pageNumber=+fc["default"](c.currentTarget).text(),this.updatePagination(c),!1):void 0}},{key:"initRow",value:function(G,X,M,L){var ae=this,J=[],Q={},Z=[],U="",F={},Y=[];if(!(br.findIndex(this.hiddenRows,G)>-1)){if(Q=br.calculateObjectValue(this.options,this.options.rowStyle,[G,X],Q),Q&&Q.css){for(var W=0,K=Object.entries(Q.css);W<K.length;W++){var V=fm(K[W],2),E=V[0],aa=V[1];Z.push("".concat(E,": ").concat(aa))}}if(F=br.calculateObjectValue(this.options,this.options.rowAttributes,[G,X],F)){for(var N=0,z=Object.entries(F);N<z.length;N++){var D=fm(z[N],2),j=D[0],B=D[1];Y.push("".concat(j,'="').concat(br.escapeHTML(B),'"'))}}if(G._data&&!br.isEmptyObject(G._data)){for(var R=0,s=Object.entries(G._data);R<s.length;R++){var i=fm(s[R],2),H=i[0],q=i[1];if("index"===H){return}U+=" data-".concat(H,"='").concat("object"===fD(q)?JSON.stringify(q):q,"'")}}J.push("<tr",br.sprintf(" %s",Y.length?Y.join(" "):void 0),br.sprintf(' id="%s"',Array.isArray(G)?void 0:G._id),br.sprintf(' class="%s"',Q.classes||(Array.isArray(G)?void 0:G._class)),br.sprintf(' style="%s"',Array.isArray(G)?void 0:G._style),' data-index="'.concat(X,'"'),br.sprintf(' data-uniqueid="%s"',br.getItemField(G,this.options.uniqueId,!1)),br.sprintf(' data-has-detail-view="%s"',this.options.detailView&&br.calculateObjectValue(null,this.options.detailFilter,[X,G])?"true":void 0),br.sprintf("%s",U),">"),this.options.cardView&&J.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var A="";return br.hasDetailViewIcon(this.options)&&(A="<td>",br.calculateObjectValue(null,this.options.detailFilter,[X,G])&&(A+='\n          <a class="detail-icon" href="#">\n          '.concat(br.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),A+="</td>"),A&&"right"!==this.options.detailViewAlign&&J.push(A),this.header.fields.forEach(function(eo,dt){var dn="",ee=br.getItemField(G,eo,ae.options.escape),es="",de="",fe={},fa="",di=ae.header.classes[dt],et="",da="",fi="",ea="",co="",ct="",r=ae.columns[dt];if((!ae.fromHtml&&!ae.autoMergeCells||void 0!==ee||r.checkbox||r.radio)&&r.visible&&(!ae.options.cardView||r.cardVisible)){if(r.escape&&(ee=br.escapeHTML(ee)),Z.concat([ae.header.styles[dt]]).length&&(da+="".concat(Z.concat([ae.header.styles[dt]]).join("; "))),G["_".concat(eo,"_style")]&&(da+="".concat(G["_".concat(eo,"_style")])),da&&(et=' style="'.concat(da,'"')),G["_".concat(eo,"_id")]&&(fa=br.sprintf(' id="%s"',G["_".concat(eo,"_id")])),G["_".concat(eo,"_class")]&&(di=br.sprintf(' class="%s"',G["_".concat(eo,"_class")])),G["_".concat(eo,"_rowspan")]&&(ea=br.sprintf(' rowspan="%s"',G["_".concat(eo,"_rowspan")])),G["_".concat(eo,"_colspan")]&&(co=br.sprintf(' colspan="%s"',G["_".concat(eo,"_colspan")])),G["_".concat(eo,"_title")]&&(ct=br.sprintf(' title="%s"',G["_".concat(eo,"_title")])),fe=br.calculateObjectValue(ae.header,ae.header.cellStyles[dt],[ee,G,X,eo],fe),fe.classes&&(di=' class="'.concat(fe.classes,'"')),fe.css){for(var cs=[],ei=0,an=Object.entries(fe.css);ei<an.length;ei++){var e=fm(an[ei],2),ca=e[0],ai=e[1];cs.push("".concat(ca,": ").concat(ai))}et=' style="'.concat(cs.concat(ae.header.styles[dt]).join("; "),'"')}if(es=br.calculateObjectValue(r,ae.header.formatters[dt],[ee,G,X,eo],ee),r.checkbox||r.radio||(es=void 0===es||null===es?ae.options.undefinedText:es),r.searchable&&ae.searchText&&ae.options.searchHighlight){var be="",ci=RegExp("(".concat(ae.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),")"),"gim"),cn="<mark>$1</mark>",t=es&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(es);if(t){var bo=(new DOMParser).parseFromString(""+es,"text/html").documentElement.textContent,en=bo.replace(ci,cn);be=es.replace(RegExp("(>\\s*)(".concat(bo,")(\\s*)"),"gm"),"$1".concat(en,"$3"))}else{be=(""+es).replace(ci,cn)}es=br.calculateObjectValue(r,r.searchHighlightFormatter,[es,ae.searchText],be)}if(G["_".concat(eo,"_data")]&&!br.isEmptyObject(G["_".concat(eo,"_data")])){for(var fn=0,ao=Object.entries(G["_".concat(eo,"_data")]);fn<ao.length;fn++){var bn=fm(ao[fn],2),bt=bn[0],c=bn[1];if("index"===bt){return}fi+=" data-".concat(bt,'="').concat(c,'"')}}if(r.checkbox||r.radio){de=r.checkbox?"checkbox":de,de=r.radio?"radio":de;var ce=r["class"]||"",ba=br.isObject(es)&&es.hasOwnProperty("checked")?es.checked:(es===!0||ee)&&es!==!1,bi=!r.checkboxEnabled||es&&es.disabled;dn=""+(ae.options.cardView?'<div class="card-view '.concat(ce,'">'):'<td class="bs-checkbox '.concat(ce,'"').concat(di).concat(et,">"))+'<label>\n            <input\n            data-index="'.concat(X,'"\n            name="').concat(ae.options.selectItemName,'"\n            type="').concat(de,'"\n            ').concat(br.sprintf('value="%s"',G[ae.options.idField]),"\n            ").concat(br.sprintf('checked="%s"',ba?"checked":void 0),"\n            ").concat(br.sprintf('disabled="%s"',bi?"disabled":void 0)," />\n            <span></span>\n            </label>")+(ae.header.formatters[dt]&&"string"==typeof es?es:"")+(ae.options.cardView?"</div>":"</td>"),G[ae.header.stateField]=es===!0||!!ee||es&&es.checked}else{if(ae.options.cardView){var at=ae.options.showHeader?'<span class="card-view-title '.concat(fe.classes,'"').concat(et,">").concat(br.getFieldTitle(ae.columns,eo),"</span>"):"";dn='<div class="card-view">'.concat(at,'<span class="card-view-value ').concat(fe.classes,'"').concat(et,">").concat(es,"</span></div>"),ae.options.smartDisplay&&""===es&&(dn='<div class="card-view"></div>')}else{dn="<td".concat(fa).concat(di).concat(et).concat(fi).concat(ea).concat(co).concat(ct,">").concat(es,"</td>")}}J.push(dn)}}),A&&"right"===this.options.detailViewAlign&&J.push(A),this.options.cardView&&J.push("</div></td>"),J.push("</tr>"),J.join("")}}},{key:"initBody",value:function(m){var j=this,h=this.getData();this.trigger("pre-body",h),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=fc["default"]("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=h.length);var f=[],d=fc["default"](document.createDocumentFragment()),k=!1;this.autoMergeCells=br.checkAutoMergeCells(h.slice(this.pageFrom-1,this.pageTo));for(var p=this.pageFrom-1;p<this.pageTo;p++){var c=h[p],g=this.initRow(c,p,h,d);k=k||!!g,g&&"string"==typeof g&&(this.options.virtualScroll?f.push(g):d.append(g))}k?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new fk({rows:f,fixedScroll:m,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(){j.fitHeader(),j.initBodyEvent()}})):this.$body.html(d):this.$body.html('<tr class="no-records-found">'.concat(br.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+br.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),m||this.scrollTo(0),this.initBodyEvent(),this.updateSelected(),this.initFooter(),this.resetView(),"server"!==this.options.sidePagination&&(this.options.totalRows=h.length),this.trigger("post-body",h)}},{key:"initBodyEvent",value:function(){var c=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(v){var p=fc["default"](v.currentTarget),k=p.parent(),j=fc["default"](v.target).parents(".card-views").children(),y=fc["default"](v.target).parents(".card-view"),A=k.data("index"),g=c.data[A],m=c.options.cardView?j.index(y):p[0].cellIndex,x=c.getVisibleFields(),q=x[m-br.getDetailViewIndexOffset(c.options)],z=c.columns[c.fieldsColumnsIndex[q]],w=br.getItemField(g,q,c.options.escape);if(!p.find(".detail-icon").length){if(c.trigger("click"===v.type?"click-cell":"dbl-click-cell",q,w,g,p),c.trigger("click"===v.type?"click-row":"dbl-click-row",g,k,q),"click"===v.type&&c.options.clickToSelect&&z.clickToSelect&&!br.calculateObjectValue(c.options,c.options.ignoreClickToSelectOn,[v.target])){var t=k.find(br.sprintf('[name="%s"]',c.options.selectItemName));t.length&&t[0].click()}"click"===v.type&&c.options.detailViewByClick&&c.toggleDetailView(A,c.header.detailFormatters[c.fieldsColumnsIndex[q]])}}).off("mousedown").on("mousedown",function(d){c.multipleSelectRowCtrlKey=d.ctrlKey||d.metaKey,c.multipleSelectRowShiftKey=d.shiftKey}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(d){return d.preventDefault(),c.toggleDetailView(fc["default"](d.currentTarget).parent().parent().data("index")),!1}),this.$selectItem=this.$body.find(br.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(f){f.stopImmediatePropagation();var d=fc["default"](f.currentTarget);c._toggleCheck(d.prop("checked"),d.data("index"))}),this.header.events.forEach(function(j,f){var l=j;if(l){"string"==typeof l&&(l=br.calculateObjectValue(null,l));var k=c.header.fields[f],d=c.getVisibleFields().indexOf(k);if(-1!==d){d+=br.getDetailViewIndexOffset(c.options);var g=function(n){if(!l.hasOwnProperty(n)){return"continue"}var m=l[n];c.$body.find(">tr:not(.no-records-found)").each(function(v,p){var q=fc["default"](p),e=q.find(c.options.cardView?".card-views>.card-view":">td").eq(d),t=n.indexOf(" "),o=n.substring(0,t),i=n.substring(t+1);e.find(i).off(o).on(o,function(w){var x=q.data("index"),r=c.data[x],u=r[k];m.apply(c,[w,u,r,x])})})};for(var h in l){g(h)}}}})}},{key:"initServer",value:function(x,p,k){var g=this,f={},v=this.header.fields.indexOf(this.options.sortName),y={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[v]&&(y.sortName=this.header.sortNames[v]),this.options.pagination&&"server"===this.options.sidePagination&&(y.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,y.pageNumber=this.options.pageNumber),!this.options.firstLoad&&!firstLoadTable.includes(this.options.id)){return void firstLoadTable.push(this.options.id)}if(k||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(y={search:y.searchText,sort:y.sortName,order:y.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(y.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),y.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,0===y.limit&&delete y.limit)),this.options.search&&"server"===this.options.sidePagination&&this.columns.filter(function(c){return !c.searchable}).length){y.searchable=[];var d,j=fg(this.columns);try{for(j.s();!(d=j.n()).done;){var q=d.value;!q.checkbox&&q.searchable&&(this.options.visibleSearch&&q.visible||!this.options.visibleSearch)&&y.searchable.push(q.field)}}catch(m){j.e(m)}finally{j.f()}}if(br.isEmptyObject(this.filterColumnsPartial)||(y.filter=JSON.stringify(this.filterColumnsPartial,null)),fc["default"].extend(y,p||{}),f=br.calculateObjectValue(this.options,this.options.queryParams,[y],f),f!==!1){x||this.showLoading();var w=fc["default"].extend({},br.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:k||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(f):f,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(l,h,n){var c=br.calculateObjectValue(g.options,g.options.responseHandler,[l,n],l);g.load(c),g.trigger("load-success",c,n&&n.status,n),x||g.hideLoading(),"server"===g.options.sidePagination&&c[g.options.totalField]>0&&!c[g.options.dataField].length&&g.updatePagination()},error:function(h){var c=[];"server"===g.options.sidePagination&&(c={},c[g.options.totalField]=0,c[g.options.dataField]=[]),g.load(c),g.trigger("load-error",h&&h.status,h),x||g.$tableLoading.hide()}});return this.options.ajax?br.calculateObjectValue(this,this.options.ajax,[w],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=fc["default"].ajax(w)),f}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var c=br.getSearchInput(this);c.val(this.options.searchText),this.onSearch({currentTarget:c,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var c=this;this.$header.find("th").each(function(f,d){fc["default"](d).find(".sortable").removeClass("desc asc").addClass(fc["default"](d).data("field")===c.options.sortName?c.options.sortOrder:"both")})}},{key:"updateSelected",value:function(){var c=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",c),this.$selectItem.each(function(d,f){fc["default"](f).closest("tr")[fc["default"](f).prop("checked")?"addClass":"removeClass"]("selected")})}},{key:"updateRows",value:function(){var c=this;this.$selectItem.each(function(f,d){c.data[fc["default"](d).data("index")][c.header.stateField]=fc["default"](d).prop("checked")})}},{key:"resetRows",value:function(){var d,f=fg(this.data);try{for(f.s();!(d=f.n()).done;){var c=d.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(c[this.header.stateField]=!1)}}catch(g){f.e(g)}finally{f.f()}this.initHiddenRows()}},{key:"trigger",value:function(e){for(var d,j,h="".concat(e,".bs.table"),c=arguments.length,f=Array(c>1?c-1:0),g=1;c>g;g++){f[g-1]=arguments[g]}(d=this.options)[a.EVENTS[h]].apply(d,[].concat(f,[this])),this.$el.trigger(fc["default"].Event(h,{sender:this}),f),(j=this.options).onAll.apply(j,[h].concat([].concat(f,[this]))),this.$el.trigger(fc["default"].Event("all.bs.table",{sender:this}),[h,f])}},{key:"resetHeader",value:function(){var c=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(function(){return c.fitHeader()},this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var x=this;if(this.$el.is(":hidden")){return void (this.timeoutId_=setTimeout(function(){return x.fitHeader()},100))}var p=this.$tableBody.get(0),k=p.scrollWidth>p.clientWidth&&p.scrollHeight>p.clientHeight+this.$header.outerHeight()?br.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var g=fc["default"](":focus");if(g.length>0){var f=g.parents("th");if(f.length>0){var v=f.attr("data-field");if(void 0!==v){var y=this.$header.find("[data-field='".concat(v,"']"));y.length>0&&y.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",k).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var d=fc["default"](".focus-temp:visible:eq(0)");d.length>0&&(d.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(h,c){x.$header_.find(br.sprintf('th[data-field="%s"]',fc["default"](c).data("field"))).data(fc["default"](c).data())});for(var j=this.getVisibleFields(),q=this.$header_.find("th"),m=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);m.length&&m.find('>td[colspan]:not([colspan="1"])').length;){m=m.next()}var w=m.find("> *").length;m.find("> *").each(function(A,l){var C=fc["default"](l);if(br.hasDetailViewIcon(x.options)&&(0===A&&"right"!==x.options.detailViewAlign||A===w-1&&"right"===x.options.detailViewAlign)){var B=q.filter(".detail"),c=B.innerWidth()-B.find(".fht-cell").width();return void B.find(".fht-cell").width(C.innerWidth()-c)}var u=A-br.getDetailViewIndexOffset(x.options),z=x.$header_.find(br.sprintf('th[data-field="%s"]',j[u]));z.length>1&&(z=fc["default"](q[C[0].cellIndex]));var t=z.innerWidth()-z.find(".fht-cell").width();z.find(".fht-cell").width(C.innerWidth()-t)}),this.horizontalScroll(),this.trigger("post-header")}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var s=this.getData(),H=[],D="";br.hasDetailViewIcon(this.options)&&(D='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),D&&"right"!==this.options.detailViewAlign&&H.push(D);var A,z=fg(this.columns);try{for(z.s();!(A=z.n()).done;){var L=A.value,v="",C="",J=[],E={},q=br.sprintf(' class="%s"',L["class"]);if(L.visible&&(!(this.footerData&&this.footerData.length>0)||L.field in this.footerData[0])){if(this.options.cardView&&!L.cardVisible){return}if(v=br.sprintf("text-align: %s; ",L.falign?L.falign:L.align),C=br.sprintf("vertical-align: %s; ",L.valign),E=br.calculateObjectValue(null,this.options.footerStyle,[L]),E&&E.css){for(var I=0,G=Object.entries(E.css);I<G.length;I++){var x=fm(G[I],2),F=x[0],K=x[1];J.push("".concat(F,": ").concat(K))}}E&&E.classes&&(q=br.sprintf(' class="%s"',L["class"]?[L["class"],E.classes].join(" "):E.classes)),H.push("<th",q,br.sprintf(' style="%s"',v+C+J.concat().join("; ")));var B=0;this.footerData&&this.footerData.length>0&&(B=this.footerData[0]["_".concat(L.field,"_colspan")]||0),B&&H.push(' colspan="'.concat(B,'" ')),H.push(">"),H.push('<div class="th-inner">');var j="";this.footerData&&this.footerData.length>0&&(j=this.footerData[0][L.field]||""),H.push(br.calculateObjectValue(L,L.footerFormatter,[s,j],j)),H.push("</div>"),H.push('<div class="fht-cell"></div>'),H.push("</div>"),H.push("</th>")}}}catch(k){z.e(k)}finally{z.f()}D&&"right"===this.options.detailViewAlign&&H.push(D),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(H.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var f=this;if(this.$el.is(":hidden")){return void setTimeout(function(){return f.fitFooter()},100)}var g=this.$tableBody.get(0),d=g.scrollWidth>g.clientWidth&&g.scrollHeight>g.clientHeight+this.$header.outerHeight()?br.getScrollBarWidth():0;this.$tableFooter.css("margin-right",d).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var j=this.$tableFooter.find("th"),h=this.$body.find(">tr:first-child:not(.no-records-found)");for(j.find(".fht-cell").width("auto");h.length&&h.find('>td[colspan]:not([colspan="1"])').length;){h=h.next()}var c=h.find("> *").length;h.find("> *").each(function(q,m){var t=fc["default"](m);if(br.hasDetailViewIcon(f.options)&&(0===q&&"left"===f.options.detailViewAlign||q===c-1&&"right"===f.options.detailViewAlign)){var n=j.filter(".detail"),p=n.innerWidth()-n.find(".fht-cell").width();return void n.find(".fht-cell").width(t.innerWidth()-p)}var k=j.eq(q),u=k.innerWidth()-k.find(".fht-cell").width();k.find(".fht-cell").width(t.innerWidth()-u)}),this.horizontalScroll()}},{key:"horizontalScroll",value:function(){var c=this;this.$tableBody.off("scroll").on("scroll",function(){var d=c.$tableBody.scrollLeft();c.options.showHeader&&c.options.height&&c.$tableHeader.scrollLeft(d),c.options.showFooter&&!c.options.cardView&&c.$tableFooter.scrollLeft(d),c.trigger("scroll-body",c.$tableBody)})}},{key:"getVisibleFields",value:function(){var f,g=[],d=fg(this.header.fields);try{for(d.s();!(f=d.n()).done;){var j=f.value,h=this.columns[this.fieldsColumnsIndex[j]];h&&h.visible&&g.push(j)}}catch(c){d.e(c)}finally{d.f()}return g}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var c=fc["default"].extend({},this.options);return delete c.data,fc["default"].extend(!0,{},c)}},{key:"refreshOptions",value:function(c){br.compareObjects(this.options,c,!0)||(this.options=fc["default"].extend(this.options,c),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(d){var f=this,c=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&br.isEmptyObject(this.filterColumns)&&br.isEmptyObject(this.filterColumnsPartial)||d&&d.unfiltered||(c=this.data),d&&d.useCurrentPage&&(c=c.slice(this.pageFrom-1,this.pageTo)),d&&!d.includeHiddenRows){var g=this.getHiddenRows();c=c.filter(function(e){return -1===br.findIndex(g,e)})}return d&&d.formatted&&c.forEach(function(k){for(var j=0,q=Object.entries(k);j<q.length;j++){var p=fm(q[j],2),h=p[0],m=p[1],e=f.columns[f.fieldsColumnsIndex[h]];if(!e){return}k[h]=br.calculateObjectValue(e,f.header.formatters[e.fieldIndex],[m,k,k.index,e.field],m)}}),c}},{key:"getSelections",value:function(){var c=this;return(this.options.maintainMetaData?this.options.data:this.data).filter(function(d){return d[c.header.stateField]===!0})}},{key:"load",value:function(d){var f=!1,c=d;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=c[this.options.totalField],this.options.totalNotFiltered=c[this.options.totalNotFilteredField],this.footerData=c[this.options.footerField]?[c[this.options.footerField]]:void 0),f=c.fixedScroll,c=Array.isArray(c)?c:c[this.options.dataField],this.initData(c),this.initSearch(),this.initPagination(),this.initBody(f)}},{key:"append",value:function(c){this.initData(c,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(c){this.initData(c,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(d){for(var f=0,c=this.options.data.length-1;c>=0;c--){var g=this.options.data[c];(g.hasOwnProperty(d.field)||"$index"===d.field)&&(!g.hasOwnProperty(d.field)&&"$index"===d.field&&d.values.includes(c)||d.values.includes(g[d.field]))&&(f++,this.options.data.splice(c,1))}f&&("server"===this.options.sidePagination&&(this.options.totalRows-=f,this.data=fp(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(c){c.hasOwnProperty("index")&&c.hasOwnProperty("row")&&(this.options.data.splice(c.index,0,c.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(f){var g,d=Array.isArray(f)?f:[f],j=fg(d);try{for(j.s();!(g=j.n()).done;){var h=g.value;h.hasOwnProperty("index")&&h.hasOwnProperty("row")&&(h.hasOwnProperty("replace")&&h.replace?this.options.data[h.index]=h.row:fc["default"].extend(this.options.data[h.index],h.row))}}catch(c){j.e(c)}finally{j.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(f){var j,d,l,k=this.options.uniqueId,c=this.options.data.length,g=f,h=null;for(j=c-1;j>=0;j--){if(d=this.options.data[j],d.hasOwnProperty(k)){l=d[k]}else{if(!d._data||!d._data.hasOwnProperty(k)){continue}l=d._data[k]}if("string"==typeof l?g=""+g:"number"==typeof l&&(+l===l&&l%1===0?g=parseInt(g):l===+l&&0!==l&&(g=parseFloat(g))),l===g){h=d;break}}return h}},{key:"updateByUniqueId",value:function(f){var h,d=Array.isArray(f)?f:[f],k=fg(d);try{for(k.s();!(h=k.n()).done;){var j=h.value;if(j.hasOwnProperty("id")&&j.hasOwnProperty("row")){var c=this.options.data.indexOf(this.getRowByUniqueId(j.id));-1!==c&&(j.hasOwnProperty("replace")&&j.replace?this.options.data[c]=j.row:fc["default"].extend(this.options.data[c],j.row))}}}catch(g){k.e(g)}finally{k.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"removeByUniqueId",value:function(d){var f=this.options.data.length,c=this.getRowByUniqueId(d);c&&this.options.data.splice(this.options.data.indexOf(c),1),f!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=fp(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"updateCell",value:function(c){c.hasOwnProperty("index")&&c.hasOwnProperty("field")&&c.hasOwnProperty("value")&&(this.data[c.index][c.field]=c.value,c.reinit!==!1&&(this.initSort(),this.initBody(!0)))}},{key:"updateCellByUniqueId",value:function(d){var f=this,c=Array.isArray(d)?d:[d];c.forEach(function(h){var g=h.id,k=h.field,j=h.value,e=f.options.data.indexOf(f.getRowByUniqueId(g));-1!==e&&(f.options.data[e][k]=j)}),d.reinit!==!1&&(this.initSort(),this.initBody(!0))}},{key:"showRow",value:function(c){this._toggleRow(c,!0)}},{key:"hideRow",value:function(c){this._toggleRow(c,!1)}},{key:"_toggleRow",value:function(d,f){var c;if(d.hasOwnProperty("index")?c=this.getData()[d.index]:d.hasOwnProperty("uniqueId")&&(c=this.getRowByUniqueId(d.uniqueId)),c){var g=br.findIndex(this.hiddenRows,c);f||-1!==g?f&&g>-1&&this.hiddenRows.splice(g,1):this.hiddenRows.push(c),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(f){if(f){return this.initHiddenRows(),this.initBody(!0),void this.initPagination()}var h,d=this.getData(),k=[],j=fg(d);try{for(j.s();!(h=j.n()).done;){var c=h.value;this.hiddenRows.includes(c)&&k.push(c)}}catch(g){j.e(g)}finally{j.f()}return this.hiddenRows=k,k}},{key:"showColumn",value:function(d){var f=this,c=Array.isArray(d)?d:[d];c.forEach(function(e){f._toggleColumn(f.fieldsColumnsIndex[e],!0,!0)})}},{key:"hideColumn",value:function(d){var f=this,c=Array.isArray(d)?d:[d];c.forEach(function(e){f._toggleColumn(f.fieldsColumnsIndex[e],!1,!0)})}},{key:"_toggleColumn",value:function(d,f,c){if(-1!==d&&this.columns[d].visible!==f&&(this.columns[d].visible=f,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var g=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);c&&g.filter(br.sprintf('[value="%s"]',d)).prop("checked",f),g.filter(":checked").length<=this.options.minimumCountColumns&&g.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var c=this;return this.columns.filter(function(d){return d.visible&&!c.isSelectionColumn(d)})}},{key:"getHiddenColumns",value:function(){return this.columns.filter(function(c){var d=c.visible;return !d})}},{key:"isSelectionColumn",value:function(c){return c.radio||c.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(f){var h,d=this,k=fg(this.columns.slice().reverse());try{for(k.s();!(h=k.n()).done;){var j=h.value;if(j.switchable){if(!f&&this.options.showColumns&&this.getVisibleColumns().length===this.options.minimumCountColumns){continue}j.visible=f}}}catch(c){k.e(c)}finally{k.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var g=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);f?g.prop("checked",f):g.get().reverse().forEach(function(i){g.filter(":checked").length>d.options.minimumCountColumns&&fc["default"](i).prop("checked",f)}),g.filter(":checked").length<=this.options.minimumCountColumns&&g.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(m){var j,h,f=m.index,d=this.getVisibleFields().indexOf(m.field),k=m.rowspan||1,p=m.colspan||1,c=this.$body.find(">tr");d+=br.getDetailViewIndexOffset(this.options);var g=c.eq(f).find(">td").eq(d);if(!(0>f||0>d||f>=this.data.length)){for(j=f;f+k>j;j++){for(h=d;d+p>h;h++){c.eq(j).find(">td").eq(h).hide()}}g.attr("rowspan",k).attr("colspan",p).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(d){var f=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",d),this.$selectItem.filter(":enabled").prop("checked",d),this.updateRows(),this.updateSelected();var c=this.getSelections();return d?void this.trigger("check-all",c,f):void this.trigger("uncheck-all",c,f)}},{key:"checkInvert",value:function(){var c=this.$selectItem.filter(":enabled"),d=c.filter(":checked");c.each(function(f,g){fc["default"](g).prop("checked",!fc["default"](g).prop("checked"))}),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",d),d=this.getSelections(),this.trigger("check-some",d)}},{key:"check",value:function(c){this._toggleCheck(!0,c)}},{key:"uncheck",value:function(c){this._toggleCheck(!1,c)}},{key:"_toggleCheck",value:function(A,v){var p=this.$selectItem.filter('[data-index="'.concat(v,'"]')),k=this.data[v];if(p.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var j,y=fg(this.options.data);try{for(y.s();!(j=y.n()).done;){var g=j.value;g[this.header.stateField]=!1}}catch(m){y.e(m)}finally{y.f()}this.$selectItem.filter(":checked").not(p).prop("checked",!1)}if(k[this.header.stateField]=A,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0){for(var x=this.multipleSelectRowLastSelectedIndex<v?[this.multipleSelectRowLastSelectedIndex,v]:[v,this.multipleSelectRowLastSelectedIndex],q=fm(x,2),z=q[0],w=q[1],s=z+1;w>s;s++){this.data[s][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(s,'"]')).prop("checked",!0)}}this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=A?v:-1}p.prop("checked",A),this.updateSelected(),this.trigger(A?"check":"uncheck",this.data[v],p)}},{key:"checkBy",value:function(c){this._toggleCheckBy(!0,c)}},{key:"uncheckBy",value:function(c){this._toggleCheckBy(!1,c)}},{key:"_toggleCheckBy",value:function(d,f){var c=this;if(f.hasOwnProperty("field")&&f.hasOwnProperty("values")){var g=[];this.data.forEach(function(i,e){if(!i.hasOwnProperty(f.field)){return !1}if(f.values.includes(i[f.field])){var h=c.$selectItem.filter(":enabled").filter(br.sprintf('[data-index="%s"]',e));if(h=d?h.not(":checked"):h.filter(":checked"),!h.length){return}h.prop("checked",d),i[c.header.stateField]=d,g.push(i),c.trigger(d?"check":"uncheck",i,h)}}),this.updateSelected(),this.trigger(d?"check-some":"uncheck-some",g)}}},{key:"refresh",value:function(c){c&&c.url&&(this.options.url=c.url),c&&c.pageNumber&&(this.options.pageNumber=c.pageNumber),c&&c.pageSize&&(this.options.pageSize=c.pageSize),table.rememberSelecteds={},table.rememberSelectedIds={},this.trigger("refresh",this.initServer(c&&c.silent,c&&c.query,c&&c.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),fc["default"](this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")}},{key:"resetView",value:function(f){var j=0;if(f&&f.height&&(this.options.height=f.height),this.$selectAll.prop("checked",this.$selectItem.length>0&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),j+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(j+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen")){this.$tableContainer.css("height",""),this.$tableContainer.css("width","")}else{if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var d=this.$toolbar.outerHeight(!0),l=this.$pagination.outerHeight(!0),k=this.options.height-d-l,c=this.$tableBody.find(">table"),g=c.outerHeight();if(this.$tableContainer.css("height","".concat(k,"px")),this.$tableBorder&&c.is(":visible")){var h=k-g-2;this.$tableBody[0].scrollWidth-this.$tableBody.innerWidth()&&(h-=br.getScrollBarWidth()),this.$tableBorder.css("width","".concat(c.outerWidth(),"px")),this.$tableBorder.css("height","".concat(h,"px"))}}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(j,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var c=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(c=0.04*this.$tableLoading.width(),c=Math.max(12,c),c=Math.min(32,c),c="".concat(c,"px")),this.$tableLoading.find(".loading-text").css("font-size",c)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"toggleShowSearch",value:function(){this.$el.parents(".select-table").siblings().slideToggle()}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var c=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",d=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(br.sprintf(this.constants.html.icon,this.options.iconsPrefix,c)," ").concat(d)),this.updatePagination()}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var c=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",d=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(br.sprintf(this.constants.html.icon,this.options.iconsPrefix,c)," ").concat(d)),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(c){var d=br.getSearchInput(this);d.val(c||""),this.onSearch({currentTarget:d})}},{key:"filterBy",value:function(c,d){this.filterOptions=br.isEmptyObject(d)?this.options.filterOptions:fc["default"].extend(this.options.filterOptions,d),this.filterColumns=br.isEmptyObject(c)?{}:c,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function b(c){var d={unit:"px",value:0};"object"===fD(c)?d=Object.assign(d,c):"string"==typeof c&&"bottom"===c?d.value=this.$tableBody[0].scrollHeight:("string"==typeof c||"number"==typeof c)&&(d.value=c);var f=d.value;"rows"===d.unit&&(f=0,this.$body.find("> tr:lt(".concat(d.value,")")).each(function(g,h){f+=fc["default"](h).outerHeight(!0)})),this.$tableBody.scrollTop(f)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(c){c>0&&c<=this.options.totalPages&&(this.options.pageNumber=c,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(d,f){var c=this.$body.find(br.sprintf('> tr[data-index="%s"]',d));c.next().is("tr.detail-view")?this.collapseRow(d):this.expandRow(d,f),this.resetView()}},{key:"expandRow",value:function(f,h){var d=this.data[f],k=this.$body.find(br.sprintf('> tr[data-index="%s"][data-has-detail-view]',f));if(!k.next().is("tr.detail-view")){this.options.detailViewIcon&&k.find("a.detail-icon").html(br.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),k.after(br.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',k.children("td").length));var j=k.next().find("td"),c=h||this.options.detailFormatter,g=br.calculateObjectValue(this.options,c,[f,d,j],"");1===j.length&&j.append(g),this.trigger("expand-row",f,d,j)}}},{key:"expandRowByUniqueId",value:function(c){var d=this.getRowByUniqueId(c);d&&this.expandRow(this.data.indexOf(d))}},{key:"collapseRow",value:function(d){var f=this.data[d],c=this.$body.find(br.sprintf('> tr[data-index="%s"][data-has-detail-view]',d));c.next().is("tr.detail-view")&&(this.options.detailViewIcon&&c.find("a.detail-icon").html(br.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",d,f,c.next()),c.next().remove())}},{key:"collapseRowByUniqueId",value:function(c){var d=this.getRowByUniqueId(c);d&&this.collapseRow(this.data.indexOf(d))}},{key:"expandAllRows",value:function(){for(var c=this.$body.find("> tr[data-index][data-has-detail-view]"),d=0;d<c.length;d++){this.expandRow(fc["default"](c[d]).data("index"))}}},{key:"collapseAllRows",value:function(){for(var c=this.$body.find("> tr[data-index][data-has-detail-view]"),d=0;d<c.length;d++){this.collapseRow(fc["default"](c[d]).data("index"))}}},{key:"updateColumnTitle",value:function(c){c.hasOwnProperty("field")&&c.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[c.field]].title=this.options.escape?br.escapeHTML(c.title):c.title,this.columns[this.fieldsColumnsIndex[c.field]].visible&&(this.$header.find("th[data-field]").each(function(f,d){return fc["default"](d).data("field")===c.field?(fc["default"](fc["default"](d).find(".th-inner")[0]).text(c.title),!1):void 0}),this.resetView()))}},{key:"updateFormatText",value:function(c,d){/^format/.test(c)&&this.options[c]&&("string"==typeof d?this.options[c]=function(){return d}:"function"==typeof d&&(this.options[c]=d),this.initToolbar(),this.initPagination(),this.initBody())}}]),a}();return d5.VERSION=fb.VERSION,d5.DEFAULTS=fb.DEFAULTS,d5.LOCALES=fb.LOCALES,d5.COLUMN_DEFAULTS=fb.COLUMN_DEFAULTS,d5.METHODS=fb.METHODS,d5.EVENTS=fb.EVENTS,fc["default"].BootstrapTable=d5,fc["default"].fn.bootstrapTable=function(c){for(var d=arguments.length,g=Array(d>1?d-1:0),f=1;d>f;f++){g[f-1]=arguments[f]}var b;return this.each(function(j,k){var h=fc["default"](k).data("bootstrap.table"),i=fc["default"].extend({},d5.DEFAULTS,fc["default"](k).data(),"object"===fD(c)&&c);if("string"==typeof c){var a;if(!fb.METHODS.includes(c)){throw Error("Unknown method: ".concat(c))}if(!h){return}b=(a=h)[c].apply(a,g),"destroy"===c&&fc["default"](k).removeData("bootstrap.table")}h||(h=new fc["default"].BootstrapTable(k,i),fc["default"](k).data("bootstrap.table",h),h.init())}),void 0===b?this:b},fc["default"].fn.bootstrapTable.Constructor=d5,fc["default"].fn.bootstrapTable.theme=fb.THEME,fc["default"].fn.bootstrapTable.VERSION=fb.VERSION,fc["default"].fn.bootstrapTable.defaults=d5.DEFAULTS,fc["default"].fn.bootstrapTable.columnDefaults=d5.COLUMN_DEFAULTS,fc["default"].fn.bootstrapTable.events=d5.EVENTS,fc["default"].fn.bootstrapTable.locales=d5.LOCALES,fc["default"].fn.bootstrapTable.methods=d5.METHODS,fc["default"].fn.bootstrapTable.utils=br,fc["default"](function(){fc["default"]('[data-toggle="table"]').bootstrapTable()}),d5});var TABLE_EVENTS="all.bs.table click-cell.bs.table dbl-click-cell.bs.table click-row.bs.table dbl-click-row.bs.table sort.bs.table check.bs.table uncheck.bs.table onUncheck check-all.bs.table uncheck-all.bs.table check-some.bs.table uncheck-some.bs.table load-success.bs.table load-error.bs.table column-switch.bs.table page-change.bs.table search.bs.table toggle.bs.table show-search.bs.table expand-row.bs.table collapse-row.bs.table refresh-options.bs.table reset-view.bs.table refresh.bs.table",firstLoadTable=[],union=function(a,b){return $.isPlainObject(b)?addRememberRow(a,b):$.isArray(b)?$.each(b,function(d,c){$.isPlainObject(c)?addRememberRow(a,c):-1==$.inArray(c,a)&&(a[a.length]=c)}):-1==$.inArray(b,a)&&(a[a.length]=b),a},difference=function(b,c){if($.isPlainObject(c)){removeRememberRow(b,c)}else{if($.isArray(c)){$.each(c,function(f,d){if($.isPlainObject(d)){removeRememberRow(b,d)}else{var g=$.inArray(d,b);-1!=g&&b.splice(g,1)}})}else{var a=$.inArray(c,b);-1!=a&&b.splice(a,1)}}return b},_={union:union,difference:difference};