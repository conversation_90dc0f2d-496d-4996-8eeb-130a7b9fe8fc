/**
 * 基于bootstrapTreeTable/bootstrap-table-treegrid修改
 * Copyright (c) 2019 ruoyi
 */
!function(e){e.fn.bootstrapTreeTable=function(t,a){var n=e(this).data("bootstrap.tree.table");if(n=n?n:e(this),"string"==typeof t){return e.fn.bootstrapTreeTable.methods[t](n,a)}t=e.extend({},e.fn.bootstrapTreeTable.defaults,t||{}),n.hasSelectItem=!1,n.data_list=null,n.data_obj=null,n.hiddenColumns=[],n.lastAjaxParams,n.isFixWidth=!1;var l=function(){d(),i(),o(),r(),p(),h(!0),n.data("bootstrap.tree.table",n)},d=function(){var a=e("<div class='bootstrap-tree-table'></div>"),l=e("<div class='treetable-table'></div>");n.before(a),a.append(l),l.append(n),n.addClass("table"),t.striped&&n.addClass("table-striped"),t.bordered&&n.addClass("table-bordered"),t.hover&&n.addClass("table-hover"),t.condensed&&n.addClass("table-condensed"),n.html("")},i=function(){var a=e("<div class='treetable-bars'></div>");t.toolbar&&(e(t.toolbar).addClass("tool-left"),a.append(e(t.toolbar)));var l=e('<div class="btn-group tool-right">');if(a.append(l),n.parent().before(a),t.showSearch){var d=e('<button class="btn btn-default btn-outline" type="button" aria-label="search" title="搜索"><i class="glyphicon glyphicon-search"></i></button>');l.append(d),v(d)}if(t.showRefresh){var i=e('<button class="btn btn-default btn-outline" type="button" aria-label="refresh" title="刷新"><i class="glyphicon glyphicon-repeat"></i></button>');l.append(i),C(i)}if(t.showColumns){var s=e('<div class="btn-group pull-right" title="列"><button type="button" aria-label="columns" class="btn btn-default btn-outline dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><i class="glyphicon glyphicon-list"></i> <span class="caret"></span></button></div>'),o=e('<ul class="dropdown-menu columns" role="menu"></ul>');e.each(t.columns,function(t,a){if("selectItem"!=a.field){var l=null;void 0===a.visible||1==a.visible?l=e('<li role="menuitem"><label><input type="checkbox" checked="checked" data-field="'+a.field+'" value="'+a.field+'" > '+a.title+"</label></li>"):(l=e('<li role="menuitem"><label><input type="checkbox" data-field="'+a.field+'" value="'+a.field+'" > '+a.title+"</label></li>"),n.hiddenColumns.push(a.field)),o.append(l)}}),s.append(o),l.append(s),x()}else{e.each(t.columns,function(e,t){"selectItem"!=t.field&&void 0!==t.visible&&1!=t.visible&&n.hiddenColumns.push(t.field)})}},s=function(){e.each(n.hiddenColumns,function(e,t){n.find("."+t+"_cls").hide()})},o=function(){var a=e("<tr></tr>");e.each(t.columns,function(t,l){var d=null;0==t&&"selectItem"==l.field?(n.hasSelectItem=!0,d=e('<th style="width:36px"></th>')):d=e('<th style="'+(l.width?"width:"+l.width+(l.widthUnit?l.widthUnit:"px"):"")+'" class="'+l.field+'_cls"></th>'),!n.isFixWidth&&l.width&&(n.isFixWidth=l.width.indexOf("px")>-1?!0:!1),d.html(l.title),a.append(d)});var l=e('<thead class="treetable-thead"></thead>');l.append(a),n.append(l)},r=function(){var a=e('<tbody class="treetable-tbody"></tbody>');n.append(a),t.height&&a.css("height",t.height)},p=function(a){n.data_list={},n.data_obj={};var l=n.find("tbody"),d='<tr><td colspan="'+t.columns.length+'"><div style="display: block;text-align: center;">正在努力地加载数据中，请稍候……</div></td></tr>';l.html(d),t.url?e.ajax({type:t.type,url:t.url,data:a?a:t.ajaxParams,dataType:"JSON",success:function(e,a,n){e=y(t,t.responseHandler,[e],e),c(e),y(t,t.onLoadSuccess,[e],e)},error:function(e,a){var n='<tr><td colspan="'+t.columns.length+'"><div style="display: block;text-align: center;">'+e.responseText+"</div></td></tr>";l.html(n)}}):c(t.data)},c=function(a){var l=n.find("tbody");if(l.html(""),!a||a.length<=0){var d='<tr><td colspan="'+t.columns.length+'"><div style="display: block;text-align: center;">没有找到匹配的记录</div></td></tr>';return void l.html(d)}f(a);var i=n.data_list._root_;i&&e.each(i,function(e,t){var a="row_id_"+e;u(t,1,a,"row_root")}),e.each(a,function(e,t){if(!t.isShow){var a=b(t,!1,1,"","");l.append(a)}}),n.append(l),_(),m(),s(),h()},h=function(a){if(t.height>0){var l=n.find("thead"),d=n.find("tbody"),i=parseInt(n.css("border-left-width"))+parseInt(n.css("border-right-width"));if(l.css("width",d.children(":first").width()),a){var s=!1;e(window).resize(function(){s||(s=!0,setTimeout(function(){n.isFixWidth||d.css("width",n.parent().width()-i),l.css("width",d.children(":first").width()),s=!1},300))})}}},f=function(a){var l=t.rootIdValue?t.rootIdValue:null,d=[],i=!1;e.each(a,function(a,n){-1==e.inArray(n[t.parentCode],d)&&d.push(n[t.parentCode])}),e.each(a,function(a,s){s.isShow=!1;var o="0"==s[t.parentCode]||0==s[t.parentCode]||null==s[t.parentCode]||""==s[t.parentCode]||e.inArray(s[t.code],d)>0&&!i;!s[t.parentCode]||(l?s[t.parentCode]==t.rootIdValue:o)?(i=!0,n.data_list._root_||(n.data_list._root_=[]),n.data_obj["id_"+s[t.code]]||n.data_list._root_.push(s)):(n.data_list["_n_"+s[t.parentCode]]||(n.data_list["_n_"+s[t.parentCode]]=[]),n.data_obj["id_"+s[t.code]]||n.data_list["_n_"+s[t.parentCode]].push(s)),n.data_obj["id_"+s[t.code]]=s})},u=function(a,l,d,i){var s=n.find("tbody"),o=n.data_list["_n_"+a[t.code]],r=b(a,o?!0:!1,l,d,i);s.append(r),o&&e.each(o,function(e,t){var a=d+"_"+e;u(t,l+1,a,d)})},b=function(a,l,d,i,s){a.isShow=!0,a.row_id=i,a.p_id=s,a.lv=d;var o=e('<tr id="'+i+'" pid="'+s+'"></tr>'),r=t.expanderCollapsedClass;return t.expandAll?(o.css("display","table"),r=t.expanderExpandedClass):1==d?(o.css("display","table"),r=t.expandFirst?t.expanderExpandedClass:t.expanderCollapsedClass):2==d?(t.expandFirst?o.css("display","table"):o.css("display","none"),r=t.expanderCollapsedClass):(o.css("display","none"),r=t.expanderCollapsedClass),e.each(t.columns,function(i,s){if("selectItem"==s.field){n.hasSelectItem=!0;var p=e('<td style="text-align:center;width:36px"></td>');if(s.radio){var c=e('<input name="select_item" type="radio" value="'+a[t.code]+'"></input>');p.append(c)}if(s.checkbox){var c=e('<input name="select_item" type="checkbox" value="'+a[t.code]+'"></input>');p.append(c)}o.append(p)}else{var p=e('<td name="'+s.field+'" class="'+s.field+'_cls"></td>');if(s.width&&p.css("width",s.width+(s.widthUnit?s.widthUnit:"px")),s.align&&p.css("text-align",s.align),t.expandColumn==i&&p.css("text-align","left"),s.valign&&p.css("vertical-align",s.valign),t.showTitle&&p.addClass("ellipsis"),s.formatter?p.html(s.formatter.call(this,w(a,s.field),a,i)):(t.showTitle&&p.attr("title",a[s.field]),p.text(w(a,s.field))),t.expandColumn==i){l?p.prepend('<span class="treetable-expander '+r+'"></span>'):p.prepend('<span class="treetable-expander"></span>');for(var h=0;d-1>h;h++){p.prepend('<span class="treetable-indent"></span>')}}o.append(p)}}),o},v=function(t){e(t).off("click").on("click",function(){e(".search-collapse").slideToggle()})},C=function(t){e(t).off("click").on("click",function(){n.refresh()})},x=function(){e(".bootstrap-tree-table .treetable-bars .columns label input").off("click").on("click",function(){var t=e(this);t.prop("checked")?n.showColumn(e(this).val()):n.hideColumn(e(this).val())})},m=function(){n.find("tbody").find("tr").unbind(),n.find("tbody").find("tr").click(function(){if(n.hasSelectItem){var t=e(this).find("input[name='select_item']");"radio"==t.attr("type")?(t.prop("checked",!0),n.find("tbody").find("tr").removeClass("treetable-selected"),e(this).addClass("treetable-selected")):"checkbox"==t.attr("type")?t.prop("checked")?(t.prop("checked",!0),n.find("tbody").find("tr").removeClass("treetable-selected"),e(this).addClass("treetable-selected")):(t.prop("checked",!1),n.find("tbody").find("tr").removeClass("treetable-selected")):t.prop("checked")?(t.prop("checked",!1),e(this).removeClass("treetable-selected")):(t.prop("checked",!0),e(this).addClass("treetable-selected"))}})},_=function(){n.find("tbody").find("tr").find(".treetable-expander").unbind(),n.find("tbody").find("tr").find(".treetable-expander").click(function(){var a=e(this).hasClass(t.expanderExpandedClass),l=e(this).hasClass(t.expanderCollapsedClass);if(a||l){var d=e(this).parent().parent(),i=d.attr("id"),s=n.find("tbody").find("tr[id^='"+i+"_']");a?(e(this).removeClass(t.expanderExpandedClass),e(this).addClass(t.expanderCollapsedClass),s&&s.length>0&&e.each(s,function(t,a){e(a).css("display","none")})):(e(this).removeClass(t.expanderCollapsedClass),e(this).addClass(t.expanderExpandedClass),s&&s.length>0&&e.each(s,function(a,n){var l=e("#"+e(n).attr("pid")).children().eq(t.expandColumn).find(".treetable-expander");l.hasClass(t.expanderExpandedClass)&&e(n).css("display","table")}))}})};n.refresh=function(e){e&&(n.lastAjaxParams=e),p(n.lastAjaxParams)},n.appendData=function(a){e.each(a,function(a,l){var d,i=n.data_obj["id_"+l[t.code]],s=n.data_obj["id_"+l[t.parentCode]],o=n.data_list["_n_"+l[t.parentCode]],r="",p="",c=1;if(i&&i.row_id&&""!=i.row_id&&(r=i.row_id),s){if(p=s.row_id,""==r){var h=0;o&&o.length>0&&(h=o.length),r=s.row_id+"_"+h}c=s.lv+1,d=b(l,!1,c,r,p);var u=e("#"+s.row_id).children().eq(t.expandColumn).find(".treetable-expander"),v=u.hasClass(t.expanderExpandedClass),C=u.hasClass(t.expanderCollapsedClass);if(v||C?v&&d.css("display","table"):u.addClass(t.expanderCollapsedClass),i){e("#"+i.row_id).before(d),e("#"+i.row_id).remove()}else{var x=s.row_id.split("_"),m=s.row_id.substring(0,s.row_id.length-1)+(parseInt(x[x.length-1])+1);e("#"+m).before(d)}}else{if(d=b(l,!1,c,r,p),i){e("#"+i.row_id).before(d),e("#"+i.row_id).remove()}else{var _=n.find("tbody");_.append(d)}}l.isShow=!0,f([l])}),_(),m(),s()},n.toggleRow=function(t){var a=n.data_obj["id_"+t],l=e("#"+a.row_id).find(".treetable-expander");l.trigger("click")},n.expandRow=function(t){var a=n.data_obj["id_"+t],l=e("#"+a.row_id).find(".treetable-expander"),d=l.hasClass(n.options.expanderCollapsedClass);d&&l.trigger("click")},n.collapseRow=function(t){var a=n.data_obj["id_"+t],l=e("#"+a.row_id).find(".treetable-expander"),d=l.hasClass(n.options.expanderExpandedClass);d&&l.trigger("click")},n.expandAll=function(){n.find("tbody").find("tr").find(".treetable-expander").each(function(a,n){var l=e(n).hasClass(t.expanderCollapsedClass);l&&e(n).trigger("click")})},n.collapseAll=function(){n.find("tbody").find("tr").find(".treetable-expander").each(function(a,n){var l=e(n).hasClass(t.expanderExpandedClass);l&&e(n).trigger("click")})},n.showColumn=function(a,l){var d=e.inArray(a,n.hiddenColumns);if(d>-1&&n.hiddenColumns.splice(d,1),n.find("."+a+"_cls").show(),l&&t.showColumns){var i=e(".bootstrap-tree-table .treetable-bars .columns label").find("input[value='"+a+"']");i.prop("checked","checked")}},n.hideColumn=function(a,l){if(n.hiddenColumns.push(a),n.find("."+a+"_cls").hide(),l&&t.showColumns){var d=e(".bootstrap-tree-table .treetable-bars .columns label").find("input[value='"+a+"']");d.prop("checked","")}};var w=function(e,t){var a=e;if("string"!=typeof t||e.hasOwnProperty(t)){return e[t]}var n=t.split(".");for(var l in n){a=a&&a[n[l]]}return a},y=function(t,a,n,l){var d=a;if("string"==typeof a){var i=a.split(".");i.length>1?(d=window,e.each(i,function(e,t){d=d[t]})):d=window[a]}return"object"==typeof d?d:"function"==typeof d?d.apply(t,n):!d&&"string"==typeof a&&sprintf.apply(this,[a].concat(n))?sprintf.apply(this,[a].concat(n)):l};return l(),n},e.fn.bootstrapTreeTable.methods={getSelections:function(t,a){var n=t.find("tbody").find("tr").find("input[name='select_item']:checked"),l=[];if("radio"==n.attr("type")){var d=t.data_obj["id_"+n.val()];l.push(d)}else{n.each(function(a,n){var d=t.data_obj["id_"+e(n).val()];l.push(d)})}return l},refresh:function(e,t){t?e.refresh(t):e.refresh()},appendData:function(e,t){t&&e.appendData(t)},toggleRow:function(e,t){e.toggleRow(t)},expandRow:function(e,t){e.expandRow(t)},collapseRow:function(e,t){e.collapseRow(t)},expandAll:function(e){e.expandAll()},collapseAll:function(e){e.collapseAll()},showColumn:function(e,t){e.showColumn(t,!0)},hideColumn:function(e,t){e.hideColumn(t,!0)}},e.fn.bootstrapTreeTable.defaults={code:"code",parentCode:"parentCode",rootIdValue:null,data:null,type:"GET",url:null,ajaxParams:{},expandColumn:0,expandAll:!1,expandFirst:!0,striped:!1,bordered:!0,hover:!0,condensed:!1,columns:[],toolbar:null,height:0,showTitle:!0,showSearch:!0,showColumns:!0,showRefresh:!0,expanderExpandedClass:"glyphicon glyphicon-chevron-down",expanderCollapsedClass:"glyphicon glyphicon-chevron-right",responseHandler:function(e){return !1},onLoadSuccess:function(e){return !1}}}(jQuery);