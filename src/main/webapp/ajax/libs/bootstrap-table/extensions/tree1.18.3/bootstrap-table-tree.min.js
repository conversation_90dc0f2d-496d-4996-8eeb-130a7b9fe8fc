/**
 * 基于bootstrapTreeTable/bootstrap-table-treegrid修改
 * Copyright (c) 2019 ruoyi
 */
!function(e){e.fn.bootstrapTreeTable=function(a,t){var n=e(this).data("bootstrap.tree.table");if(n=n?n:e(this),"string"==typeof a){return e.fn.bootstrapTreeTable.methods[a](n,t)}a=e.extend({},e.fn.bootstrapTreeTable.defaults,a||{}),n.hasSelectItem=!1,n.data_list=null,n.data_obj=null,n.hiddenColumns=[],n.lastAjaxParams,n.isFixWidth=!1,n.totalRows=0,n.totalPages=0;var i=function(){s(),l(),o(),r(),p(),C(!0),n.data("bootstrap.tree.table",n)},s=function(){var t=e("<div class='bootstrap-tree-table'></div>"),i=e("<div class='treetable-table'></div>");n.before(t),t.append(i),i.append(n),n.addClass("table"),a.striped&&n.addClass("table-striped"),a.bordered&&n.addClass("table-bordered"),a.hover&&n.addClass("table-hover"),a.condensed&&n.addClass("table-condensed"),n.html("")},l=function(){var t=e("<div class='treetable-bars'></div>");a.toolbar&&(e(a.toolbar).addClass("tool-left"),t.append(e(a.toolbar)));var i=e('<div class="btn-group tool-right">');if(t.append(i),n.parent().before(t),a.showSearch){var s=e('<button class="btn btn-default btn-outline" type="button" aria-label="search" title="搜索"><i class="glyphicon glyphicon-search"></i></button>');i.append(s),_(s)}if(a.showRefresh){var l=e('<button class="btn btn-default btn-outline" type="button" aria-label="refresh" title="刷新"><i class="glyphicon glyphicon-repeat"></i></button>');i.append(l),k(l)}if(a.showColumns){var d=e('<div class="btn-group pull-right" title="列"><button type="button" aria-label="columns" class="btn btn-default btn-outline dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><i class="glyphicon glyphicon-list"></i> <span class="caret"></span></button></div>'),o=e('<ul class="dropdown-menu columns" role="menu"></ul>');e.each(a.columns,function(a,t){if("selectItem"!=t.field){var i=null;void 0===t.visible||1==t.visible?i=e('<li role="menuitem"><label><input type="checkbox" checked="checked" data-field="'+t.field+'" value="'+t.field+'" > '+t.title+"</label></li>"):(i=e('<li role="menuitem"><label><input type="checkbox" data-field="'+t.field+'" value="'+t.field+'" > '+t.title+"</label></li>"),n.hiddenColumns.push(t.field)),o.append(i)}}),d.append(o),i.append(d),j()}else{e.each(a.columns,function(e,a){"selectItem"!=a.field&&void 0!==a.visible&&1!=a.visible&&n.hiddenColumns.push(a.field)})}},d=function(){e.each(n.hiddenColumns,function(e,a){n.find("."+a+"_cls").hide()})},o=function(){var t=e("<tr></tr>");e.each(a.columns,function(a,i){var s=null;0==a&&"selectItem"==i.field?(n.hasSelectItem=!0,s=e('<th style="width:36px"></th>')):(s=e('<th style="'+(i.width?"width:"+i.width+(i.widthUnit?i.widthUnit:"px"):"")+'" class="'+i.field+'_cls"></th>'),i.align&&s.css("text-align",i.align)),!n.isFixWidth&&i.width&&(n.isFixWidth=i.width.indexOf("px")>-1?!0:!1),s.html(i.title),t.append(s)});var i=e('<thead class="treetable-thead"></thead>');i.append(t),n.append(i)},r=function(){var t=e('<tbody class="treetable-tbody"></tbody>');if(n.append(t),a.height&&t.css("height",a.height),a.pagination){var i=e('<div class="fixed-table-pagination"></div>');n.append(i)}},p=function(t){if(a.pagination&&((void 0==t||null==t)&&(t={}),t[a.parentCode]=a.rootIdValue),n.data_list={},n.data_obj={},a.pagination){var i={};i.offset=a.pageSize*(a.pageNumber-1),i.limit=a.pageSize;var s={pageSize:i.limit,pageNum:i.offset/i.limit+1};t=e.extend(s,t)}var l=n.find("tbody"),d='<tr><td colspan="'+a.columns.length+'"><div style="display: block;text-align: center;">正在努力地加载数据中，请稍候……</div></td></tr>';l.html(d),a.url?e.ajax({type:a.type,url:a.url,data:e.extend(t,a.ajaxParams),dataType:"json",success:function(e,t,n){e=N(a,a.responseHandler,[e],e),c(e),N(a,a.onLoadSuccess,[e],e)},error:function(e,t){var n='<tr><td colspan="'+a.columns.length+'"><div style="display: block;text-align: center;">'+e.responseText+"</div></td></tr>";l.html(n)}}):c(a.data)},c=function(t){var i,s=0,l=0;a.pagination?(i=t.rows,l=a.pageNumber,s=~~((t.total-1)/a.pageSize)+1,n.totalPages=s,n.totalRows=t.total):i=t,t=i;var o=n.find("tbody");if(o.html(""),!t||t.length<=0){var r='<tr><td colspan="'+a.columns.length+'"><div style="display: block;text-align: center;">没有找到匹配的记录</div></td></tr>';return o.html(r),a.pageNumber=1,void u(0,0)}m(t);var p=n.data_list._root_;p&&e.each(p,function(e,t){var n="row_id_"+e;w(t,1,n,"row_root",t[a.code])}),e.each(t,function(e,t){if(!t.isShow){var n=y(t,!1,1,"","",a.pagination,t[a.code]);o.append(n)}}),S(),T(),d(),C(),a.pagination&&u(s,l);var c=e(n).parent(".treetable-table"),f=c.outerWidth();if(e.common.isMobile()||769>f){var h="width: "+f+"px;overflow: auto;position: relative;";c.attr("style",h);var b=0;e.each(a.columns,function(e,a){b+=0==e&&"selectItem"==a.field?36:200}),e(n).attr("style","width:"+b+"px")}},u=function(t,i){var s=n.find(".fixed-table-pagination");s.empty();var l=[],d=(a.pageNumber-1)*a.pageSize+1,o=a.pageNumber*a.pageSize;o>n.totalRows&&(o=n.totalRows),d>o&&(d=o),l.push('<div class="pull-left pagination-detail">'),l.push('<span class="pagination-info">'+z(d,o,n.totalRows)+"</span>");var r=!1;if(e.each(a.pageList,function(e,a){n.totalRows>a&&(r=!0)}),r){var p=[];p.push('<span class="page-list">'),p.push('<span class="btn-group dropup">'),p.push('<button type="button" class="btn btn-default btn-outline dropdown-toggle" data-toggle="dropdown">'),p.push('<span class="page-size">'+a.pageSize+"</span>"),p.push('<span class="caret"></span>'),p.push("</button>"),p.push('<ul class="dropdown-menu" role="menu">'),e.each(a.pageList,function(e,t){t==a.pageSize?p.push('<li class="active"><a href="javascript:void(0)">'+t+"</a></li>"):t>=n.totalRows&&1===e?p.push('<li><a href="javascript:void(0)">'+t+"</a></li>"):t<=n.totalRows&&p.push('<li><a href="javascript:void(0)">'+t+"</a></li>")}),p.push("</ul>"),p.push("</span>"),l.push(I(p.join(""))),l.push("</span>")}if(l.push("</div>"),t>1){l.push('<div class="pull-right pagination">'),l.push('<ul class="pagination pagination-outline">'),l.push('<li class="page-pre"><a href="javascript:void(0)">'+a.paginationPreText+"</a></li>");var c,u;5>t?(c=1,u=t):(c=i-2,u=c+4,1>c&&(c=1,u=5),u>t&&(u=t,c=u-4)),t>=6&&(i>=3&&(l.push('<li class="page-first'+(1==i?" active":"")+'">','<a href="javascript:void(0)">',1,"</a>","</li>"),c++),i>=4&&(4==i||6==t||7==t?c--:l.push('<li class="page-first-separator disabled">','<a href="javascript:void(0)">...</a>',"</li>"),u--)),t>=7&&i>=t-2&&c--,6==t?i>=t-2&&u++:t>=7&&(7==t||i>=t-3)&&u++;for(var C=c;u>=C;C++){l.push('<li class="page-number'+(C==i?" active":"")+'">','<a href="javascript:void(0)">',C,"</a>","</li>")}t>=8&&t-4>=i&&l.push('<li class="page-last-separator disabled">','<a href="javascript:void(0)">...</a>',"</li>"),t>=6&&t-3>=i&&l.push('<li class="page-last'+(t===i?" active":"")+'">','<a href="javascript:void(0)">',t,"</a>","</li>"),l.push('<li class="page-next"><a href="javascript:void(0)">'+a.paginationNextText+"</a></li>"),l.push("</ul></div>")}s.append(l.join(""));var m=s.find(".page-list a"),w=s.find(".page-pre"),y=s.find(".page-next"),_=s.find(".page-number"),k=s.find(".page-first"),j=s.find(".page-last");w.off("click").on("click",e.proxy(h,this)),m.off("click").on("click",e.proxy(f,this)),_.off("click").on("click",e.proxy(b,this)),k.off("click").on("click",e.proxy(v,this)),j.off("click").on("click",e.proxy(g,this)),y.off("click").on("click",e.proxy(x,this))},f=function(t){var i=e(t.currentTarget);i.parent().addClass("active").siblings().removeClass("active");var s=n.find(".fixed-table-pagination");a.pageSize=i.text().toUpperCase()===n.totalRows?n.totalRows:+i.text(),n.totalRows<a.pageSize*a.pageNumber&&(a.pageNumber=1),s.find(".page-size").text(a.pageSize),p()},h=function(e){a.pageNumber-1===0?a.pageNumber=n.totalPages:a.pageNumber--,p()},b=function(t){a.pageNumber!=e(t.currentTarget).text()&&(a.pageNumber=e(t.currentTarget).text(),p())},v=function(e){a.pageNumber=1,p()},g=function(e){a.pageNumber=n.totalPages,p()},x=function(e){a.pageNumber+1>n.totalPages?a.pageNumber=1:a.pageNumber++,p()},C=function(t){if(a.height>0){var i=n.find("thead"),s=n.find("tbody"),l=parseInt(n.css("border-left-width"))+parseInt(n.css("border-right-width"));if(i.css("width",s.children(":first").width()),t){var d=!1;e(window).resize(function(){d||(d=!0,setTimeout(function(){n.isFixWidth||s.css("width",n.parent().width()-l),i.css("width",s.children(":first").width()),d=!1},300))})}}},m=function(t){var i=a.rootIdValue?a.rootIdValue:null,s=[],l=!1;e.each(t,function(t,n){-1==e.inArray(n[a.parentCode],s)&&s.push(n[a.parentCode])}),e.each(t,function(t,d){d.isShow=!1,a.pagination&&(void 0==d.isTreeLeaf||null==d.isTreeLeaf?d.isTreeLeaf=!1:d.isTreeLeaf=!((1==d.isTreeLeaf?0:1)&&("true"==d.isTreeLeaf||1==d.isTreeLeaf?0:1)));var o="0"==d[a.parentCode]||0==d[a.parentCode]||null==d[a.parentCode]||""==d[a.parentCode]||e.inArray(d[a.code],s)>0&&!l;!d[a.parentCode]||(i?d[a.parentCode]==a.rootIdValue:o)?(l=!0,n.data_list._root_||(n.data_list._root_=[]),n.data_obj["id_"+d[a.code]]||n.data_list._root_.push(d)):(n.data_list["_n_"+d[a.parentCode]]||(n.data_list["_n_"+d[a.parentCode]]=[]),n.data_obj["id_"+d[a.code]]||n.data_list["_n_"+d[a.parentCode]].push(d)),n.data_obj["id_"+d[a.code]]=d})},w=function(t,i,s,l,d){var o=n.find("tbody"),r=n.data_list["_n_"+t[a.code]],p=y(t,r?!0:!1,i,s,l,a.pagination,d);o.append(p),r&&e.each(r,function(e,t){var n=s+"_"+e;w(t,i+1,n,s,t[a.code])})},y=function(t,i,s,l,d,o,r){t.isShow=!0,t.row_id=l,t.p_id=d,t.lv=s;var p=e('<tr id="'+l+'" data-id="'+r+'"pid="'+d+'"></tr>'),c=a.expanderCollapsedClass;return a.expandAll?(p.css("display","table"),c=a.expanderExpandedClass):1==s?(p.css("display","table"),c=a.expandFirst?a.expanderExpandedClass:a.expanderCollapsedClass):2==s?(a.expandFirst?p.css("display","table"):p.css("display","none"),c=a.expanderCollapsedClass):o?t.isTreeLeaf&&(c=a.expanderCollapsedClass):(p.css("display","none"),c=a.expanderCollapsedClass),e.each(a.columns,function(l,d){if("selectItem"==d.field){n.hasSelectItem=!0;var r=e('<td style="text-align:center;width:36px"></td>');if(d.radio){var u=e('<input name="select_item" type="radio" value="'+t[a.code]+'"></input>');r.append(u)}if(d.checkbox){var u=e('<input name="select_item" type="checkbox" value="'+t[a.code]+'"></input>');r.append(u)}p.append(r)}else{var r=e('<td name="'+d.field+'" class="'+d.field+'_cls"></td>');if(d.width&&r.css("width",d.width+(d.widthUnit?d.widthUnit:"px")),d.align&&r.css("text-align",d.align),a.expandColumn==l&&r.css("text-align","left"),d.valign&&r.css("vertical-align",d.valign),a.showTitle&&r.addClass("ellipsis"),d.formatter?r.html(d.formatter.call(this,R(t,d.field),t,l)):(a.showTitle&&r.attr("title",t[d.field]),r.text(R(t,d.field))),a.expandColumn==l){o?t.isTreeLeaf?r.prepend('<span class="treetable-expander '+c+'"></span>'):r.prepend('<span class="treetable-expander"></span>'):i?r.prepend('<span class="treetable-expander '+c+'"></span>'):r.prepend('<span class="treetable-expander"></span>');for(var f=0;f<s-a.expandColumn;f++){r.prepend('<span class="treetable-indent"></span>')}}p.append(r)}}),p},_=function(a){e(a).off("click").on("click",function(){e(".search-collapse").slideToggle()})},k=function(a){e(a).off("click").on("click",function(){n.refresh()})},j=function(){e(".bootstrap-tree-table .treetable-bars .columns label input").off("click").on("click",function(){var a=e(this);a.prop("checked")?n.showColumn(e(this).val()):n.hideColumn(e(this).val())})},T=function(){n.find("tbody").find("tr").unbind(),n.find("tbody").find("tr").click(function(){if(n.hasSelectItem){var t=e(this).find("input[name='select_item']");"radio"==t.attr("type")?(t.prop("checked",!0),n.find("tbody").find("tr").removeClass("treetable-selected"),e(this).addClass("treetable-selected")):"checkbox"==t.attr("type")?t.prop("checked")?(t.prop("checked",!0),n.find("tbody").find("tr").removeClass("treetable-selected"),e(this).addClass("treetable-selected")):(t.prop("checked",!1),n.find("tbody").find("tr").removeClass("treetable-selected")):t.prop("checked")?(t.prop("checked",!1),e(this).removeClass("treetable-selected")):(t.prop("checked",!0),e(this).addClass("treetable-selected"));var i=n.data_obj["id_"+e(this).data("id")];N(a,a.onClickRow,[i],i)}})},S=function(){n.find("tbody").find("tr").find(".treetable-expander").unbind(),n.find("tbody").find("tr").find(".treetable-expander").click(function(){var t=e(this).hasClass(a.expanderExpandedClass),i=e(this).hasClass(a.expanderCollapsedClass);if(t||i){var s=e(this).parent().parent(),l=s.attr("id"),d=s.attr("data-id"),o=n.find("tbody").find("tr[id^='"+l+"_']");if(a.pagination){var o=n.find("tbody").find("tr[id^='"+l+"_']");if(o&&o.length>0){t?e.each(o,function(a,t){e(t).css("display","none")}):e.each(o,function(t,n){var i=e(n).eq(a.expandColumn).find(".treetable-expander");i&&i.hasClass(a.expanderExpandedClass)?e(n).css("display","table"):e(n).css("display","table")})}else{if(a.pagination){var r={};r[a.parentCode]=d,a.dataUrl&&e.ajax({type:a.type,url:a.dataUrl,data:r,dataType:"json",success:function(a,t,i){e("#"+l+"_load").remove();var s=a;a=s,n.appendData(a)},error:function(t,n){var i='<tr><td colspan="'+a.columns.length+'"><div style="display: block;text-align: center;">'+t.responseText+"</div></td></tr>";e("#"+l).after(i)}})}}t?(e(this).removeClass(a.expanderExpandedClass),e(this).addClass(a.expanderCollapsedClass)):(e(this).removeClass(a.expanderCollapsedClass),e(this).addClass(a.expanderExpandedClass))}else{t?(e(this).removeClass(a.expanderExpandedClass),e(this).addClass(a.expanderCollapsedClass),o&&o.length>0&&e.each(o,function(a,t){e(t).css("display","none")})):(e(this).removeClass(a.expanderCollapsedClass),e(this).addClass(a.expanderExpandedClass),o&&o.length>0&&e.each(o,function(t,n){var i=e("#"+e(n).attr("pid")).children().eq(a.expandColumn).find(".treetable-expander"),s=e("#"+e(n).attr("pid")).css("display");i.hasClass(a.expanderExpandedClass)&&"table"==s&&e(n).css("display","table")}))}}})};n.refresh=function(e){e&&(n.lastAjaxParams=e),p(n.lastAjaxParams)},n.appendData=function(t){t.reverse(),e.each(t,function(t,i){a.pagination&&(i.__nodes=!((1==i.nodes?0:1)&&("true"==i.nodes||1==i.nodes?0:1)));var s,l=n.data_obj["id_"+i[a.code]],d=n.data_obj["id_"+i[a.parentCode]],o=n.data_list["_n_"+i[a.parentCode]],r="",p="",c=1;if(l&&l.row_id&&""!=l.row_id&&(r=l.row_id),d){if(p=d.row_id,""==r){var u=0;o&&o.length>0&&(u=o.length),r=d.row_id+"_"+u}c=d.lv+1,s=y(i,!0,c,r,p,a.pagination,i[a.code]);var f=e("#"+d.row_id).children().eq(a.expandColumn).find(".treetable-expander"),h=f.hasClass(a.expanderExpandedClass),b=f.hasClass(a.expanderCollapsedClass);if(h||b?h&&s.css("display","table"):f.addClass(a.expanderCollapsedClass),l){e("#"+l.row_id).before(s),e("#"+l.row_id).remove()}else{var v=d.row_id.split("_");d.row_id.substring(0,d.row_id.length-(v[v.length-1]+"").length)+(parseInt(v[v.length-1])+1);e("#"+d.row_id).after(s)}}else{if(s=y(i,!1,c,r,p,a.pagination,i[a.code]),l){e("#"+l.row_id).before(s),e("#"+l.row_id).remove()}else{var g=n.find("tbody");g.append(s)}}i.isShow=!0,m([i])}),S(),T(),d()},n.toggleRow=function(a){var t=n.data_obj["id_"+a],i=e("#"+t.row_id).find(".treetable-expander");i.trigger("click")},n.expandRow=function(a){var t=n.data_obj["id_"+a],i=e("#"+t.row_id).find(".treetable-expander"),s=i.hasClass(n.options.expanderCollapsedClass);s&&i.trigger("click")},n.collapseRow=function(a){var t=n.data_obj["id_"+a],i=e("#"+t.row_id).find(".treetable-expander"),s=i.hasClass(n.options.expanderExpandedClass);s&&i.trigger("click")},n.expandAll=function(){n.find("tbody").find("tr").find(".treetable-expander").each(function(t,n){var i=e(n).hasClass(a.expanderCollapsedClass);i&&e(n).trigger("click")})},n.collapseAll=function(){n.find("tbody").find("tr").find(".treetable-expander").each(function(t,n){var i=e(n).hasClass(a.expanderExpandedClass);i&&e(n).trigger("click")})},n.showColumn=function(t,i){var s=e.inArray(t,n.hiddenColumns);if(s>-1&&n.hiddenColumns.splice(s,1),n.find("."+t+"_cls").show(),i&&a.showColumns){var l=e(".bootstrap-tree-table .treetable-bars .columns label").find("input[value='"+t+"']");l.prop("checked","checked")}},n.hideColumn=function(t,i){if(n.hiddenColumns.push(t),n.find("."+t+"_cls").hide(),i&&a.showColumns){var s=e(".bootstrap-tree-table .treetable-bars .columns label").find("input[value='"+t+"']");s.prop("checked","")}};var R=function(e,a){var t=e;if("string"!=typeof a||e.hasOwnProperty(a)){return e[a]}var n=a.split(".");for(var i in n){t=t&&t[n[i]]}return t},N=function(a,t,n,i){var s=t;if("string"==typeof t){var l=t.split(".");l.length>1?(s=window,e.each(l,function(e,a){s=s[a]})):s=window[t]}return"object"==typeof s?s:"function"==typeof s?s.apply(a,n):!s&&"string"==typeof t&&sprintf.apply(this,[t].concat(n))?sprintf.apply(this,[t].concat(n)):i},I=function(e){return"每页显示 "+e+" 条记录"},z=function(e,a,t){return"显示第 "+e+" 到第 "+a+" 条记录，总共 "+t+" 条记录。"};return i(),n},e.fn.bootstrapTreeTable.methods={getSelections:function(a,t){var n=a.find("tbody").find("tr").find("input[name='select_item']:checked"),i=[];if("radio"==n.attr("type")){var s=a.data_obj["id_"+n.val()];i.push(s)}else{n.each(function(t,n){var s=a.data_obj["id_"+e(n).val()];i.push(s)})}return i},refresh:function(e,a){a?e.refresh(a):e.refresh()},appendData:function(e,a){a&&e.appendData(a)},toggleRow:function(e,a){e.toggleRow(a)},expandRow:function(e,a){e.expandRow(a)},collapseRow:function(e,a){e.collapseRow(a)},expandAll:function(e){e.expandAll()},collapseAll:function(e){e.collapseAll()},showColumn:function(e,a){e.showColumn(a,!0)},hideColumn:function(e,a){e.hideColumn(a,!0)}},e.fn.bootstrapTreeTable.defaults={code:"code",parentCode:"parentCode",rootIdValue:0,data:null,type:"GET",url:null,ajaxParams:{},expandColumn:1,expandAll:!1,expandFirst:!0,striped:!1,bordered:!1,hover:!0,condensed:!1,columns:[],toolbar:null,height:0,pagination:!1,dataUrl:null,pageNumber:1,pageSize:10,onClickRow:null,pageList:[10,25,50],showTitle:!0,showSearch:!0,showColumns:!0,showRefresh:!0,paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",expanderExpandedClass:"glyphicon glyphicon-chevron-down",expanderCollapsedClass:"glyphicon glyphicon-chevron-right",responseHandler:function(e){return !1},onLoadSuccess:function(e){return !1}}}(jQuery);