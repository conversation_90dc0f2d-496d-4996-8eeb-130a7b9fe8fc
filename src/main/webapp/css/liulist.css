@charset "utf-8";
.starBox{ width: 990px; margin-left:auto; margin-right:auto; margin-top:10px; margin-bottom:10px;}
.control_processSteps {
	display: flex;
	white-space: nowrap;
}

.processStep {
	display: inline-block;
	position: relative;
	flex-shrink: 1;
	flex-basis: 50%;
	margin-right: 0px;
}

.step_main {
	position: relative;
	white-space: normal;
	text-align: left;
}
/* 线条 */

.step_line {
	position: absolute;
	height: 4px;
	top: 10px;
	left: 0;
	right: 0;
	border-color: inherit;
	background-color: #c0c4cc;
}

.processStep:last-of-type .step_line {
	display: none;
}

.step_icon_inner {
	position: relative;
	z-index: 1;
	display: inline-flex;
	justify-content: center;
	width: 24px;
	height: 24px;
	box-sizing: border-box;
	border: 2px solid;
	border-radius: 50%;
	text-align: center;
	font-weight: 700;
	align-items: center;
	font-size: 14px;
	background: #fff;
}

.step_title {
	font-size: 14px;
	line-height: 38px;
	cursor: pointer;
}

.step_description {
	display: none;
	position: absolute;
	margin-top: -5px;
	font-size: 12px;
	line-height: 20px;
	font-weight: 400;
	background: #fff;
	box-shadow: 0px 4px 4px #ccc;
	border-radius: 5px;
	padding: 10px;
	z-index: 99;
}

.step_line_active {
	background-color:#77b852;
}
/* 已完结状态 */

.is_finish {
	color:#77b852;
	border-color:#77b852;
}

.is_process {
	color: #303133;
	border-color: #303133;
}

.is_wait {
	color: #c0c4cc;
	border-color: #c0c4cc;
}

.step_main:hover .step_title+.is_finish {
	display: inline-block;
}