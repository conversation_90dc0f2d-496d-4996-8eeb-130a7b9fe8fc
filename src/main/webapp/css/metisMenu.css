@charset "UTF-8";
/*!
* metismenu https://github.com/onokumus/metismenu#readme
* A collapsible jQuery menu plugin
* @version 3.0.6
* <AUTHOR> <<EMAIL>> (https://github.com/onokumus)
* @license: MIT
*/
.metismenu .arrow {
  float: right;
  line-height: 1.42857;
}

*[dir=rtl] .metismenu .arrow {
  float: left;
}

/*
 * Require Bootstrap 3.x
 * https://github.com/twbs/bootstrap
*/
.metismenu .glyphicon.arrow:before {
  content: "";
}

.metismenu .mm-active > a > .glyphicon.arrow:before {
  content: "";
}

/*
 * Require <PERSON>ont-<PERSON>ome
 * http://fortawesome.github.io/Font-Awesome/
*/
.metismenu .fa.arrow:before {
  content: "";
}

.metismenu .mm-active > a > .fa.arrow:before {
  content: "";
}

/*
 * Require Ionicons
 * http://ionicons.com/
*/
.metismenu .ion.arrow:before {
  content: "";
}

.metismenu .mm-active > a > .ion.arrow:before {
  content: "";
}

.metismenu .plus-times {
  float: right;
}

*[dir=rtl] .metismenu .plus-times {
  float: left;
}

.metismenu .fa.plus-times:before {
  content: "";
}

.metismenu .mm-active > a > .fa.plus-times {
  transform: rotate(45deg);
}

.metismenu .plus-minus {
  float: right;
}

*[dir=rtl] .metismenu .plus-minus {
  float: left;
}

.metismenu .fa.plus-minus:before {
  content: "";
}

.metismenu .mm-active > a > .fa.plus-minus:before {
  content: "";
}

.metismenu .mm-collapse:not(.mm-show) {
  display: none;
}

.metismenu .mm-collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition-timing-function: ease;
  transition-duration: 0.35s;
  transition-property: height, visibility;
}

.metismenu .has-arrow {
  position: relative;
}

.metismenu .has-arrow::after {
  position: absolute;
  content: "";
  width: 0.5em;
  height: 0.5em;
  border-width: 1px 0 0 1px;
  border-style: solid;
  border-color: initial;
  right: 1em;
  transform: rotate(-45deg) translate(0, -50%);
  transform-origin: top;
  top: 50%;
  transition: all 0.3s ease-out;
}

*[dir=rtl] .metismenu .has-arrow::after {
  right: auto;
  left: 1em;
  transform: rotate(135deg) translate(0, -50%);
}

.metismenu .mm-active > .has-arrow::after,
.metismenu .has-arrow[aria-expanded=true]::after {
  transform: rotate(-135deg) translate(0, -50%);
}

*[dir=rtl] .metismenu .mm-active > .has-arrow::after,
*[dir=rtl] .metismenu .has-arrow[aria-expanded=true]::after {
  transform: rotate(225deg) translate(0, -50%);
}

/*# sourceMappingURL=metisMenu.css.map */