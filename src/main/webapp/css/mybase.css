.fn-clear:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}
.fn-clear {
	zoom: 1;
}
.fr {
	float: right;
}
.fixed-table-toolbar .columns label {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.select-list {
	margin-top: 5px;
}
.fixed-table-toolbar .columns label input[type=checkbox], .fixed-table-toolbar .columns label input[type=radio] {
	margin: 0px 0 0;
	margin-top: 1px\9;
	line-height: normal;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open .dropdown-toggle.btn-primary {
	background-color: #2631d4;
	border-color: #2631d4;
	color: #fff
}
.table-striped table thead {
	background-color: #e5efff;
}
.table-striped table thead {
	background-color: #e5efff;
}
.panel-default > .panel-heading {
	color: #333;
	border-top: #0270C1 solid 4px;
	background-color: #E8F4FD;
}
.panel-heading {
	padding: 8px 15px;
}
.form-horizontal .control-label {
	color: #000;
}
.rankingbox {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.toolbar {
	padding: 0.8rem;
	padding-right: 50px;
}
.toolbar > .btn {
	display: inline-block;
	text-decoration: none;
	text-align: center;
	text-transform: none;
	vertical-align: middle;
	user-select: none;
	cursor: pointer;
	margin-left: 30px;
}
.toolbar-bottom {
	text-align: right;
	margin-top: -10px;

}
