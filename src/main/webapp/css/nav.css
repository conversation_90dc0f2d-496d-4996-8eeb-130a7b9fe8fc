.navbar-nav>li {
    float: left;
}
.nav>li {
    position: relative;
    display: block;
}
.navbar-brand {
    font-size: 36px;
    color: #fff;
    text-transform: uppercase;
    font-weight: 700;
}
.navbar-brand {
    margin-top: 6px;
}
.navbar-brand span{
    color:#00d690;
}
.navbar-default .navbar-brand{
    color:#fff;
}
.navbar-default .navbar-brand:hover{
    color:#fff;
}

.button a {
    display: block;
    border: 1px solid #00d690;
    text-align: center;
    padding: 10px 6px;
    border-radius: 30px;
    color: #00d690;
    margin-top: 30px;
}
@media(max-width: 350px){
    .navbar-brand {
        padding: 15px 0px;
    }
}

/*--------------------------------------------------------------
#0.2    Global Elements
--------------------------------------------------------------*/
.page-wrapper {
    position: relative;
    overflow: hidden;
}

.wow {
    visibility: hidden;
}

.fi:before {
    margin: 0;
}

.section-padding {
    padding: 110px 0;
}

@media (max-width: 991px) {
    .section-padding {
        padding: 100px 0;
    }
}

@media (max-width: 767px) {
    .section-padding {
        padding: 90px 0;
    }
}

@media (max-width: 530px) {
    .section-padding {
        padding: 65px 0;
    }
}

/*** contact form error handling ***/
.contact-validation-active .error-handling-messages {
    margin-top: 15px;
}

.contact-validation-active label.error {
    color: red;
    font-size: 0.875rem;
    font-weight: normal;
    margin: 5px 0 0 0;
    text-align: left;
    display: block;
}

.contact-validation-active #loader {
    display: none;
    margin-top: 10px;
}

.contact-validation-active #loader i {
    font-size: 30px;
    font-size: 1.875rem;
    color: #fff;
    display: inline-block;
    -webkit-animation: rotating linear 2s infinite;
    animation: rotating linear 2s infinite;
}

.contact-validation-active #success,
.contact-validation-active #error {
    width: 100%;
    color: #fff;
    padding: 5px 10px;
    font-size: 16px;
    text-align: center;
    display: none;
}

@media (max-width: 767px) {
    .contact-validation-active #success,
    .contact-validation-active #error {
        font-size: 15px;
    }
}

.contact-validation-active #success {
    background-color: #009a00;
    border-left: 5px solid green;
    margin-bottom: 5px;
}

.contact-validation-active #error {
    background-color: #ff1a1a;
    border-left: 5px solid red;
}

/** for popup image ***/
.mfp-wrap {
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 99999;
}

.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
    opacity: 0;
    -webkit-backface-visibility: hidden;
    -webkit-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
    opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
    opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
    opacity: 0;
}

/*** for fancybox video ***/
.fancybox-overlay {
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999 !important;
}

.fancybox-wrap {
    z-index: 99999 !important;
}

@-webkit-keyframes rotating {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes rotating {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.rotating {
    -webkit-animation: rotating 5s alternate infinite;
    animation: rotating 5s alternate infinite;
}

.section-title {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
}



.section-title span{
    font-size: 18px;
    font-weight: 500;
    color:#00d690;
    display:block;
    margin-bottom: 10px;
}
.section-title h2 {
    font-size: 36px;
    font-weight: 700;
    margin: 0;
    display: inline-block;
    position: relative;
}

@media (max-width: 991px) {
    .section-title h2 {
        font-size: 45px;
        font-size: 2.8125rem;
    }
}

@media (max-width: 767px) {
    .section-title h2 {
        font-size: 38px;
        font-size: 2.375rem;
    }
}


.theme-btn, .theme-btn-s2,.theme-btn-s4 {
    color: #00d690;
    font-weight: 600;
    padding: 15px 27px;
    border: 0;
    text-transform: capitalize;
    display: block;
    background: #fff;
    display: inline-block;
    position: relative;
    overflow: hidden;
}
.theme-btn:before,
.theme-btn-s2:before,
.theme-btn-s4:before{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    opacity: .3;
    z-index: 9;
    content: '';
    -webkit-transition: -webkit-transform 0.6s;
    transition: -webkit-transform 0.6s;
    transition: transform 0.6s;
    transition: transform 0.6s, -webkit-transform 0.6s;
    -webkit-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 180deg) translate3d(0, 100%, 0);
    transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 180deg) translate3d(0, 100%, 0);
}

.theme-btn:hover:before,
.theme-btn-s2:hover:before,
.theme-btn-s4:hover:before{
    -webkit-transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 180deg) translate3d(0, -100%, 0);
    transform: scale3d(1.9, 1.4, 1) rotate3d(0, 0, 1, 180deg) translate3d(0, -100%, 0);
}

.theme-btn:hover, .theme-btn-s2:hover, .theme-btn:focus, .theme-btn-s2:focus, .theme-btn:active, .theme-btn-s2:active {
    background-color: #00d690;
    color: #fff;
}
.theme-btn-s2 {
    background: #00d690;
    color: #fff;
}

@media (max-width: 991px) {
    .theme-btn, .theme-btn-s2 {
        font-size: 15px;
        font-size: 0.9375rem;
    }
}



.form input,
.form textarea,
.form select {
    border-color: #bfbfbf;
    border-radius: 0;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #595959;
}

.form input:focus,
.form textarea:focus,
.form select:focus {
    border-color: #f8c000;
    -webkit-box-shadow: 0 0 5px 0 #fde2dd;
    -moz-box-shadow: 0 0 5px 0 #fde2dd;
    -o-box-shadow: 0 0 5px 0 #fde2dd;
    -ms-box-shadow: 0 0 5px 0 #fde2dd;
    box-shadow: 0 0 5px 0 #fde2dd;
}

.form ::-webkit-input-placeholder {
    font-style: 14px;
    font-style: italic;
    color: #595959;
}

.form :-moz-placeholder {
    font-style: 14px;
    font-style: italic;
    color: #595959;
}

.form ::-moz-placeholder {
    font-style: 14px;
    font-style: italic;
    color: #595959;
}

.form :-ms-input-placeholder {
    font-style: 14px;
    font-style: italic;
    color: #595959;
}

.form select {
    font-style: italic;
    background: url(../images/select-icon.png) no-repeat right center;
    display: inline-block;
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
    cursor: pointer;
}

.form select::-ms-expand {
    /* for IE 11 */
    display: none;
}

.social-links {
    overflow: hidden;
}

.social-links li {
    float: left;
    width: 35px;
    height: 35px;
    margin-right: 1px;
}

.social-links li a {
    background-color: #fde2dd;
    width: 35px;
    height: 35px;
    line-height: 35px;
    display: block;
    color: #fff;
    text-align: center;
}

.social-links li a:hover {
    background-color: #f8c000;
}


/*************************************
	header style 1
*************************************/
.header-style-1 {
    z-index: 999;
}

@media screen and (min-width: 992px) {

}
.header-style-1 .navigation {
    background-color: transparent;
}



.topbar {
    background: #283a5e;
    padding: 0 100px;
}

@media (max-width: 991px) {

    .topbar {
        text-align: center;
        padding: 0;
    }
}

@media (max-width: 767px) {


}

.topbar {
    font-size: 15px;
    font-size: 1rem;
    color: #636363;
    margin: 0;
}

@media (max-width: 991px) {

    .header-style-1 p {
        margin-bottom: 8px;
        font-size: 14px;
        font-size: 0.93333rem;
    }
}

.topbar ul{
    overflow: hidden;
    list-style: none;
    float: right;
    margin-bottom: 0;
}

@media (max-width: 991px) {

    .topbar ul {
        float: none;
        display: inline-block;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.topbar ul li {
    font-size: 14px;
    float: left;
    padding: 10px 20px;
    color: #fff;
    line-height: 1em;
}

.topbar .contact-info ul li a {
    color: #fff;
}


.topbar .contact-info ul li a:hover {
    color: #7db1f0;
}


.topbar ul li:first-child{
    padding-left: 0;
}
.topbar .contact-info ul li:last-child{
    padding: 0;
}

.topbar .contact-intro ul {
    float: none;
    margin-bottom: 0;
}

@media (max-width: 1200px) {

    .topbar ul li {
        font-size: 12px;
    }
}

@media (max-width: 991px) {

    .topbar ul li{
        font-size: 13px;
    }
    .topbar {
        padding: 10px 0;
    }
    .topbar .theme-btn-s2 {
        padding: 15px 4px;
    }
}

@media (max-width: 767px) {

    .topbar ul li {
        float: left;
        border: none;
        padding: 8px 6px;
        font-size: 13px;
    }

}

@media (max-width: 590px) {

    .topbar ul li {
        float: none;
    }

    .topbar .contact-info ul li {
        float: left;
    }
    .topbar ul {
        flex-wrap: wrap;
    }
    .topbar .theme-btn-s2 {
        padding: 15px 6px;
        font-size: 13px;
    }
}



.topbar .theme-btn-s2{
    padding: 15px 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom:-3px;
    color: #fff !important;
}

.topbar ul .fi {
    position: relative;
    top: 1px;
    display: inline-block;
    padding-right: 3px;
}

.topbar ul .fi:before {
    font-size: 15px;
    font-size: 1rem;
    color: #fff;
}



@media (max-width: 991px) {

    .header-style-1 ul .fi:before {
        font-size: 20px;
        font-size: 1.33333rem;
    }

    .topbar ul li{
        font-size: 13px;
    }
    .topbar {
        padding: 10px 0;
    }
    .topbar .theme-btn-s2 {
        padding: 15px 4px;
    }
}

@media (max-width: 767px) {

    .topbar ul .fi:before{
        font-size: 16px;
    }
}
/*--------------------------------------------------------------
#0.3  header
--------------------------------------------------------------*/
.site-header {
    /* navigation open and close btn hide for width screen */
    /* style for navigation less than 992px */
    /*navbar collaps less then 992px*/
}

.site-header .navigation {
    background-color: #fff;
    margin-bottom: 0;
    border: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -o-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0;
}

.site-header .navigation > .container {
    position: relative;
}

.site-header .navigation .navbar-brand {
    height: auto;
}

.site-header #navbar {
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    -ms-transition: all 0.5s;
    transition: all 0.5s;
    /*** mega-menu style ***/
}

.site-header #navbar ul {  list-style: none;
}

.site-header #navbar > ul li a:hover,
.site-header #navbar > ul li a:focus {
    text-decoration: none;

}

@media screen and (min-width: 992px) {
    .site-header #navbar {
        /*** hover effect ***/
    }
    .site-header #navbar li {
        position: relative;
    }
    .site-header #navbar > ul > li > a {
        font-size: 14px;
        font-weight: 400;
        color: #283a5e;
        padding: 40px 16px;
    }
    .header-style-1 #navbar > ul > li > a {
        color: #fff;
    }
    .site-header #navbar > ul .sub-menu {
        background-color: #fff;
        padding: 5px 0px;
        position: absolute;
        left: 0;
        top: 130%;
        z-index: 10;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: all 0.3s;
        -moz-transition: all 0.3s;
        -o-transition: all 0.3s;
        -ms-transition: all 0.3s;
        transition: all 0.3s;
        -webkit-box-shadow: 0px 15px 60px -19px black;
        box-shadow: 0px 15px 60px -19px black;
    }
    .site-header #navbar > ul > li .sub-menu li {
        border-bottom: 1px solid #f2f2f2;
    }
    .site-header #navbar > ul > li .sub-menu li:last-child {
        border-bottom: 0;
    }
    .site-header #navbar > ul > li .sub-menu a {
        font-size: 14px;
        color: #06163a;
        display: block;
        padding: 0px;
    }
    .site-header #navbar > ul > li .sub-menu a:hover {
        color: #000000;
    }
    .site-header #navbar > ul > li > .sub-menu .sub-menu {
        left: 110%;
        top: 0;
    }
    .site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a {
        position: relative;
    }
    .site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a:before {
        font-family: "themify";
        content: ">";
        font-size: 11px;
        font-size: 0.73333rem;
        position: absolute;
        right: 15px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
    .site-header #navbar > ul > li:hover > .sub-menu {
        top: 100%;
        visibility: visible;
        opacity: 1;
    }
    .site-header #navbar .sub-menu > li:hover > .sub-menu {
        left:100%;
        visibility: visible;
        opacity: 1;
    }
}

@media (max-width: 991px) {
    .site-header #navbar > ul > li a {
        display: block;
        font-size: 14px;
        font-size: 0.93333rem;
    }
    .site-header #navbar > ul > li .sub-menu li {
        border-bottom: 1px solid #e6e6e6;
    }
    .site-header #navbar > ul .sub-menu > li:last-child {
        border-bottom: 0;
    }
    .site-header #navbar > ul > li > .sub-menu a {
        padding: 8px 15px 8px 45px;
    }
    .site-header #navbar > ul > li > .sub-menu .sub-menu a {
        padding: 8px 15px 8px 65px;
    }
    .site-header #navbar > ul .menu-item-has-children > a {
        position: relative;
    }
    .site-header #navbar > ul .menu-item-has-children > a:before {
        font-family: "themify";
        content: "\e64b";
        font-size: 11px;
        font-size: 0.73333rem;
        position: absolute;
        right: 15px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
}

@media screen and (min-width: 992px) {
    .site-header #navbar {
        /*** hover effect ***/
    }
    .site-header #navbar .has-mega-menu {
        position: static;
    }
    .site-header #navbar .mega-menu,
    .site-header #navbar .half-mega-menu {
        background-color: #fff;
        padding: 20px;
        border-top: 2px solid #00d690;
        position: absolute;
        right: 0;
        top: 120%;
        z-index: 10;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: all 0.3s;
        -moz-transition: all 0.3s;
        -o-transition: all 0.3s;
        -ms-transition: all 0.3s;
        transition: all 0.3s;
    }
    .site-header #navbar .mega-menu {
        width: 1140px;
        right: 15px;
    }
    .site-header #navbar .half-mega-menu {
        width: 585px;
    }
    .site-header #navbar .mega-menu-box-title {
        font-size: 14px;
        font-size: 0.93333rem;
        text-transform: uppercase;
        font-weight: bold;
        display: block;
        padding-bottom: 7px;
        margin-bottom: 7px;
        border-bottom: 1px solid #e6e6e6;
    }
    .site-header #navbar .mega-menu-list-holder li a {
        font-size: 14px;
        font-size: 0.93333rem;
        display: block;
        padding: 7px 8px;
        margin-left: -8px;
    }
    .site-header #navbar .has-mega-menu:hover > ul {
        top: 100%;
        visibility: visible;
        opacity: 1;
    }
}

@media (max-width: 1199px) {
    .site-header #navbar > ul .mega-menu {
        width: 950px;
        right: 15px;
    }
    .site-header #navbar > ul .half-mega-menu {
        width: 485px;
    }
}

@media (max-width: 991px) {
    .site-header #navbar > ul .mega-menu,
    .site-header #navbar > ul .half-mega-menu {
        width: auto;
    }
    .site-header #navbar > ul .mega-menu .row,
    .site-header #navbar > ul .half-mega-menu .row {
        margin: 0;
    }
    .site-header #navbar .mega-menu-content > .row > .col {
        margin-bottom: 25px;
    }
}

@media (max-width: 991px) {
    .site-header #navbar .mega-menu .mega-menu-list-holder a {
        padding: 5px 15px 5px 40px;
    }
    .site-header #navbar .mega-menu .mega-menu-box-title {
        font-size: 14px;
        font-size: 0.93333rem;
        text-transform: uppercase;
        display: block;
        border-bottom: 1px dotted #b3b3b3;
        padding: 0 0 4px 5px;
        margin: 0 25px 8px 25px;
    }
}

@media screen and (min-width: 992px) {
    .site-header .navbar-header .open-btn {
        display: none;
    }
    .site-header #navbar .close-navbar {
        display: none;
    }
}

@media (max-width: 991px) {
    .site-header {
        /* class for show hide navigation */
    }
    .site-header .container {
        width: 100%;
    }
    .site-header .navbar-header button {
        background-color: #00d690;
        width: 40px;
        height: 35px;
        border: 0;
        padding: 5px 10px;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        -o-border-radius: 3px;
        -ms-border-radius: 3px;
        border-radius: 3px;
        outline: 0;
        position: absolute;
        right: 15px;
        top: 20px;
        z-index: 20;
    }
    .site-header .navbar-header button span {
        background-color: #fff;
        display: block;
        height: 2px;
        margin-bottom: 5px;
    }
    .site-header .navbar-header button span:last-child {
        margin: 0;
    }
    .site-header #navbar {
        background: #fff;
        display: block !important;
        width: 280px;
        height: 100% !important;
        margin: 0;
        padding: 0;
        border-left: 1px solid #cccccc;
        border-right: 1px solid #cccccc;
        position: fixed;
        right: -300px;
        top: 0;
        z-index: 100;
    }
    .site-header #navbar ul a {
        color: #000;
    }
    .site-header #navbar ul a:hover,
    .site-header #navbar ul li.current a {
        color: #00d690;
    }
    .site-header #navbar .navbar-nav {
        height: 100%;
        overflow: auto;
    }
    .site-header #navbar .close-navbar {
        background-color: #00d690;
        width: 40px;
        height: 40px;
        color: #fff;
        border: 0;
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        -o-border-radius: 50%;
        -ms-border-radius: 50%;
        border-radius: 50%;
        outline: none;
        position: absolute;
        left: -18px;
        top: 10px;
        z-index: 20;
    }
    .site-header #navbar > ul > li {
        border-bottom: 1px solid #cccccc;
    }
    .site-header #navbar > ul > li > a {
        padding: 10px 15px 10px 35px;
    }
    .site-header .slideInn {
        right: 0 !important;
    }
}

@media (max-width: 767px) {
    .site-header .navbar-header .navbar-brand {
        font-size: 24px;
    }
    .site-header #navbar .navbar-nav {
        margin: 0;
    }
}

@media (max-width: 991px) {
    .site-header .navbar-collapse.collapse {
        display: none;
    }
    .site-header .navbar-collapse.collapse.in {
        display: block;
    }
    .site-header .navbar-header .collapse,
    .site-header .navbar-toggle {
        display: block;
    }
    .site-header .navbar-header {
        float: none;
    }
    .site-header .navbar-right {
        float: none;
    }
    .site-header .navbar-nav {
        float: none;
    }
    .site-header .navbar-nav > li {
        float: none;
    }
}

/**********************************
  header-style 1
***********************************/
.header-style-1, .header-style-2, .header-style-3 {
    /*** topbar ***/
    /*** cart-search-contact ***/
}

@media screen and (min-width: 992px) {

    .header-style-1 .navigation, .header-style-2 .navigation, .header-style-3 .navigation {
        background-color: transparent;
    }
    .header-style-1 .navbar-header, .header-style-2 .navbar-header, .header-style-3 .navbar-header {
        margin-top: 0px;
    }
    .header-style-1 #navbar, .header-style-2 #navbar, .header-style-3 #navbar {
        margin-right: 0;
    }
    .header-style-1 #navbar > ul > li > a, .header-style-2 #navbar > ul > li > a, .header-style-3 #navbar > ul > li > a {
        font-size: 14px;
        padding: 14px 20px 14px 25px;
    }
}

@media screen and (min-width: 1200px) {
    .header-style-1 #navbar > ul > li > a, .header-style-2 #navbar > ul > li > a, .header-style-3 #navbar > ul > li > a {
        font-size: 14px;
        padding: 14px 20px 14px 25px;

        text-transform: capitalize;
    }
    .header-style-1 #navbar, .header-style-2 #navbar, .header-style-3 #navbar {
        margin-left: 285px;
    }
}

@media (max-width:1550px) {
    .header-style-1 #navbar,
    .header-style-2 #navbar,
    .header-style-3 #navbar {
        margin-left: 225px;
    }

}

@media (max-width: 991px) {
    .header-style-1 .navigation, .header-style-2 .navigation, .header-style-3 .navigation {
        background: #1b2c4d;
        border-bottom: 1px solid rgba(255,255,255,.2);
    }
    .header-style-2 .navigation{
        background: #fff;
    }
    .header-style-1 .navigation .container, .header-style-2 .navigation .container, .header-style-3 .navigation .container {
        padding: 10px 15px;
    }

}

.header-style-1 .cart-search-contact, .header-style-2 .cart-search-contact, .header-style-3 .cart-search-contact {
    position: absolute;
    right: 15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 10;
}

@media (max-width: 991px) {
    .header-style-1 .cart-search-contact, .header-style-2 .cart-search-contact, .header-style-3 .cart-search-contact {
        right: 95px;
    }
}

.header-style-1 .cart-search-contact button, .header-style-2 .cart-search-contact button, .header-style-3 .cart-search-contact button {
    padding: 0;
    border: 0;
    outline: 0;
    width: 50px;
    height: 50px;
    background: #ecfdf5;
    border-radius: 50%;
    line-height: 50px;
}
.header-style-1 .cart-search-contact button{
    background: rgba(236,253,245,.2);
}
.header-style-1 .cart-search-contact button i{
    color: #fff;
}
.cart-search-contact .header-search-form button{
    width: unset;
    height: unset;
    background: none;
}
.cart-search-contact .mini-cart-content .mini-cart-action .theme-btn-s4 {
    float: right;
    background: #00d690;
    color: #fff;
}
.header-style-1 .cart-search-contact .flaticon-support-1, .header-style-2 .cart-search-contact .flaticon-support-1, .header-style-3 .cart-search-contact .flaticon-support-1 {
    font-size: 30px;
    font-size: 2rem;
    color: #00d690;
}
.header-style-1 .header-search-form .fi:before,
.header-style-2 .header-search-form .fi:before,
.header-style-3 .header-search-form .fi:before {
    font-size: 20px;
    color: #283a5e;
}

.header-style-1 .cart-search-contact > div, .header-style-2 .cart-search-contact > div, .header-style-3 .cart-search-contact > div {
    float: left;
    position: relative;
}

.header-style-1 .cart-search-contact > div + div, .header-style-2 .cart-search-contact > div + div, .header-style-3 .cart-search-contact > div + div {
    margin-left: 20px;
    margin-top: 5px;
}

@media (max-width: 450px) {
    .header-style-1 .cart-search-contact .header-search-form-wrapper, .header-style-2 .cart-search-contact .header-search-form-wrapper, .header-style-3 .cart-search-contact .header-search-form-wrapper {
        display: none;
    }
}

.header-style-1 .cart-search-contact .header-search-form, .header-style-2 .cart-search-contact .header-search-form, .header-style-3 .cart-search-contact .header-search-form {
    position: absolute;
    width: 250px;
    right: -25px;
    top: 75px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    -ms-transition: all 0.5s;
    transition: all 0.5s;
    -webkit-box-shadow: 0px 15px 60px -19px black;
    box-shadow: 0px 15px 60px -19px black;
}

.header-style-1 .cart-search-contact form div, .header-style-2 .cart-search-contact form div, .header-style-3 .cart-search-contact form div {
    position: relative;
}

.header-style-1 .cart-search-contact form div button,
.header-style-2 .cart-search-contact form div button,
.header-style-3 .cart-search-contact form div button {
    position: absolute;
    right: 15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.header-style-1 .cart-search-contact input,
.header-style-2 .cart-search-contact input,
.header-style-3 .cart-search-contact input {
    width: 100%;
    height: 50px;
    padding: 6px 20px;
    border: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.header-style-1 .cart-search-contact .mini-cart .fi:before,
.header-style-2 .cart-search-contact .mini-cart .fi:before,
.header-style-3 .cart-search-contact .mini-cart .fi:before {
    font-size: 20px;
}

.header-style-1 .cart-search-contact .mini-cart .cart-count,
.header-style-2 .cart-search-contact .mini-cart .cart-count,
.header-style-3 .cart-search-contact .mini-cart .cart-count {
    background: #00d690;
    width: 22px;
    height: 22px;
    line-height: 22px;
    font-size: 10px;
    font-size: 0.66667rem;
    color: white;
    position: absolute;
    top: 0;
    right: -11px;
    border-radius: 50%;
}

.header-style-1 .cart-search-contact .mini-cart-content, .header-style-2 .cart-search-contact .mini-cart-content, .header-style-3 .cart-search-contact .mini-cart-content {
    background: #fff;
    width: 300px;
    border-top: 2px solid #00d690;
    z-index: 10;
    position: absolute;
    right: -25px;
    top: 75px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    -ms-transition: all 0.5s;
    transition: all 0.5s;
    -webkit-box-shadow: 0px 15px 60px -19px black;
    box-shadow: 0px 15px 60px -19px black;
}

@media (max-width: 450px) {
    .header-style-1 .cart-search-contact .mini-cart-content, .header-style-2 .cart-search-contact .mini-cart-content, .header-style-3 .cart-search-contact .mini-cart-content {
        right: auto;
        left: -185px;
    }
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-title, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-title, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-title {
    padding: 12px 15px;
    border-bottom: 1px solid #efefef;
}

.header-style-1 .cart-search-contact .mini-cart-content p, .header-style-2 .cart-search-contact .mini-cart-content p, .header-style-3 .cart-search-contact .mini-cart-content p {
    font-size: 15px;
    font-size: 1rem;
    font-weight: 500;
    color: #06163a;
    margin: 0;
    text-transform: uppercase;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-items, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-items, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-items {
    padding: 15px;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item {
    padding-top: 15px;
    margin-top: 15px;
    border-top: 1px solid #efefef;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item:first-child, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item:first-child, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item:first-child {
    padding-top: 0;
    margin-top: 0;
    border-top: 0;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-image, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-image, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-image {
    width: 50px;
    height: 50px;
    border: 2px solid #eee;
    float: left;
    margin-right: 15px;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-image a, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-image a, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-image a,
.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-image img,
.header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-image img,
.header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-image img {
    display: block;
    width: 46px;
    height: 46px;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-image:hover, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-image:hover, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-image:hover {
    border-color: #fdc900;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-des, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-des, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-des {
    position: relative;
    overflow: hidden;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-des a, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-des a, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-des a {
    font-size: 14px;
    font-size: 0.93333rem;
    font-weight: 500;
    text-align: left;
    color: #06163a;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-des a:hover, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-des a:hover, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-des a:hover {
    color: #00d690;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-des .mini-cart-item-price, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-des .mini-cart-item-price, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-des .mini-cart-item-price {
    font-size: 13px;
    font-size: 0.86667rem;
    color: #888;
    display: block;
    margin-top: 3px;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-item-des .mini-cart-item-quantity, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-item-des .mini-cart-item-quantity, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-item-des .mini-cart-item-quantity {
    font-size: 12px;
    font-size: 0.8rem;
    color: #444;
    display: block;
    position: absolute;
    right: 0;
    top: 2px;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-action, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-action, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-action {
    padding: 20px 15px;
    border-top: 1px solid #efefef;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-action .theme-btn-s2, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-action .theme-btn-s2, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-action .theme-btn-s2 {
    float: right;
}

.header-style-1 .cart-search-contact .mini-cart-content .mini-cart-action .mini-checkout-price, .header-style-2 .cart-search-contact .mini-cart-content .mini-cart-action .mini-checkout-price, .header-style-3 .cart-search-contact .mini-cart-content .mini-cart-action .mini-checkout-price {
    font-size: 15px;
    font-size: 1rem;
    font-weight: 500;
    color: #06163a;
    margin-top: 10px;
    display: inline-block;
}

.header-style-1 .cart-search-contact .mini-cart-content-toggle, .header-style-2 .cart-search-contact .mini-cart-content-toggle, .header-style-3 .cart-search-contact .mini-cart-content-toggle,
.header-style-1 .cart-search-contact .header-search-content-toggle,
.header-style-2 .cart-search-contact .header-search-content-toggle,
.header-style-3 .cart-search-contact .header-search-content-toggle {
    opacity: 1;
    visibility: visible;
    right: 0;
}

@media (max-width: 991px) {
    .header-style-1 .cart-search-contact .get-quote, .header-style-2 .cart-search-contact .get-quote, .header-style-3 .cart-search-contact .get-quote {
        display: none;
    }
}

.get-quote a {
    font-size: 24px;
    color: #283a5e;
    display: flex;
    align-items: center;
    margin-right: 30px;
}
.header-style-1 .get-quote a {
    color: #fff;
}
.get-quote a i{
    margin-right: 10px;
}

.get-quote a {
    font-size: 14px;
}

@media (max-width:992px) {
    .site-header {
        padding: 0 0px;
    }

}
@media (max-width:1200px) {
    .get-quote a {
        display: none;
    }

}
@media (max-width:1750px) {
    .get-quote a {
        display: none;
    }

}

.header-style-2{
    position: relative;
}
.header-style-1{
    top:0px;
}
.header-style-1 .sticky-on {
    background: #283a5e !important;
}


/*--------------------------------------------------------------
 #0.4 wpo-hero-style
--------------------------------------------------------------*/
/*--------------------------------------------------------------
0.4 hero-slider
--------------------------------------------------------------*/
.hero {
    position: relative;
    height: 100vh;
    /** slider controls **/
    /*** hero slider animation ***/
}

@media (max-width: 767px) {
    .hero {
        min-height: 420px;
    }
}

.hero .slide {
    height: 100vh;
    position: relative;
    background-repeat: no-repeat;
    position: relative;
}

@media (max-width: 767px) {
    .hero .slide {
        min-height: 420px;
    }
}

.hero .slide .slider-bg {
    display: none;
}

.hero .slide:focus {
    outline: none;
}

.hero .slide .container {
    height: 100%;
    display: table;
}

.hero .slide .row {
    display: table-cell;
    vertical-align: middle;
}

.hero .slick-prev,
.hero .slick-next {
    background-color: rgba(0, 214, 144, 0.52);
    width: 55px;
    height: 55px;
    z-index: 10;
    border-radius: 50%;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    -ms-transition: all 0.5s;
    transition: all 0.5s;
}

.hero .slick-prev:hover,
.hero .slick-next:hover {
    background-color: rgba(0, 214, 144);
}

@media (max-width: 991px) {

    .hero .slick-prev,
    .hero .slick-next {
        display: none !important;
    }
}

.hero .slick-prev {
    left: -100px;
}

.hero .slick-prev:before {
    font-family: "themify";
    content: "\e629";
    opacity: 1;
}

.hero .slick-next {
    right: -100px;
}

.hero .slick-next:before {
    font-family: "themify";
    content: "\e628";
    opacity: 1;
}

.hero:hover .slick-prev {
    left: 25px;
}

.hero:hover .slick-next {
    right: 25px;
}

.hero .slick-dots {
    bottom: 30px;
}

@media screen and (min-width: 992px) {
    .hero .slick-dots {
        display: none !important;
    }
}

.hero .slick-dots li {
    margin: 0;
}

.hero .slick-dots button {
    background-color: #00d690;
    width: 14px;
    height: 14px;
    border: 2px solid #fff;
    border-radius: 50%;
}

.hero .slick-dots button:before,
.hero .slick-dots button:before {
    display: none;
}

.hero .slide-caption>div {
    overflow: hidden;
}

.slide-caption .btns .theme-btn {
    margin-right: 30px;
}

.slide-caption .btns {
    margin-top: 30px;
}

.hero .slide-caption>div * {
    -webkit-animation: fadeOutLeft 1.5s both;
    animation: fadeOutLeft 1.5s both;
}

.hero .slide-caption>.slider-pic * {
    -webkit-animation: fadeOutRight 1.5s both;
    animation: fadeOutRight 1.5s both;
}

.hero .slide-caption>div.slide-title * {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}

.hero .slide-caption>div.slide-subtitle * {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}

.hero .slide-caption>div.btns * {
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
}

.hero .hero-slider .slick-current .slide-caption>div * {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}


.hero .hero-slider .slick-current .slide-caption>.slider-pic * {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

.hero .hero-slider .slick-current .slide-caption>div.slide-title * {
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
}

.hero .hero-slider .slick-current .slide-caption>div.slide-subtitle * {
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
}

.hero .hero-slider .slick-current .slide-caption>div.btns * {
    -webkit-animation-delay: 1.5s;
    animation-delay: 1.5s;
}

.hero .hero-slider .slick-current .slide-caption>div.slider-pic * {
    -webkit-animation-delay: 2s;
    animation-delay: 2s;
}

/************************************************
 hero-style-1
**************************************************/
.hero-style-1{
    margin:0 40px;
}
.hero-style-1,
.hero-style-2 {
    height: 900px;
    position: relative;
    overflow: hidden;
}

.hero-style-1 .slide:before,
.hero-style-2 .slide:before {
    content: "";
    background-color: rgba(40, 58, 94, 0.34);
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.hero-style-2 .slide:before{
    background-color: rgba(40, 58, 94, 0.4);
}

.slide-caption {
    z-index: 99;
    position: relative;
}

.hero-style-1 .slide .slide-caption h2 span {
    color: #f7c33f;
}

@media (max-width: 1199px) {

    .hero-style-1,
    .hero-style-2 {
        height: 750px;
    }
}

@media (max-width: 991px) {

    .hero-style-1,
    .hero-style-2 {
        height: 550px;
    }
}

@media (max-width: 767px) {

    .hero-style-1,
    .hero-style-2 {
        height: 450px;
    }
}

.hero-style-1 .slide,
.hero-style-2 .slide {
    height: 900px;
}

@media (max-width: 1199px) {

    .hero-style-1 .slide,
    .hero-style-2 .slide {
        height: 750px;
    }

    .hero-style-2 .slide {
        height: 650px;
    }

}

@media (max-width: 991px) {

    .hero-style-1 .slide,
    .hero-style-2 .slide {
        height: 550px;
    }
}

@media (max-width: 767px) {

    .hero-style-1 .slide,
    .hero-style-2 .slide {
        height: 450px;
    }

    .hero .slide .container {
        margin-left: 20px;
    }

    .hero-style-1 .slide-caption,
    .hero-style-2 .slide-caption {
        padding-top: 190px;
    }

    .hero-style-2 .slide {
        height: 450px;
    }
}

.hero-style-1 .slide .row,
.hero-style-2 .slide .row {
    position: relative;
}


.hero-style-1 .slide-caption h2,
.hero-style-2 .slide-caption h2,
.hero-style-3 .slide-caption h2 {
    font-size: 95px;
    font-weight: 700;
    line-height: 120px;
    color: #fff;
    margin: 0 0 0.45em;
}

@media (max-width: 1200px) {

    .hero-style-1 .slide-caption h2,
    .hero-style-2 .slide-caption h2,
    .hero-style-3 .slide-caption h2 {
        font-size: 70px;
        font-weight: 700;
        line-height: 85px;
    }
}

@media (max-width: 991px) {

    .hero-style-1 .slide-caption h2,
    .hero-style-2 .slide-caption h2,
    .hero-style-3 .slide-caption h2 {
        font-size: 45px;
        font-size: 2.8125rem;
    }
}

@media (max-width: 767px) {

    .hero-style-1 .slide-caption h2,
    .hero-style-2 .slide-caption h2,
    .hero-style-3 .slide-caption h2 {
        font-size: 35px;
        font-size: 2.1875rem;
        line-height: 45px;
    }
}

.hero-style-1 .slide-caption p,
.hero-style-3 .slide-caption p,
.hero-style-2 .slide-caption p {
    color: #fff;
    margin: 0 0 2.8em;
}

.hero-style-1 .slider-pic,
.hero-style-2 .slider-pic {
    position: absolute;
    bottom: -33%;
    right: -113%;
    z-index: -1;
    max-width: 680px;
}

@media (max-width: 991px) {

    .hero-style-1 .slider-pic,
    .hero-style-2 .slider-pic {
        max-width: 350px;
        right: -60%;
        bottom: 8px;
    }
}

@media (max-width: 767px) {

    .hero-style-1 .slider-pic,
    .hero-style-2 .slider-pic {
        display: none;
    }
}

.slide-caption .slide-subtitle p {
    margin-bottom: 10px;
}

@media (max-width: 767px) {

    .hero-style-1 .slide-caption,
    .hero-style-2 .slide-caption {
        padding-top: 0px;
    }
}

@media (max-width: 590px) {
    .hero .slide .container {
        margin-left: 0px;
    }

    .theme-btn,
    .theme-btn-s2,
    .theme-btn-s4,
    .theme-btn-s3 {
        padding: 10px 18px;
    }

    .slide-caption .btns .theme-btn {
        margin-right: 5px;
    }

    .wpo-site-header .cart-search-contact {
        right: 74px;
    }
}

/************************************************
    hero-style2
**************************************************/



.hero-style-2:after {
    display: none;
}

.hero-style-2 .slide .container {
    position: relative;
    z-index: 10;
}

@media screen and (min-width: 992px) {
    .hero-style-2 .slide-caption {
        margin-top: 0;
        padding-bottom: 60px;
    }
}

.navbar-header .flaticon-aeroplane:before {
    content: "\f102";
    font-weight: 400;
}

.hero-style-2 .slider-pic {
    position: absolute;
    bottom: -15%;
    right: -110%;
    z-index: -1;
}

@media (max-width: 991px) {
    .hero-style-2 {
        height: 600px;
    }
}

@media (max-width: 767px) {
    .hero-style-2 {
        height: 450px;
    }
}

.hero-style-2 {
    height: 850px;
    position: relative;
}

@media (max-width: 1199px) {
    .hero-style-2 {
        height: 650px;
    }

}

@media (max-width: 991px) {
    .hero-style-2 {
        height: 600px;
    }
}

@media (max-width: 767px) {
    .hero-style-2 {
        height: 450px;
    }
}

/************************************************
hero-style3
**************************************************/
.hero-style-3 {
    position: relative;
    height: 800px;
}

.hero-style-3 .slide {
    height: 800px;
}

@media (max-width: 991px) {
    .hero-style-3 {
        height: 600px;
    }
}

@media (max-width: 767px) {
    .hero-style-3 {
        height: 450px;
    }
}

.hero-style-3 .hero-outer {
    height: 850px;
    position: relative;
}

@media (max-width: 991px) {
    .hero-style-3 .hero-outer {
        height: 600px;
    }
}

@media (max-width: 767px) {
    .hero-style-3 .hero-outer {
        height: 450px;
    }
}

.hero-style-3 .hero-outer .container {
    height: 100%;
    display: table;
}

.hero-style-3 .hero-outer .row {
    display: table-cell;
    vertical-align: middle;
}

.hero-style-3 .hero-text {
    margin-top: 60px;
}

@media (max-width: 991px) {
    .hero-style-3 .hero-text {
        margin-top: 40px;
    }
}

.hero-style-3 .hero-text>span {
    font-weight: bold;
    color: #193e85;
    text-transform: uppercase;
    letter-spacing: 5px;
}

@media (max-width: 767px) {
    .hero-style-3 .hero-text>span {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.hero-style-3 .hero-text h2 {
    font-size: 48px;
    font-size: 3rem;
    font-weight: bold;
    color: #fff;
    margin: 0.5em 0 0.8em;
}

@media (max-width: 991px) {
    .hero-style-3 .hero-text h2 {
        font-size: 40px;
        font-size: 2.5rem;
    }
}

@media (max-width: 767px) {
    .hero-style-3 .hero-text h2 {
        font-size: 32px;
        font-size: 2rem;
    }
}

@media (max-width: 767px) {
    .hero-style-3 .img-holder {
        display: none;
    }
}

.hero-bg-slider:before {
    display: none;
}


.sticky-header {
    width: 100%;
    position: fixed;
    left: 0;
    top: -200px;
    z-index: 9999;
    opacity: 0;
    -webkit-transition: all 0.7s;
    -moz-transition: all 0.7s;
    -o-transition: all 0.7s;
    -ms-transition: all 0.7s;
    transition: all 0.7s;
}

.sticky-on {
    opacity: 1;
    top: 0;
}



.header-style-2 .sticky-header,
.header-style-3 .sticky-header {
    background-color: #fff;
    border-bottom: 0;
    -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}
/*==================================================
#0.5 wpo-serch-area
=====================================================*/
.wpo-hero-style-2 .wpo-select-wrap {
    max-width: 800px;
    margin: 0 auto;

}

.wpo-select-wrap {
    -webkit-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    -moz-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    margin-top: -80px;
    background: #fff;
}

.wpo-select-wrap-2 {
    margin-top: -100px;
}

@media(max-width: 1200px) {
    .wpo-select-wrap-2 {
        margin-top: 30px;
    }

    .wpo-hero-style-2 .wpo-hero-text {
        padding-top: 60px;
    }

}

.wpo-select-area form {
    background: #fff;
    display: flex;
    justify-content: space-between;
}

.wpo-select-area form input,
.wpo-select-area form select,
.wpo-select-area form button {
    width: 100%;
    font-size: 20px;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0;
    color: #283a5e;
    font-weight: 700;
}
.wpo-select-area form input{
    padding-left: 0;
}

@media (max-width: 767px) {

    .wpo-select-area form input,
    .wpo-select-area form select,
    .wpo-select-area form button {
        height: 50px;
        font-size: 12px;
        /* font-size: 0.9375rem; */
    }
}



.wpo-select-area form select {
    border-radius: 70px 0 0 70px;
    padding: 0 25px;
    background: white;
    border-right: 1px solid #6cf5fd;
}

.wpo-select-area form input:focus,
.wpo-select-area form select:focus {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #283a5e;
}

.select-sub{
    margin-right: 20px;
    position: relative;
}
.select-sub:first-child{
    width: 220px;
}
.select-sub:nth-child(2){
    width: 220px;
}
.select-sub:last-child{
    margin-right: 0;
}
.select-sub .fi:before{
    font-size: 20px;
}
.input-group-addon{
    background: none;
    border: none;
}
.wpo-select-area form button {
    font-weight: 600;
    color: #fff;
    z-index: 1;
    position: relative;
    top: 10px;
    right: 10px;
    font-size: 15px;
    width: 175px;
}


.wpo-select-area form button:focus {
    outline: none;
    border: none;
}

.wpo-select-area .nice-select {
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding-left: 0;
}

.wpo-select-area .nice-select span {
    position: relative;
    font-size: 20px;
    color: #283a5e;
    font-weight: 700;
}
.select-sub span{
    text-transform: uppercase;
    display: block;
    margin-bottom: 20px;
    font-size: 15px;
    color: #687693;
}
.select-sub span i{
    margin-right: 10px;
}
.wpo-select-area .select-sub:before {
    position: absolute;
    left: -30px;
    top: 0;
    width: 1px;
    height: 70%;
    background: #dddddd;
    content: "";
}
.select-sub:first-child:before{
    display: none;
}
.select-sub:last-child:before{
    display: none;
}
.wpo-select-area input {
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}
@media (max-width: 767px) {
    .wpo-select-area .nice-select {
        height: 50px;
        line-height: 50px;
    }
}

.wpo-select-area {
    padding: 50px 20px 20px;
}

@media (max-width: 590px) {
    .wpo-select-area {
        padding: 20px 2px;
    }
}

.nice-select:after {
    right: 0px;
}



.wpo-select-area ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color:#283a5e;
}

.wpo-select-area ::-moz-placeholder {
    /* Firefox 19+ */

    color:#283a5e;
}

.wpo-select-area :-ms-input-placeholder {
    /* IE 10+ */
    color:#283a5e;
}

.wpo-select-area :-moz-placeholder {
    /* Firefox 18- */

    color:#283a5e;
}
.datepicker table tr td.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover{
    background-image: linear-gradient(to bottom,#00d690,#00d690);
}
.datepicker table tr td.day.focused,
.datepicker table tr td.day:hover{
    background: #00d690;
    color: #fff;
}

@media (max-width: 1200px) {
    .wpo-select-area form input,
    .wpo-select-area form select{
        font-size: 20px;
    }
    .wpo-select-area .nice-select span {
        font-size: 20px;
    }
    .select-sub span {
        font-size: 15px;
    }
    .wpo-select-area form button {
        font-size: 15px;
    }
    .select-sub:first-child {
        width: 145px;
    }
    .select-sub:nth-child(2) {
        width: 160px;
    }
}

@media (max-width: 992px) {
    .select-sub span {
        font-size: 12px;
    }
    .select-sub .fi:before {
        font-size: 14px;
    }
    .wpo-select-area form input,
    .wpo-select-area form select {
        font-size: 14px;
    }
    .select-sub:first-child {
        width: 125px;
    }
    .select-sub:nth-child(2) {
        width: 125px;
    }
    .wpo-select-area .nice-select span {
        font-size: 15px;
    }
    .wpo-select-area .select-sub:before{
        display: none;
    }
    .hero-style-1 {
        margin:0;
    }
}
@media (max-width: 767px) {
    .wpo-select-area form{
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .wpo-select-wrap{
        margin-top: 0;
        padding: 20px;
    }
    .wpo-select-section {
        padding-top: 80px;
    }
}
@media (max-width: 550px) {

    .select-sub{
        width: 100%;
        margin-bottom: 30px;
        border-bottom: 1px solid #ddd;
    }
    .select-sub:first-child {
        width: 100%;
    }
    .select-sub:nth-child(2) {
        width: 100%;
    }
    .select-sub:last-child{
        margin-left: 20px;
        border-bottom: 0;
    }
}
/*====================================================
#0.6 wpo-about-area
======================================================*/

.wpo-about-area-2,
.wpo-about-area{
}

.wpo-about-text .wpo-section-title,
.wpo-about-text-2 .wpo-section-title {
    text-align: left;
    margin: unset;
    margin-bottom: 35px;
}

.wpo-about-text p,
.wpo-about-text-2 p {
    margin-bottom: 15px;
    color: #666666;
}

.wpo-about-text span,
.wpo-about-text-2 span {
    margin-bottom: 15px;
    display: block;
    font-size: 25px;
    color: #666666;
}


.wpo-about-img img,
.wpo-about-img-2 img {
    width: 100%;
}

.wpo-about-text .btns ul {
    list-style: none;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0;
}


.wpo-section-title h2 {
    font-size: 50px;
    font-weight: 700;
    margin: 0;
    display: inline-block;
    position: relative;
    line-height: 75px;
}
.wpo-section-title{
    margin-bottom:35px;
}
.wpo-section-title span {
    font-size: 18px;
    font-weight: 400;
    color: #00d690;
    display: block;
    margin-bottom: 10px;
}
.wpo-about-text {
    padding-left: 80px;
}
.wpo-about-text .btns {
    display: flex;
    margin-top: 30px;
}

@media(max-width: 1200px){
    .wpo-about-text {
        padding-left: 10px;
        padding: 0;
    }
}
@media(max-width: 991px){
    .wpo-about-img{
        margin-bottom: 30px;
    }
    .wpo-about-text {
        padding-top: 50px;
    }
}

@media(max-width: 530px){
    .about-thumb-text {
        top: -65px;
    }
    .wpo-section-title h2 {
        font-size: 29px;
        line-height: 34px;
    }
    .wpo-about-text .btns {
        display: unset;
        flex-wrap: wrap;
    }
    .wpo-about-text li.video-holder {
        margin-left: -25px;
        margin-top: 0;
    }
    .wpo-about-text .btns ul {
        justify-content: flex-start;
        align-items: center;
        margin-top: 20px;
        align-items: center;
    }
}

/*==============================================
#0.7 destination-area
================================================*/

.destination-area{
    background: #f5fbf9;
    padding-top: 200px;
    padding-bottom: 80px;
}
.destination-area .container{
    width: 1425px;
    max-width: 100%;
}
.destination-text{
    padding-top: 100px;
}
.destination-text p{
    margin-bottom: 40px;
}
.destination-item{
    position: relative;
    margin-bottom: 30px;
}
.destination-content{
    background: #fff;
    padding: 20px;
    overflow: hidden;
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: 90%;
    transform: translateX(-50%);
    border-radius: 10px;
    transition: all .3s;
}
.content-left{
    float: left;
}
.content-right{
    float: right;
}
.content-left h5{
    color: #283a5e;
    font-size: 22px;
    font-weight: 700;
    margin-top: 0;
}
.content-left small{
    color: #687693;
    font-size: 12px;
}
.content-right h5{
    color: #02cb89;
    font-size: 25px;
    margin-top: 0;
}
.content-right p{
    color: #616f8c;
    font-size: 14px;
}

.destination-right{
    margin-top: -100px;
}
.destination-img img{
    width: 100%;
}

@media(max-width: 992px){
    .destination-area {
        padding-top: 0;
    }
    .destination-right{
        margin-top: 0;
    }
    .destination-text {
        padding-top: 100px;
        padding-bottom: 50px;
    }
}
@media (min-width: 620px) and (max-width: 767px) {
    .custom-grid {
        width: 50%;
        float: left;
    }
}
/*--------------------------------------------------------------
0.8 Room-section
--------------------------------------------------------------*/
.Room-area,
.Room-area2{
    padding-bottom: 100px;
}

@media (max-width: 991px) {
    .Room-section {
        padding-bottom: 83px;
    }
}

@media (max-width: 767px) {
    .Room-section {
        padding-bottom: 73px;
    }
}

.Room-section .gallery-filters {
    margin-bottom: 35px;
}

@media (max-width: 767px) {
    .Room-section .gallery-filters {
        margin-bottom: 30px;
    }
}

.Room-section .gallery-filters ul {
    display: inline-block;
    overflow: hidden;
    list-style: none;
}

.Room-section .gallery-filters ul li {
    display: inline-block;
}

.Room-section .gallery-filters ul > li + li {
    margin-left: 15px;
}

.Room-section .gallery-filters ul li a {
    font-weight: 500;
    color: #687693;
    display: block;
    text-transform: capitalize;
}
.Room-section .gallery-filters ul .current {
    color: #00d690;
    text-decoration: none;
}

@media (max-width: 767px) {
    .Room-section .gallery-filters ul li a {
        font-size: 15px;
        font-size: 1rem;
    }
}

.Room-section .masonry-gallery {
    margin: 0 -7.5px;
}

@media (max-width: 991px) {
    .Room-section .masonry-gallery {
        margin: 0 5px;
    }
}

@media (max-width: 500px) {
    .Room-section .masonry-gallery {
        margin: 0;
    }
}

.Room-section .masonry-gallery .grid {
    width: 33.33%;
    float: left;
    padding: 0 7.5px 15px;
}

@media (max-width: 991px) {
    .Room-section .masonry-gallery .grid {
        padding: 0 5px 10px;
    }
    .Room-section .masonry-gallery .grid {
        width: 50%;
    }
}

@media (max-width: 767px) {
    .Room-section .masonry-gallery .grid {
        width: 50%;
    }
}

@media (max-width: 690px) {
    .Room-section .masonry-gallery .grid {
        width: 100%;
        float: none;
        padding: 0 0 15px;
    }
}
.Room-section .room-item{
    position: relative;
    overflow: hidden;
}
.Room-section .grid img{
    width: 100%;
}

.Room-section .item{
    position: relative;
    margin-bottom: 30px;
    transition: all .3s;
    overflow: hidden;
}
.room-text-show {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    text-align: center;
    z-index: 999;
    padding: 20px;
    background: rgba(255,255,255,.8);
    transition: all .8s;
    z-index: 11;
}
.room-text-hide {
    position: absolute;
    bottom: -80%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    z-index: 999;
    padding: 30px;
    background: rgba(255,255,255,.9);
    transition: all .5s;
    opacity: 0;
    visibility: hidden;
    z-index: 1111;
}
.room-item:hover .room-text-hide{
    opacity: 1;
    visibility: visible;
    bottom: 4%;
}
.room-item:hover .room-text-show{
    opacity: 0;
    visibility: hidden;
}
.room-text-show h2{
    font-size: 25px;
    margin-top: 0;
    margin-bottom: 0;
}
.room-text-hide h2{
    font-size: 25px;
    margin-top: 0;
    margin-bottom: 10px;
}
.room-text-hide span{
    font-size: 18px;
    color: #727171;
    margin-bottom: 20px;
    display: block;
}
.room-text-hide small{
    color: #283a5e;
    display: block;
    margin-bottom: 20px;
}
.room-text-hide small span{
    display: inline-block;
    font-size: 20px;
    color: #283a5e;
    font-weight: 700;
}
@media(max-width: 1200px){
    .room-text-show h2 {
        font-size: 20px;
    }
    .room-text-hide p{
        font-size: 14px;
    }
    .room-text-hide small {
        margin-bottom: 0;
    }
    .room-text-hide {
        padding: 10px 15px;
    }
    .room-text-hide span {
        margin-bottom: 8px;
    }
    .room-text-hide h2 {
        font-size: 20px;
    }
}
@media(max-width: 991px){
    .room-text-show h2 {
        font-size: 20px;
    }
    .room-text-hide p{
        font-size: 16px;
    }
    .room-text-hide small {
        margin-bottom: 0;
    }
    .room-text-hide {
        padding: 20px;
    }
    .room-text-hide span {
        margin-bottom: 8px;
    }
    .room-text-hide h2 {
        font-size: 25px;
        margin-bottom: 20px;
    }
}
@media(max-width: 350px){
    .room-text-show h2 {
        font-size: 20px;
    }
    .room-text-hide p{
        font-size: 14px;
    }
    .room-text-hide small {
        margin-bottom: 0;
    }
    .room-text-hide {
        padding: 10px 15px;
    }
    .room-text-hide span {
        margin-bottom: 8px;
    }
    .room-text-hide h2 {
        font-size: 20px;
    }
}
/*===========================================
#0.9 video-banner-area
=============================================*/

.video-banner-area .container,
.video-banner-area-2 .container{
    width: 1410px;
    max-width: 100%;
    margin: auto;
}
.video-banner-area,
.video-banner-area-2{
    position: relative;
}
.video-banner-area:before,
.video-banner-area-2:before{
    position: absolute;
    left: 0;
    background: #f5fbf9;
    bottom: 0;
    content: '';
    width: 100%;
    height: 50%;
}

.banner-img{
    position: relative;
}
.banner-img:before{
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    content: "";
    background: rgba(0,0,0,.20);
}
.banner-img .video-holder{
    position: relative;
}

.banner-video{
    position: absolute;
    top: 50%;
    left: 50%;
}
.video-holder a {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #fff;
    width: 83px;
    height: 83px;
    border-radius: 83px;
    display: inline-block;
    -webkit-animation: spineer 2s infinite;
    animation: spineer 2s infinite;
    z-index: 99;
    border: 1px solid #fff;
}

.video-holder a:before {
    content: "";
    width: 0px;
    height: 0px;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 20px solid #00d690;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

/*===================================
#1.0 testimonial-area
===================================*/

.testimonial-area{
    background: #f5fbf9;
    padding-bottom: 85px;
}

.testimonial-slider .ratting ul{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    margin-bottom: 20px;
}

.testimonial-slider .quote p{
    color: #556482;
    margin-bottom: 40px;
}
.testimonial-slider .grid{
    padding: 30px;
    transition: all .3s;
    border-bottom: 2px solid #00d690;
}
.testimonial-slider .grid .ratting .fi:before{
    font-size: 20px;
}
.testimonial-slider .grid:hover{
    box-shadow: 0px 4px 17px 0px rgba(85, 85, 85, 0.1);
}
.testimonial-slider .owl-stage-outer{
    padding: 20px;
}
.testimonial-slider .ratting ul li{
    margin-right: 5px;
}
.testimonial-slider .ratting ul li i{
    color: #e0a403;
}
.client-img{
    width: 70px;
    height: 70px;
    border-radius: 50%;
    float: left;
    overflow: hidden;
    margin-right: 20px;
}
.client-text{
    overflow: hidden;
    margin-left: 20px;
}
.client-text h5{
    font-size: 19px;
    text-transform: uppercase;
    font-weight: 700;
    font-family: 'Nunito Sans', sans-serif;
}
.client-text p{
    color: #8e9fc0;
}
@media(max-width: 1200px){
    .client-text h5 {
        font-size: 15px;
    }
}
/*===================================
#1.1 blog-area start
======================================*/


.blog-area .container{
    width: 1425px;
    max-width: 100%;
}

.blog-content h3 {
    font-size: 30px;
    line-height: 47px;
    margin-bottom: 20px;
    margin-top: 15px;
}
.blog-content{
    padding-top: 20px;
}

.blog-content h3 a {
    color: #283a5e;
    transition: all .3s;
    font-weight: 700;
    text-decoration: none;
}

.blog-content h3 a:hover {
    color: #00d690;
}

.blog-content ul li {
    display: inline-block;
    padding: 0 10px;
}
.blog-content a{
    color: #00d690;
    font-weight: 700;
    text-decoration: underline;
}
.blog-content ul li a {
    color: #9298a4;
    transition: all .3s;
    font-weight: 400;
    text-decoration: none;
}

.blog-content ul li {
    color: #9298a4;
}

.blog-img img {
    -webkit-filter: grayscale(0);
    -moz-filter: grayscale(0);
    -o-filter: grayscale(0);
    -ms-filter: grayscale(0);
    filter: grayscale(0);
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    -ms-transition: all 0.5s;
    transition: all 0.5s;
    width: 100%;
}

.blog-img img:hover {
    -webkit-filter: grayscale(50%);
    -moz-filter: grayscale(50%);
    -o-filter: grayscale(50%);
    -ms-filter: grayscale(50%);
    filter: grayscale(50%);
}
.blog-item{
    margin-bottom: 30px;
}
.blog-page-area .blog-item .post-meta {
    padding-top: 20px;
}

.blog-page-area .blog-item .post-meta li {
    color: #666;
    font-size: 16px;
    display: inline-block;
    position: relative;
    padding: 0 15px;
}

.blog-area .blog-item .post-meta .clr {
    color: #c0b596;
}

.blog-area .blog-item .post-meta>li:first-child {
    padding: 0;
}

.blog-area .blog-item .post-meta>li a {
    color: #666;
    padding: 5px 15px;
    border: 1px solid #d7d7d7;
    border-radius: 20px;
}
.blog-area .blog-item .post-meta li{
    position: relative;
}

.blog-area .blog-item .post-meta>li+li+li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    width: 2px;
    height: 15px;
    background: #ccc;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.blog-area .blog-item .post-meta li img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-top: -2px;
}

@media(max-width: 992px){
    .blog-content h3 {
        font-size: 28px;
    }
}
@media(max-width: 530px){
    .blog-content h3 {
        font-size: 20px;
        line-height: 30px;
    }
    .Room-section .gallery-filters ul > li + li {
        margin-left: 7px;
    }
}

/*==========================================
#1.2 footer-area
==========================================*/

.wpo-site-footer {
    background: #062265;
    position: relative;
    font-size: 15px;
    overflow: hidden;
    /*** about-widget ***/
    /*** link-widget ***/
    /*** resource-widget ***/
    /*** market-widget ***/
    /*** wpo-lower-footer ***/
}

.wpo-ne-footer {
    background: #062265;
}

.wpo-site-footer-2 {
    background: #fcfcfc;
}
.footer-middle{
    padding-top: 100px;
}
.wpo-site-footer ul,
.wpo-site-footer-2 ul {
    list-style: none;
}

.wpo-site-footer p,
.wpo-site-footer li {
    color: #fff;
}

.wpo-site-footer-2 p,
.wpo-site-footer-2 li {
    color: #444444;
}

.wpo-site-footer .container,
.wpo-site-footer-2 .container {
    position: relative;
}

.wpo-site-footer .wpo-upper-footer,
.wpo-site-footer-2 .wpo-upper-footer {
    padding: 40px 0 70px;
    background: url(../images/footer.jpg)center center/cover no-repeat local;
}

@media (max-width: 991px) {

    .wpo-site-footer .wpo-upper-footer,
    .wpo-site-footer-2 .wpo-upper-footer {
        padding: 70px 0;
        padding-bottom: 0;
    }
}

@media (max-width: 767px) {

    .wpo-site-footer .wpo-upper-footer,
    .wpo-site-footer-2 .wpo-upper-footer {
        padding:40px 0;
    }
}

@media (max-width: 991px) {

    .wpo-site-footer .wpo-upper-footer .col,
    .wpo-site-footer-2 .wpo-upper-footer .col {
        min-height: 235px;
        margin-bottom: 70px;
    }
}

@media (max-width: 767px) {

    .wpo-site-footer .wpo-upper-footer .col,
    .wpo-site-footer-2 .wpo-upper-footer .col {
        min-height: auto;
        margin-bottom: 60px;
    }
}

.wpo-site-footer .widget-title,
.wpo-site-footer-2 .widget-title {
    margin-bottom: 30px;
}

@media (max-width: 767px) {

    .wpo-site-footer .widget-title,
    .wpo-site-footer-2 .widget-title {
        margin-bottom: 20px;
    }
}

.wpo-site-footer .widget-title h3,
.wpo-site-footer-2 .widget-title h3 {
    font-size: 23px;
    font-weight: bold;
    color: #fff;
    margin: 0;
    text-transform: capitalize;
    position: relative;
}

.wpo-site-footer-2 .widget-title h3 {
    color: #2d4277;
}

@media (max-width: 991px) {

    .wpo-site-footer .widget-title h3,
    .wpo-site-footer-2 .widget-title h3 {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

.wpo-site-footer .about-widget .logo {
    max-width: 180px;
}


.wpo-site-footer .about-widget ul,
.wpo-site-footer-2 .about-widget ul {
    overflow: hidden;
    padding-top: 10px;
}

.wpo-site-footer .about-widget ul li,
.wpo-site-footer-2 .about-widget ul li {
    font-size: 22px;
    float: left;
}

.wpo-site-footer .about-widget ul>li+li,
.wpo-site-footer-2 .about-widget ul>li+li {
    margin-left: 25px;
}

.wpo-site-footer .about-widget ul a {
    color: #fff;
}

.wpo-site-footer-2 .about-widget ul a {
    color: #404040;
}

.wpo-site-footer .about-widget ul a:hover,
.wpo-site-footer-2 .about-widget ul a:hover {
    color: #08cc7f;
}

.wpo-site-footer-2 .about-widget ul a {
    color: #08cc7f;
}
.wpo-footer-top{
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255,255,255,.10);
}
.wpo-footer-top ul{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    justify-content: flex-end;
}
.wpo-footer-top ul li{
    margin-right: 10px;
}
.wpo-footer-top ul li a{
    display: block;
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,.10);
    text-align: center;
    line-height: 65px;
    border-radius: 50%;
    font-size: 20px;
    color: #fff;
    transition: all .3s;
}
.wpo-footer-top ul li a:hover{
    background: rgba(255,255,255);
    color: #1e3054;
}
.wpo-site-footer .link-widget,
.wpo-site-footer-2 .link-widget {
    overflow: hidden;
}

@media screen and (min-width: 1200px) {
    .wpo-site-footer .link-widget {
        padding-left: 20px;
    }

    .wpo-site-footer-2 .link-widget {
        padding-left: 10px;
    }
}

@media (max-width: 1199px) {

    .wpo-site-footer .link-widget,
    .wpo-site-footer-2 .link-widget {
        padding-left: 20px;
    }
}

@media (max-width: 991px) {

    .wpo-site-footer .link-widget,
    .wpo-site-footer-2 .link-widget {
        padding-left: 0;
    }
}

@media (max-width: 767px) {

    .wpo-site-footer .link-widget,
    .wpo-site-footer-2 .link-widget {
        max-width: 350px;
    }
}
@media (max-width: 620px) {
    .wpo-footer-top ul {
        justify-content: flex-start;
    }
}

.wpo-site-footer .wpo-footer-top ul li,
.wpo-site-footer-2 .wpo-footer-top ul li {
    position: relative;
}

.wpo-site-footer .link-widget ul a,
.wpo-site-footer-2 .link-widget ul a {
    color: #fff;
}

.wpo-site-footer-2 .link-widget ul a {
    color: #404040;
}

.wpo-site-footer .link-widget ul a:hover,
.wpo-site-footer-2 .link-widget ul a:hover,
.wpo-site-footer .link-widget ul li:hover:before,
.wpo-site-footer-2 .link-widget ul li:hover:before {
    text-decoration: underline;
}

.wpo-site-footer .link-widget ul>li+li,
.wpo-site-footer-2 .link-widget ul>li+li {
    margin-top: 15px;
}

@media screen and (min-width: 1200px) {

    .wpo-site-footer .resource-widget,
    .wpo-site-footer-2 .resource-widget {
        padding-left: 0px;
    }
}

.wpo-site-footer .contact-ft ul li .fi:before,
.wpo-site-footer-2 .contact-ft ul li .fi:before {
    font-size: 20px;
    margin-right: 15px;
}

.wpo-site-footer .contact-ft ul li,
.wpo-site-footer-2 .contact-ft ul li {
    padding-bottom: 10px;
}

.wpo-site-footer .contact-ft,
.wpo-site-footer-2 .contact-ft {
    margin-top: 20px;
}

.wpo-site-footer .wpo-lower-footer,
.wpo-site-footer-2 .wpo-lower-footer {
    text-align: center;
    position: relative;
    background: #192b4e;
}

.wpo-site-footer .wpo-lower-footer .row,
.wpo-site-footer-2 .wpo-lower-footer .row {
    padding: 20px 0;
    position: relative;
}

.wpo-site-footer .wpo-lower-footer .copyright,
.wpo-site-footer-2 .wpo-lower-footer .copyright {
    display: inline-block;
    font-size: 15px;
    font-size: 0.9375rem;
    margin: 0;
    text-align: right;
    justify-content: flex-end;
    display: flex;
}
.wpo-site-footer .wpo-lower-footer .term ul{
    text-align: left;
    margin-bottom: 0;
    margin-top: 9px;
}
.wpo-site-footer .wpo-lower-footer .term ul li{
    display: inline-block;
    margin-right: 10px;
}
.wpo-site-footer .wpo-lower-footer .term ul li a{
    color: #fff;
}

@media (max-width: 991px) {

    .wpo-site-footer .wpo-lower-footer .copyright,
    .wpo-site-footer-2 .wpo-lower-footer .copyright {
        float: none;
        display: block;
    }
}

.wpo-site-footer .wpo-lower-footer .copyright a,
.wpo-site-footer-2 .wpo-lower-footer .copyright a {
    color: #fff;
    text-decoration: underline;
}

.wpo-site-footer .news-text h3 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin-top: -5px;
    line-height: 25px;
    margin-bottom: 0px;
}

.wpo-site-footer .news-text span {
    color: #ccc;
    font-size: 12px;
}
.wpo-site-footer .news-text h2{
    font-size: 20px;
    margin-top: 10px;
    color:#02cb89;
}

.wpo-site-footer .news-text {
    overflow: hidden;
    padding-left: 10px;
}

.wpo-site-footer .news-wrap {
    overflow: hidden;
    margin-bottom: 20px;
}

.wpo-site-footer .news-img {
    float: left;
}
.wpo-site-footer .newsletter-widget form input{
    background-color: transparent;
    height: 50px;
    color: #666;
    padding: 6px 20px;
    border: 1px solid rgba(255,255,255,.5);
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0;
    position: relative;
}
.wpo-site-footer .newsletter-widget form button{
    background-color: transparent;
    height: 50px;
    color: #fff;
    padding: 6px 20px;
    background: #00d690;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0;
    position: relative;
    width: 100%;
    margin-top: 30px;
    text-align: left;
    border:none;
    text-transform: uppercase;
}
.wpo-site-footer .newsletter-widget form button i{
    position: absolute;
    right: 0;
    width: 50px;
    height: 50px;
    background: #00b67a;
    line-height: 50px;
    text-align: center;
    top: 0;
}


@media(max-width: 767px){
    .wpo-site-footer .wpo-lower-footer .term ul {
        text-align: center;
    }
    .wpo-site-footer .wpo-lower-footer .copyright,
    .wpo-site-footer-2 .wpo-lower-footer .copyright{
        justify-content: center;
        text-align: center;
        font-size: 14px;
    }
    .wpo-site-footer .wpo-lower-footer .term ul li a {
        font-size: 14px;
    }
}

/*====================================
#2.0 Home-style-2
=====================================*/

.wpo-about-area .row,
.wpo-about-area-2 .row{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    align-items: center;
}
.wpo-about-area-2 .wpo-about-text{
    text-align: center;
}
.wpo-about-area-2 .wpo-section-title{
    text-align: center;
}
.wpo-about-area-2 .wpo-about-text .btns{
    justify-content: center;
}
.wpo-about-area-2 .wpo-about-text {
    padding-left: 45px;
}
@media(max-width: 991px){
    .wpo-about-area .row,
    .wpo-about-area-2 .row{
        display: unset;
        align-items: center;
    }
    .wpo-about-area-2 .wpo-about-text{
        padding-left: 0;
    }

}

/*============================
#2.0 featured-area start
=============================*/

.featured-wrap {
    position: relative;
}

.featured-img {
    position: relative;
    overflow: hidden;
}

.featured-img img {
    width: 100%;
    zoom: 1;
    transition: all .6s;
    -webkit-transition: all .6s;
    -moz-transition: all .6s;
    transform: scale(1);
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
}

.featured-wrap:hover .featured-img img {
    transform: scale(1.2);
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
}

.featured-img:before,
.featured-img:after {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #fff;
    content: "";
    z-index: 9;
    opacity: .4;
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
    -webkit-transition-property: transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
}

.featured-img:before {
    left: 0px;
    top: 0px;
    -webkit-transform-origin: top;
    transform-origin: top;
}

.featured-img:after {
    right: 0px;
    bottom: 0px;
    -webkit-transform-origin: bottom;
    transform-origin: bottom;
}

.featured-img:hover:before,
.featured-img:hover:after {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
}

.featured-content {
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translateX(-50%);
    z-index: 99;
}

.featured-content a {
    display: inline-block;
    padding: 12px 30px;
    color: #283a5e;
    font-weight: 700;
    background: #fff;
}

.featured-content a:hover {
    background: #00d690;
    color: #fff;
}

.featured-area .items{
    padding: 1px;
    padding-left: 0;
    padding-top: 0;
}
.service-media-bx {
    transition: all 0.8s;
    -moz-transition: all 0.8s;
    -webkit-transition: all 0.8s;
    -ms-transition: all 0.8s;
    -o-transition: all 0.8s;
}
.service-media-bx:hover {
    transform: translateY(-15px);
    -moz-transform: translateY(-15px);
    -webkit-transform: translateY(-15px);
    -ms-transform: translateY(-15px);
    -o-transform: translateY(-15px);
}

.service-media-bx .dlab-title a {
    color: #000;
}
.service-media-bx p {
    color: #8e8e99;
    line-height: 30px;
    margin-bottom: 15px;
}
.service-media-bx .dlab-title {
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 24px;
    line-height: 34px;
}
.service-media-bx .dlab-info {
    padding: 35px 0 20px 0;
}
.service-media-bx .site-button {
    text-transform: uppercase;
    font-weight: 600;
    padding: 12px 30px;
}
.destination-carousel.owl-carousel .owl-stage-outer {
    overflow: inherit;
}

.destination-service{
    background: #f5fbf9;
}


/*------featured-area end------*/

/*====================================
#3.0 Home-atyle-3
======================================*/
.header-style-3 {
    position: absolute;
    width: 100%;
    background: transparent;
    z-index: 999;
}
.hero-shape-wrap{
    position: relative;
}

.hero-shape{
    position: absolute;
    right: 0;
    top: 0;
}
.hero-style-3{
    height: 1000px;
}
.hero-style-3 .slide{
    height: 1000px;
}
.hero-style-3 .container{
    width: 1530px;
    max-width: 100%;
}
.hero-style-3 .slide-caption h2{
    color:#283a5e;
}
.hero-style-3 .slide-caption p{
    color:#687693;
}
.wpo-select-section-2 .wpo-select-wrap,
.wpo-select-section-3 .wpo-select-wrap{
    margin-top: 0;
}

.wpo-select-section-2,
.wpo-select-section-3{
    padding-top: 100px;
}
.country-r{
    margin-right: -220px;
}
.destination-area-2{
    background: #f5fbf9;
    padding-bottom: 70px;
}
.destination-area-2 .destination-text{
    padding-left: 250px;
    padding-top: 70px;
}
.service-area .col{
    padding: 0;
}
.service-item{
    padding: 32px;
    text-align: center;
    border:1px solid #e1ebfe;
    margin-left: -1px;
    margin-top: -1px;
}
.service-item .fi:before{
    color:#00d690;
    font-size:50px;
}
.service-text p{
    color: #687693;
}
.service-text h2{
    font-size: 30px;
    color: #283a5e;
    font-weight: 700;
    margin-bottom: 15px;
}
.Room-area-2{
    background: #f5fbf9;
}

@media(max-width: 1780px){
    .destination-area-2 .destination-text{
        padding-left: 100px;
    }
}
@media(max-width: 1500px){
    .destination-area-2 .destination-text{
        padding-top: 0px;
        padding-left: 70px;
    }
}
@media(max-width: 1200px){
    .destination-area-2 .destination-text{
        padding-top: 0px;
        padding-left: 30px;
    }
    .hero-style-3{
        height: 900px;
    }
    .hero-style-3 .slide{
        height: 900px;
    }
}
@media(max-width: 991px){
    .hero-style-3{
        height: 780px;
    }
    .hero-style-3 .slide{
        height: 780px;
    }
    .header-style-3 .navigation{
        background: #fff;
    }
    .header-style-3 {
        position: relative;
    }
}
@media(max-width: 920px){
    .hero-style-3{
        height: 650px;
    }
    .hero-style-3 .slide{
        height: 650px;
    }
}
@media(max-width: 767px){
    .hero-style-3{
        height: 450px;
        background: #f5f5f5;
    }
    .hero-style-3 .slide{
        height: 450px;
    }
    .hero-shape{
        display: none;
    }
    .hero-style-3 .container {
        width: unset;
        max-width: 100%;
    }

}
@media(max-width: 590px){
    .destination-area-2 .destination-text{
        padding-left: 20px;
    }
    .country-r{
        margin-right: 0;
    }
    .service-area .col{
        padding: 0 15px;
    }
}
/*====================================
#4.0 About-page
=====================================*/

/*.===============================
4.1 counter-area
===============================*/
.hx-counter-area,
.counter-style-2 {
    padding: 100px 0;
    background: url(../images/counter.jpg) no-repeat center center / cover;
    position: relative;
}

.hx-counter-area:before,
.counter-style-2:before {
    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.66);
}

.hx-counter-area .hx-counter-grids .grid,
.counter-style-2 .hx-counter-grids .grid {
    width: 24%;
    float: left;
    margin-right: 10px;
    margin-bottom: 10px;
    text-align: center;
    position: relative;
    background: rgba(255,255,255,.25);
    padding:40px 0;
}
.hx-counter-area .hx-counter-grids .grid .fi:before{
    color: #fff;
    font-size: 50px;
}

.hx-counter-area .hx-counter-grids .grid p,
.counter-style-2 .hx-counter-grids .grid p {
    font-size: 18px;
    font-weight: normal;
    color: #e4e4e4;
    padding-top: 5px;
    margin-bottom: 0;
}

.hx-counter-area .hx-counter-grids,
.counter-style-2 .hx-counter-grids {
    overflow: hidden;
    position: relative;
}

.hx-counter-area .odometer.odometer-auto-theme,
.hx-counter-area .odometer.odometer-theme-default,
.counter-style-2 .odometer.odometer-auto-theme,
.counter-style-2 .odometer.odometer-theme-default {
    line-height: 0.8em;
}

.hx-counter-area .hx-counter-grids .grid h2,
.counter-style-2 .hx-counter-grids .grid h2 {
    font-size: 40px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    line-height: 10px;
    margin-top: 8px;
    padding-bottom: 15px;
}

@media(max-width: 1200px) {

    .hx-counter-area .hx-counter-grids .grid,
    .counter-style-2 .hx-counter-grids .grid {
        width: 23%;
    }
}

@media(max-width: 992px) {

    .hx-counter-area .hx-counter-grids .grid h2,
    .counter-style-2 .hx-counter-grids .grid h2 {
        font-size: 35px;
    }
}

@media(max-width: 767px) {

    .hx-counter-area .hx-counter-grids .grid,
    .counter-style-2 .hx-counter-grids .grid {
        width: 31.33%;
        padding:20px 0;
    }
}

@media(max-width: 560px) {

    .hx-counter-area .hx-counter-grids .grid,
    .counter-style-2 .hx-counter-grids .grid {
        width: 48%;
        padding:20px 0;
    }
}
@media(max-width: 530px) {

    .hx-counter-area .hx-counter-grids .grid,
    .counter-style-2 .hx-counter-grids .grid {
        width: 100%;
        padding:20px 0;
    }
}

/*==============================================
#5.0 Room-single-pages
================================================*/

.select-sub h2{
    font-size: 22px;
}

.wpo-select-section-3 .select-sub span{
    font-size: 18px;
}
.wpo-select-section-3 .select-sub:first-child{
    width: unset;
}
.wpo-select-section-3 .select-sub:nth-child(2) {
    width: unset;
}
.wpo-select-section-3 .container{
    width: 1425px;
    max-width: 100%;
}
.wpo-select-section-3 .select-sub h5{
    margin-top: 0;
    font-size: 18px;
    color: #687693;
    margin-bottom: 10px;
    line-height: 0;
}
.wpo-select-section-3 .select-sub h5 span{
    font-size: 48px;
    color: #03c485;
    font-weight: 700;
    display: inline-block;
}
.wpo-select-section-3 .select-sub:last-child{
    text-align: center;
    margin-top: -10px;
}
.wpo-select-section-3 .select-sub:last-child:before{
    display: block;
}
.wpo-select-section-3 .wpo-select-area {
    padding: 50px 20px 35px;
}

.Room-carousel .owl-nav div {
    position: absolute;
    left: 70px;
    top: 50%;
    text-align: center;
    color: #00d690!important;
    font-size: 30px;
    width: 50px;
    height: 50px;
    background: #fff!important;
    line-height: 46px;
    text-align: center;
    border-radius: 50%;
    transform: translateY(-50%);
    border-radius: 50%!important;
}
.Room-carousel .owl-nav div:hover{
    background:#00d690!important;
    color: #fff!important;
}
.Room-carousel .owl-nav div.owl-next {
    right:70px;
    left: auto;
    top: 50%;
    border-radius: 50%;
    text-align: center;
}
.room-slide-area{
    padding-bottom: 70px;
}
.room-title h2{
    margin-top: 0;
    font-size: 35px;
    color: #283a5e;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebebeb;
    margin-bottom: 50px;
    font-weight: 700;
}
.room-description p{
    margin-bottom: 40px;
}
.room-description .p-wrap{
    font-size: 20px;
    color: #384f7c;
}
.room-d-text .room-title h2{
    margin-bottom: 10px;
}
.room-d-text ul li a{
    color: #283a5e;
    display: block;
    padding: 7px 0;
    position: relative;
    padding-left: 20px;
}
.room-d-text ul li a:before{
    position: absolute;
    left: 0;
    top: 6px;
    content: "\f105";
    font-family: "fontAwesome";
}
.room-details-service{
    margin-top: 20px;
}
.room-d-img{
    margin-bottom: 60px;
}

.pricing-wrap td,
.pricing-wrap th {
    border: 1px solid #ebebeb;
    text-align: center;
    font-weight: 500;
    padding: 10px;
    width: 5%;
    color: #6a7998;
}
.pricing-wrap th{
    color: #283a5e;
    font-weight: 600;
}
.map-area{
    margin-top: 60px;
}
.map-area iframe{
    width: 100%;
    height: 350px;
    filter: grayscale(100%);
}

.room-review{
    padding: 80px 0;
}
.review-item .review-img{
    float: left;
    margin-right: 40px;
}
.review-text{
    overflow: hidden;
}
.review-text h2{
    font-size: 22px;
    color: #283a5e;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 25px;
}
.review-text ul{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    align-items: center;
    margin-left: 20px;
}
.review-text ul li{
    margin-right: 5px;
}
.review-text ul li .fi:before{
    font-size: 20px;
    color: #e0a403;
}
.r-title{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
}
.r-title h2{
    margin-bottom: 0;
}
.review-text p{
    line-height: 35px;
}
.review-item{
    margin-bottom: 50px;
}
.add-review .comment-respond{
    margin-top: 30px!important;
}
.add-review .review-text ul{
    margin-left: 0;
}

.shipp input[type="checkbox"] {
    display: none;
}

.shipp input[type="checkbox"] + label span {
    display: inline-block;
    width: 25px;
    height: 25px;
    vertical-align: middle;
    background: #fff;
    position: relative;
    margin-right: 10px;
    margin-top: -2px;
    border: 1px solid #999999;
    line-height: 25px;
    text-align: center;
}
.shipp input[type="checkbox"] + label span:before {
    content: '\f00c';
    font-family: fontawesome;
    position: absolute;
    color: #fff;
    font-size: 14px;
    opacity: 0;
    visibility: hidden;
    top: -2px;
    left: 5px;
}

.shipp input[type="checkbox"]:checked + label span {
    background-color: #00d690;
    border-color: transparent;
}
.shipp input[type="checkbox"]:checked + label span:before {
    visibility: visible;
    opacity: 1;
}
.form-check{
    margin: 25px 0 40px;
}
.shipp label{
    color: #283a5e;
    font-weight: 400;
}
.other-room{
    padding: 80px 0 100px;
}
.other-room .container{
    padding: 0;
}

@media(max-width: 1200px){
    .wpo-select-section-3 .select-sub span {
        font-size: 13px;
    }
    .select-sub h2 {
        font-size: 15px;
    }
    .wpo-select-section-3 .select-sub h5 span {
        font-size: 30px;
    }
    .wpo-select-section-3 .select-sub:last-child:before{
        display: none;
    }
    .wpo-select-section-3 .select-sub h5 {
        font-size: 14px;
    }

    .wpo-select-area form button {
        font-size: 12px;
        width: 152px;
    }
}


@media(max-width: 992px){
    .room-details-item {
        padding: 0 15px;
    }
    .room-d-img img{
        width: 100%;
    }
}

@media(max-width: 767px){
    .wpo-select-section-3 .select-sub:last-child {
        margin-top: 40px;
    }
    .wpo-select-section-3 {
        padding-top: 60px;
    }
    .wpo-select-section-3 .wpo-select-wrap {
        padding: 0px;
    }
}

@media(max-width: 650px){
    .wpo-select-section-3 .select-sub:first-child {
        width: 100%;
    }
    .wpo-select-section-3 .select-sub:nth-child(2){
        width: 100%;
    }
    .wpo-select-section-3 .select-sub {
        width: 100%;
        margin-bottom: 30px;
        border-bottom: 1px solid #ddd;
    }
    .wpo-select-section-3 .select-sub:last-child{
        border-bottom: 0;
    }
    .pricing-table{
        overflow-y: hidden;
        overflow-x: scroll;
    }
}

@media(max-width:540px){
    .review-item .review-img{
        float: none;
        margin-bottom: 20px;
    }
    .review-text h2 {
        font-size: 20px;
    }
}



/*--------------------------------------------------------------
#6.0 service-single-page
--------------------------------------------------------------*/

/*--------------------------------------
  service single sidebar
----------------------------------------*/
.service-sidebar {
    /*** service-list-widget ***/
    /*** contact-widget ***/
}

@media (max-width: 991px) {
    .service-sidebar {
        max-width: 300px;
        margin-top: 80px;
    }
}

@media screen and (min-width: 1200px) {
    .service-sidebar {
        padding-right: 30px;
    }
}

.service-sidebar > .widget + .widget {
    margin-top: 60px;
}

.service-sidebar .service-list-widget {
    background-color: #f8f8f8;
    padding: 55px 30px;
}

.service-sidebar .service-list-widget h3 {
    font-size: 24px;
    font-size: 1.5rem;
    margin: 0 0 0.7em;
    padding-bottom: 0.70em;
    position: relative;
}

.service-sidebar .service-list-widget h3:before {
    content: "";
    background-color: #00d690;
    width: 80px;
    height: 4px;
    border-radius: 10px;
    position: absolute;
    left: 0;
    bottom: 0;
}

.service-sidebar .service-list-widget h3:after {
    content: "";
    background-color: #00d690;
    width: 15px;
    height: 4px;
    border-radius: 10px;
    position: absolute;
    left: 85px;
    bottom: 0;
}

.service-sidebar .service-list-widget ul {
    list-style: none;
    border-bottom: 0;
    margin-bottom: 0;
}

.service-sidebar .service-list-widget ul li {
    border-bottom: 1px solid #e1e1e1;
}

.service-sidebar .service-list-widget ul > li:last-child {
    border-bottom: 0;
    padding-bottom: 0;
    margin-bottom: 0;
}
.service-sidebar .service-list-widget ul > li:last-child a{
    padding-bottom: 0;
    margin-bottom: 0;
}

.service-sidebar .service-list-widget a {
    background: transparent;
    display: block;
    color: #787878;
    padding: 15px 0;
}

.service-sidebar .service-list-widget a:hover,
.service-sidebar .service-list-widget .current a {
    color: #00d690;
}

.service-sidebar .contact-widget {
    background-color: #00d690;
    padding: 55px 35px;
    position: relative;
}

.service-sidebar .contact-widget h4 {
    font-size: 20px;
    color: #fff;
    margin: 0;
}

.service-sidebar .contact-widget h2 {
    font-size: 35px;
    color: #fff;
    margin: 0.7em 0 0;
}



.service-single-section .service-single-content h2 {
    font-size: 35px;
    font-size: 2.1875rem;
    margin: 0 0 0.7em;
    text-transform: capitalize;
}

@media (max-width: 991px) {
    .service-single-section .service-single-content h2 {
        font-size: 25px;
        font-size: 1.5625rem;
    }
}

@media (max-width: 767px) {
    .service-single-section .service-single-content h2 {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

.service-single-section .service-single-content h3 {
    font-size: 30px;
    font-size: 1.875rem;
    text-transform: capitalize;
    margin: 0 0 1em;
}

@media (max-width: 991px) {
    .service-single-section .service-single-content h3 {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

@media (max-width: 767px) {
    .service-single-section .service-single-content h3 {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

.service-single-section .service-single-content p {
    margin-bottom: 1.3em;
}

.service-single-section .service-single-content .service-single-img {
    position: relative;
    margin-bottom: 60px;
}

@media (max-width: 991px) {
    .service-single-section .service-single-content .service-single-img {
        margin-bottom: 50px;
    }
}

.service-single-section .service-single-content .why-choose-section {
    padding-top: 15px;
}

.service-single-section .service-single-content .why-choose-section .feature-grids {
    margin: 0 -15px;
}

@media (max-width: 767px) {
    .service-single-section .service-single-content .why-choose-section .feature-grids {
        margin: 0 -7.5px;
    }
}

.service-single-section .service-single-content .why-choose-section .feature-grids .grid {
    background: #fff;
    width: calc(33% - 30px);
    float: left;
    margin: 0 15px 30px;
    padding: 40px 30px;
    -webkit-box-shadow: 0px 0px 20px 0px rgba(102, 102, 102, 0.1);
    box-shadow: 0px 0px 20px 0px rgba(102, 102, 102, 0.1);
    position: relative;
    overflow: hidden;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    -ms-transition: all 0.5s;
    transition: all 0.5s;
}

@media (max-width: 1199px) {
    .service-single-section .service-single-content .why-choose-section .feature-grids .grid {
        padding: 30px;
    }
}

@media (max-width: 991px) {
    .service-single-section .service-single-content .why-choose-section .feature-grids .grid {
        width: calc(50% - 30px);
    }
}

@media (max-width: 767px) {
    .service-single-section .service-single-content .why-choose-section .feature-grids .grid {
        width: calc(50% - 15px);
        margin: 0 7.5px 30px;
    }
}

@media (max-width: 600px) {
    .service-single-section .service-single-content .why-choose-section .feature-grids .grid {
        width: calc(100% - 15px);
        float: none;
    }
}

.service-single-section .service-single-content .why-choose-section .feature-grids .fi:before {
    font-size: 45px;
    color: #00d690;
}

.service-single-section .service-single-content .why-choose-section .feature-grids .grid h3 {
    font-size: 19px;
    margin: 0.5em 0 0.73em;
}

@media (max-width: 1199px) {
    .service-single-section .service-single-content .why-choose-section .feature-grids .grid h3 {
        font-size: 18px;
    }
}

.service-single-section .service-single-content .why-choose-section .feature-grids .grid p {
    margin: 0;
}

.service-single-section .service-single-content .why-choose-section .feature-grids .hover-icon {
    position: absolute;
    right: -40px;
    top: -40px;
}

.service-single-section .service-single-content .why-choose-section .feature-grids .hover-icon .fi:before {
    font-size: 100px;
    color: rgba(255, 255, 255, 0.1);
}

.service-single-section .service-single-content .why-choose-section .feature-grids .grid:hover {
    background: #00d690;
    border-radius: 15px 15px 15px 0;
}

.service-single-section .service-single-content .why-choose-section .feature-grids .grid:hover .icon .fi:before,
.service-single-section .service-single-content .why-choose-section .feature-grids .grid:hover h3,
.service-single-section .service-single-content .why-choose-section .feature-grids .grid:hover p {
    color: #fff;
}

.service-single-section .service-single-content .tab-area {
    margin-top: 40px;
}

.service-single-section .service-single-content .tab-area .tablinks {
    border-top: 1px solid #eeeeee;
    margin-bottom: 15px;
}

.service-single-section .service-single-content .tab-area .tablinks ul {
    overflow: hidden;
}

.service-single-section .service-single-content .tab-area .tablinks li {
    width: 25%;
    float: left;
    position: relative;
}

@media (max-width: 500px) {
    .service-single-section .service-single-content .tab-area .tablinks li {
        width: auto;
    }
}

.service-single-section .service-single-content .tab-area .tablinks ul > li + li:before {
    content: "";
    background: #787878;
    width: 5px;
    height: 2px;
    position: absolute;
    left: -30px;
    top: 50%;
}

@media (max-width: 500px) {
    .service-single-section .service-single-content .tab-area .tablinks ul > li + li:before {
        display: none;
    }
}

.service-single-section .service-single-content .tab-area .tablinks a {
    font-size: 18px;
    color: #787878;
    padding: 25px 0;
    display: block;
    position: relative;
}

@media (max-width: 767px) {
    .service-single-section .service-single-content .tab-area .tablinks a {
        font-size: 16px;
    }
}

@media (max-width: 500px) {
    .service-single-section .service-single-content .tab-area .tablinks a {
        padding: 25px 15px;
    }
}

.service-single-section .service-single-content .tab-area .tablinks a:before {
    content: "";
    background: #00d690;
    width: 100%;
    height: 2px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}

.service-single-section .service-single-content .tab-area .tablinks li.active a:before {
    opacity: 1;
}

.service-single-section .service-single-content .tab-area .tablinks li.active a {
    text-decoration: none;
}

.service-single-section .service-single-content .tab-area .tab-pane > p:first-child {
    font-size: 20px;
    margin-bottom: 1.3em;
}

@media (max-width: 767px) {
    .service-single-section .service-single-content .tab-area .tab-pane > p:first-child {
        font-size: 16px;
    }
}

.service-single-section .service-single-content .tab-area .tab-pane > p:last-child {
    margin-bottom: 0;
}

.service-single-section .service-single-content .tab-area .img-area {
    margin: 0 -15px;
    padding: 40px 0 30px;
    overflow: hidden;
}

@media (max-width: 767px) {
    .service-single-section .service-single-content .tab-area .img-area {
        margin: 0 -7.5px;
        padding: 30px 0 20px;
    }
}

.service-single-section .service-single-content .tab-area .img-area img {
    width: calc(50% - 30px);
    float: left;
    margin: 0 15px 30px;
}

@media (max-width: 767px) {
    .service-single-section .service-single-content .tab-area .img-area img {
        width: calc(50% - 15px);
        float: left;
        margin: 0 7.5px 30px;
    }
}

@media (max-width: 500px) {
    .service-single-section .service-single-content .tab-area .img-area img {
        width: calc(100% - 15px);
        float: none;
    }
}

/*=======================================
#7.0 FAQ page
==========================================*/
.theme-accordion-s1 {
    margin-bottom: 0;
}

.theme-accordion-s1 .panel-default {
    background: transparent;
    border: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.theme-accordion-s1 .panel-heading {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

.theme-accordion-s1 .panel + .panel {
    margin-top: 15px;
}

.theme-accordion-s1 .panel-heading a {
    background: #999999;
    font-size: 16px;
    font-size: 1rem;
    font-weight: 600;
    color: #fff;
    display: block;
    padding: 18px 25px;
    position: relative;
}

@media (max-width: 991px) {
    .theme-accordion-s1 .panel-heading a {
        font-size: 15px;
        font-size: 0.9375rem;
        padding: 15px 25px;
    }
}

@media (max-width: 767px) {
    .theme-accordion-s1 .panel-heading a {
        padding: 12px 15px;
    }
}

.theme-accordion-s1 .panel-heading a:before {
    font-family: "themify";
    content: "\e64b";
    font-size: 15px;
    font-size: 0.9375rem;
    position: absolute;
    right: 25px;
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    -ms-transition: all 0.3s;
    transition: all 0.3s;
}

@media (max-width: 991px) {
    .theme-accordion-s1 .panel-heading a:before {
        font-size: 18px;
        font-size: 1.125rem;
        right: 20px;
    }
}

.theme-accordion-s1 .panel-heading a:focus {
    text-decoration: none;
}

.theme-accordion-s1 .panel-heading .collapsed {
    background-color: #fff;
    color: #212122;
    -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
    box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
}

.theme-accordion-s1 .panel-heading .collapsed:before {
    -webkit-transform: rotate(0);
    -ms-transform: rotate(0);
    transform: rotate(0);
}

.theme-accordion-s1 .panel-heading + .panel-collapse > .panel-body {
    background-color: #fff;
    border: 0;
    padding: 40px 25px 15px;
    -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
    box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
}

@media (max-width: 991px) {
    .theme-accordion-s1 .panel-heading + .panel-collapse > .panel-body {
        padding: 20px 25px 10px;
        font-size: 15px;
        font-size: 0.9375rem;
    }
}

@media (max-width: 767px) {
    .theme-accordion-s1 .panel-heading + .panel-collapse > .panel-body {
        padding: 15px 15px 8px;
    }
}

.theme-accordion-s1 .panel-heading + .panel-collapse > .panel-body p {
    margin-bottom: 1.3em;
}


/*=======================================
#8.0 Contact-page
==========================================*/

.info-item{
    text-align: center;
}

.wpo-contact-info .info-icon {
    width: 90px;
    height: 90px;
    background: #fff;
    line-height: 90px;
    border-radius: 50%;
    -webkit-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    -moz-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    text-align: center;
    margin-right: 20px;
    align-items: center;
    margin: auto;
    margin-bottom: 20px;
}

.info-item {
    -webkit-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    -moz-box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    box-shadow: 0px 10px 40px 0px rgba(50, 50, 50, 0.1);
    padding: 33px 40px;
    margin-bottom: 30px;
    background: #fff;
    box-shadow: 0px 1px 18px 0px rgba(21, 44, 91, 0.1);
    border-radius: 5px;
}

.info-item h2 {
    font-size: 24px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 15px;
    color: #283a5e;

}

.info-icon .fi:before {
    font-size: 40px;
    color: #00d690;
}

.info-text span {
    color: #666666;
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 10px;
    display: block;
}


@media(max-width: 590px) {
    .info-item h2 {
        font-size: 20px;
    }

    .info-text span {
        font-size: 15px;
    }

    .wpo-contact-info form textarea {
        height: 100px;
    }

    .info-item {
        padding: 32px 25px;
    }
}
.contact-area form,
.contact-area-s2 form {
    overflow: hidden;
    margin: 0 -15px;
}

.contact-area form .half-col,
.contact-area-s2 form .half-col {
    width: 50%;
    float: left;
}

@media (max-width: 600px) {

    .contact-area form .half-col,
    .contact-area-s2 form .half-col {
        width: 100%;
        float: left;
    }
}

.contact-area form div,
.contact-area-s2 form div {
    padding: 0 15px 30px;
}


.contact-area form .submit-btn-wrapper,
.contact-area-s2 form .submit-btn-wrapper {
    padding-bottom: 0;
}

.contact-area form input,
.contact-area-s2 form input,
.contact-area form textarea,
.contact-area-s2 form textarea,
.contact-area-s2 form select {
    background:transparent;
    height: 50px;
    padding: 6px 15px;
    border-radius: 5px;
    -webkit-box-shadow: none;
    box-shadow: none;
    border:none;
    border:1px solid #e1e1e1;
}

.contact-area select.form-control:not([size]):not([multiple]) {
    height: 50px;
    padding: 6px 15px;
    color: #bbb5a5;
    border:none;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    position: relative;
    -webkit-appearance:none;
    -ms-appearance:none;
    -o-appearance:none;
    appearance:none;
    -moz-appearance: none;
}

.contact-area form input:focus,
.contact-area-s2 form input:focus,
.contact-area form textarea:focus,
.contact-area-s2 form textarea:focus {
    -webkit-box-shadow: none;
    box-shadow: none;
    background:transparent;
    border:1px solid #e1e1e1;
}


.contact-area form textarea,
.contact-area-s2 form textarea {
    height: 150px;
    padding: 15px;
}
.contact-area .submit-btn-wrapper{
    text-align: center;
    margin-top: 30px;
}
.contact-validation-active #success,
.contact-validation-active #error {
    width: 100%;
    color: #fff;
    padding: 5px 10px;
    font-size: 16px;
    text-align: center;
    display: none;
}

.contact-validation-active #loader {
    display: none;
    margin-top: 10px;
}

.contact-section form .submit-btn-wrapper,
.contact-section-s2 form .submit-btn-wrapper {
    padding-bottom: 0;
}

.theme-btn-s3 {
    border-radius: 0;
    text-transform: uppercase;
}

.contact-validation-active .error-handling-messages {
    margin-top: 15px;
}

.contact-validation-active #error {
    background-color: #ff1a1a;
    border-left: 5px solid red;
}

.contact-validation-active label.error {
    color: red;
    font-size: 0.875rem;
    font-weight: normal;
    margin: 5px 0 0 0;
    text-align: left;
    display: block;
}

.contact-form ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color:#777;
    font-size: 14px;
}

.contact-form ::-moz-placeholder {
    /* Firefox 19+ */

    color:#777;
    font-size: 14px;
}

.contact-form :-ms-input-placeholder {
    /* IE 10+ */
    color:#777;
    font-size: 14px;
}

.contact-form :-moz-placeholder {
    /* Firefox 18- */

    color:#777;
    font-size: 14px;
}
.contact-content h2{
    font-size: 40px;
    font-weight: 700;
    color: #283a5e;
    margin-bottom: 50px;
    text-align: center;
}
.contact-content span{
    color: #00d690;
}
.contact-text h2 span{
    color: #00d690;
}
.contact-content .theme-btn{
    color: #fff;
    background: #00d690;
    transition: all .5s;
}
.contact-content .theme-btn:hover{
    color: #00d690;
    background: transparent;
}

.contact-map {
    padding: 30px;
    margin-top: 80px;
    max-width: 100%;
    height: 455px;
    box-shadow: 0px 1px 18px 0px rgba(21, 44, 91, 0.1);
}

@media (max-width: 767px) {
    .contact-map {
        height: 350px;
    }
}

.contact-map iframe {
    width: 100%;
    height: 100%;
    border: 0;
}

.contact-map {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
}

.contact-content{
    padding: 40px;
    box-shadow: 0px 1px 18px 0px rgba(21, 44, 91, 0.1);
    padding-bottom: 10px;
}
@media(max-width: 991px){
    .contact-text{
        margin-bottom: 40px;
    }
}
@media (max-width: 767px) {
    .contact-content {
        padding: 25px;
        padding-bottom: 10px;
    }
}
/*============================================
#9.0 blog-page-section start
=============================================*/

/******************************
  #page title
******************************/
.page-title {
    background: url("../images/page-title.jpg") center center/cover no-repeat local;
    height: 400px;
    text-align: center;
    position: relative;
    margin:0 40px;
}


.page-title:before {
    content: "";
    background: rgba(10,54,94,.3);
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.page-title .container {
    height: 100%;
    display: table;
}

.page-title .container > .row {
    vertical-align: middle;
    display: table-cell;
}

.page-title h2,
.page-title ol {
    color: #283a5e;
}

.page-title h2 {
    font-size: 40px;
    font-size: 2.5rem;
    margin-bottom: 0.30em;
    text-transform: capitalize;
    font-weight: 700;
    color: #fff;
}

@media (max-width: 991px) {
    .page-title h2 {
        font-size: 35px;
        font-size: 2.1875rem;
        margin-top: 30px;
    }

    .page-title {
        height: 310px;
        margin: 0;
    }

}

@media (max-width: 767px) {
    .page-title h2 {
        font-size: 30px;
        font-size: 1.875rem;
        margin-top: 20px;
    }
    .page-title {
        height: 250px;
    }
}

.page-title .breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0;
}

@media (max-width: 767px) {
    .page-title .breadcrumb {
        text-align: center;
    }
}

.page-title .breadcrumb li {
    font-size: 16px;
    font-size: 1rem;
    color: #fff;
    margin-right: 5px;
    position: relative;
}

@media (max-width: 767px) {
    .page-title .breadcrumb li {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.page-title .breadcrumb li a {
    color: #fff;
}

.page-title .breadcrumb li a:hover {
    color: #00d690;
}

.page-title .breadcrumb > li + li {
    margin-left: 20px;
    padding-left: 15px;
}

.page-title .breadcrumb > li + li:before {
    content: "/";
    color: #fff;
    padding: 0;
    position: absolute;
    left: -8px;
    top: 2px;
}


.preloader {
    background-color: #fff;
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 999999;
}

.preloader .spinner {
    width: 70px;
    position: absolute;
    left: calc(50% - 35px);
    top: calc(50% - 12px);
}

.preloader .spinner > div {
    width: 18px;
    height: 18px;
    background-color: #00d690;
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.preloader .spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.preloader .spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {

    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
    }

    40% {
        -webkit-transform: scale(1);
    }
}

@keyframes sk-bouncedelay {

    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

/*--------------------------------------------------------------
#15.0 wpo-blog page
--------------------------------------------------------------*/

/*------------------------------------
wpo-blog sidebar
------------------------------------*/
.wpo-blog-sidebar {
    /*** search-widget ***/
    /*** category-widget ***/
    /*** recent-post-widget ***/
    /*** quote-widget ****/
    /*** tag-widget ***/
}

@media screen and (min-width: 992px) {
    .wpo-blog-sidebar {
        padding-left: 0px;
    }
}

@media (max-width: 991px) {
    .wpo-blog-sidebar {
        margin-top: 80px;
        max-width: 400px;
    }
}

@media (max-width: 991px) {
    .wpo-blog-sidebar-2 {
        margin-top: 0;
        max-width: 400px;
        margin-bottom: 80px;
    }
}

@media (max-width: 767px) {
    .wpo-blog-sidebar {
        margin-top: 60px;
        max-width: 400px;
    }
}

@media (max-width: 767px) {
    .wpo-blog-sidebar-2 {
        margin-top: 0;
        max-width: 400px;
        margin-bottom: 80px;
    }
}


.wpo-blog-sidebar .widget h3 {
    font-size: 18px;
    font-size: 1.125rem;
    font-weight: 700;
    position: relative;
    text-transform: capitalize;
    padding-bottom: 20px;
    margin-bottom: 30px;
    border-bottom: 1px solid #ebebeb;
}
.wpo-blog-sidebar .widget h3:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 60px;
    height: 4px;
    background: #00d690;
}

.wpo-blog-sidebar>.widget+.widget {
    margin-top: 65px;
}

.wpo-blog-sidebar .search-widget {
    border: 0;
    padding: 0;
}

.wpo-blog-sidebar .search-widget form div {
    position: relative;
}

.wpo-blog-sidebar .search-widget input {
    height: 50px;
    font-size: 16px;
    font-size: 1rem;
    border: 1px solid #ebebeb;
    padding: 6px 50px 6px 20px;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    -ms-transition: all 0.3s;
    transition: all 0.3s;
}

.wpo-blog-sidebar .search-widget input:focus {
    background-color: rgba(0, 198, 209, 0.1);
}

.wpo-blog-sidebar .search-widget form button {
    background: #00d690;
    font-size: 20px;
    font-size: 1.25rem;
    color: #fff;
    border: 0;
    outline: 0;
    width: 50px;
    height: 50px;
    line-height: 54px;
    position: absolute;
    right: 0;
    top: 0;
}

.wpo-blog-sidebar .category-widget ul {
    list-style: none;
}

.wpo-blog-sidebar .category-widget ul li {
    font-size: 15px;
    font-size: 0.9375rem;
    position: relative;
    padding-left: 20px;
    padding-bottom: 10px;
}

.wpo-blog-sidebar .category-widget ul li:before {
    content: "";
    background: #bbb;
    width: 8px;
    height: 8px;
    position: absolute;
    left: 0;
    top: 4px;
    border-radius: 50%;
}

@media (max-width: 767px) {
    .wpo-blog-sidebar .category-widget ul li {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.wpo-blog-sidebar .category-widget ul li span {
    position: absolute;
    right: 0;
}

.wpo-blog-sidebar .category-widget ul>li+li {
    margin-top: 12px;
}

.wpo-blog-sidebar .category-widget ul a {
    display: block;
    color: #777;
}

.wpo-blog-sidebar .category-widget ul a:hover {
    color: #00d690;
}

.wpo-blog-sidebar .category-widget ul li:hover:before {
    background: #00d690;
}

.wpo-blog-sidebar .recent-post-widget .post {
    overflow: hidden;
}

.wpo-blog-sidebar .recent-post-widget .posts>.post+.post {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f2f0f0;
}

.wpo-blog-sidebar .recent-post-widget .post .img-holder {
    width: 68px;
    float: left;
}

.wpo-blog-sidebar .recent-post-widget .post .details {
    width: calc(100% - 68px);
    float: left;
    padding-left: 20px;
}

.wpo-blog-sidebar .recent-post-widget .post h4 {
    font-size: 16px;
    line-height: 1.3em;
    margin: 0 0 0.3em;
    color: #444444;
}

@media (max-width: 1199px) {
    .wpo-blog-sidebar .recent-post-widget .post h4 {
        margin: 0;
    }
}

.wpo-blog-sidebar .recent-post-widget .post h4 a {
    display: inline-block;
    color: #242f6c;
}

@media (max-width: 1199px) {
    .wpo-blog-sidebar .recent-post-widget .post h4 {
        font-size: 14px;
    }
}

.wpo-blog-sidebar .recent-post-widget .post h4 a:hover {
    color: #00d690;
}

.wpo-blog-sidebar .recent-post-widget .post .details .date {
    font-size: 14px;
    color: #888;
}

.wpo-blog-sidebar .recent-post-widget .post .details .date i {
    display: inline-block;
    padding-right: 7px;
}

.wpo-blog-sidebar .quote-widget {
    border: 0;
    padding: 0;
}

.wpo-blog-sidebar .quote-widget .quote {
    background-color: #f5f5f5;
    padding: 30px;
}

.wpo-blog-sidebar .quote-widget .quote p {
    margin: 0;
}

.wpo-blog-sidebar .quote-widget i {
    display: block;
}

.wpo-blog-sidebar .quote-widget .fi:before {
    font-size: 35px;
}

.wpo-blog-sidebar .quote-widget .quoter {
    position: relative;
    padding: 25px 25px 25px 110px;
}

.wpo-blog-sidebar .quote-widget .quoter .img-holder {
    position: absolute;
    left: 0;
}

.wpo-blog-sidebar .quote-widget .quoter img {
    padding: 5px;
    border: 1px solid #dcdcdc;
    border-radius: 50%;
}

.wpo-blog-sidebar .quote-widget .quoter h4 {
    font-size: 16px;
    font-size: 1rem;
    font-weight: bold;
    margin: 0 0 0.3em;
    padding-top: 1.3em;
}

.wpo-blog-sidebar .quote-widget .quoter h4+p {
    font-size: 14px;
    font-size: 0.875rem;
    margin: 0;
}

.wpo-blog-sidebar .tag-widget ul {
    overflow: hidden;
    list-style: none;
}

.wpo-blog-sidebar .tag-widget ul li {
    float: left;
    margin: 0 8px 8px 0;
}

.wpo-blog-sidebar .tag-widget ul li a {
    font-size: 14px;
    display: inline-block;
    padding: 10px 22px;
    color: #525252;
    border: 1px solid #ebebeb;
    border-radius: 0;
}

@media(max-width: 1200px) {
    .wpo-blog-sidebar .tag-widget ul li a {
        padding: 10px 13px;
        font-size: 14px;
    }
}

.wpo-blog-sidebar .tag-widget ul li a:hover {
    background: #00d690;
    color: #fff;
}

.instagram ul {
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    flex-wrap: wrap;
    margin: 0px -3px;
}

.instagram ul li {
    -ms-flex: 0 0 33.33%;
    flex: 0 0 33.33%;
    max-width: 33.33%;
    margin-bottom: 5px;
    padding: 0px 3px;
}

.instagram ul li img {
    width: 100%;
}

/**** pagination ****/
.pagination-wrapper {
    text-align: center;
    margin-top: 45px;
}

@media (max-width: 991px) {
    .pagination-wrapper {
        text-align: left;
        margin-top: 40px;
    }
}

.pagination-wrapper .pg-pagination {
    display: inline-block;
    overflow: hidden;
    list-style-type: none;
    text-align: center;
}

.pagination-wrapper .pg-pagination li {
    float: left;
    margin-right: 10px;
}

@media (max-width: 767px) {
    .pagination-wrapper .pg-pagination li {
        margin-right: 5px;
    }
}

.pagination-wrapper .pg-pagination li:last-child {
    margin-right: 0;
}

.pagination-wrapper .pg-pagination li a {
    background-color: #f5f5f5;
    width: 35px;
    height: 35px;
    line-height: 35px;
    font-size: 15px;
    font-size: 0.9375rem;
    font-weight: 600;
    color: #666;
    border: 0;
    display: block;
    border-radius: 5px;
}

.pagination-wrapper .pg-pagination .active a,
.pagination-wrapper .pg-pagination li a:hover {
    background: #00d690;
    border-color: #00d690;
    color: #fff;
}

.pagination-wrapper .pg-pagination .fi:before {
    font-size: 15px;
    font-size: 0.9375rem;
}

.pagination-wrapper-left {
    text-align: left;
}

.pagination-wrapper-right {
    text-align: right;
}

@media screen and (min-width: 1200px) {
    .pagination-wrapper-right {
        padding-right: 50px;
    }
}

@media (max-width: 991px) {
    .pagination-wrapper-right {
        margin-top: 45px;
        text-align: left;
    }
}

/*------------------------------------
  category sidebar
------------------------------------*/
.category-sidebar {
    /*** category-widget ***/
    /*** tag-widget ***/
}

@media screen and (min-width: 992px) {
    .category-sidebar {
        padding-right: 45px;
    }
}

@media (max-width: 991px) {
    .category-sidebar {
        margin-bottom: 80px;
        max-width: 400px;
    }
}

@media (max-width: 767px) {
    .category-sidebar {
        margin-bottom: 60px;
        max-width: 400px;
    }
}

.category-sidebar .widget {
    padding: 30px;
    -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
    box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
}

.category-sidebar .widget h3 {
    font-size: 18px;
    font-size: 1.125rem;
    font-weight: 600;
    border-bottom: 1px solid #ebebeb;
    margin: 0 -30px 30px;
    padding: 0 0 30px 30px;
    position: relative;
    text-transform: capitalize;
}

.category-sidebar>.widget+.widget {
    margin-top: 30px;
}

.category-sidebar .category-widget ul {
    list-style: none;
}

.category-sidebar .category-widget ul li {
    font-size: 15px;
    font-size: 0.9375rem;
    position: relative;
    padding-left: 20px;
}

.category-sidebar .category-widget ul li:before {
    content: "";
    background: #bbb;
    width: 6px;
    height: 6px;
    position: absolute;
    left: 0;
    top: 8px;
}

@media (max-width: 767px) {
    .category-sidebar .category-widget ul li {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.category-sidebar .category-widget ul li span {
    position: absolute;
    right: 0;
}

.category-sidebar .category-widget ul>li+li {
    margin-top: 12px;
}

.category-sidebar .category-widget ul a {
    display: block;
    color: #525252;
}

.category-sidebar .category-widget ul a:hover {
    color: #00d690;
}

.category-sidebar .category-widget ul li:hover:before {
    background: #00d690;
}

.category-sidebar .tag-widget {
    padding: 15px 0;
}

.category-sidebar .tag-widget ul {
    list-style: none;
}

.category-sidebar .tag-widget ul li {
    font-size: 15px;
    font-size: 0.9375rem;
    position: relative;
}

.category-sidebar .tag-widget ul li:before {
    content: "";
    width: 15px;
    height: 15px;
    border: 3px solid #d7d7d7;
    border-radius: 50%;
    position: absolute;
    left: 30px;
    top: 15px;
}

@media (max-width: 767px) {
    .category-sidebar .tag-widget ul li {
        font-size: 14px;
        font-size: 0.875rem;
    }
}

.category-sidebar .tag-widget ul a {
    display: block;
    color: #525252;
    padding: 12px 15px 12px 60px;
}

.category-sidebar .tag-widget ul a:hover {
    background: #00d690;
    background: -webkit-linear-gradient(left, #00d690, #3a9ce0);
    background: -moz-linear-gradient(left, #00d690, #3a9ce0);
    background: -o-linear-gradient(left, #00d690, #3a9ce0);
    background: -ms-linear-gradient(left, #00d690, #3a9ce0);
    background: -webkit-gradient(linear, left top, right top, from(#00d690), to(#3a9ce0));
    background: linear-gradient(left, #00d690, #3a9ce0);
    color: #fff;
}

.category-sidebar .tag-widget ul li:hover:before {
    border-color: #fff;
}

.wpo-blog-pg-section {
    /*** format-standard ***/
    /*** format-gallery ***/
    /*** format-quote ***/
    /*** format-video ***/
}

.wpo-blog-pg-section .wpo-blog-content .post {
    margin-bottom: 60px;
}

@media (max-width: 991px) {
    .wpo-blog-pg-section .wpo-blog-content .post {
        margin-bottom: 70px;
    }
}

@media (max-width: 767px) {
    .wpo-blog-pg-section .wpo-blog-content .post {
        margin-bottom: 60px;
    }
}

.wpo-blog-pg-section .entry-meta {
    list-style: none;
    overflow: hidden;
    margin-bottom: 25px;
    padding-top: 20px;
}

@media (max-width: 767px) {
    .wpo-blog-pg-section .entry-meta {
        margin: 10px 0 25px;
    }
}

.wpo-blog-pg-section .entry-meta li {
    font-size: 14px;
    font-size: 0.875rem;
    float: left;
    color: #9298a4;
    text-transform: uppercase;
    font-weight: 500;
}

@media (max-width: 767px) {
    .wpo-blog-pg-section .entry-meta li {
        font-size: 12px;
        font-size: 0.75rem;
    }
}

@media (max-width: 490px) {
    .wpo-blog-pg-section .entry-meta li {
        float: none;
        display: block;
        margin-bottom: 5px;
    }
}

.wpo-blog-pg-section .entry-meta li .fi:before {
    font-size: 16px;
    font-size: 1rem;
    display: inline-block;
    padding-right: 5px;
}

@media (max-width: 767px) {
    .wpo-blog-pg-section .entry-meta li .fi:before {
        font-size: 12px;
        font-size: 0.75rem;
    }
}

.wpo-blog-pg-section .entry-meta>li+li {
    margin-left: 15px;
    padding-left: 15px;
    position: relative;
}

@media (max-width: 490px) {
    .wpo-blog-pg-section .entry-meta>li+li {
        margin-left: 0;
        padding-left: 0;
    }
}

.wpo-blog-pg-section .entry-meta li a {
    color: #525252;
    font-weight: 600;
}

.wpo-blog-pg-section .entry-meta li a:hover {
    color: #00d690;
}

.wpo-blog-pg-section .entry-meta>li:first-child {
    position: relative;
}

.wpo-blog-pg-section .entry-meta>li:first-child img {
    display: inline-block;
    border-radius: 50%;
    margin-right: 8px;
}

.wpo-blog-pg-section .entry-meta>li:last-child {
    position: relative;
}

.wpo-blog-pg-section .entry-meta>li:last-child i {
    position: relative;
    top: 2px;
}

@media (max-width: 490px) {
    .wpo-blog-pg-section .entry-meta>li:last-child {
        margin-left: 0px;
        top: 0;
    }
}

.wpo-blog-pg-section .post h3 {
    font-size: 28px;
    font-size: 1.75rem;
    line-height: 1.2em;
    font-weight: 700;
    margin: -0.27em 0 0.7em;
}

@media (max-width: 991px) {
    .wpo-blog-pg-section .post h3 {
        font-size: 25px;
        font-size: 1.5625rem;
    }
}

@media (max-width: 767px) {
    .wpo-blog-pg-section .post h3 {
        font-size: 20px;
    }
}

.wpo-blog-pg-section .post h3 a {
    color: #242f6c;
}

.wpo-blog-pg-section .post h3 a:hover {
    color: #00d690;
}

.wpo-blog-pg-section .post p {
    margin-bottom: 1.5em;
    font-size: 18px;
}

@media (max-width: 991px) {
    .wpo-blog-pg-section .post p {
        font-size: 16px;
        font-size: 1rem;
    }
}

.wpo-blog-pg-section .post .read-more {
    font-weight: 700;
    background:#00d690;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-bottom: 2px;
    border-bottom: 1px solid #00d690;
}

.wpo-blog-pg-section .format-standard,
.wpo-blog-pg-section .format-quote {
    background-color: #f5f9fd;
    padding: 25px 35px 45px;
}

@media (max-width: 767px) {

    .wpo-blog-pg-section .format-standard,
    .wpo-blog-pg-section .format-quote {
        padding: 25px 20px 45px;
    }
}

.wpo-blog-pg-section .format-gallery {
    position: relative;
}

.wpo-blog-pg-section .format-gallery .owl-controls {
    width: 100%;
    margin: 0;
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.wpo-blog-pg-section .format-gallery .owl-controls .owl-nav [class*=owl-]:hover {
    background: #00d690;
    color: #fff;
}

.wpo-blog-pg-section .format-gallery .owl-controls .owl-nav [class*=owl-] {
    background: #fff;
    color: #666;
    width: 50px;
    height: 50px;
    line-height: 54px;
    padding: 0;
    margin: 0;
    border-radius: 5px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    -ms-transition: all 0.3s;
    transition: all 0.3s;
}

.wpo-blog-pg-section .format-gallery .owl-controls .owl-nav .owl-prev,
.wpo-blog-pg-section .format-gallery .owl-controls .owl-nav .owl-next {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.wpo-blog-pg-section .format-gallery .owl-controls .owl-nav .owl-prev {
    left: 15px;
}

.wpo-blog-pg-section .format-gallery .owl-controls .owl-nav .owl-next {
    right: 15px;
}

.wpo-blog-pg-section .format-quote {
    background: #fbfbfb;
    padding: 40px 60px;
    position: relative;
    padding-top: 15px;
}

@media (max-width: 767px) {
    .wpo-blog-pg-section .format-quote {
        padding: 0 20px;
        padding-bottom: 30px;
    }
}

.wpo-blog-pg-section .format-quote p {
    margin-bottom: 0;
}

.wpo-blog-pg-section .format-quote:before {
    font-family: "Flaticon";
    content: "\f11a";
    font-size: 150px;
    color: #f2f3f3;
    margin-left: 0;
    position: absolute;
    right: 0%;
    top: 30%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

@media(max-width: 767px) {
    .wpo-blog-pg-section .format-quote:before {
        font-size: 100px;
        top: 26%;
    }
}

.wpo-blog-pg-section .format-quote h3,
.wpo-blog-pg-section .format-quote p {
    position: relative;
}

.wpo-blog-pg-section .format-video .video-holder {
    position: relative;
    text-align: center;
}

.wpo-blog-pg-section .format-video .video-holder:after {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: #000;
    opacity: .25;
}

.wpo-blog-pg-section .format-video .video-holder a {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #fff;
    width: 83px;
    height: 83px;
    border-radius: 83px;
    display: inline-block;
    -webkit-animation: spineer 2s infinite;
    animation: spineer 2s infinite;
    z-index: 99;
}

@media (max-width: 991px) {
    .wpo-blog-pg-section .format-video .video-holder a {
        width: 65px;
        height: 65px;
        border-radius: 65px;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }
}

.wpo-blog-pg-section .format-video .video-holder a:before {
    content: "";
    width: 0px;
    height: 0px;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 20px solid #00d690;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

@media screen and (min-width: 1200px) {
    .wpo-blog-pg-left-sidebar .wpo-blog-sidebar {
        padding-right: 45px;
        padding-left: 0;
    }
}

@media screen and (min-width: 1200px) {
    .wpo-blog-pg-fullwidth .wpo-blog-content {
        padding: 0;
    }
}
.entry-bottom{
    display: flex;
    justify-content: space-between;
}
.entry-bottom ul{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;

}

.entry-bottom ul li .fi:before{
    font-size: 18px;
    margin-right: 10px;
    color: #9298a4;
}
.entry-bottom ul li:first-child .fi:before{
    color: #fd67d1;
}
.entry-bottom ul li{
    margin-right: 10px;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    color: #9298a4;
}
.post{
    position: relative;
}
.blog-thumb-badge{
    position: absolute;
    left: 15px;
    top: 15px;
    z-index: 99;
}
.blog-thumb-text span{
    display: inline-block;
    padding: 8px 25px;
    color: #fff;
    font-weight: 700;
    text-transform: uppercase;
    background: #00d690;
    border-radius: 5px;
    font-size: 14px;
}
.post-text-wrap{
    padding: 40px;
    background: #f5fbf9;
}
.post-slider{
    position: relative;
}
.post-bloquote p{
    margin-bottom: 10px!important;
    font-size: 22px!important;
    font-weight: 700;
    color: #283a5e;
    position: relative;
}
.post-bloquote span{
    color: #8f96a3;
}
.post-bloquote p:before{
    position: absolute;
    left: -15px;
    top: 0;
    width: 3px;
    height: 100%;
    content: '';
    background: #03bf81;
}
.post-bloquote{
    margin-bottom: 30px;
    padding-left: 60px;
    position: relative;
}
.post-bloquote:before {
    font-family: "Flaticon";
    content: "\f113";
    font-size: 50px;
    margin-left: 0;
    position: absolute;
    left: 0%;
    top: 30%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background:#00d690;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.post-text-wrap h5{
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 30px;
}
.post-text-wrap h5 .fi:before{
    background:#00d690;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.profile-img{
    padding: 40px;
}
.profile-img img{
    border-radius: 50%;
}
.profile-img h2{
    font-size: 25px;
    font-weight: 700;
}
.profile-widget{
    text-align: center;
    background: #f5fbf9;
    border: 1px solid #ebebeb;
}
.pro-social ul{
    display: flex;
    justify-content: center;
    padding: 20px 0;
    background: #eefbf7;
    border-top: 1px solid #ebebeb;
}
.pro-social ul li{
    margin: 0 10px;
}
.pro-social ul li a{
    color: #283a5e;
}
.wpo-blog-sidebar {
    padding-left: 30px;
}
@media(max-width: 767px){
    .post-bloquote p {
        font-size: 15px!important;
    }
    .post-text-wrap {
        padding: 20px;
    }

}
@media(max-width: 991px){
    .wpo-blog-sidebar {
        padding-left: 0px;
    }
}

.get-intouch{
    text-align: center;
    background: url(../images/blog/get.png) no-repeat center center;
    position: relative;
    z-index: 1;
    object-fit: cover;
    max-width: 345px;
}
.get-item{
    padding: 40px 30px;
}
.get-intouch:before{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: #000;
    opacity: .40;
    z-index: -1;
}
.get-intouch h2{
    font-size: 35px;
    line-height: 50px;
    color: #fff;
    margin-bottom: 40px;
    font-weight: 700;
}

@media(max-width: 1200px){
    .get-intouch h2 {
        font-size: 25px;
        line-height: 30px;
        margin-bottom: 20px;
    }
}
/*--------------------------------------------------------------
#10.0 wpo-blog-single-section
--------------------------------------------------------------*/
.wpo-blog-single-section {
    /*** tag-share ***/
    /*** author-box ***/
    /*** more-posts ***/
    /*** comments area ***/
    /*** comment-respond ***/
}

@media screen and (min-width: 1200px) {
    .wpo-blog-single-section .wpo-blog-content {
        padding-right: 0px;
    }
}

.wpo-blog-single-section .entry-meta {
    list-style: none;
    overflow: hidden;
    margin-bottom: 25px;
    padding-top: 35px;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .entry-meta {
        margin: 10px 0 25px;
    }
}

.wpo-blog-single-section .entry-meta li {
    font-size: 14px;
    font-size: 0.875rem;
    float: left;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .entry-meta li {
        font-size: 12px;
        font-size: 0.75rem;
    }
}

@media (max-width: 600px) {
    .wpo-blog-single-section .entry-meta li {
        display: block;
        margin-bottom: 5px;
    }
}

.wpo-blog-single-section .entry-meta li .fi:before {
    font-size: 16px;
    font-size: 1rem;
    display: inline-block;
    padding-right: 5px;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .entry-meta li .fi:before {
        font-size: 12px;
        font-size: 0.75rem;
    }
}

.wpo-blog-single-section .entry-meta>li+li {
    margin-left: 15px;
    padding-left: 15px;
    position: relative;
}

@media (max-width: 600px) {
    .wpo-blog-single-section .entry-meta>li+li {
        margin-left: 13px;
        padding-left: 9px;
    }
}

.wpo-blog-single-section .entry-meta li a {
    color: #525252;
    text-transform: uppercase;
}

.wpo-blog-single-section .entry-meta li a:hover {
    color: #00d690;
}

.wpo-blog-single-section .entry-meta>li:first-child {
    position: relative;
    top: 0px;
}

.wpo-blog-single-section .entry-meta>li:first-child img {
    display: inline-block;
    border-radius: 50%;
    padding-right: 8px;
}

.wpo-blog-single-section .entry-meta>li:last-child {
    position: relative;
}

.wpo-blog-single-section .entry-meta>li:last-child i {
    position: relative;
    top: 2px;
}

@media (max-width: 600px) {
    .wpo-blog-single-section .entry-meta>li:last-child {
        margin-left: 0px;
    }
}

.wpo-blog-single-section .post h2 {
    font-size: 30px;
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0 0 0.5em;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .post h2 {
        font-size: 25px;
        font-size: 1.5625rem;
    }
}

@media (max-width: 767px) {
    .wpo-blog-single-section .post h2 {
        font-size: 22px;
        font-size: 1.375rem;
    }
}

.wpo-blog-single-section .post p {
    margin-bottom: 1.5em;
}

.wpo-blog-single-section .post h3 {
    font-size: 22px;
    font-size: 1.375rem;
    line-height: 1.5em;
    margin: 1.8em 0 1em;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .post h3 {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

@media (max-width: 767px) {
    .wpo-blog-single-section .post h3 {
        font-size: 18px;
        font-size: 1.125rem;
    }
}

.wpo-blog-single-section .post blockquote {
    background-color: #f8f8f8;
    padding: 50px 30px 50px 95px;
    margin: 60px 0;
    border: 0;
    line-height: 1.9em;
    position: relative;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .post blockquote {
        font-size: 15px;
        font-size: 0.9375rem;
    }
}

.wpo-blog-single-section .post blockquote:before {
    font-family: "Flaticon";
    content: "\f113";
    background:#00d690;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 35px;
    position: absolute;
    left: 30px;
    top: 40px;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .post blockquote:before {
        display: none;
    }
}

@media (max-width: 991px) {
    .wpo-blog-single-section .post blockquote {
        padding: 30px 40px;
    }
}

@media (max-width: 767px) {
    .wpo-blog-single-section .post blockquote {
        padding: 20px 30px;
    }
}

.wpo-blog-single-section .post blockquote .quoter {
    display: block;
    margin-top: 15px;
}

.wpo-blog-single-section .tag-share {
    margin: 70px 0;
}

.wpo-blog-single-section .tag-share ul {
    list-style: none;
}

@media screen and (min-width: 1200px) {
    .wpo-blog-single-section .tag-share {
        -webkit-box-shadow: 0px 4px 14.1px 0.9px rgba(0, 0, 0, 0.08);
        box-shadow: 0px 4px 14.1px 0.9px rgba(0, 0, 0, 0.08);
        padding: 35px 25px;
    }
}

@media (max-width: 767px) {
    .wpo-blog-single-section .tag-share {
        margin: 60px 0;
    }
}

.wpo-blog-single-section .tag-share .tag {
    display: inline-block;
    float: left;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .tag-share .tag {
        float: none;
        display: block;
        margin-bottom: 25px;
    }
}

.wpo-blog-single-section .tag-share .share {
    display: inline-block;
    float: right;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .tag-share .share {
        float: none;
        display: block;
    }
}

.wpo-blog-single-section .tag-share ul {
    overflow: hidden;
}

.wpo-blog-single-section .tag-share ul li {
    float: left;
}

.wpo-blog-single-section .tag-share .tag ul>li+li,
.wpo-blog-single-section .tag-share .share ul>li+li {
    margin-left: 10px;
}

.wpo-blog-single-section .tag-share .tag a {
    background-color: #f8f8f8;
    display: block;
    padding: 9px 18px;
    color: #525252;
    border-radius: 50px;
}

.wpo-blog-single-section .tag-share .tag a:hover {
    background-color: #00d690;
    color: #fff;
}

.wpo-blog-single-section .tag-share .share a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-size: 0.875rem;
    text-align: center;
    color: #525252;
    border-radius: 50%;
    padding: 0;
    display: block;
    border: 1px solid #dadada;
}

.wpo-blog-single-section .tag-share .share a:hover {
    background: #00d690;
    color: #fff;
    border-color: #00d690;
}

.wpo-blog-single-section .author-box {
    padding: 45px 50px 35px;
    margin: 70px 0;
    border: 1px solid #e1e1e1;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .author-box {
        padding: 35px 40px;
    }
}

@media (max-width: 767px) {
    .wpo-blog-single-section .author-box {
        padding: 25px;
    }
}

.wpo-blog-single-section .author-box .author-avatar {
    float: left;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .author-box .author-avatar {
        float: none;
    }
}

.wpo-blog-single-section .author-box .author-avatar img {
    border-radius: 50%;
}

.wpo-blog-single-section .author-box .author-content {
    display: block;
    overflow: hidden;
    padding-left: 25px;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .author-box .author-content {
        padding: 0;
        margin: 15px 0 0 0;
    }
}

.wpo-blog-single-section .author-box .author-content p {
    margin-bottom: 20px;
}

.wpo-blog-single-section .author-box .author-name {
    font-size: 20px;
    display: inline-block;
    margin-bottom: 10px;
    color: #242f6c;
    font-weight: 600;
    font-family: 'Red Hat Display', sans-serif;
}

.wpo-blog-single-section .author-box .social-link {
    display: inline-block;
    list-style: none;
}

.wpo-blog-single-section .author-box .social-link li {
    float: left;
    margin-right: 15px;
}

.wpo-blog-single-section .author-box .social-link a {
    display: block;
    font-size: 15px;
    font-size: 0.9375rem;
    color: #242f6c;
}

.wpo-blog-single-section .author-box .social-link a:hover {
    color: #00d690;
}

.wpo-blog-single-section .more-posts {
    margin: 70px 0;
}

.wpo-blog-single-section .more-posts .previous-post {
    display: inline-block;
    float: left;
}

.wpo-blog-single-section .more-posts .next-post {
    display: inline-block;
    float: right;
}

.wpo-blog-single-section .more-posts .previous-post a,
.wpo-blog-single-section .more-posts .next-post a {
    background-color: #f8f8f8;
    font-weight: 600;
    color: #525252;
    display: block;
    padding: 15px 40px;
    border-radius: 60px;
}

@media (max-width: 767px) {

    .wpo-blog-single-section .more-posts .previous-post a,
    .wpo-blog-single-section .more-posts .next-post a {
        padding: 12px 35px;
    }
}

.wpo-blog-single-section .more-posts .previous-post a:hover,
.wpo-blog-single-section .more-posts .next-post a:hover {
    background-color: #00d690;
    color: #fff;
}

.wpo-blog-single-section .comments-area {
    margin-top: 70px;
}

.wpo-blog-single-section .comments-area li>div {
    background: #f8f8f8;
    padding: 30px;
    margin-bottom: 20px;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .comments-area li>div {
        padding: 30px 25px;
    }
}

.wpo-blog-single-section .comments-area ol {
    list-style-type: none;
    padding-left: 0;
}

.wpo-blog-single-section .comments-area ol ul {
    padding-left: 30px;
    list-style: none;
}

.wpo-blog-single-section .comments-area ol>li:last-child div {
    border-bottom: 0;
}

.wpo-blog-single-section .comments-area .comments-title {
    font-size: 30px;
    font-weight: 600;
    margin: 0 0 1.5em;
    color: #242f6c;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .comments-area .comments-title {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

.wpo-blog-single-section .comments-area li>div {
    position: relative;
}

.wpo-blog-single-section .comments-area .comment-theme {
    position: absolute;
    left: 35px;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .comments-area .comment-theme {
        position: static;
    }
}

.wpo-blog-single-section .comments-area .comment-theme img {
    border-radius: 50%;
}

.wpo-blog-single-section .comments-area .comment-main-area {
    padding-left: 100px;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .comments-area .comment-main-area {
        padding-left: 0;
        margin-top: 25px;
    }
}

.wpo-blog-single-section .comments-area .comment-main-area p {
    margin-bottom: 0px;
}

.wpo-blog-single-section .comments-area .comments-meta h4 {
    font-size: 16px;
    font-size: 1rem;
    font-weight: 700;
    margin: 0 0 1em;
}

.wpo-blog-single-section .comments-area .comments-meta h4 span {
    font-weight: normal;
    text-transform: none;
    display: block;
    font-size: 14px;
    font-size: 0.875rem;
    color: #666;
    margin-top: 8px;
    font-weight: 400;
}

.wpo-blog-single-section .comments-area .comment-reply-link {
    font-size: 16px;
    color: #00d690;
    display: inline-block;
    border-radius: 50px;
    display: inline-block;
    position: absolute;
    top: 20px;
    right: 30px;
    font-weight: 700;
}

.wpo-blog-single-section .comments-area .comment-reply-link i {
    margin-right: 5px;
}

.wpo-blog-single-section .comment-respond {
    margin-top: 70px;
}

.wpo-blog-single-section .comment-respond .comment-reply-title {
    font-size: 30px;
    font-weight: 600;
    margin: 0 0 1.5em;
    color: #283a5e;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .comment-respond .comment-reply-title {
        font-size: 20px;
        font-size: 1.25rem;
    }
}

.wpo-blog-single-section .comment-respond form input,
.wpo-blog-single-section .comment-respond form textarea {
    background-color: #f8f8f8;
    width: 100%;
    height: 50px;
    border: 1px solid #f8f8f8;
    padding: 6px 15px;
    margin-bottom: 15px;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    -ms-transition: all 0.3s;
    transition: all 0.3s;
}

.wpo-blog-single-section .comment-respond form input:focus,
.wpo-blog-single-section .comment-respond form textarea:focus {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-color: #00d690;
}

@media (max-width: 991px) {

    .wpo-blog-single-section .comment-respond form input,
    .wpo-blog-single-section .comment-respond form textarea {
        height: 40px;
    }
}

.wpo-blog-single-section .comment-respond form textarea {
    height: 220px;
    padding: 15px;
}

@media (max-width: 991px) {
    .wpo-blog-single-section .comment-respond form textarea {
        height: 150px;
    }
}

.wpo-blog-single-section .comment-respond .form-inputs {
    overflow: hidden;
}

.wpo-blog-single-section .comment-respond .form-inputs>input:nth-child(1) {
    width: 49%;
    float: left;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .comment-respond .form-inputs>input:nth-child(1) {
        width: 100%;
        float: none;
    }
}

.wpo-blog-single-section .comment-respond .form-inputs>input:nth-child(2) {
    width: 49%;
    float: right;
}

@media (max-width: 767px) {
    .wpo-blog-single-section .comment-respond .form-inputs>input:nth-child(2) {
        width: 100%;
        float: none;
    }
}

.wpo-blog-single-section .comment-respond .form-submit input {
    max-width: 180px;
    background: #00d690;
    color: #fff;
    font-weight: 500;
    margin-bottom: 0;
    border: 0;
    outline: 0;
}

@media screen and (min-width: 1200px) {
    .wpo-blog-single-left-sidebar-section .wpo-blog-content {
        padding-right: 0;
        padding-left: 50px;
    }
}

@media screen and (min-width: 1200px) {
    .wpo-blog-single-fullwidth .wpo-blog-content {
        padding: 0;
    }
}

.entry-media img {
    width: 100%;
}

.author-btn a {
    padding: 15px 35px;
    background: #f8f8f8;
    display: inline-block;
    border-radius: 40px;
    color: #555555;
}

.author-btn a:hover {
    background-color: #00d690;
    color: #fff;
}

.wpo-blog-pg-section .format-video .video-holder a {
    background-color: #fff;
    -webkit-animation: spineer 2s infinite;
    animation: spineer 2s infinite;
}

.wpo-blog-pg-section .format-video .video-holder a:before {
    border-left: 20px solid #111;
}
.wpo-blog-sidebar-2{
    margin-top: 0px;
    margin-bottom: 60px;
}
.wpo-blog-sidebar blockquote{
    background-color: #f8f8f8;
    padding: 50px 30px 50px 95px;
    margin: 60px 0 30px;
    border: 0;
    line-height: 1.9em;
    position: relative;
}
.wpo-blog-sidebar blockquote:before {
    font-family: "Flaticon";
    content: "\f119";
    font-size: 35px;
    position: absolute;
    left: 30px;
    top: 40px;
}
.quote-img img{
    border-radius: 50%;
}
.quote-img{
    position: relative;
    width: 96px;
    height: 96px;
    float: left;
    margin-right: 35px;
}
.quote-img:before{
    position: absolute;
    left: -9px;
    top: -9px;
    width: 120%;
    height: 120%;
    border:1px solid #d7d7d7;
    content: "";
    border-radius: 50%;
}
.quote-text{
    overflow: hidden;
}
.quote-text h2{
    font-size: 16px;
    color: #242f6c;
}
.quote-text span{
    font-size: 15px;
    color: #666666;
}
.quote-wrap {
    overflow: hidden;
    padding: 20px 10px 10px;
}

.blog-pg-left-sidebar .wpo-blog-sidebar{
    padding-left: 0;
    padding-right: 30px;
}
@media(max-width: 430px){
    .wpo-blog-single-section .comments-area ol ul {
        padding-left: 0;
        list-style: none;
    }
    .wpo-blog-sidebar blockquote {
        padding: 50px 30px 24px 70px;
        margin: 60px 0 30px;
    }
    .quote-img {
        float: none;
        margin-bottom: 20px;
    }
    .wpo-blog-single-section .tag-share .tag a {
        padding: 9px 16px;
    }
}




@-webkit-keyframes spineer {
    0% {
        -webkit-box-shadow: 0 0 0 0 #c7c1ea;
    }

    70% {
        -webkit-box-shadow: 0 0 0 20px rgba(1, 93, 199, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(1, 93, 199, 0);
    }
}

@keyframes spineer {
    0% {
        -webkit-box-shadow: 0 0 0 0 #c7c1ea;
        box-shadow: 0 0 0 0 #c7c1ea;
    }

    70% {
        -webkit-box-shadow: 0 0 0 20px rgba(1, 93, 199, 0);
        box-shadow: 0 0 0 20px rgba(1, 93, 199, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(1, 93, 199, 0);
        box-shadow: 0 0 0 0 rgba(1, 93, 199, 0);
    }
}


/*--------------------------------------------------------------
#11.0 error-404-section
--------------------------------------------------------------*/
.error-404-section {
    text-align: center;
}

.error-404-section .error-message {
    margin-top: 70px;
    padding: 0 200px;
}

@media (max-width: 991px) {
    .error-404-section .error-message {
        margin-top: 50px;
        padding: 0 100px;
    }
}

@media (max-width: 767px) {
    .error-404-section .error-message {
        padding: 0;
    }
}

.error-404-section .error-message h3 {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 0.8em;
    color: #062856;
}

.error-404-section .error-message p {
    margin-bottom: 1.8em;
}

.error-message .theme-btn-s4 {
    background-color: #08cc7f;
    color: #fff;
}



/*----------------------------------------------
end*
-----------------------------------------------*/



.site-header #navbar > ul .sub-menu{ width: 150px; }


.tab-pane ul li{width: 200px;}


.site-header #navbar > ul > li > .sub-menu .sub-menu li {
    padding-top:5px;
    padding-bottom: 5px;
    padding-left:20px;




}









