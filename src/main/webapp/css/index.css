.clearfix:before, .clearfix:after {
	display: table;
	content: "";
	line-height: 0;
}
.clearfix:after {
	clear: both;
}
html {
	font-size: 100%;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}
a:hover, a:active {
	outline: 0;
}
img {
	/* Responsive images (ensure images don't scale beyond their parents) */
	max-width: 100%;
	/* Part 1: Set a maxium relative to the parent */
	width: auto\9;
	/* IE7-8 need help adjusting responsive images */
	/* height: auto; */
	/* Part 2: Scale the height according to the width, otherwise you get stretching */
	vertical-align: middle;
	border: 0;
	-ms-interpolation-mode: bicubic;
}
html, body {
	height: auto;
}
body {
	margin: 0;
	font-family: PingFang-medium, Microsoft Yahei, 'tahoma, arial,"Hiragino Sans GB", \5b8b\4f53, sans-serif';
	font-size: 12px;
	line-height: 18px;
	color: #595959;
	background-color: #f6f6f6;
}
a {
	color: #0088FF;
	text-decoration: none;
}
h1, h2, h3, h4, h5, h6 {
	font-weight: 400
}
body, button, dd, div, dl, dt, form, h1, h2, h3, h4, h5, h6, input, li, ol, p, pre, td, textarea, th, ul {
	margin: 0;
	padding: 0;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}
li {
	list-style: none
}
table {
	border-collapse: collapse;
	border-spacing: 0
}
button, input, optgroup, option, select, textarea {
	font-family: inherit;
	font-size: inherit;
	font-style: inherit;
	font-weight: inherit;
	outline: 0
}
em, i {
	font-style: normal;
}
/* top */
.gf-top {
	height: 80px;
	background-color: #ffffff;
	display: -webkit-flex;
	display: flex;
	align-items: center;
}
.gf-top .logo {
	margin-left: 30px;
}
.gf-top .logo img {
	height: 34px;
}
.gf-top .top-right {
	margin-left: auto;
	display: -webkit-flex;
	display: flex;
	align-items: center;
}
.gf-top .top-nav {
	height: 30px;
	line-height: 30px;
}
.gf-top .top-nav a {
	font-size: 18px;
	color: #262a47;
	margin-right: 50px;
	display: inline-block;
}
.gf-top .top-nav a i {
	margin-right: 10px;
	font-size: 18px;
}
.gf-top .top-searchb {
	position: relative;
}
.gf-top .top-searchb input {
	border: none;
	width: 270px;
	height: 42px;
	background-color: #eef2fb;
	border-radius: 8px;
	padding: 0 50px 0 20px;
	box-sizing: border-box;
}
.gf-top .top-login-info {
	margin-left: 50px;
	margin-right: 30px;
	height: 30px;
	line-height: 30px;
	display: -webkit-flex;
	display: flex;
}
.gf-top .top-login-info p {
	font-size: 18px;
	color: #262a47;
}
.gf-top .top-login-info a i {
	margin-left: 20px;
	color: #262a47;
	font-weight: 600;
	font-size: 18px;
}
.flex {
	display: -webkit-flex;
	display: flex;
}
.flex1 {
	flex: 1;
}
.flex-col {
	flex-direction: column;
}
.flex-center {
	align-items: center;
}
.gf-container {
	background-color: #e1e9f6;
}
/* 左侧菜单 */
.gf-leftnav {
	width: 300px;
	background: #2d353c;
	transition: all .3s;
}
.gf-leftnav a {
	display: block;
	overflow: hidden;
	padding-left: 30px;
	line-height: 70px;
	height: 70px;
	color: #ABB1B7;
	transition: all .3s;
	font-size: 20px;
	color: #ffffff;
}
.gf-leftnav a span {
	margin-left: 20px;
}
.gf-leftnav a i {
	font-size: 18px;
}
.nav-item {
	position: relative;
}
.nav-item ul {
	display: none;
	background: #20282e;
}
.nav-item ul li a {
	padding-left: 48px;
}
.nav-item.nav-show ul {
	display: block;
}
.nav-show, .nav-item>a:hover, .nav-item>a.active {
	color: #FFF;
	background: #02499f;
}
.nav-item li:hover a {
	color: #FFF;
	background: #02499f;
}
/* 右侧内容 */
.gf-right-content {
	margin: 0 30px;
}
.gf-top-box {
	margin: 18px 0;
	height: 30px;
}
.gf-fl-scroll {
	flex: 1;
}
.gf-scroll-text-title {
	float: left;
	font-size: 18px;
	color: #262a47;
	font-family: zzgffh;
	line-height: 30px;
	margin-right: 5px;
}
.gf-scroll-text-title span {
	color: #ff0000;
}
.gf-scroll {
	width: 70%;
	height: 30px;
	overflow: hidden;
	background-color: #dce3f4;
	border-radius: 20px;
	display: flex;
	align-items: center;
	padding: 0 15px;
}
.gf-scroll-text-list {
	flex: 1;
	height: 30px;
	overflow: hidden;
	position: relative;
	margin-right: 100px;
}
.gf-scroll-text-list .gf-scroll-text {
	width: 2300px;
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 20;
}
.gf-scroll-text-list .gf-scroll-text li {
	float: left;
	margin-right: 100px;
	line-height: 30px;
}
.gf-scroll-text-list .gf-scroll-text li a {
	font-size: 16px;
	color: #262a47;
	line-height: 30px;
}
.gf-scroll-more {
	margin-left: auto;
	font-size: 16px;
	color: #7b8696;
	line-height: 30px;
}
.gf-weather {
	margin-right: 25px;
	margin-left: auto;
}
.gf-weather span {
	font-size: 18px;
	color: #262a47;
	line-height: 30px;
	margin-left: 40px;
}
.gf-weather span i {
	color: #f9c626;
	margin-right: 5px;
	font-size: 22px;
}
.gf-panel {
	background-color: #ffffff;
	border-radius: 10px;
	padding: 25px;
	box-sizing: border-box;
	margin-bottom: 18px;
}
.gf-subsidiary, .w1 {
	width: 508px;
}
.mar-right-18 {
	margin-right: 18px;
}
.gf-subsidiary-title {
	position: relative;
	height: 30px;
	text-align: center;
	margin-bottom: 20px;
}
.gf-subsidiary-title span:after, .gf-subsidiary-title span:before {
	content: '';
	width: 53px;
	height: 1px;
	background-color: #02499f;
	position: absolute;
	top: 14.5px;
}
.gf-subsidiary-title span:after {
	right: -73px;
}
.gf-subsidiary-title span:before {
	left: -73px;
}
.gf-subsidiary-title span {
	display: inline-block;
	font-size: 22px;
	color: #02499f;
	line-height: 30px;
	z-index: 10;
	background-color: #ffffff;
	position: relative;
	font-family: zzgffh;
}
.gf-activity {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	overflow: hidden;
	margin-bottom: 0px;
	margin-top: 10px;
}
.gf-activity li {
	width: 49%;
	height:96px;
	box-sizing: border-box;
	border: 1px solid #dce3f4;
	border-radius: 6px;
	margin-bottom: 8px;
}
.gf-activity li a {
	display: inline-block;
	width: 100%;
	height: 100%;
	align-items: center;
	justify-content: left;
	font-size:14px;
	color: #262a47;
	text-align:center;
	padding-top:10px;
}
.gf-activity li span {
	display: inline-block;
	width:30px;
	height: 30px;
	text-align: center;
	line-height:30px;
	margin-right: 8px;
	border-radius: 10px;
}
.gf-activity li span i {
	font-size: 24px;
}
.activitynum{    font-size:18px; margin-top:5px; color: #1758a8;}
/*.gf-activity li span.icon-1 {
	background-color: #f5f3ff;
}
.gf-activity li span.icon-2 {
	background-color: #ebfff3;
}
.gf-activity li span.icon-3 {
	background-color: #fff7eb;
}
.gf-activity li span.icon-4 {
	background-color: #fff0f0;
}*/
.gf-activity li span.icon-1 i {
	color: #02499f;
}
.gf-activity li span.icon-2 i {
	color: #2bb860;
}
.gf-activity li span.icon-3 i {
	color: #ffa735;
}
.gf-activity li span.icon-4 i {
	color: #ff5b3d;
}
.gf-backlog-title {
	margin-bottom: 20px;
	background-image: url(../img/gf_backlog_title.png);
	background-repeat: no-repeat;
	background-position: left center;
	padding-left: 15px;
}
.gf-backlog-title .title {
	font-size: 18px;
	color: #262a47;
	line-height: 30px;
}
.fr {
	float: right;
}
.gf-backlog-title a {
	font-size: 16px;
	color: #262a47;
}
.gf-backlog-title a i {
	margin-left: 5px;
}
.gf-backlog-list {
	margin-bottom: -10px;
	overflow: hidden;
	width: 48%;
	float: left;
	margin-right: 0px;
}
.gf-backlog-list li {
	display: flex;
	align-items: center;
	padding: 12px;
	height: 22px;
	line-height: 22px;
	background: url(../img/backlog.png) no-repeat #f6f6f6;
	border-radius: 5px;
	margin-bottom: 10px;
}
.gf-backlog-list li .backlog-text {
	flex: 1;
	margin-left: 0px;
	display: flex;
	align-items: center;
}
.backlog-text .dot {
	display: inline-block;
	width: 10px;
	height: 10px;
	border-radius: 100%;
	margin-right: 15px;
	border: 4px solid #ffffff;
}
.backlog-text .dot.darkblue {
	background-color: #02499f;
}
.backlog-text .dot.warning {
	background-color: #02499f;
}
.backlog-text .code, .backlog-text .title {
	font-size: 14px;
	color: #262a47
}
.backlog-text .code {
	margin-left: 25px;
}
.backlog-btn {
	margin-left: auto;
}
.backlog-btn a {
	display: inline-block;
	font-size: 18px;
	color: #4e74bb;
	line-height: 30px;
	box-sizing: border-box;
	border: 1px solid #4e74bb;
	border-radius: 20px;
	margin-right: 20px;
	padding: 0 18px;
	background-color: #edf3ff;
}
.backlog-number {
	color: #f0534e;
	font-size: 16px;
}
.gf-achievement ul {
	overflow: hidden;
	margin-bottom: -20px;
}
.gf-achievement ul li {
	display: flex;
	align-items: center;
	margin-bottom:20px;
}
.gf-achievement li .achievement {
	font-size: 14px;
	color: #e7a601;
	display: flex;
	align-items: center;
	flex: 1;
}
.gf-achievement li .achievement.top1 {
	color: #e7a601;
}
.gf-achievement li .achievement.top2 {
	color: #64a851;
}
.gf-achievement li .achievement img {
	margin-right: 15px;
}
.gf-achievement li p {
	width: 260px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 13px;
	color: #262a47;
	text-align: left;
}
.gf-tabs-nav {
	box-sizing: border-box;
	border-bottom: 1px solid #dddddd;
	height: 40px;
}
.gf-tabs-nav ul {
	overflow: hidden;
	display: inline-block;
}
.gf-tabs-nav ul li {
	float: left;
	height: 40px;
	margin-right: 50px;
	font-size: 20px;
	color: #262a47;
	line-height: 22px;
	padding-bottom: 18px;
	cursor: pointer;
	border-bottom: 4px solid transparent;
	box-sizing: border-box;
	padding: 0 5px;
}
.gf-tabs-nav ul li.active {
	border-bottom-color: #02499f;
}
.gf-tabs-subnav {
	margin: 0;
}
.gf-tabs-subnav ul {
	border: 1px solid #02499f;
	border-radius: 4px;
	height: 40px;
	box-sizing: border-box;
}
.gf-tabs-subnav ul li {
	float: left;
	line-height: 38px;
	text-align: center;
	font-size: 12px;
	color: #02499f;
	padding: 0 2px;
	min-width: 70px;
	box-sizing: border-box;
	cursor: pointer;
}
.gf-tabs-subnav ul li + li {
	border-left: 1px solid #02499f;
}
.gf-tabs-subnav ul li.active {
	background-color: #02499f;
	color: #ffffff;
}
.text-center {
	text-align: center;
}
.mar-top-25 {
	margin-top: 25px;
}
.hide {
	display: none;
}
.gf-view-all {
	font-size: 13px;
	color: #262a47;
	line-height: 22px;
}
.gf-view-all i {
	margin-left: 5px;
}
.gf-news-list {
	margin-top: 25px;
	overflow: hidden;
	margin-bottom: -25px;
}
.gf-news-list li {
	margin-bottom: 25px;
	min-height: 95px;
}
.gf-news-list li .img {
	float: left;
	width: 144px;
}
.gf-news-list li .img img {
	width: 144px;
	height: auto;
}
.gf-news-list li .news-info {
	margin-left: 154px;
}
.gf-news-list li .news-info p {
	font-size: 16px;
	color: #262a47;
	line-height: 22px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
}
.gf-news-list li .news-date {
	text-align: right;
	font-size: 16px;
	color: #999999;
	line-height: 22px;
}
.gf-title {
	height: 40px;
	box-sizing: border-box;
}
.gf-title span {
	display: inline-block;
	font-size: 20px;
	color: #262a47;
	line-height: 22px;
	padding-bottom: 18px;
	cursor: pointer;
	box-sizing: border-box;
	padding: 0 5px;
	background-image: url(../img/gf_backlog_title.png);
	background-repeat: no-repeat;
	background-position: left center;
	padding-left: 15px;
}
.gf-notice-list {
	margin-top: 0px;
}
.gf-notice-list ul li {
	overflow: hidden;
	height: 26px;
	line-height: 26px;
	font-size: 13px;
	margin-bottom: 10px;
	cursor: pointer;
}
.gf-notice-list ul li i {
	float: left;
	transform: rotate(-45deg);
	margin-right: 5px;
}
.gf-notice-list ul li a {
	float: left;
	width: 300px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #262a47;
}
.gf-notice-list ul li span {
	float: right;
	color: #999999;
}
.gf-notice-list ul li a strong {
	margin-right: 20px;
	font-weight: normal;
}
.gf-notice-list ul li i {
	font-size: 12px;
}
.cursorLi p {
	width: 70%;
	float: left;
}
.prompt-access {
	margin: 0px;
	padding: 0px;
	font-size: 13px;
	margin-top: 10px;
}
.prompt-access li {
	margin: 0px;
	padding: 0px;
	float: left;
	font-size: 13px;
	width: 140px;
	display: block;
	height:110px;
	margin-top: 10px;
	margin-left:0px;
	text-align: center;



}
.prompt-access a {
	font-size: 13px;
}
.card-primar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	overflow: hidden;
	margin-bottom: 0px;
	margin-top: 10px;
}
.card-body {
	background-color: #68a756;
	width:49%;
	height: 86px;
	box-sizing: border-box;
	border-radius: 6px;
	margin-bottom:8px;
	color: #FFF;
}
.text-white {
	font-size: 14px;
	margin-top: 15px;
	margin-left: 10px;
}
.mb-0 span {
	font-size: 24px;
	margin-left: 10px;
	font-weight: bold;
}
.mb-0 {
	margin-top: 10px;
}
.cardblue.card-body {
	background-color: #1758a8;
}
.gf-banner {
	background-color: #3a7d76;
	height: 35px;
	margin-bottom: 10px;
	border-radius: 10px
}
.gf-l {
	background-image: url(../img/bannerl.png);
	width: 543px;
	height: 34px;
	float: left;
}
.gf-r {
	background-image: url(../img/bannerr.png);
	width: 194px;
	height: 34px;
	float: right;
}
.mar-right-20 {
	margin-right: 20px;
}
.backlog-text i {
	color: #FFF;
	font-size: 22px;
}
.inlblo {
	display: inline-block;
	diplay: inline;
	zoom: 1;
}
.section-focus-pic {
	margin: 0px;
	width: 508px;
	height: 278px;
	overflow: hidden;
	position: relative;
	border-radius: 10px;
}
.section-focus-pic ul li {
	position: absolute;
	top: 0;
	left: 508px;
	width: 508px;
	height: 278px;
}
.section-focus-pic ul li h3 {
	position: absolute;
	bottom: 11px;
	left: 0;
	width: 508px;
	font-weight: normal;
	color: #FFF;
	margin-left: 20px;
	z-index: 111;
	font-size: 18px;
	font-family: "微软雅黑";
}
.section-focus-pic ul li div {
	position: absolute;
	bottom: 0;
	left: 0;
	height: 50px;
	background-color: #000;
	width: 508px;
	opacity: 0.6;
	filter: alpha(opacity=60);
}
.section-focus-pic .pages {
	width: 508px;
	height: 366px;
}
.section-focus-pic .controler {
	position: absolute;
	bottom: 20px;
	left: 220px;
}
.section-focus-pic .controler b {
	cursor: pointer;
	width: 20px;
	height: 10px;
	display: block;
	overflow: hidden;
	text-indent: 20px;
	background: url(../img/news_index_icon.png) no-repeat -136px 3px;
	float: left;
	margin: 0 0 0 5px;
}
.section-focus-pic .controler b.down {
	background-position: -161px 3px;
}
.section-focus-pic .controler2 a {
	display: block;
	width: 44px;
	position: absolute;
	top: 120px;
	height: 44px;
}
.section-focus-pic .controler2 a i {
	cursor: pointer;
	display: block;
	width: 44px;
	height: 44px;
	background: url(../img/star.png) no-repeat;
}
.section-focus-pic .controler2 .prev {
	left: 5px;
}
.section-focus-pic .controler2 .prev i {
	background-position: 0px -55px;
	_background: none;
	margin-left: 5px;
}
.section-focus-pic .controler2 .prev:hover i {
	left: 10px;
	background-position: 0px -7px;
	_background: none;
}
.section-focus-pic .controler2 .next {
	right: 10px;
}
.section-focus-pic .controler2 .next i {
	background-position: -45px -55px;
	_background: none;
	margin-left: 0px;
}
.section-focus-pic .controler2 .next:hover i {
	right: 10px;
	background-position: -45px -7px;
	_background: none;
}
.pages ul {
	margin: 0px;
	padding: 0px;
}
.pages ul .item a img {
	width: 508px;
	height: 278px;
}
.db-box {
	height: 278px;
}
.cloud-banner {
	background: url(../img/cloudbannebg.jpg) right #0e1f4d;
	height: 90px;
	background-repeat: no-repeat;
	margin-bottom: 10px;
	border-radius: 10px;
}
.cloud-banner-tit {
	width: 778px;
	height: 40px;
	margin: auto;
	padding-top: 10px;
}
.cloud-list{ margin-left:10px; margin-right:10px; }
.cloud-list ul{display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	overflow: hidden;
	margin-bottom: 0px;
}
.cloud-list ul li{   margin:0px;background-color: #132c62; border:#7fb8fc 1px solid;
	width: 23%;
	height:33px;
	box-sizing: border-box;
	border-radius: 6px;
	color: #FFF; font-size:14px; text-align:center; font-size:16px; line-height:33px ; box-shadow: 0 2px 10px 0 rgb(127 184 252 / 15%);}
.cloud-list ul li a{ color:#FFF; }
.cloud-list ul li  span{ margin-right:10px; color:#7fb8fc }
.prompt-tit{ margin-top:5px;}
/**********************滚动图片end********************/
/* 苹方字体引入 */
/* 中 */
@font-face {
	font-family: 'PingFang-medium';
	src: url(../fonts/PingFang\ Medium_0.ttf);
}
/* 细 */
@font-face {
	font-family: 'PingFang SC';
	src: url(../fonts/PingFang\ Light_0.ttf);
}
/* 造字工房方黑字体引入 */
@font-face {
	font-family: 'zzgffh';
	src: url(../fonts/ZaoZiGongFangFangHei.ttf);
}
/*新加*/
