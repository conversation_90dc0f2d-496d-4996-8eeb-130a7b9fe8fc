.fn-clear:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}
.fn-clear {
  zoom: 1;
}
.fr {
  float: right;
}
.mt10 {
  margin-top: 10px;
}
.txt-impt {
  color: red;
  margin-right: 2px;
  font-size: 14px;
  line-height: 14px;
}
.uesr-photo {
  height: 170px;
  width: 150px;
  float: right;
}
.uesr-photo img {
  height: 170px;
  width: 150px;
  border: #ddd 1px solid;
}
.fl {
  float: left;
}
.center {
  text-align: center;
}
.select-list li span {
  /* float: left; */
  /* margin-top: 5px; */
  /* margin-right: 6px; */
}
.select-list li input[type=radio] {
  height: 15px;
  width: 15px;
  margin-top: 2px;
  margin-right: 0px;
}
.row .toolbar-bottom {
  z-index: 700;
  bottom: 0px;
  left: 0;
  width: 100%;
  position: fixed;
  text-align: right;
  background: #fff;
  box-shadow: 0 -2px 6px 1px hsl(223deg 8% 83%/ 50%);
  border-top: 1px solid #e3e4e8;
}
.row .toolbar > .btn {
  display: inline-block;
  text-decoration: none;
  text-align: center;
  text-transform: none;
  vertical-align: middle;
  user-select: none;
  margin-left: 0.2rem;
  margin-right: 0.2rem;
  cursor: pointer;
}
.toolbar-bottom {
  z-index: 700;
  bottom: 0px;
  left: 0;
  width: 100%;
  position: fixed;
  text-align: right;
  background: #fff;
  box-shadow: 0 -2px 6px 1px hsl(223deg 8% 83%/ 50%);
  border-top: 1px solid #e3e4e8;
  padding-right: 80px;
}
.toolbar {
  padding: 0.8rem;
  padding-right: 80px;
}
.toolbar > .btn {
  display: inline-block;
  text-decoration: none;
  text-align: center;
  text-transform: none;
  vertical-align: middle;
  user-select: none;
  margin-left: 0.2rem;
  margin-right: 0.2rem;
  cursor: pointer;
}
.sw-theme-arrows {
  border: 0px solid #eee;
}
.form-horizontal .control-label {
  color: #000;
}
.p-title {
  color: #000;
}
.table-striped table thead {
  background-color: #e5efff;
}
.panel-default > .panel-heading {
  color: #333;
  border-top: #1c6ac7 solid 4px;
  background-color: #dfebfe;
}
.panel-heading {
  padding: 8px 15px;
}
.form-group {
  margin-bottom: 5px;
}
.layui-upload .btn {
  font-size: 12px;
}
.sw-theme-arrows > .nav .nav-link.active {
  color: #fff;
  border-color: #1c6ac7;
  background: #1c6ac7;
  cursor: pointer;
}
.sw-theme-arrows > .nav .nav-link.active::after {
  border-left-color: #1c6ac7;
}
.col-sm-8 .help-block {
  float: left;
  color: #ed5565;
}
.col-md-6 .help-block {
  color: #ed5565;
  font-size: 12px;
}
.help-block2 {
  color: #ed5565;
  font-size: 12px;
}
.col-sm-3 .help-block {
  color: #ed5565;
  font-size: 12px;
}
.illustrate-p {
  font-size: 14px;
  margin-top: 10px;
  border-top: #b6b6b6 dashed 1px;
  padding-top: 10px;
}
.illustrate-p p {
  margin-bottom: 10px;
}
.illustrate-p span {
  color: #1c6ac7;
}
.sw-theme-arrows .toolbar > .btn {
  color: #fff;
  background-color: #2668d4;
  border-color: #2668d4;
  padding: .375rem .75rem;
  border-radius: .25rem;
  font-weight: 400;
}
.bk-group {
  width: 450px;
  margin: 0px;
  padding: 0px;
}
.bk-group li {
  height: 40px;
}
.kl-group {
  margin-top: 20px;
}
.kl-group li {
  height: 25px;
  line-height: 25px;
}
.kl-group li a {
  color: #1c6ac7;
}
.kl-group li a:hover {
  color: #fb1515;
}
.col-sm-p {
  margin-top: 5px;
  margin-bottom: 5px;
}
.form-a {
  margin-top: 6px;
  float: left;
  color: #1c6ac7;
}
.kl-group li a:hover {
  color: #fb1515;
}
.panel-tit {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  height: 32px;
  line-height: 32px;
  background-color: #f3f5fb;
  border-radius: 10px;
  margin-bottom: 10px;
  color: #000;
}
.panel-unit {
  display: block;
  margin-top: 10px;
  margin-left: 10px;
}
.warning-elemen-tit {
  color: #000;
  margin-bottom: 10px;
}
.newstitle {
  width: 100%;
  font-size: 18px;
  color: #000000;
  line-height: 22px;
  text-align: center;
  border-bottom: #bcbcbc dashed 1px;
  margin: 0 auto;
  padding-top: 15px;
  padding-bottom: 15px;
}
.about {
  width: auto;
  margin: auto;
  line-height: 28px;
  font-size: 14px;
  margin-bottom: 5px;
  padding-bottom: 10px;
}
.about p {
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 10px;
  margin-bottom: 0px;
  font-size: 14px;
  line-height: 22px;
}
.abouttit {
  color: #000
}
.titleright {
  float: right;
}
.titleright p {
  text-align: right;
  margin-bottom: 5px;
}
.col-list {
  margin: 0px;
  padding: 0px;
}
.col-list li {
  margin-bottom: 10px;
}
.mnote-editor-title {
  margin-left: 10px;
  margin-bottom: 10px;
  margin-right: 10px;
  border-radius: 10px;
  height: 32px;
  line-height: 32px;
  color: #000;
  background-color: #f3f5fb;
  padding-left: 10px;
  font-size: 16px;
}
.mnote-editor-box {
  margin-left: 10px;
  margin-bottom: 10px;
  margin-right: 10px;
}
.panel-body .select-list .select-time input {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: transparent;
  outline: 0;
  height: 30px;
  width: 200px;
  padding-left: 5px;
}
/******拖拽*******/
.drag-box {
  border: 1px dashed #ddd;
  width: 200px;
  height: 33px;
  background-color: #fff;
  display: inline-block;
  font-size: 12px;
  color: #666;
  line-height: 33px;
  cursor: pointer;
}
.drag-box {
  padding-left: 30px;
}
/******拖拽end*******/
/******表格*******/
.table-f12 {
  font-size: 12px;
}
.table-f12 table thead {
  background-color: #e5efff;
}
.table-f12 .table-bordered > thead > tr > th, .table-f12 .table-bordered > thead > tr > td {
  background-color: #e5efff;
  border-bottom-width: 1px;
}
.g-table-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
  color: #000;
  margin-bottom: 10px;
}
.g-table-unit {
  text-align: right;
  margin-top: 10px;
  margin-bottom: 10px;
}
/******表格end*******/
/******进度条*******/
.for-liucheng2 {
  width: 100%;
  height: 50px;
  padding: 20px 0 0 0;
  position: relative;
  font-size: 16px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 0px;
  margin-bottom: 30px;
}
.for-liucheng2 .liutextbox .liutext {
  float: left;
  width: 16.6%;
  text-align: center;
}
.for-liucheng2 .liulist {
  float: left;
  width: 16.6%;
  height: 7px;
  background: #ccc;
}
.for-liucheng {
  width: 900px;
  height: 50px;
  padding: 20px 0 0 0;
  position: relative;
  font-size: 16px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 0px;
  margin-bottom: 30px;
}
.liulist {
  float: left;
  width: 16.6%;
  height: 7px;
  background: #ccc;
}
.liutextbox {
  position: absolute;
  width: 100%;
  left: 0;
  top: 10px;
}
.liutextbox .liutext {
  float: left;
  width: 16.6%;
  text-align: center;
}
.liutextbox .liutext em {
  display: inline-block;
  width: 24px;
  height: 24px;
  -moz-border-radius: 24px;
  -webkit-border-radius: 24px;
  border-radius: 24px;
  background: #ccc;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  font-style: normal;
  font-weight: bold;
  color: #fff;
}
.liutextbox .liutext strong {
  display: inline-block;
  height: 26px;
  line-height: 26px;
  font-weight: 400;
}
.liulist.for-cur {
  background: #77b852;
}
.liutextbox .for-cur em {
  background: #77b852;
}
.liutextbox .for-cur strong {
  color: #77b852;
}
/******进度条end*******/
.m-table-container {
  margin-left: 10px;
}
.table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
  background-color: #e5efff;
  border-bottom-width: 1px;
}
.w200 {
  width: 200px;
}
.m-num {
  color: #ed5565;
}
.sbl {
  margin-left: 0px;
  width: 220px;
  float: left;
}
.sbr {
  margin-left: 0px;
  float: left;
}
.mnote-editor-subtitle {
  color: #000;
  margin-left: 12px;
  margin-bottom: 10px;
}
.mnote-editor-subtitle .fa {
  color: #2668d4;
}
.form-control-static input[type=radio] {
  vertical-align: middle;
  margin-right: 2px;
}
.btninner {
  margin-left: 10px;
}
#LoutiNav {
  width: 84px;
  position: fixed;
  top: 40px;
  right: 0px;
  display: none;
  z-index: 700;
  color: #000;
  font: 13px Microsoft YaHei, Heiti SC, tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
}
#LoutiNav li {
  width: 84px;
  height: 40px;
  cursor: pointer;
  position: relative;
  width: 84px;
  height: 35px;
  cursor: pointer;
  text-align: left;
}
#LoutiNav li:last-child {
  background: none;
}
#LoutiNav li i {
  font-style: normal;
  font-weight: normal;
  color: #000;
  vertical-align: middle;
  ont-style: normal;
  font-weight: normal;
  color: #000;
  display: block;
  width: 84px;
  height: 32px;
  line-height: 32px;
  border: 1px solid #e3e3e3;
  background-color: #fff;
  border-radius: 3px;
  padding-left: 10px;
}
#LoutiNav span {
  display: none;
  height: 40px;
}
#LoutiNav .active i {
  background-color: #2668d4;
  color: #FFFFFF;
}
#LoutiNav li:hover span {
  display: block;
  background-color: #2668d4;
  color: #FFFFFF;
  width: 84px;
  height: 32px;
  line-height: 32px;
  border-radius: 3px;
  padding-left: 5px;
}
#LoutiNav li:hover i {
  display: none;
  font-style: normal;
}
#LoutiNav li i .fa {
  color: #2668d4
}
#LoutiNav .active i .fa {
  color: #FFFFFF;
}
#goTop {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: gray;
  position: fixed;
  bottom: 60px;
  right: 30px;
  cursor: pointer;
  border-radius: 5px;
  display: none;
}
#goTop:hover {
  background: darkred;
  color: white;
}
#goTop:hover span {
  display: block;
}
.elevator_lk:after {
  position: absolute;
  display: inline-block;
  width: 40px;
  height: 1px;
  left: 50%;
  bottom: 0;
  margin-left: -20px;
  background: -webkit-gradient(linear, right top, left top, from(white), color-stop(#eeeeee), color-stop(#eeeeee), to(white));
  background: linear-gradient(270deg, white, #eeeeee, #eeeeee, white);
  z-index: 1;
  content: "";
}
.w1200 {
  margin-left: 45px;
  margin-right: 45px;
}
.kl-group input[type=checkbox] {
  margin-right: 4px;
  padding-top: 10px;
}
.mnote-list {
  margin-top: 10px;
  margin-bottom: 10px;
  float: left;
}
.mnote-list ul li {
  float: left;
}
.mnote-textarea {
  float: left;
}
.mnote-editor-subtitle2 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.mnote-editor-subtitle2 a {
  color: #1c6ac7;
}
.mnote-editor-subtitle2 a:hover {
  color: #fb1515;
}
.tabs-container .nav-tabs > li.active > a, .tabs-container .nav-tabs > li.active > a:hover, .tabs-container .nav-tabs > li.active > a:focus {
  border-top: #1c6ac7 solid 4px;
}
.application-select li {
  color: #000;
  margin-bottom: 15px;
  font-size: 14px;
}
.application-select li input[type=checkbox], .application-select li input[type=radio] {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: 0px;
}
.a-select-layer {
  margin-bottom: 5px;
}
.b-select-layer {
  margin-bottom: 20px;
}
.b-select-layer span {
  margin-left: 20px;
  margin-right: 5px;
  color: #676a6c;
  font-size: 14px;
}
/******浮动*******/
#moquu_top, #moquu_save {
  z-index: 2;
  width: 63px;
  /*height: 59px;*/
  right: 0px;
  position: fixed;
  cursor: pointer;
  width: 84px;
  z-index: 700;
  color: #ffffff;
  font: 13px Microsoft YaHei, Heiti SC, tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
}
#moquu_save {
  top: 110px;
  _margin-top: 110px;
}
#moquu_save a {
  display: inline-block;
  font-weight: normal;
  color: #ffffff;
  display: block;
  width: 84px;
  height: 32px;
  line-height: 32px;
  border: 1px solid #e3e3e3;
  background-color: #70ac54;
  border-radius: 3px;
  font-size: 13px;
}
#moquu_save a i {
  font-size: 16px;
  margin-left: 6px;
  margin-right: 2px;
  color: #ffffff;
}
#moquu_save a:hover {
  background-color: #658b52;
}
#moquu_save i {
  font-size: 30px;
}
.moquu_save, .moquu_save {
  position: relative;
  z-index: 2
}
.moquu_save a:hover {
  display: block
}
/***附件详细***/
.fjtable {
  width: 100%;
}
.layui-upload-list .fjtable tr {
  border: 1px solid #e7eaec;
  border-spacing: 0;
  border-collapse: collapse;
}
.layui-upload-list .fjtable tr td {
  border: 1px solid #e7eaec;
  border-spacing: 0;
  border-collapse: collapse;
}
.layui-upload-list .fjtable tr td:nth-child(3) {
  text-align: right;
}
/***只读框最小高度 ***/
pre {
  min-height: 100px;
  padding-top: 5px;
  padding-left: 5px;
  padding-right: 5px;
  paddin-bottom: 5px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  opacity: 1;
}
/***链接 ***/
.lookbox {
  margin-top: 10px;
  border-top: #CCC dashed 1px;
  padding-top: 20px;
}
.lookbox a {
  color: #2668d4;
}
.lookbox ul {
  margin: 0px;
  padding: 0px;
}
.lookbox ul li {
  padding: 0px;
  margin-bottom: 10px;
  margin-left: 50px;
}
.lookbox i {
  margin-right: 5px;
}
.lookbox a:hover {
  color: #FF6600;
}
.row-left {
  float: left;
  width: calc(12.5% - 3.75px);
  padding-left: 15px;
  padding-right: 15px;
}
.row-right {
  float: left;
  padding-left: 15px;
  padding-right: 15px;
  width: calc(87.5% + 3.75px);
}
/***0118 ***/
.col-list {
  color: #000000;
  margin-left: 20px;
  margin-right: 20px;
}
.col-list li {
  float: left;
  line-height: 36px;
  height: 36px;
}
.best-regard {
  float: right;
  color: #000000;
  font-size: 14px;
  text-align: right;
  margin-right: 0px;
  border-bottom: #CCC dashed 1px;
  width: 100%;
  margin-bottom: 10px;
}
.best-regard p {
  margin-bottom: 10px;
  margin-right: 20px;
}
/***0121 ***/
.kjtree-button {
  right: 0px;
  position: fixed;
  cursor: pointer;
  z-index: 700;
  color: #ffffff;
  font: 13px Microsoft YaHei, Heiti SC, tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
	bottom:60px;
}
.kjtree-button a {
  display: inline-block;
  font-weight: normal;
  color: #ffffff;
  display: block;
  width: 84px;
  height: 32px;
  line-height: 32px;
  border: 1px solid #e3e3e3;
  background-color: #70ac54;
  border-radius: 3px;
  font-size: 13px;
}
.kjtree-button a i {
  font-size: 16px;
  margin-left: 6px;
  margin-right: 2px;
  color: #ffffff;
}
.kjtree-button a:hover {
  background-color: #658b52;
}
.treeico {
  background: url(../img/treeico.svg) 0px 1px no-repeat;
  background-size: 16px 16px;
  width: 16px;
  height: 16px;
  display: inline-block;
  color: #FFFFFF;
  margin-left: 10px;
  margin-top: 0px;
  margin-right: 1px;
}

/***0315 ***/

.animated .panel-body .form-group {
    margin-bottom: 0px;
	padding-right:100px;
}

/***0407 ***/

.pack-up-button a {
  font-style: normal;
  font-weight: normal;
  color: #000;
  vertical-align: middle;
  ont-style: normal;
  font-weight: normal;
  color: #000;
  display: block;
  width: 84px;
  height: 32px;
  line-height: 32px;
  border: 1px solid #e3e3e3;
  background-color: #fff;
  border-radius: 3px;
  margin-bottom:10px;
}

.pack-up-button a:hover {
  display: block;
  background-color: #2668d4;
  color: #FFFFFF;
  width: 84px;
  height: 32px;
  line-height: 32px;
  border-radius: 3px;
}


.pack-up-button .fa-chevron-circle-right{
  color: #2668d4;
  font-size:18px;
  margin-left:12px;
  vertical-align:middle;
  margin-right:2px;
}

.pack-up-button .fa-chevron-circle-left{
  color: #2668d4;
  font-size:18px;
  margin-left:12px;
  vertical-align:middle;
  margin-right:2px;
}

.pack-up-button a:hover .fa-chevron-circle-right{ color:#FFF;}
.pack-up-button a:hover .fa-chevron-circle-left{ color:#FFF;}


.kjtree-img {
  content: url(../img/tree2.png);
}

.kjtree-button2 {
	margin-bottom: 65px;
	right: 0px;
	position: fixed;
	cursor: pointer;
	z-index: 700;
	color: #ffffff;
  	font: 13px Microsoft YaHei, Heiti SC, tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
	bottom:2px;
	text-align:center;
	z-index:1000;
}
.kjtree-button2 a {
  display: inline-block;
  font-weight: normal;
  color: #ffffff;
  display: block;
  width: 60px;
  height:60px;
	/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#1e5799+0,2989d8+50,7db9e8+100 */
background: #1e5799; /* Old browsers */
background: -moz-linear-gradient(top,  #1e5799 0%, #2989d8 50%, #7db9e8 100%); /* FF3.6-15 */
background: -webkit-linear-gradient(top,  #1e5799 0%,#2989d8 50%,#7db9e8 100%); /* Chrome10-25,Safari5.1-6 */
background: linear-gradient(to bottom,  #1e5799 0%,#2989d8 50%,#7db9e8 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1e5799', endColorstr='#7db9e8',GradientType=0 ); /* IE6-9 */

  border-radius: 50%;
  font-size: 13px;
}

.kjtree-button2 a:hover {
  /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#6db3f2+0,54a3ee+50,1e69de+100 */
background: rgb(109,179,242); /* Old browsers */
background: -moz-linear-gradient(top,  rgba(109,179,242,1) 0%, rgba(84,163,238,1) 50%, rgba(30,105,222,1) 100%); /* FF3.6-15 */
background: -webkit-linear-gradient(top,  rgba(109,179,242,1) 0%,rgba(84,163,238,1) 50%,rgba(30,105,222,1) 100%); /* Chrome10-25,Safari5.1-6 */
background: linear-gradient(to bottom,  rgba(109,179,242,1) 0%,rgba(84,163,238,1) 50%,rgba(30,105,222,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6db3f2', endColorstr='#1e69de',GradientType=0 ); /* IE6-9 */

}

.kjtree-button2 .fa-history {
  font-size: 30px;
  margin-top: 7px;
  color: #01fcfe;
}

/**新加2022-04-16**/
.form-control-static {
    min-height: 28px;
    padding-top: 7px;
    padding-bottom: 4px;
    margin-bottom: 0;
}
.nav-tabs>li>a {
	font-size: 16px;
}
.panel-title {
    font-size: 16px;
}
.form-read .form-group {
	margin-bottom: 0px;
}
.form-read .form-control-static{
margin-top: 0px;
}
.form-read .form-control-static {
    margin-top: 0px;
    font-size: 16px;
}
.form-read .control-label {
	font-size: 16px;
}
.form-read .form-control-static {
    min-height: 33px;
    padding-top: 2px;
    padding-bottom: 0px;
    margin-bottom: 0;
    line-height: 33px;
}
/**新加419**/

.word-box {
	background-color: #fff;
	margin-bottom: 0;
	border-radius: 4px;
	padding-left: 30px;
	padding-right: 30px;
}
.word-boxtit {
	text-align: center;
	letter-spacing: 1px;
	font-size: 24px;
	font-weight: bold;
	padding-top: 4px;
	margin-bottom: 4px;
	line-height: 40px;
	margin-top: 0px;
	padding-top: 20px;
	color: #000;
}
.word-head-info {
	font-size: 21px;
	color: #000;

	margin-top: 30px;
	margin-bottom:10px;



}
.word-article-content {
	font-size: 21px;
	color: #000;
	line-height: 40px;
	margin-bottom:5px;
	text-indent:45px;

}
.word-wrapper {
	margin-top:0px;
	padding-bottom: 20px;
}
.word-article-table {
	margin-top: 10px;
	margin-bottom: 10px;
}
.word-table {
	border-collapse: collapse;
	width: 100%;
}
.word-table tr td {
	border-top: 1px solid #ddd;
	border-left: 1px solid #ddd;
	border-right: 1px solid #ddd;
	border-bottom: 1px solid #ddd;

	    color: #676a6c;
	font-size: 14px;
	padding-left: 5px;
	padding-top: 5px;
	padding-bottom: 5px;
	border-collapse: collapse;
}
.word-table tr th {
	border-top: 1px solid #ddd;
	border-left: 1px solid #ddd;
	border-right: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	    color: #676a6c;
	font-size: 14px;
	padding-left: 5px;
	padding-top: 5px;
	padding-bottom: 5px;
	border-collapse: collapse;
	    background-color: #e5efff;

}
.word-table table tr {
	border-collapse: collapse;
}
.word-department {
	text-align: right;
	font-size: 21px;
	color: #000;
	margin-bottom: 5px;
	line-height:30px;
	margin-top:20px;
}

.textarea-readonly{
	white-space: pre-wrap;
	word-wrap: break-word;
}

.tooltip .tooltip-inner{
	text-align: left;
}


.panel-body-header{ font-size:16px; color:#000; font-weight:bold; margin-bottom:5px; margin-top:5px;}


.panel-body-txt{ font-size:14px; line-height:24px; color:#000;
font-weight:100; }

/**新加427**/

.table .btn-circle {
    width: 25px;
    height: 25px;
    padding: 6px 0;
    border-radius: 15px;
    text-align: center;
    font-size: 12px;
    line-height: 1.428571429;
}

/**新加512**/

.select-list {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-start;
  margin-top: 5px;
  margin-left: 10px;
}
.select-list li {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.noDb {
  content: url(../img/zhanwu.png);
  height: 380px;
  margin-left:30px;
  margin-top:-15px;
}

/**新加412**/

.wrapper-tip {
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  background-color: #eef4ff;
  padding-top:5px;
  padding-bottom:5px;
  padding-left:5px;
  padding-right:5px;
  margin-top:0px;
  margin-bottom:40px;
  border:1px solid #589dff;
  border-radius: 5px;
  line-height:20px;

}

.wrapper-tip .i-tipbtn {
  color: #0099FF;
  font-size: 14px;
}

.wrapper-tip .fa-warning:before, .wrapper-tip .fa-exclamation-triangle:before {
  content: "\f071";
  color:#589dff;
  margin-right:5px;
}


/**2024新加423**/
.radio-list {
  box-sizing: content-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.radio-list li {
  box-sizing: content-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
}
.radio-list li span {
  margin-right: 5px;
}