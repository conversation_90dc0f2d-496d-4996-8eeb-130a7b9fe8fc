/**
 * 专利工具类
 * 包含专利申请号校验、专利类型自动计算等功能
 */
var PatentUtils = {
    
    /**
     * 专利类型映射
     */
    PATENT_TYPE_MAP: {
        '1': 'FM',        // 发明
        '2': 'SYXX',      // 实用新型
        '3': 'WGSJ',      // 外观设计
        '8': 'PCT_FM',    // PCT发明
        '9': 'PCT_SYXX'   // PCT实用新型
    },
    
    /**
     * 专利类型中文名称映射
     */
    PATENT_TYPE_NAME_MAP: {
        'FM': '发明',
        'SYXX': '实用新型',
        'WGSJ': '外观设计',
        'PCT_FM': 'PCT发明',
        'PCT_SYXX': 'PCT实用新型'
    },
    
    /**
     * 校验专利申请号格式
     * @param {string} patentNo 专利申请号
     * @returns {object} 校验结果 {valid: boolean, message: string, patentType: string}
     */
    validatePatentNo: function(patentNo) {
        var result = {
            valid: false,
            message: '',
            patentType: ''
        };
        
        if (!patentNo) {
            result.message = '申请号不能为空';
            return result;
        }
        
        // 去除空格
        patentNo = patentNo.trim();
        
        // 基本格式校验：年份(4位) + 申请类型(1位) + 流水号(7位) + . + 校验位(1位)
        var basicPattern = /^\d{4}\d{8}\.\d$/;
        if (!basicPattern.test(patentNo)) {
            result.message = '申请号格式不正确！正确格式：年份(4位)+申请类型(1位)+流水号(7位)+.+校验位(1位)';
            return result;
        }
        
        // 提取年份
        var year = parseInt(patentNo.substring(0, 4));
        var currentYear = new Date().getFullYear();
        
        // 年份合理性校验
        if (year < 1985 || year > currentYear + 1) {
            result.message = '申请号中的年份不合理，应在1985年至' + (currentYear + 1) + '年之间';
            return result;
        }
        
        // 提取申请类型（第5位）
        var typeChar = patentNo.charAt(4);
        
        // 校验申请类型
        if (!this.PATENT_TYPE_MAP[typeChar]) {
            result.message = '申请号第5位不正确！1-发明，2-实用新型，3-外观设计，8-PCT发明，9-PCT实用新型';
            return result;
        }
        
        // 校验位计算（简化版本，实际可能需要更复杂的算法）
        var checkDigit = patentNo.charAt(patentNo.length - 1);
        var calculatedCheckDigit = this.calculateCheckDigit(patentNo.substring(0, patentNo.length - 2));
        
        result.valid = true;
        result.patentType = this.PATENT_TYPE_MAP[typeChar];
        result.message = '申请号格式正确，专利类型：' + this.PATENT_TYPE_NAME_MAP[result.patentType];
        
        return result;
    },
    
    /**
     * 计算校验位（简化算法）
     * @param {string} baseNumber 基础号码
     * @returns {string} 校验位
     */
    calculateCheckDigit: function(baseNumber) {
        var sum = 0;
        var weights = [1, 2, 3, 4, 5, 6, 7, 8, 9, 2, 3, 4]; // 权重数组
        
        for (var i = 0; i < baseNumber.length && i < weights.length; i++) {
            sum += parseInt(baseNumber.charAt(i)) * weights[i];
        }
        
        return (sum % 10).toString();
    },
    
    /**
     * 根据申请号自动设置专利类型
     * @param {string} patentNo 专利申请号
     * @param {string} selectId 专利类型选择框ID（现在是隐藏字段ID）
     */
    autoSetPatentType: function(patentNo, selectId) {
        var validation = this.validatePatentNo(patentNo);
        
        if (validation.valid && validation.patentType) {
            // 直接设置专利类型编码到隐藏字段
            $('#' + selectId).val(validation.patentType);
            
            // 如果存在对应的显示字段，也设置中文名称
            var nameFieldId = selectId + 'Name';
            if ($('#' + nameFieldId).length > 0) {
                $('#' + nameFieldId).val(this.PATENT_TYPE_NAME_MAP[validation.patentType]);
            }
        }
        
        return validation;
    },
    
    /**
     * 获取专利类型对应的数字值
     * @param {string} patentTypeCode 专利类型代码（如：FM, SYXX, WGSJ等）
     * @returns {string} 专利类型数字值（如：1, 2, 3等）
     */
    getPatentTypeValue: function(patentTypeCode) {
        var typeMapping = {
            'FM': '1',        // 发明
            'SYXX': '2',      // 实用新型
            'WGSJ': '3',      // 外观设计
            'PCT_FM': '8',    // PCT发明
            'PCT_SYXX': '9'   // PCT实用新型
        };
        return typeMapping[patentTypeCode] || null;
    },
    
    /**
     * 获取专利类型对应的中文名称
     * @param {string} patentTypeCode 专利类型代码（如：FM, SYXX, WGSJ等）
     * @returns {string} 专利类型中文名称（如：发明、实用新型等）
     */
    getPatentTypeName: function(patentTypeCode) {
        return this.PATENT_TYPE_NAME_MAP[patentTypeCode] || '';
    },
    
    /**
     * 调试函数：检查专利类型映射
     * @param {string} patentTypeCode 专利类型代码
     */
    debugPatentTypeMapping: function(patentTypeCode) {
        console.log('调试专利类型映射:');
        console.log('输入编码:', patentTypeCode);
        console.log('映射表:', this.PATENT_TYPE_NAME_MAP);
        console.log('映射结果:', this.PATENT_TYPE_NAME_MAP[patentTypeCode]);
        console.log('所有可用的专利类型编码:', Object.keys(this.PATENT_TYPE_NAME_MAP));
    },
    
    /**
     * 格式化专利申请号
     * @param {string} patentNo 原始申请号
     * @returns {string} 格式化后的申请号
     */
    formatPatentNo: function(patentNo) {
        if (!patentNo) return '';
        
        // 去除所有非数字和点号的字符
        var cleaned = patentNo.replace(/[^\d.]/g, '');
        
        // 如果长度足够，自动添加点号
        if (cleaned.length >= 12 && cleaned.indexOf('.') === -1) {
            cleaned = cleaned.substring(0, 12) + '.' + cleaned.substring(12);
        }
        
        return cleaned;
    },
    
    /**
     * 生成鄂钢编号
     * 规则:1位子公司类别(总部(BSVK00)-0，其它-Q) + 7位同一个子公司累计的流水号
     * @param {string} companyPrefix 子公司类别前缀
     * @param {number} sequence 序号
     * @returns {string} 鄂钢编号
     */
    generateEgbh: function(companyPrefix, sequence) {
        var sequenceStr = sequence.toString().padStart(7, '0');
        return companyPrefix + sequenceStr;
    },
    
    /**
     * 校验权利要求数量
     * @param {number} count 权利要求数量
     * @param {string} patentType 专利类型
     * @returns {object} 校验结果
     */
    validateClaimCount: function(count, patentType) {
        var result = {
            valid: true,
            message: ''
        };
        
        if (!count || count < 1) {
            result.valid = false;
            result.message = '权利要求数量必须大于0';
            return result;
        }
        
        // 根据专利类型设置合理范围
        var maxCount = 50; // 默认最大值
        switch(patentType) {
            case 'FM':
                maxCount = 50;
                break;
            case 'SYXX':
                maxCount = 20;
                break;
            case 'WGSJ':
                maxCount = 1; // 外观设计通常只有1项权利要求
                break;
        }
        
        if (count > maxCount) {
            result.valid = false;
            result.message = '权利要求数量超出合理范围（最大' + maxCount + '项）';
        }
        
        return result;
    },
    
    /**
     * 校验说明书页数
     * @param {number} pages 说明书页数
     * @param {string} patentType 专利类型
     * @returns {object} 校验结果
     */
    validateSpecificationPages: function(pages, patentType) {
        var result = {
            valid: true,
            message: ''
        };
        
        if (!pages || pages < 1) {
            result.valid = false;
            result.message = '说明书页数必须大于0';
            return result;
        }
        
        // 根据专利类型设置合理范围
        var maxPages = 200; // 默认最大值
        var minPages = 1;
        
        switch(patentType) {
            case 'FM':
                minPages = 3; // 发明专利说明书通常较长
                maxPages = 200;
                break;
            case 'SYXX':
                minPages = 2;
                maxPages = 100;
                break;
            case 'WGSJ':
                minPages = 1;
                maxPages = 20; // 外观设计说明书较短
                break;
        }
        
        if (pages < minPages) {
            result.valid = false;
            result.message = '说明书页数过少（最少' + minPages + '页）';
        } else if (pages > maxPages) {
            result.valid = false;
            result.message = '说明书页数过多（最多' + maxPages + '页）';
        }
        
        return result;
    },
    
    /**
     * 初始化专利申请号输入框
     * @param {string} inputId 输入框ID
     * @param {string} typeSelectId 专利类型选择框ID
     * @param {function} callback 回调函数
     */
    initPatentNoInput: function(inputId, typeSelectId, callback) {
        var self = this;
        
        $('#' + inputId).on('blur', function() {
            var patentNo = $(this).val();
            if (patentNo) {
                // 格式化申请号
                var formatted = self.formatPatentNo(patentNo);
                $(this).val(formatted);
                
                // 校验并自动设置专利类型
                var validation = self.autoSetPatentType(formatted, typeSelectId);
                
                // 显示校验结果
                if (validation.valid) {
                    $(this).removeClass('error').addClass('success');
                    self.showMessage(validation.message, 'success');
                } else {
                    $(this).removeClass('success').addClass('error');
                    self.showMessage(validation.message, 'error');
                }
                
                // 执行回调
                if (typeof callback === 'function') {
                    callback(validation);
                }
            }
        });
        
        // 实时格式化输入
        $('#' + inputId).on('input', function() {
            var value = $(this).val();
            var formatted = self.formatPatentNo(value);
            if (formatted !== value) {
                var cursorPos = this.selectionStart;
                $(this).val(formatted);
                this.setSelectionRange(cursorPos, cursorPos);
            }
        });
    },
    
    /**
     * 显示消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型：success, error, warning, info
     */
    showMessage: function(message, type) {
        if (typeof $.modal !== 'undefined') {
            switch(type) {
                case 'success':
                    $.modal.msgSuccess(message);
                    break;
                case 'error':
                    $.modal.alertError(message);
                    break;
                case 'warning':
                    $.modal.alertWarning(message);
                    break;
                default:
                    $.modal.msg(message);
            }
        } else {
            // 降级到alert
            alert(message);
        }
    }
};

// 如果是AMD环境，导出模块
if (typeof define === 'function' && define.amd) {
    define(function() {
        return PatentUtils;
    });
}

// 如果是CommonJS环境，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PatentUtils;
}

// 全局暴露
window.PatentUtils = PatentUtils; 