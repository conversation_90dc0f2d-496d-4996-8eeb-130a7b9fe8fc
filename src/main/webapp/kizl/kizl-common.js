/**
 * KIZL模块通用JavaScript方法
 * 用于处理申请基础信息相关的通用操作
 */
var KizlCommon = {
    // 默认前缀
    prefix: ctx + "kizl/applyBaseinfo",
    wfPrefix: ctx + "kizl/workFlow",

    /**
     * 查看基本信息
     * @param {string} applyId 申请ID，如果不传则自动从页面获取
     */
    viewBasicInfo: function(applyId) {
        // 如果没有传入applyId，尝试从页面获取
        if (!applyId) {
            applyId = $('#applyId').val();
        }
        var url = ctx + 'kizl/applyBaseinfo/queryDT?businessId=' + applyId;
        $.modal.openTab("专利申报基本信息", url);
    },

    /**
     * 查看基本信息（自动获取页面申请ID）
     * 专门用于页面按钮点击事件
     */
    viewBasicInfoAuto: function() {
        this.viewBasicInfo();
    },

    /**
     * 根据swsGuid获取事务所名称并设置到指定元素
     * @param {string} swsGuid 事务所GUID
     * @param {string} targetElementId 目标元素ID，用于显示事务所名称
     * @param {string} defaultText 默认显示文本，当swsGuid为空时显示
     * @param {function} callback 可选的回调函数，参数为(success, swsName, swsData)
     */
    loadSwsName: function(swsGuid, targetElementId, defaultText, callback) {
        defaultText = defaultText || '未选择事务所';
        
        if (!swsGuid || swsGuid.trim() === '') {
            if (targetElementId) {
                $('#' + targetElementId).text(defaultText);
            }
            if (callback && typeof callback === 'function') {
                callback(false, defaultText, null);
            }
            return;
        }
        
        // 调用后端接口获取事务所详细信息
        $.ajax({
            url: ctx + 'kizl/maintainSwsinfo/getSwsDetailInfo/' + swsGuid,
            type: 'GET',
            dataType: 'json',
            success: function(result) {
                var swsName = defaultText;
                var success = false;
                
                if (result.code === 0 && result.data) {
                    swsName = result.data.swsName || '未知事务所';
                    success = true;
                    
                    if (targetElementId) {
                        $('#' + targetElementId).text(swsName);
                    }
                    
                    if (callback && typeof callback === 'function') {
                        callback(true, swsName, result.data);
                    }
                } else {
                    swsName = '获取事务所名称失败';
                    if (targetElementId) {
                        $('#' + targetElementId).text(swsName);
                    }
                    
                    if (callback && typeof callback === 'function') {
                        callback(false, swsName, null);
                    }
                    
                    console.error('获取事务所详细信息失败：', result.msg || '未知错误');
                }
            },
            error: function(xhr, status, error) {
                var swsName = '获取事务所名称失败';
                if (targetElementId) {
                    $('#' + targetElementId).text(swsName);
                }
                
                if (callback && typeof callback === 'function') {
                    callback(false, swsName, null);
                }
                
                console.error('获取事务所详细信息失败：', error);
            }
        });
    },

    /**
     * 自动从页面获取swsGuid并加载事务所名称
     * @param {string} swsGuidElementId 存储swsGuid的元素ID，默认为'swsGuid'
     * @param {string} targetElementId 显示事务所名称的元素ID，默认为'swsNameText'
     * @param {string} defaultText 默认显示文本
     * @param {function} callback 可选的回调函数
     */
    autoLoadSwsName: function(swsGuidElementId, targetElementId, defaultText, callback) {
        swsGuidElementId = swsGuidElementId || 'swsGuid';
        targetElementId = targetElementId || 'swsNameText';
        
        var swsGuid = $('#' + swsGuidElementId).val();
        this.loadSwsName(swsGuid, targetElementId, defaultText, callback);
    },

    /**
     * 初始化事务所名称显示（页面加载时调用）
     * 这是一个便捷方法，适用于大多数标准页面布局
     */
    initSwsNameDisplay: function() {
        // 延迟执行，确保页面元素已经加载完成
        $(document).ready(function() {
            KizlCommon.autoLoadSwsName();
        });
    },

    /**
     * 初始化表单验证
     * @param {string} formId 表单ID，默认为 'form-applyBaseinfo-edit'
     * @param {object} customRules 自定义验证规则
     * @param {object} customMessages 自定义验证消息
     */
    initFormValidation: function(formId, customRules, customMessages) {
        formId = formId || 'form-applyBaseinfo-edit';
        customRules = customRules || {};
        customMessages = customMessages || {};
        
        // 默认验证规则
        var defaultRules = {
            zgbmNc: {
                maxlength: 1000
            }
        };
        
        // 默认验证消息
        var defaultMessages = {
            zgbmNc: {
                maxlength: "内部参考意见不能超过1000个字符"
            }
        };
        
        // 合并规则和消息
        var rules = $.extend({}, defaultRules, customRules);
        var messages = $.extend({}, defaultMessages, customMessages);
        
        $("#" + formId).validate({
            focusCleanup: true,
            onkeyup: false,
            onfocusout: false,
            ignore: "",
            rules: rules,
            messages: messages
        });
    },
    
    /**
     * 表单提交处理
     * @param {string} transitionKey 流转键
     * @param {string} formId 表单ID，默认为 'form-applyBaseinfo-edit'
     * @param {string} submitUrl 提交URL，默认为 prefix + "/doSubmit"
     */
    submitHandler: function(transitionKey, formId, submitUrl) {
        formId = formId || 'form-applyBaseinfo-edit';
        submitUrl = submitUrl || this.prefix + "/doSubmit";
        
        if ($.validate.form()) {
            $.modal.confirm("确认流转吗", function() {
                $.operate.saveTabAlert(submitUrl, $('#' + formId).serialize());
            });
        }
    },

    returnHandler: function (formId,returnUrl) {
        // 返回处理函数
        $.modal.confirm("确认退回吗", function() {
            formId = formId || 'form-applyBaseinfo-edit';
            returnUrl = returnUrl || KizlCommon.wfPrefix + "/returnWF";
            $.operate.saveTabAlert(returnUrl, $('#' + formId).serialize());
        });
    },
    
    /**
     * 暂存功能
     * @param {string} formId 表单ID，默认为 'form-applyBaseinfo-edit'
     * @param {string} saveUrl 保存URL，默认为 prefix + "/doSave"
     */
    saveDraft: function(formId, saveUrl) {
        formId = formId || 'form-applyBaseinfo-edit';
        saveUrl = saveUrl || this.prefix + "/doSave";
        
        $.ajax({
            url: saveUrl,
            type: "post",
            dataType: "json",
            data: $('#' + formId).serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code === 0) {
                    if ($.common.isNotEmpty(result.data)) {
                        if ($.common.isNotEmpty(result.data.applyId)) {
                            $("#applyId").val(result.data.applyId);
                        }
                    }
                    $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        });
    },
    
    /**
     * 切换意见相关字段的显示（KIZLSB02专用）
     * @param {string} opinion 意见值
     */
    toggleOpinionFields: function(opinion) {
        // 根据选择显示对应字段
        if (opinion === "3") { // 指定专家评审
            $("#expertReviewGroup").show();
            // 添加专家选择的必填验证
            $("#extra3").rules("add", {
                required: true,
                messages: {
                    required: "请选择专家"
                }
            });
        } else {
            $("#expertReviewGroup").hide();
            // 移除专家选择的必填验证
            $("#extra3").rules("remove", "required");
            // 清空专家选择
            $("#extra3").val("");
            $("#extra4").val("");
        }
        // 重新验证表单
        $("#form-applyBaseinfo-edit").valid();
    },
    
    /**
     * 选择专家（KIZLSB02专用）
     */
    selectExpert: function() {
        var url = ctx + "system/user/selectExpertList?callback=selectExpertCallback";
        $.modal.openTab("选择专家", url);
    },
    
    /**
     * 专家选择回调函数（KIZLSB02专用）
     * @param {string} userCode 用户编码
     * @param {string} userName 用户名称
     */
    selectExpertCallback: function(userCode, userName) {
        if (userCode && userName) {
            $("#extra3").val(userCode);
            $("#extra4").val(userName);
        }
    },

    getSource: function (fromNo) {
        console.log("获取来源信息，fromNo: " + fromNo);
        if (fromNo && fromNo.trim() !== "") {
            $.ajax({
                url: ctx + "kizl/applyBaseinfo/related",
                type: "GET",
                data: {fromNo: fromNo},
                dataType: "json",
                success: function (result) {
                    if (result && result.code === 0) {
                        var secretList = result.data.secretList;
                        var patentList = result.data.patentList
                        // 填充专利列表
                        if ($.common.isNotEmpty(patentList)) {
                            $('#sameSourcePatentTable').bootstrapTable('load', patentList);
                            $('#sameSourcePatent').show();
                        } else {
                            $('#sameSourcePatent').hide();
                        }
                        // 填充技术秘密列表
                        if ($.common.isNotEmpty(secretList)) {
                            $('#sameSourceSecretTable').bootstrapTable('load', secretList);
                            $('#sameSourceSecret').show();
                        } else {
                            $('#sameSourceSecret').hide();
                        }
                    }
                },
                error: function () {
                }
            });
        }
    },


    /**
     * 查看专利受理信息（代理中信息）
     * @param applyId 申请ID
     */
    viewPatentInfo: function(applyId) {
        if (!applyId) {
            applyId = $("#applyId").val();
        }
        if (!applyId) {
            $.modal.alertWarning("申请ID不能为空！");
            return;
        }

        var url = ctx + "kizl/applyBaseinfo/viewPatentInfo/" + applyId;
        $.modal.openTab("专利受理信息查看", url);
    },

    /**
     * 查看授权通知信息
     * @param applyId 申请ID
     */
    viewAuthNoticeInfo: function(applyId) {
        if (!applyId) {
            applyId = $("#applyId").val();
        }
        if (!applyId) {
            $.modal.alertWarning("申请ID不能为空！");
            return;
        }

        var url = ctx + "kizl/applyBaseinfo/viewAuthNoticeInfo/" + applyId;
        $.modal.openTab("授权通知信息查看", url);
    },

    /**
     * 查看授权信息
     * @param applyId 申请ID
     */
    viewAuthInfo: function(applyId) {
        if (!applyId) {
            applyId = $("#applyId").val();
        }
        if (!applyId) {
            $.modal.alertWarning("申请ID不能为空！");
            return;
        }

        var url = ctx + "kizl/applyBaseinfo/viewAuthInfo/" + applyId;
        $.modal.openTab("授权信息查看", url);
    }
};

// 全局函数，保持向后兼容
function submitHandler(transitionKey) {
    KizlCommon.submitHandler(transitionKey);
}

function saveDraft() {
    KizlCommon.saveDraft();
}

function returnHandler () {
    KizlCommon.returnHandler();
}

// KIZLSB02专用全局函数
function toggleOpinionFields(opinion) {
    KizlCommon.toggleOpinionFields(opinion);
}

function selectExpert() {
    KizlCommon.selectExpert();
}

function selectExpertCallback(userCode, userName) {
    KizlCommon.selectExpertCallback(userCode, userName);
}

// 为了向后兼容，提供全局函数
function viewBasicInfo(applyId) {
    KizlCommon.viewBasicInfo(applyId);
}

function viewPatentInfo(applyId) {
    KizlCommon.viewPatentInfo(applyId);
}

function viewAuthNoticeInfo(applyId) {
    KizlCommon.viewAuthNoticeInfo(applyId);
}

function viewAuthInfo(applyId) {
    KizlCommon.viewAuthInfo(applyId);
}

// 事务所名称相关的全局函数
function loadSwsName(swsGuid, targetElementId, defaultText, callback) {
    KizlCommon.loadSwsName(swsGuid, targetElementId, defaultText, callback);
}

function autoLoadSwsName(swsGuidElementId, targetElementId, defaultText, callback) {
    KizlCommon.autoLoadSwsName(swsGuidElementId, targetElementId, defaultText, callback);
}

function initSwsNameDisplay() {
    KizlCommon.initSwsNameDisplay();
}