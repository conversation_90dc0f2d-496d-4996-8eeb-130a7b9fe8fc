/**
 * KIZL专利附件名称管理工具类
 * 用于动态获取和更新附件名称
 */
var KizlAttachmentUtils = {
    // 附件名称映射缓存
    attachmentNameMap: {},
    
    // 初始化附件名称映射
    init: function(callback) {
        $.ajax({
            url: ctx + "kizl/maintainAttachment/getAllAttachments",
            type: "GET",
            dataType: "json",
            success: function (result) {
                if (result.code === 0 && result.data) {
                    // 构建附件编码到附件名称的映射
                    KizlAttachmentUtils.attachmentNameMap = {};
                    $.each(result.data, function(index, attachment) {
                        if (attachment.attCode && attachment.attName) {
                            KizlAttachmentUtils.attachmentNameMap[attachment.attCode] = attachment.attName;
                        }
                    });
                    
                    // 执行回调函数
                    if (typeof callback === 'function') {
                        callback(KizlAttachmentUtils.attachmentNameMap);
                    }
                } else {
                    console.warn("获取附件名称映射失败：", result.msg);
                }
            },
            error: function () {
                console.error("获取附件名称映射失败");
            }
        });
    },
    
    // 根据附件编码获取附件名称
    getAttachmentName: function(attCode) {
        return this.attachmentNameMap[attCode] || '';
    },
    
    // 更新指定元素的附件标签
    updateLabel: function(elementId, attCode, suffix) {
        suffix = suffix || '：';
        var attachmentName = this.getAttachmentName(attCode);
        if (attachmentName) {
            $("#" + elementId).text(attachmentName + suffix);
        }
    },
    
    // 批量更新附件标签
    updateLabels: function(labelMappings) {
        var self = this;
        $.each(labelMappings, function(elementId, attCode) {
            self.updateLabel(elementId, attCode);
        });
    },
    
    // 预定义的常用附件标签映射
    commonLabelMappings: {
        // 申请页面
        'jsjds-label': 'jsjds',           // 技术交底书
        'gtsqxy-label': 'gtsqxy',         // 共同申请协议
        'rhxgfj-label': 'rhxgfj',         // 相关附件
        
        // 只读页面
        'readonly-jsjds-label': 'jsjds',
        'readonly-gtsqxy-label': 'gtsqxy', 
        'readonly-rhxgfj-label': 'rhxgfj',
        
        // 专利状态维护页面
        'ztwhfj-label': 'ztwhfj',         // 状态维护附件
        
        // 授权通知页面
        'sqtzxgfj-label': 'sqtzxgfj',     // 授权通知相关附件
        
        // 专利证书页面
        'zlzsfj-label': 'zlzsfj',         // 专利证书附件
        'sqxxxgfj-label': 'sqxxxgfj',     // 申请信息相关附件
        
        // 专利信息维护页面
        'patentInfoFj-label': 'patentInfoFj' // 专利信息附件
        
    },
    
    // 更新常用附件标签
    updateCommonLabels: function() {
        this.updateLabels(this.commonLabelMappings);
    },
    
    // 动态添加标签映射
    addLabelMapping: function(elementId, attCode) {
        this.commonLabelMappings[elementId] = attCode;
    },
    
    // 批量添加标签映射
    addLabelMappings: function(mappings) {
        var self = this;
        $.each(mappings, function(elementId, attCode) {
            self.addLabelMapping(elementId, attCode);
        });
    },
    
    // 为特定页面更新附件标签
    updatePageLabels: function(pageType) {
        var self = this;
        switch(pageType) {
            case 'apply':
                // 申请页面的附件标签
                self.updateLabels({
                    'jsjds-label': 'jsjds',
                    'gtsqxy-label': 'gtsqxy',
                    'rhxgfj-label': 'rhxgfj'
                });
                break;
            case 'readonly':
                // 只读页面的附件标签
                self.updateLabels({
                    'readonly-jsjds-label': 'jsjds',
                    'readonly-gtsqxy-label': 'gtsqxy',
                    'readonly-rhxgfj-label': 'rhxgfj'
                });
                break;
            case 'maintain':
                // 维护页面的附件标签
                self.updateLabels({
                    'ztwhfj-label': 'ztwhfj',
                    'sqtzxgfj-label': 'sqtzxgfj',
                    'zlzsfj-label': 'zlzsfj',
                    'sqxxxgfj-label': 'sqxxxgfj'
                });
                break;
            default:
                // 默认更新所有常用标签
                self.updateCommonLabels();
                break;
        }
    },
    
    // 刷新附件名称映射
    refresh: function(callback) {
        this.init(callback);
    },
    
    // 检查附件编码是否存在
    hasAttachmentCode: function(attCode) {
        return this.attachmentNameMap.hasOwnProperty(attCode);
    },
    
    // 获取所有附件编码
    getAllAttachmentCodes: function() {
        return Object.keys(this.attachmentNameMap);
    },
    
    // 获取所有附件名称
    getAllAttachmentNames: function() {
        return Object.values(this.attachmentNameMap);
    },
    
    // 根据附件名称查找附件编码
    getAttachmentCodeByName: function(attName) {
        for (var code in this.attachmentNameMap) {
            if (this.attachmentNameMap[code] === attName) {
                return code;
            }
        }
        return '';
    }
}; 