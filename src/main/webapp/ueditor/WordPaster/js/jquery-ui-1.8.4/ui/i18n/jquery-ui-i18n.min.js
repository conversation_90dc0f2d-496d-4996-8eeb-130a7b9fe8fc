jQuery(function(a){a.datepicker.regional.af={closeText:"Se<PERSON><PERSON><PERSON>",prevText:"Vorige",nextText:"Volgende",currentText:"Vandag",monthNames:["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>","September","<PERSON><PERSON><PERSON>","November","<PERSON><PERSON><PERSON>"],monthNamesShort:["<PERSON>","Feb","<PERSON>t","<PERSON>","<PERSON>","<PERSON>","Jul","Aug","Sep","Okt","Nov","Des"],dayNames:["Sondag","Maandag","Dinsdag","Woensdag","Donderdag","Vrydag","Saterdag"],dayNamesShort:["Son","Maa","<PERSON>","Woe","<PERSON>","<PERSON><PERSON>","Sat"],dayNamesMin:["So",
"<PERSON>","<PERSON>","W<PERSON>","<PERSON>","Vr","<PERSON>"],weekHeader:"Wk",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.af)});
jQuery(function(a){a.datepicker.regional.ar={closeText:"\u0625\u063a\u0644\u0627\u0642",prevText:"&#x3c;\u0627\u0644\u0633\u0627\u0628\u0642",nextText:"\u0627\u0644\u062a\u0627\u0644\u064a&#x3e;",currentText:"\u0627\u0644\u064a\u0648\u0645",monthNames:["\u0643\u0627\u0646\u0648\u0646 \u0627\u0644\u062b\u0627\u0646\u064a","\u0634\u0628\u0627\u0637","\u0622\u0630\u0627\u0631","\u0646\u064a\u0633\u0627\u0646","\u0622\u0630\u0627\u0631","\u062d\u0632\u064a\u0631\u0627\u0646","\u062a\u0645\u0648\u0632",
"\u0622\u0628","\u0623\u064a\u0644\u0648\u0644","\u062a\u0634\u0631\u064a\u0646 \u0627\u0644\u0623\u0648\u0644","\u062a\u0634\u0631\u064a\u0646 \u0627\u0644\u062b\u0627\u0646\u064a","\u0643\u0627\u0646\u0648\u0646 \u0627\u0644\u0623\u0648\u0644"],monthNamesShort:["1","2","3","4","5","6","7","8","9","10","11","12"],dayNames:["\u0627\u0644\u0633\u0628\u062a","\u0627\u0644\u0623\u062d\u062f","\u0627\u0644\u0627\u062b\u0646\u064a\u0646","\u0627\u0644\u062b\u0644\u0627\u062b\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621",
"\u0627\u0644\u062e\u0645\u064a\u0633","\u0627\u0644\u062c\u0645\u0639\u0629"],dayNamesShort:["\u0633\u0628\u062a","\u0623\u062d\u062f","\u0627\u062b\u0646\u064a\u0646","\u062b\u0644\u0627\u062b\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062e\u0645\u064a\u0633","\u062c\u0645\u0639\u0629"],dayNamesMin:["\u0633\u0628\u062a","\u0623\u062d\u062f","\u0627\u062b\u0646\u064a\u0646","\u062b\u0644\u0627\u062b\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062e\u0645\u064a\u0633","\u062c\u0645\u0639\u0629"],
weekHeader:"\u0623\u0633\u0628\u0648\u0639",dateFormat:"dd/mm/yy",firstDay:0,isRTL:true,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.ar)});
jQuery(function(a){a.datepicker.regional.az={closeText:"Ba\u011fla",prevText:"&#x3c;Geri",nextText:"\u0130r\u0259li&#x3e;",currentText:"Bug\u00fcn",monthNames:["Yanvar","Fevral","Mart","Aprel","May","\u0130yun","\u0130yul","Avqust","Sentyabr","Oktyabr","Noyabr","Dekabr"],monthNamesShort:["Yan","Fev","Mar","Apr","May","\u0130yun","\u0130yul","Avq","Sen","Okt","Noy","Dek"],dayNames:["Bazar","Bazar ert\u0259si","\u00c7\u0259r\u015f\u0259nb\u0259 ax\u015fam\u0131","\u00c7\u0259r\u015f\u0259nb\u0259",
"C\u00fcm\u0259 ax\u015fam\u0131","C\u00fcm\u0259","\u015e\u0259nb\u0259"],dayNamesShort:["B","Be","\u00c7a","\u00c7","Ca","C","\u015e"],dayNamesMin:["B","B","\u00c7","\u0421","\u00c7","C","\u015e"],weekHeader:"Hf",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.az)});
jQuery(function(a){a.datepicker.regional.bg={closeText:"\u0437\u0430\u0442\u0432\u043e\u0440\u0438",prevText:"&#x3c;\u043d\u0430\u0437\u0430\u0434",nextText:"\u043d\u0430\u043f\u0440\u0435\u0434&#x3e;",nextBigText:"&#x3e;&#x3e;",currentText:"\u0434\u043d\u0435\u0441",monthNames:["\u042f\u043d\u0443\u0430\u0440\u0438","\u0424\u0435\u0432\u0440\u0443\u0430\u0440\u0438","\u041c\u0430\u0440\u0442","\u0410\u043f\u0440\u0438\u043b","\u041c\u0430\u0439","\u042e\u043d\u0438","\u042e\u043b\u0438","\u0410\u0432\u0433\u0443\u0441\u0442",
"\u0421\u0435\u043f\u0442\u0435\u043c\u0432\u0440\u0438","\u041e\u043a\u0442\u043e\u043c\u0432\u0440\u0438","\u041d\u043e\u0435\u043c\u0432\u0440\u0438","\u0414\u0435\u043a\u0435\u043c\u0432\u0440\u0438"],monthNamesShort:["\u042f\u043d\u0443","\u0424\u0435\u0432","\u041c\u0430\u0440","\u0410\u043f\u0440","\u041c\u0430\u0439","\u042e\u043d\u0438","\u042e\u043b\u0438","\u0410\u0432\u0433","\u0421\u0435\u043f","\u041e\u043a\u0442","\u041d\u043e\u0432","\u0414\u0435\u043a"],dayNames:["\u041d\u0435\u0434\u0435\u043b\u044f",
"\u041f\u043e\u043d\u0435\u0434\u0435\u043b\u043d\u0438\u043a","\u0412\u0442\u043e\u0440\u043d\u0438\u043a","\u0421\u0440\u044f\u0434\u0430","\u0427\u0435\u0442\u0432\u044a\u0440\u0442\u044a\u043a","\u041f\u0435\u0442\u044a\u043a","\u0421\u044a\u0431\u043e\u0442\u0430"],dayNamesShort:["\u041d\u0435\u0434","\u041f\u043e\u043d","\u0412\u0442\u043e","\u0421\u0440\u044f","\u0427\u0435\u0442","\u041f\u0435\u0442","\u0421\u044a\u0431"],dayNamesMin:["\u041d\u0435","\u041f\u043e","\u0412\u0442","\u0421\u0440",
"\u0427\u0435","\u041f\u0435","\u0421\u044a"],weekHeader:"Wk",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.bg)});
jQuery(function(a){a.datepicker.regional.bs={closeText:"Zatvori",prevText:"&#x3c;",nextText:"&#x3e;",currentText:"Danas",monthNames:["Januar","Februar","Mart","April","Maj","Juni","Juli","August","Septembar","Oktobar","Novembar","Decembar"],monthNamesShort:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Aug","Sep","Okt","Nov","Dec"],dayNames:["Nedelja","Ponedeljak","Utorak","Srijeda","\u010cetvrtak","Petak","Subota"],dayNamesShort:["Ned","Pon","Uto","Sri","\u010cet","Pet","Sub"],dayNamesMin:["Ne","Po",
"Ut","Sr","\u010ce","Pe","Su"],weekHeader:"Wk",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.bs)});
jQuery(function(a){a.datepicker.regional.ca={closeText:"Tancar",prevText:"&#x3c;Ant",nextText:"Seg&#x3e;",currentText:"Avui",monthNames:["Gener","Febrer","Mar&ccedil;","Abril","Maig","Juny","Juliol","Agost","Setembre","Octubre","Novembre","Desembre"],monthNamesShort:["Gen","Feb","Mar","Abr","Mai","Jun","Jul","Ago","Set","Oct","Nov","Des"],dayNames:["Diumenge","Dilluns","Dimarts","Dimecres","Dijous","Divendres","Dissabte"],dayNamesShort:["Dug","Dln","Dmt","Dmc","Djs","Dvn","Dsb"],dayNamesMin:["Dg",
"Dl","Dt","Dc","Dj","Dv","Ds"],weekHeader:"Sm",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.ca)});
jQuery(function(a){a.datepicker.regional.cs={closeText:"Zav\u0159\u00edt",prevText:"&#x3c;D\u0159\u00edve",nextText:"Pozd\u011bji&#x3e;",currentText:"Nyn\u00ed",monthNames:["leden","\u00fanor","b\u0159ezen","duben","kv\u011bten","\u010derven","\u010dervenec","srpen","z\u00e1\u0159\u00ed","\u0159\u00edjen","listopad","prosinec"],monthNamesShort:["led","\u00fano","b\u0159e","dub","kv\u011b","\u010der","\u010dvc","srp","z\u00e1\u0159","\u0159\u00edj","lis","pro"],dayNames:["ned\u011ble","pond\u011bl\u00ed",
"\u00fater\u00fd","st\u0159eda","\u010dtvrtek","p\u00e1tek","sobota"],dayNamesShort:["ne","po","\u00fat","st","\u010dt","p\u00e1","so"],dayNamesMin:["ne","po","\u00fat","st","\u010dt","p\u00e1","so"],weekHeader:"T\u00fdd",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.cs)});
jQuery(function(a){a.datepicker.regional.da={closeText:"Luk",prevText:"&#x3c;Forrige",nextText:"N\u00e6ste&#x3e;",currentText:"Idag",monthNames:["Januar","Februar","Marts","April","Maj","Juni","Juli","August","September","Oktober","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Aug","Sep","Okt","Nov","Dec"],dayNames:["S\u00f8ndag","Mandag","Tirsdag","Onsdag","Torsdag","Fredag","L\u00f8rdag"],dayNamesShort:["S\u00f8n","Man","Tir","Ons","Tor","Fre","L\u00f8r"],dayNamesMin:["S\u00f8",
"Ma","Ti","On","To","Fr","L\u00f8"],weekHeader:"Uge",dateFormat:"dd-mm-yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.da)});
jQuery(function(a){a.datepicker.regional.de={closeText:"schlie\u00dfen",prevText:"&#x3c;zur\u00fcck",nextText:"Vor&#x3e;",currentText:"heute",monthNames:["Januar","Februar","M\u00e4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],monthNamesShort:["Jan","Feb","M\u00e4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],dayNames:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],dayNamesShort:["So","Mo","Di","Mi","Do","Fr","Sa"],dayNamesMin:["So",
"Mo","Di","Mi","Do","Fr","Sa"],weekHeader:"Wo",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.de)});
jQuery(function(a){a.datepicker.regional.el={closeText:"\u039a\u03bb\u03b5\u03af\u03c3\u03b9\u03bc\u03bf",prevText:"\u03a0\u03c1\u03bf\u03b7\u03b3\u03bf\u03cd\u03bc\u03b5\u03bd\u03bf\u03c2",nextText:"\u0395\u03c0\u03cc\u03bc\u03b5\u03bd\u03bf\u03c2",currentText:"\u03a4\u03c1\u03ad\u03c7\u03c9\u03bd \u039c\u03ae\u03bd\u03b1\u03c2",monthNames:["\u0399\u03b1\u03bd\u03bf\u03c5\u03ac\u03c1\u03b9\u03bf\u03c2","\u03a6\u03b5\u03b2\u03c1\u03bf\u03c5\u03ac\u03c1\u03b9\u03bf\u03c2","\u039c\u03ac\u03c1\u03c4\u03b9\u03bf\u03c2",
"\u0391\u03c0\u03c1\u03af\u03bb\u03b9\u03bf\u03c2","\u039c\u03ac\u03b9\u03bf\u03c2","\u0399\u03bf\u03cd\u03bd\u03b9\u03bf\u03c2","\u0399\u03bf\u03cd\u03bb\u03b9\u03bf\u03c2","\u0391\u03cd\u03b3\u03bf\u03c5\u03c3\u03c4\u03bf\u03c2","\u03a3\u03b5\u03c0\u03c4\u03ad\u03bc\u03b2\u03c1\u03b9\u03bf\u03c2","\u039f\u03ba\u03c4\u03ce\u03b2\u03c1\u03b9\u03bf\u03c2","\u039d\u03bf\u03ad\u03bc\u03b2\u03c1\u03b9\u03bf\u03c2","\u0394\u03b5\u03ba\u03ad\u03bc\u03b2\u03c1\u03b9\u03bf\u03c2"],monthNamesShort:["\u0399\u03b1\u03bd",
"\u03a6\u03b5\u03b2","\u039c\u03b1\u03c1","\u0391\u03c0\u03c1","\u039c\u03b1\u03b9","\u0399\u03bf\u03c5\u03bd","\u0399\u03bf\u03c5\u03bb","\u0391\u03c5\u03b3","\u03a3\u03b5\u03c0","\u039f\u03ba\u03c4","\u039d\u03bf\u03b5","\u0394\u03b5\u03ba"],dayNames:["\u039a\u03c5\u03c1\u03b9\u03b1\u03ba\u03ae","\u0394\u03b5\u03c5\u03c4\u03ad\u03c1\u03b1","\u03a4\u03c1\u03af\u03c4\u03b7","\u03a4\u03b5\u03c4\u03ac\u03c1\u03c4\u03b7","\u03a0\u03ad\u03bc\u03c0\u03c4\u03b7","\u03a0\u03b1\u03c1\u03b1\u03c3\u03ba\u03b5\u03c5\u03ae",
"\u03a3\u03ac\u03b2\u03b2\u03b1\u03c4\u03bf"],dayNamesShort:["\u039a\u03c5\u03c1","\u0394\u03b5\u03c5","\u03a4\u03c1\u03b9","\u03a4\u03b5\u03c4","\u03a0\u03b5\u03bc","\u03a0\u03b1\u03c1","\u03a3\u03b1\u03b2"],dayNamesMin:["\u039a\u03c5","\u0394\u03b5","\u03a4\u03c1","\u03a4\u03b5","\u03a0\u03b5","\u03a0\u03b1","\u03a3\u03b1"],weekHeader:"\u0395\u03b2\u03b4",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.el)});
jQuery(function(a){a.datepicker.regional["en-GB"]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu",
"We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional["en-GB"])});
jQuery(function(a){a.datepicker.regional.eo={closeText:"Fermi",prevText:"&lt;Anta",nextText:"Sekv&gt;",currentText:"Nuna",monthNames:["Januaro","Februaro","Marto","Aprilo","Majo","Junio","Julio","A\u016dgusto","Septembro","Oktobro","Novembro","Decembro"],monthNamesShort:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","A\u016dg","Sep","Okt","Nov","Dec"],dayNames:["Diman\u0109o","Lundo","Mardo","Merkredo","\u0134a\u016ddo","Vendredo","Sabato"],dayNamesShort:["Dim","Lun","Mar","Mer","\u0134a\u016d","Ven",
"Sab"],dayNamesMin:["Di","Lu","Ma","Me","\u0134a","Ve","Sa"],weekHeader:"Sb",dateFormat:"dd/mm/yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.eo)});
jQuery(function(a){a.datepicker.regional.es={closeText:"Cerrar",prevText:"&#x3c;Ant",nextText:"Sig&#x3e;",currentText:"Hoy",monthNames:["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"],monthNamesShort:["Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","Sep","Oct","Nov","Dic"],dayNames:["Domingo","Lunes","Martes","Mi&eacute;rcoles","Jueves","Viernes","S&aacute;bado"],dayNamesShort:["Dom","Lun","Mar","Mi&eacute;","Juv","Vie","S&aacute;b"],
dayNamesMin:["Do","Lu","Ma","Mi","Ju","Vi","S&aacute;"],weekHeader:"Sm",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.es)});
jQuery(function(a){a.datepicker.regional.et={closeText:"Sulge",prevText:"Eelnev",nextText:"J\u00e4rgnev",currentText:"T\u00e4na",monthNames:["Jaanuar","Veebruar","M\u00e4rts","Aprill","Mai","Juuni","Juuli","August","September","Oktoober","November","Detsember"],monthNamesShort:["Jaan","Veebr","M\u00e4rts","Apr","Mai","Juuni","Juuli","Aug","Sept","Okt","Nov","Dets"],dayNames:["P\u00fchap\u00e4ev","Esmasp\u00e4ev","Teisip\u00e4ev","Kolmap\u00e4ev","Neljap\u00e4ev","Reede","Laup\u00e4ev"],dayNamesShort:["P\u00fchap",
"Esmasp","Teisip","Kolmap","Neljap","Reede","Laup"],dayNamesMin:["P","E","T","K","N","R","L"],weekHeader:"Sm",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.et)});
jQuery(function(a){a.datepicker.regional.eu={closeText:"Egina",prevText:"&#x3c;Aur",nextText:"Hur&#x3e;",currentText:"Gaur",monthNames:["Urtarrila","Otsaila","Martxoa","Apirila","Maiatza","Ekaina","Uztaila","Abuztua","Iraila","Urria","Azaroa","Abendua"],monthNamesShort:["Urt","Ots","Mar","Api","Mai","Eka","Uzt","Abu","Ira","Urr","Aza","Abe"],dayNames:["Igandea","Astelehena","Asteartea","Asteazkena","Osteguna","Ostirala","Larunbata"],dayNamesShort:["Iga","Ast","Ast","Ast","Ost","Ost","Lar"],dayNamesMin:["Ig",
"As","As","As","Os","Os","La"],weekHeader:"Wk",dateFormat:"yy/mm/dd",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.eu)});
jQuery(function(a){a.datepicker.regional.fa={closeText:"\u0628\u0633\u062a\u0646",prevText:"&#x3c;\u0642\u0628\u0644\u064a",nextText:"\u0628\u0639\u062f\u064a&#x3e;",currentText:"\u0627\u0645\u0631\u0648\u0632",monthNames:["\u0641\u0631\u0648\u0631\u062f\u064a\u0646","\u0627\u0631\u062f\u064a\u0628\u0647\u0634\u062a","\u062e\u0631\u062f\u0627\u062f","\u062a\u064a\u0631","\u0645\u0631\u062f\u0627\u062f","\u0634\u0647\u0631\u064a\u0648\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627\u0646","\u0622\u0630\u0631",
"\u062f\u064a","\u0628\u0647\u0645\u0646","\u0627\u0633\u0641\u0646\u062f"],monthNamesShort:["1","2","3","4","5","6","7","8","9","10","11","12"],dayNames:["\u064a\u06a9\u0634\u0646\u0628\u0647","\u062f\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067e\u0646\u062c\u0634\u0646\u0628\u0647","\u062c\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"],dayNamesShort:["\u064a","\u062f","\u0633","\u0686","\u067e","\u062c","\u0634"],
dayNamesMin:["\u064a","\u062f","\u0633","\u0686","\u067e","\u062c","\u0634"],weekHeader:"\u0647\u0641",dateFormat:"yy/mm/dd",firstDay:6,isRTL:true,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.fa)});
jQuery(function(a){a.datepicker.regional.fi={closeText:"Sulje",prevText:"&laquo;Edellinen",nextText:"Seuraava&raquo;",currentText:"T&auml;n&auml;&auml;n",monthNames:["Tammikuu","Helmikuu","Maaliskuu","Huhtikuu","Toukokuu","Kes&auml;kuu","Hein&auml;kuu","Elokuu","Syyskuu","Lokakuu","Marraskuu","Joulukuu"],monthNamesShort:["Tammi","Helmi","Maalis","Huhti","Touko","Kes&auml;","Hein&auml;","Elo","Syys","Loka","Marras","Joulu"],dayNamesShort:["Su","Ma","Ti","Ke","To","Pe","Su"],dayNames:["Sunnuntai","Maanantai",
"Tiistai","Keskiviikko","Torstai","Perjantai","Lauantai"],dayNamesMin:["Su","Ma","Ti","Ke","To","Pe","La"],weekHeader:"Vk",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.fi)});
jQuery(function(a){a.datepicker.regional.fo={closeText:"Lat aftur",prevText:"&#x3c;Fyrra",nextText:"N\u00e6sta&#x3e;",currentText:"\u00cd dag",monthNames:["Januar","Februar","Mars","Apr\u00edl","Mei","Juni","Juli","August","September","Oktober","November","Desember"],monthNamesShort:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Aug","Sep","Okt","Nov","Des"],dayNames:["Sunnudagur","M\u00e1nadagur","T\u00fdsdagur","Mikudagur","H\u00f3sdagur","Fr\u00edggjadagur","Leyardagur"],dayNamesShort:["Sun","M\u00e1n",
"T\u00fds","Mik","H\u00f3s","Fr\u00ed","Ley"],dayNamesMin:["Su","M\u00e1","T\u00fd","Mi","H\u00f3","Fr","Le"],weekHeader:"Vk",dateFormat:"dd-mm-yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.fo)});
jQuery(function(a){a.datepicker.regional["fr-CH"]={closeText:"Fermer",prevText:"&#x3c;Pr\u00e9c",nextText:"Suiv&#x3e;",currentText:"Courant",monthNames:["Janvier","F\u00e9vrier","Mars","Avril","Mai","Juin","Juillet","Ao\u00fbt","Septembre","Octobre","Novembre","D\u00e9cembre"],monthNamesShort:["Jan","F\u00e9v","Mar","Avr","Mai","Jun","Jul","Ao\u00fb","Sep","Oct","Nov","D\u00e9c"],dayNames:["Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi"],dayNamesShort:["Dim","Lun","Mar","Mer","Jeu",
"Ven","Sam"],dayNamesMin:["Di","Lu","Ma","Me","Je","Ve","Sa"],weekHeader:"Sm",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional["fr-CH"])});
jQuery(function(a){a.datepicker.regional.fr={closeText:"Fermer",prevText:"&#x3c;Pr\u00e9c",nextText:"Suiv&#x3e;",currentText:"Courant",monthNames:["Janvier","F\u00e9vrier","Mars","Avril","Mai","Juin","Juillet","Ao\u00fbt","Septembre","Octobre","Novembre","D\u00e9cembre"],monthNamesShort:["Jan","F\u00e9v","Mar","Avr","Mai","Jun","Jul","Ao\u00fb","Sep","Oct","Nov","D\u00e9c"],dayNames:["Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi"],dayNamesShort:["Dim","Lun","Mar","Mer","Jeu","Ven",
"Sam"],dayNamesMin:["Di","Lu","Ma","Me","Je","Ve","Sa"],weekHeader:"Sm",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.fr)});
jQuery(function(a){a.datepicker.regional.he={closeText:"\u05e1\u05d2\u05d5\u05e8",prevText:"&#x3c;\u05d4\u05e7\u05d5\u05d3\u05dd",nextText:"\u05d4\u05d1\u05d0&#x3e;",currentText:"\u05d4\u05d9\u05d5\u05dd",monthNames:["\u05d9\u05e0\u05d5\u05d0\u05e8","\u05e4\u05d1\u05e8\u05d5\u05d0\u05e8","\u05de\u05e8\u05e5","\u05d0\u05e4\u05e8\u05d9\u05dc","\u05de\u05d0\u05d9","\u05d9\u05d5\u05e0\u05d9","\u05d9\u05d5\u05dc\u05d9","\u05d0\u05d5\u05d2\u05d5\u05e1\u05d8","\u05e1\u05e4\u05d8\u05de\u05d1\u05e8","\u05d0\u05d5\u05e7\u05d8\u05d5\u05d1\u05e8",
"\u05e0\u05d5\u05d1\u05de\u05d1\u05e8","\u05d3\u05e6\u05de\u05d1\u05e8"],monthNamesShort:["1","2","3","4","5","6","7","8","9","10","11","12"],dayNames:["\u05e8\u05d0\u05e9\u05d5\u05df","\u05e9\u05e0\u05d9","\u05e9\u05dc\u05d9\u05e9\u05d9","\u05e8\u05d1\u05d9\u05e2\u05d9","\u05d7\u05de\u05d9\u05e9\u05d9","\u05e9\u05d9\u05e9\u05d9","\u05e9\u05d1\u05ea"],dayNamesShort:["\u05d0'","\u05d1'","\u05d2'","\u05d3'","\u05d4'","\u05d5'","\u05e9\u05d1\u05ea"],dayNamesMin:["\u05d0'","\u05d1'","\u05d2'","\u05d3'",
"\u05d4'","\u05d5'","\u05e9\u05d1\u05ea"],weekHeader:"Wk",dateFormat:"dd/mm/yy",firstDay:0,isRTL:true,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.he)});
jQuery(function(a){a.datepicker.regional.hr={closeText:"Zatvori",prevText:"&#x3c;",nextText:"&#x3e;",currentText:"Danas",monthNames:["Sije\u010danj","Velja\u010da","O\u017eujak","Travanj","Svibanj","Lipanj","Srpanj","Kolovoz","Rujan","Listopad","Studeni","Prosinac"],monthNamesShort:["Sij","Velj","O\u017eu","Tra","Svi","Lip","Srp","Kol","Ruj","Lis","Stu","Pro"],dayNames:["Nedjelja","Ponedjeljak","Utorak","Srijeda","\u010cetvrtak","Petak","Subota"],dayNamesShort:["Ned","Pon","Uto","Sri","\u010cet",
"Pet","Sub"],dayNamesMin:["Ne","Po","Ut","Sr","\u010ce","Pe","Su"],weekHeader:"Tje",dateFormat:"dd.mm.yy.",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.hr)});
jQuery(function(a){a.datepicker.regional.hu={closeText:"bez\u00e1r\u00e1s",prevText:"&laquo;&nbsp;vissza",nextText:"el\u0151re&nbsp;&raquo;",currentText:"ma",monthNames:["Janu\u00e1r","Febru\u00e1r","M\u00e1rcius","\u00c1prilis","M\u00e1jus","J\u00fanius","J\u00falius","Augusztus","Szeptember","Okt\u00f3ber","November","December"],monthNamesShort:["Jan","Feb","M\u00e1r","\u00c1pr","M\u00e1j","J\u00fan","J\u00fal","Aug","Szep","Okt","Nov","Dec"],dayNames:["Vas\u00e1rnap","H\u00e9tf\u00f6","Kedd","Szerda",
"Cs\u00fct\u00f6rt\u00f6k","P\u00e9ntek","Szombat"],dayNamesShort:["Vas","H\u00e9t","Ked","Sze","Cs\u00fc","P\u00e9n","Szo"],dayNamesMin:["V","H","K","Sze","Cs","P","Szo"],weekHeader:"H\u00e9",dateFormat:"yy-mm-dd",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.hu)});
jQuery(function(a){a.datepicker.regional.hy={closeText:"\u0553\u0561\u056f\u0565\u056c",prevText:"&#x3c;\u0546\u0561\u056d.",nextText:"\u0540\u0561\u057b.&#x3e;",currentText:"\u0531\u0575\u057d\u0585\u0580",monthNames:["\u0540\u0578\u0582\u0576\u057e\u0561\u0580","\u0553\u0565\u057f\u0580\u057e\u0561\u0580","\u0544\u0561\u0580\u057f","\u0531\u057a\u0580\u056b\u056c","\u0544\u0561\u0575\u056b\u057d","\u0540\u0578\u0582\u0576\u056b\u057d","\u0540\u0578\u0582\u056c\u056b\u057d","\u0555\u0563\u0578\u057d\u057f\u0578\u057d",
"\u054d\u0565\u057a\u057f\u0565\u0574\u0562\u0565\u0580","\u0540\u0578\u056f\u057f\u0565\u0574\u0562\u0565\u0580","\u0546\u0578\u0575\u0565\u0574\u0562\u0565\u0580","\u0534\u0565\u056f\u057f\u0565\u0574\u0562\u0565\u0580"],monthNamesShort:["\u0540\u0578\u0582\u0576\u057e","\u0553\u0565\u057f\u0580","\u0544\u0561\u0580\u057f","\u0531\u057a\u0580","\u0544\u0561\u0575\u056b\u057d","\u0540\u0578\u0582\u0576\u056b\u057d","\u0540\u0578\u0582\u056c","\u0555\u0563\u057d","\u054d\u0565\u057a","\u0540\u0578\u056f",
"\u0546\u0578\u0575","\u0534\u0565\u056f"],dayNames:["\u056f\u056b\u0580\u0561\u056f\u056b","\u0565\u056f\u0578\u0582\u0577\u0561\u0562\u0569\u056b","\u0565\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056b","\u0579\u0578\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056b","\u0570\u056b\u0576\u0563\u0577\u0561\u0562\u0569\u056b","\u0578\u0582\u0580\u0562\u0561\u0569","\u0577\u0561\u0562\u0561\u0569"],dayNamesShort:["\u056f\u056b\u0580","\u0565\u0580\u056f","\u0565\u0580\u0584","\u0579\u0580\u0584","\u0570\u0576\u0563",
"\u0578\u0582\u0580\u0562","\u0577\u0562\u0569"],dayNamesMin:["\u056f\u056b\u0580","\u0565\u0580\u056f","\u0565\u0580\u0584","\u0579\u0580\u0584","\u0570\u0576\u0563","\u0578\u0582\u0580\u0562","\u0577\u0562\u0569"],weekHeader:"\u0547\u0532\u054f",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.hy)});
jQuery(function(a){a.datepicker.regional.id={closeText:"Tutup",prevText:"&#x3c;mundur",nextText:"maju&#x3e;",currentText:"hari ini",monthNames:["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","Nopember","Desember"],monthNamesShort:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agus","Sep","Okt","Nop","Des"],dayNames:["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],dayNamesShort:["Min","Sen","Sel","Rab","kam","Jum","Sab"],dayNamesMin:["Mg","Sn","Sl",
"Rb","Km","jm","Sb"],weekHeader:"Mg",dateFormat:"dd/mm/yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.id)});
jQuery(function(a){a.datepicker.regional.is={closeText:"Loka",prevText:"&#x3c; Fyrri",nextText:"N&aelig;sti &#x3e;",currentText:"&Iacute; dag",monthNames:["Jan&uacute;ar","Febr&uacute;ar","Mars","Apr&iacute;l","Ma&iacute","J&uacute;n&iacute;","J&uacute;l&iacute;","&Aacute;g&uacute;st","September","Okt&oacute;ber","N&oacute;vember","Desember"],monthNamesShort:["Jan","Feb","Mar","Apr","Ma&iacute;","J&uacute;n","J&uacute;l","&Aacute;g&uacute;","Sep","Okt","N&oacute;v","Des"],dayNames:["Sunnudagur","M&aacute;nudagur",
"&THORN;ri&eth;judagur","Mi&eth;vikudagur","Fimmtudagur","F&ouml;studagur","Laugardagur"],dayNamesShort:["Sun","M&aacute;n","&THORN;ri","Mi&eth;","Fim","F&ouml;s","Lau"],dayNamesMin:["Su","M&aacute;","&THORN;r","Mi","Fi","F&ouml;","La"],weekHeader:"Vika",dateFormat:"dd/mm/yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.is)});
jQuery(function(a){a.datepicker.regional.it={closeText:"Chiudi",prevText:"&#x3c;Prec",nextText:"Succ&#x3e;",currentText:"Oggi",monthNames:["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],monthNamesShort:["Gen","Feb","Mar","Apr","Mag","Giu","Lug","Ago","Set","Ott","Nov","Dic"],dayNames:["Domenica","Luned&#236","Marted&#236","Mercoled&#236","Gioved&#236","Venerd&#236","Sabato"],dayNamesShort:["Dom","Lun","Mar","Mer","Gio","Ven",
"Sab"],dayNamesMin:["Do","Lu","Ma","Me","Gi","Ve","Sa"],weekHeader:"Sm",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.it)});
jQuery(function(a){a.datepicker.regional.ja={closeText:"\u9589\u3058\u308b",prevText:"&#x3c;\u524d",nextText:"\u6b21&#x3e;",currentText:"\u4eca\u65e5",monthNames:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],monthNamesShort:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],dayNames:["\u65e5\u66dc\u65e5","\u6708\u66dc\u65e5","\u706b\u66dc\u65e5",
"\u6c34\u66dc\u65e5","\u6728\u66dc\u65e5","\u91d1\u66dc\u65e5","\u571f\u66dc\u65e5"],dayNamesShort:["\u65e5","\u6708","\u706b","\u6c34","\u6728","\u91d1","\u571f"],dayNamesMin:["\u65e5","\u6708","\u706b","\u6c34","\u6728","\u91d1","\u571f"],weekHeader:"\u9031",dateFormat:"yy/mm/dd",firstDay:0,isRTL:false,showMonthAfterYear:true,yearSuffix:"\u5e74"};a.datepicker.setDefaults(a.datepicker.regional.ja)});
jQuery(function(a){a.datepicker.regional.ko={closeText:"\ub2eb\uae30",prevText:"\uc774\uc804\ub2ec",nextText:"\ub2e4\uc74c\ub2ec",currentText:"\uc624\ub298",monthNames:["1\uc6d4(JAN)","2\uc6d4(FEB)","3\uc6d4(MAR)","4\uc6d4(APR)","5\uc6d4(MAY)","6\uc6d4(JUN)","7\uc6d4(JUL)","8\uc6d4(AUG)","9\uc6d4(SEP)","10\uc6d4(OCT)","11\uc6d4(NOV)","12\uc6d4(DEC)"],monthNamesShort:["1\uc6d4(JAN)","2\uc6d4(FEB)","3\uc6d4(MAR)","4\uc6d4(APR)","5\uc6d4(MAY)","6\uc6d4(JUN)","7\uc6d4(JUL)","8\uc6d4(AUG)","9\uc6d4(SEP)",
"10\uc6d4(OCT)","11\uc6d4(NOV)","12\uc6d4(DEC)"],dayNames:["\uc77c","\uc6d4","\ud654","\uc218","\ubaa9","\uae08","\ud1a0"],dayNamesShort:["\uc77c","\uc6d4","\ud654","\uc218","\ubaa9","\uae08","\ud1a0"],dayNamesMin:["\uc77c","\uc6d4","\ud654","\uc218","\ubaa9","\uae08","\ud1a0"],weekHeader:"Wk",dateFormat:"yy-mm-dd",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:"\ub144"};a.datepicker.setDefaults(a.datepicker.regional.ko)});
jQuery(function(a){a.datepicker.regional.lt={closeText:"U\u017edaryti",prevText:"&#x3c;Atgal",nextText:"Pirmyn&#x3e;",currentText:"\u0160iandien",monthNames:["Sausis","Vasaris","Kovas","Balandis","Gegu\u017e\u0117","Bir\u017eelis","Liepa","Rugpj\u016btis","Rugs\u0117jis","Spalis","Lapkritis","Gruodis"],monthNamesShort:["Sau","Vas","Kov","Bal","Geg","Bir","Lie","Rugp","Rugs","Spa","Lap","Gru"],dayNames:["sekmadienis","pirmadienis","antradienis","tre\u010diadienis","ketvirtadienis","penktadienis","\u0161e\u0161tadienis"],
dayNamesShort:["sek","pir","ant","tre","ket","pen","\u0161e\u0161"],dayNamesMin:["Se","Pr","An","Tr","Ke","Pe","\u0160e"],weekHeader:"Wk",dateFormat:"yy-mm-dd",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.lt)});
jQuery(function(a){a.datepicker.regional.lv={closeText:"Aizv\u0113rt",prevText:"Iepr",nextText:"N\u0101ka",currentText:"\u0160odien",monthNames:["Janv\u0101ris","Febru\u0101ris","Marts","Apr\u012blis","Maijs","J\u016bnijs","J\u016blijs","Augusts","Septembris","Oktobris","Novembris","Decembris"],monthNamesShort:["Jan","Feb","Mar","Apr","Mai","J\u016bn","J\u016bl","Aug","Sep","Okt","Nov","Dec"],dayNames:["sv\u0113tdiena","pirmdiena","otrdiena","tre\u0161diena","ceturtdiena","piektdiena","sestdiena"],
dayNamesShort:["svt","prm","otr","tre","ctr","pkt","sst"],dayNamesMin:["Sv","Pr","Ot","Tr","Ct","Pk","Ss"],weekHeader:"Nav",dateFormat:"dd-mm-yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.lv)});
jQuery(function(a){a.datepicker.regional.ms={closeText:"Tutup",prevText:"&#x3c;Sebelum",nextText:"Selepas&#x3e;",currentText:"hari ini",monthNames:["Januari","Februari","Mac","April","Mei","Jun","Julai","Ogos","September","Oktober","November","Disember"],monthNamesShort:["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogo","Sep","Okt","Nov","Dis"],dayNames:["Ahad","Isnin","Selasa","Rabu","Khamis","Jumaat","Sabtu"],dayNamesShort:["Aha","Isn","Sel","Rab","kha","Jum","Sab"],dayNamesMin:["Ah","Is","Se","Ra",
"Kh","Ju","Sa"],weekHeader:"Mg",dateFormat:"dd/mm/yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.ms)});
jQuery(function(a){a.datepicker.regional.nl={closeText:"Sluiten",prevText:"\u2190",nextText:"\u2192",currentText:"Vandaag",monthNames:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"],monthNamesShort:["jan","feb","maa","apr","mei","jun","jul","aug","sep","okt","nov","dec"],dayNames:["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],dayNamesShort:["zon","maa","din","woe","don","vri","zat"],dayNamesMin:["zo","ma",
"di","wo","do","vr","za"],weekHeader:"Wk",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.nl)});
jQuery(function(a){a.datepicker.regional.no={closeText:"Lukk",prevText:"&laquo;Forrige",nextText:"Neste&raquo;",currentText:"I dag",monthNames:["Januar","Februar","Mars","April","Mai","Juni","Juli","August","September","Oktober","November","Desember"],monthNamesShort:["Jan","Feb","Mar","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Des"],dayNamesShort:["S\u00f8n","Man","Tir","Ons","Tor","Fre","L\u00f8r"],dayNames:["S\u00f8ndag","Mandag","Tirsdag","Onsdag","Torsdag","Fredag","L\u00f8rdag"],dayNamesMin:["S\u00f8",
"Ma","Ti","On","To","Fr","L\u00f8"],weekHeader:"Uke",dateFormat:"yy-mm-dd",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.no)});
jQuery(function(a){a.datepicker.regional.pl={closeText:"Zamknij",prevText:"&#x3c;Poprzedni",nextText:"Nast\u0119pny&#x3e;",currentText:"Dzi\u015b",monthNames:["Stycze\u0144","Luty","Marzec","Kwiecie\u0144","Maj","Czerwiec","Lipiec","Sierpie\u0144","Wrzesie\u0144","Pa\u017adziernik","Listopad","Grudzie\u0144"],monthNamesShort:["Sty","Lu","Mar","Kw","Maj","Cze","Lip","Sie","Wrz","Pa","Lis","Gru"],dayNames:["Niedziela","Poniedzia\u0142ek","Wtorek","\u015aroda","Czwartek","Pi\u0105tek","Sobota"],dayNamesShort:["Nie",
"Pn","Wt","\u015ar","Czw","Pt","So"],dayNamesMin:["N","Pn","Wt","\u015ar","Cz","Pt","So"],weekHeader:"Tydz",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.pl)});
jQuery(function(a){a.datepicker.regional["pt-BR"]={closeText:"Fechar",prevText:"&#x3c;Anterior",nextText:"Pr&oacute;ximo&#x3e;",currentText:"Hoje",monthNames:["Janeiro","Fevereiro","Mar&ccedil;o","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],monthNamesShort:["Jan","Fev","Mar","Abr","Mai","Jun","Jul","Ago","Set","Out","Nov","Dez"],dayNames:["Domingo","Segunda-feira","Ter&ccedil;a-feira","Quarta-feira","Quinta-feira","Sexta-feira","S&aacute;bado"],dayNamesShort:["Dom",
"Seg","Ter","Qua","Qui","Sex","S&aacute;b"],dayNamesMin:["Dom","Seg","Ter","Qua","Qui","Sex","S&aacute;b"],weekHeader:"Sm",dateFormat:"dd/mm/yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional["pt-BR"])});
jQuery(function(a){a.datepicker.regional.ro={closeText:"\u00cenchide",prevText:"&laquo; Luna precedent\u0103",nextText:"Luna urm\u0103toare &raquo;",currentText:"Azi",monthNames:["Ianuarie","Februarie","Martie","Aprilie","Mai","Iunie","Iulie","August","Septembrie","Octombrie","Noiembrie","Decembrie"],monthNamesShort:["Ian","Feb","Mar","Apr","Mai","Iun","Iul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Duminic\u0103","Luni","Mar\u0163i","Miercuri","Joi","Vineri","S\u00e2mb\u0103t\u0103"],dayNamesShort:["Dum",
"Lun","Mar","Mie","Joi","Vin","S\u00e2m"],dayNamesMin:["Du","Lu","Ma","Mi","Jo","Vi","S\u00e2"],weekHeader:"S\u0103pt",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.ro)});
jQuery(function(a){a.datepicker.regional.ru={closeText:"\u0417\u0430\u043a\u0440\u044b\u0442\u044c",prevText:"&#x3c;\u041f\u0440\u0435\u0434",nextText:"\u0421\u043b\u0435\u0434&#x3e;",currentText:"\u0421\u0435\u0433\u043e\u0434\u043d\u044f",monthNames:["\u042f\u043d\u0432\u0430\u0440\u044c","\u0424\u0435\u0432\u0440\u0430\u043b\u044c","\u041c\u0430\u0440\u0442","\u0410\u043f\u0440\u0435\u043b\u044c","\u041c\u0430\u0439","\u0418\u044e\u043d\u044c","\u0418\u044e\u043b\u044c","\u0410\u0432\u0433\u0443\u0441\u0442",
"\u0421\u0435\u043d\u0442\u044f\u0431\u0440\u044c","\u041e\u043a\u0442\u044f\u0431\u0440\u044c","\u041d\u043e\u044f\u0431\u0440\u044c","\u0414\u0435\u043a\u0430\u0431\u0440\u044c"],monthNamesShort:["\u042f\u043d\u0432","\u0424\u0435\u0432","\u041c\u0430\u0440","\u0410\u043f\u0440","\u041c\u0430\u0439","\u0418\u044e\u043d","\u0418\u044e\u043b","\u0410\u0432\u0433","\u0421\u0435\u043d","\u041e\u043a\u0442","\u041d\u043e\u044f","\u0414\u0435\u043a"],dayNames:["\u0432\u043e\u0441\u043a\u0440\u0435\u0441\u0435\u043d\u044c\u0435",
"\u043f\u043e\u043d\u0435\u0434\u0435\u043b\u044c\u043d\u0438\u043a","\u0432\u0442\u043e\u0440\u043d\u0438\u043a","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0435\u0440\u0433","\u043f\u044f\u0442\u043d\u0438\u0446\u0430","\u0441\u0443\u0431\u0431\u043e\u0442\u0430"],dayNamesShort:["\u0432\u0441\u043a","\u043f\u043d\u0434","\u0432\u0442\u0440","\u0441\u0440\u0434","\u0447\u0442\u0432","\u043f\u0442\u043d","\u0441\u0431\u0442"],dayNamesMin:["\u0412\u0441","\u041f\u043d","\u0412\u0442",
"\u0421\u0440","\u0427\u0442","\u041f\u0442","\u0421\u0431"],weekHeader:"\u041d\u0435",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.ru)});
jQuery(function(a){a.datepicker.regional.sk={closeText:"Zavrie\u0165",prevText:"&#x3c;Predch\u00e1dzaj\u00faci",nextText:"Nasleduj\u00faci&#x3e;",currentText:"Dnes",monthNames:["Janu\u00e1r","Febru\u00e1r","Marec","Apr\u00edl","M\u00e1j","J\u00fan","J\u00fal","August","September","Okt\u00f3ber","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","M\u00e1j","J\u00fan","J\u00fal","Aug","Sep","Okt","Nov","Dec"],dayNames:["Nedel'a","Pondelok","Utorok","Streda","\u0160tvrtok","Piatok","Sobota"],
dayNamesShort:["Ned","Pon","Uto","Str","\u0160tv","Pia","Sob"],dayNamesMin:["Ne","Po","Ut","St","\u0160t","Pia","So"],weekHeader:"Ty",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.sk)});
jQuery(function(a){a.datepicker.regional.sl={closeText:"Zapri",prevText:"&lt;Prej&#x161;nji",nextText:"Naslednji&gt;",currentText:"Trenutni",monthNames:["Januar","Februar","Marec","April","Maj","Junij","Julij","Avgust","September","Oktober","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Avg","Sep","Okt","Nov","Dec"],dayNames:["Nedelja","Ponedeljek","Torek","Sreda","&#x10C;etrtek","Petek","Sobota"],dayNamesShort:["Ned","Pon","Tor","Sre","&#x10C;et","Pet","Sob"],
dayNamesMin:["Ne","Po","To","Sr","&#x10C;e","Pe","So"],weekHeader:"Teden",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.sl)});
jQuery(function(a){a.datepicker.regional.sq={closeText:"mbylle",prevText:"&#x3c;mbrapa",nextText:"P\u00ebrpara&#x3e;",currentText:"sot",monthNames:["Janar","Shkurt","Mars","Prill","Maj","Qershor","Korrik","Gusht","Shtator","Tetor","N\u00ebntor","Dhjetor"],monthNamesShort:["Jan","Shk","Mar","Pri","Maj","Qer","Kor","Gus","Sht","Tet","N\u00ebn","Dhj"],dayNames:["E Diel","E H\u00ebn\u00eb","E Mart\u00eb","E M\u00ebrkur\u00eb","E Enjte","E Premte","E Shtune"],dayNamesShort:["Di","H\u00eb","Ma","M\u00eb",
"En","Pr","Sh"],dayNamesMin:["Di","H\u00eb","Ma","M\u00eb","En","Pr","Sh"],weekHeader:"Ja",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.sq)});
jQuery(function(a){a.datepicker.regional["sr-SR"]={closeText:"Zatvori",prevText:"&#x3c;",nextText:"&#x3e;",currentText:"Danas",monthNames:["Januar","Februar","Mart","April","Maj","Jun","Jul","Avgust","Septembar","Oktobar","Novembar","Decembar"],monthNamesShort:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Avg","Sep","Okt","Nov","Dec"],dayNames:["Nedelja","Ponedeljak","Utorak","Sreda","\u010cetvrtak","Petak","Subota"],dayNamesShort:["Ned","Pon","Uto","Sre","\u010cet","Pet","Sub"],dayNamesMin:["Ne","Po",
"Ut","Sr","\u010ce","Pe","Su"],weekHeader:"Sed",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional["sr-SR"])});
jQuery(function(a){a.datepicker.regional.sr={closeText:"\u0417\u0430\u0442\u0432\u043e\u0440\u0438",prevText:"&#x3c;",nextText:"&#x3e;",currentText:"\u0414\u0430\u043d\u0430\u0441",monthNames:["\u0408\u0430\u043d\u0443\u0430\u0440","\u0424\u0435\u0431\u0440\u0443\u0430\u0440","\u041c\u0430\u0440\u0442","\u0410\u043f\u0440\u0438\u043b","\u041c\u0430\u0458","\u0408\u0443\u043d","\u0408\u0443\u043b","\u0410\u0432\u0433\u0443\u0441\u0442","\u0421\u0435\u043f\u0442\u0435\u043c\u0431\u0430\u0440","\u041e\u043a\u0442\u043e\u0431\u0430\u0440",
"\u041d\u043e\u0432\u0435\u043c\u0431\u0430\u0440","\u0414\u0435\u0446\u0435\u043c\u0431\u0430\u0440"],monthNamesShort:["\u0408\u0430\u043d","\u0424\u0435\u0431","\u041c\u0430\u0440","\u0410\u043f\u0440","\u041c\u0430\u0458","\u0408\u0443\u043d","\u0408\u0443\u043b","\u0410\u0432\u0433","\u0421\u0435\u043f","\u041e\u043a\u0442","\u041d\u043e\u0432","\u0414\u0435\u0446"],dayNames:["\u041d\u0435\u0434\u0435\u0459\u0430","\u041f\u043e\u043d\u0435\u0434\u0435\u0459\u0430\u043a","\u0423\u0442\u043e\u0440\u0430\u043a",
"\u0421\u0440\u0435\u0434\u0430","\u0427\u0435\u0442\u0432\u0440\u0442\u0430\u043a","\u041f\u0435\u0442\u0430\u043a","\u0421\u0443\u0431\u043e\u0442\u0430"],dayNamesShort:["\u041d\u0435\u0434","\u041f\u043e\u043d","\u0423\u0442\u043e","\u0421\u0440\u0435","\u0427\u0435\u0442","\u041f\u0435\u0442","\u0421\u0443\u0431"],dayNamesMin:["\u041d\u0435","\u041f\u043e","\u0423\u0442","\u0421\u0440","\u0427\u0435","\u041f\u0435","\u0421\u0443"],weekHeader:"\u0421\u0435\u0434",dateFormat:"dd/mm/yy",firstDay:1,
isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.sr)});
jQuery(function(a){a.datepicker.regional.sv={closeText:"St\u00e4ng",prevText:"&laquo;F\u00f6rra",nextText:"N\u00e4sta&raquo;",currentText:"Idag",monthNames:["Januari","Februari","Mars","April","Maj","Juni","Juli","Augusti","September","Oktober","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Aug","Sep","Okt","Nov","Dec"],dayNamesShort:["S\u00f6n","M\u00e5n","Tis","Ons","Tor","Fre","L\u00f6r"],dayNames:["S\u00f6ndag","M\u00e5ndag","Tisdag","Onsdag","Torsdag","Fredag",
"L\u00f6rdag"],dayNamesMin:["S\u00f6","M\u00e5","Ti","On","To","Fr","L\u00f6"],weekHeader:"Ve",dateFormat:"yy-mm-dd",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.sv)});
jQuery(function(a){a.datepicker.regional.ta={closeText:"\u0bae\u0bc2\u0b9f\u0bc1",prevText:"\u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bc8\u0baf\u0ba4\u0bc1",nextText:"\u0b85\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0ba4\u0bc1",currentText:"\u0b87\u0ba9\u0bcd\u0bb1\u0bc1",monthNames:["\u0ba4\u0bc8","\u0bae\u0bbe\u0b9a\u0bbf","\u0baa\u0b99\u0bcd\u0b95\u0bc1\u0ba9\u0bbf","\u0b9a\u0bbf\u0ba4\u0bcd\u0ba4\u0bbf\u0bb0\u0bc8","\u0bb5\u0bc8\u0b95\u0bbe\u0b9a\u0bbf","\u0b86\u0ba9\u0bbf","\u0b86\u0b9f\u0bbf","\u0b86\u0bb5\u0ba3\u0bbf",
"\u0baa\u0bc1\u0bb0\u0b9f\u0bcd\u0b9f\u0bbe\u0b9a\u0bbf","\u0b90\u0baa\u0bcd\u0baa\u0b9a\u0bbf","\u0b95\u0bbe\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bbf\u0b95\u0bc8","\u0bae\u0bbe\u0bb0\u0bcd\u0b95\u0bb4\u0bbf"],monthNamesShort:["\u0ba4\u0bc8","\u0bae\u0bbe\u0b9a\u0bbf","\u0baa\u0b99\u0bcd","\u0b9a\u0bbf\u0ba4\u0bcd","\u0bb5\u0bc8\u0b95\u0bbe","\u0b86\u0ba9\u0bbf","\u0b86\u0b9f\u0bbf","\u0b86\u0bb5","\u0baa\u0bc1\u0bb0","\u0b90\u0baa\u0bcd","\u0b95\u0bbe\u0bb0\u0bcd","\u0bae\u0bbe\u0bb0\u0bcd"],dayNames:["\u0b9e\u0bbe\u0baf\u0bbf\u0bb1\u0bcd\u0bb1\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8",
"\u0ba4\u0bbf\u0b99\u0bcd\u0b95\u0b9f\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8","\u0b9a\u0bc6\u0bb5\u0bcd\u0bb5\u0bbe\u0baf\u0bcd\u0b95\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8","\u0baa\u0bc1\u0ba4\u0ba9\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8","\u0bb5\u0bbf\u0baf\u0bbe\u0bb4\u0b95\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8","\u0bb5\u0bc6\u0bb3\u0bcd\u0bb3\u0bbf\u0b95\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8","\u0b9a\u0ba9\u0bbf\u0b95\u0bcd\u0b95\u0bbf\u0bb4\u0bae\u0bc8"],dayNamesShort:["\u0b9e\u0bbe\u0baf\u0bbf\u0bb1\u0bc1",
"\u0ba4\u0bbf\u0b99\u0bcd\u0b95\u0bb3\u0bcd","\u0b9a\u0bc6\u0bb5\u0bcd\u0bb5\u0bbe\u0baf\u0bcd","\u0baa\u0bc1\u0ba4\u0ba9\u0bcd","\u0bb5\u0bbf\u0baf\u0bbe\u0bb4\u0ba9\u0bcd","\u0bb5\u0bc6\u0bb3\u0bcd\u0bb3\u0bbf","\u0b9a\u0ba9\u0bbf"],dayNamesMin:["\u0b9e\u0bbe","\u0ba4\u0bbf","\u0b9a\u0bc6","\u0baa\u0bc1","\u0bb5\u0bbf","\u0bb5\u0bc6","\u0b9a"],weekHeader:"\u041d\u0435",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.ta)});
jQuery(function(a){a.datepicker.regional.th={closeText:"\u0e1b\u0e34\u0e14",prevText:"&laquo;&nbsp;\u0e22\u0e49\u0e2d\u0e19",nextText:"\u0e16\u0e31\u0e14\u0e44\u0e1b&nbsp;&raquo;",currentText:"\u0e27\u0e31\u0e19\u0e19\u0e35\u0e49",monthNames:["\u0e21\u0e01\u0e23\u0e32\u0e04\u0e21","\u0e01\u0e38\u0e21\u0e20\u0e32\u0e1e\u0e31\u0e19\u0e18\u0e4c","\u0e21\u0e35\u0e19\u0e32\u0e04\u0e21","\u0e40\u0e21\u0e29\u0e32\u0e22\u0e19","\u0e1e\u0e24\u0e29\u0e20\u0e32\u0e04\u0e21","\u0e21\u0e34\u0e16\u0e38\u0e19\u0e32\u0e22\u0e19",
"\u0e01\u0e23\u0e01\u0e0f\u0e32\u0e04\u0e21","\u0e2a\u0e34\u0e07\u0e2b\u0e32\u0e04\u0e21","\u0e01\u0e31\u0e19\u0e22\u0e32\u0e22\u0e19","\u0e15\u0e38\u0e25\u0e32\u0e04\u0e21","\u0e1e\u0e24\u0e28\u0e08\u0e34\u0e01\u0e32\u0e22\u0e19","\u0e18\u0e31\u0e19\u0e27\u0e32\u0e04\u0e21"],monthNamesShort:["\u0e21.\u0e04.","\u0e01.\u0e1e.","\u0e21\u0e35.\u0e04.","\u0e40\u0e21.\u0e22.","\u0e1e.\u0e04.","\u0e21\u0e34.\u0e22.","\u0e01.\u0e04.","\u0e2a.\u0e04.","\u0e01.\u0e22.","\u0e15.\u0e04.","\u0e1e.\u0e22.","\u0e18.\u0e04."],
dayNames:["\u0e2d\u0e32\u0e17\u0e34\u0e15\u0e22\u0e4c","\u0e08\u0e31\u0e19\u0e17\u0e23\u0e4c","\u0e2d\u0e31\u0e07\u0e04\u0e32\u0e23","\u0e1e\u0e38\u0e18","\u0e1e\u0e24\u0e2b\u0e31\u0e2a\u0e1a\u0e14\u0e35","\u0e28\u0e38\u0e01\u0e23\u0e4c","\u0e40\u0e2a\u0e32\u0e23\u0e4c"],dayNamesShort:["\u0e2d\u0e32.","\u0e08.","\u0e2d.","\u0e1e.","\u0e1e\u0e24.","\u0e28.","\u0e2a."],dayNamesMin:["\u0e2d\u0e32.","\u0e08.","\u0e2d.","\u0e1e.","\u0e1e\u0e24.","\u0e28.","\u0e2a."],weekHeader:"Wk",dateFormat:"dd/mm/yy",
firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.th)});
jQuery(function(a){a.datepicker.regional.tr={closeText:"kapat",prevText:"&#x3c;geri",nextText:"ileri&#x3e",currentText:"bug\u00fcn",monthNames:["Ocak","\u015eubat","Mart","Nisan","May\u0131s","Haziran","Temmuz","A\u011fustos","Eyl\u00fcl","Ekim","Kas\u0131m","Aral\u0131k"],monthNamesShort:["Oca","\u015eub","Mar","Nis","May","Haz","Tem","A\u011fu","Eyl","Eki","Kas","Ara"],dayNames:["Pazar","Pazartesi","Sal\u0131","\u00c7ar\u015famba","Per\u015fembe","Cuma","Cumartesi"],dayNamesShort:["Pz","Pt","Sa",
"\u00c7a","Pe","Cu","Ct"],dayNamesMin:["Pz","Pt","Sa","\u00c7a","Pe","Cu","Ct"],weekHeader:"Hf",dateFormat:"dd.mm.yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.tr)});
jQuery(function(a){a.datepicker.regional.uk={closeText:"\u0417\u0430\u043a\u0440\u0438\u0442\u0438",prevText:"&#x3c;",nextText:"&#x3e;",currentText:"\u0421\u044c\u043e\u0433\u043e\u0434\u043d\u0456",monthNames:["\u0421\u0456\u0447\u0435\u043d\u044c","\u041b\u044e\u0442\u0438\u0439","\u0411\u0435\u0440\u0435\u0437\u0435\u043d\u044c","\u041a\u0432\u0456\u0442\u0435\u043d\u044c","\u0422\u0440\u0430\u0432\u0435\u043d\u044c","\u0427\u0435\u0440\u0432\u0435\u043d\u044c","\u041b\u0438\u043f\u0435\u043d\u044c",
"\u0421\u0435\u0440\u043f\u0435\u043d\u044c","\u0412\u0435\u0440\u0435\u0441\u0435\u043d\u044c","\u0416\u043e\u0432\u0442\u0435\u043d\u044c","\u041b\u0438\u0441\u0442\u043e\u043f\u0430\u0434","\u0413\u0440\u0443\u0434\u0435\u043d\u044c"],monthNamesShort:["\u0421\u0456\u0447","\u041b\u044e\u0442","\u0411\u0435\u0440","\u041a\u0432\u0456","\u0422\u0440\u0430","\u0427\u0435\u0440","\u041b\u0438\u043f","\u0421\u0435\u0440","\u0412\u0435\u0440","\u0416\u043e\u0432","\u041b\u0438\u0441","\u0413\u0440\u0443"],
dayNames:["\u043d\u0435\u0434\u0456\u043b\u044f","\u043f\u043e\u043d\u0435\u0434\u0456\u043b\u043e\u043a","\u0432\u0456\u0432\u0442\u043e\u0440\u043e\u043a","\u0441\u0435\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0435\u0440","\u043f\u2019\u044f\u0442\u043d\u0438\u0446\u044f","\u0441\u0443\u0431\u043e\u0442\u0430"],dayNamesShort:["\u043d\u0435\u0434","\u043f\u043d\u0434","\u0432\u0456\u0432","\u0441\u0440\u0434","\u0447\u0442\u0432","\u043f\u0442\u043d","\u0441\u0431\u0442"],dayNamesMin:["\u041d\u0434",
"\u041f\u043d","\u0412\u0442","\u0421\u0440","\u0427\u0442","\u041f\u0442","\u0421\u0431"],weekHeader:"\u041d\u0435",dateFormat:"dd/mm/yy",firstDay:1,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.uk)});
jQuery(function(a){a.datepicker.regional.vi={closeText:"\u0110\u00f3ng",prevText:"&#x3c;Tr\u01b0\u1edbc",nextText:"Ti\u1ebfp&#x3e;",currentText:"H\u00f4m nay",monthNames:["Th\u00e1ng M\u1ed9t","Th\u00e1ng Hai","Th\u00e1ng Ba","Th\u00e1ng T\u01b0","Th\u00e1ng N\u0103m","Th\u00e1ng S\u00e1u","Th\u00e1ng B\u1ea3y","Th\u00e1ng T\u00e1m","Th\u00e1ng Ch\u00edn","Th\u00e1ng M\u01b0\u1eddi","Th\u00e1ng M\u01b0\u1eddi M\u1ed9t","Th\u00e1ng M\u01b0\u1eddi Hai"],monthNamesShort:["Th\u00e1ng 1","Th\u00e1ng 2",
"Th\u00e1ng 3","Th\u00e1ng 4","Th\u00e1ng 5","Th\u00e1ng 6","Th\u00e1ng 7","Th\u00e1ng 8","Th\u00e1ng 9","Th\u00e1ng 10","Th\u00e1ng 11","Th\u00e1ng 12"],dayNames:["Ch\u1ee7 Nh\u1eadt","Th\u1ee9 Hai","Th\u1ee9 Ba","Th\u1ee9 T\u01b0","Th\u1ee9 N\u0103m","Th\u1ee9 S\u00e1u","Th\u1ee9 B\u1ea3y"],dayNamesShort:["CN","T2","T3","T4","T5","T6","T7"],dayNamesMin:["CN","T2","T3","T4","T5","T6","T7"],weekHeader:"Tu",dateFormat:"dd/mm/yy",firstDay:0,isRTL:false,showMonthAfterYear:false,yearSuffix:""};a.datepicker.setDefaults(a.datepicker.regional.vi)});
jQuery(function(a){a.datepicker.regional["zh-CN"]={closeText:"\u5173\u95ed",prevText:"&#x3c;\u4e0a\u6708",nextText:"\u4e0b\u6708&#x3e;",currentText:"\u4eca\u5929",monthNames:["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708"],monthNamesShort:["\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d","\u4e03","\u516b","\u4e5d","\u5341","\u5341\u4e00","\u5341\u4e8c"],
dayNames:["\u661f\u671f\u65e5","\u661f\u671f\u4e00","\u661f\u671f\u4e8c","\u661f\u671f\u4e09","\u661f\u671f\u56db","\u661f\u671f\u4e94","\u661f\u671f\u516d"],dayNamesShort:["\u5468\u65e5","\u5468\u4e00","\u5468\u4e8c","\u5468\u4e09","\u5468\u56db","\u5468\u4e94","\u5468\u516d"],dayNamesMin:["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],weekHeader:"\u5468",dateFormat:"yy-mm-dd",firstDay:1,isRTL:false,showMonthAfterYear:true,yearSuffix:"\u5e74"};a.datepicker.setDefaults(a.datepicker.regional["zh-CN"])});
jQuery(function(a){a.datepicker.regional["zh-HK"]={closeText:"\u95dc\u9589",prevText:"&#x3c;\u4e0a\u6708",nextText:"\u4e0b\u6708&#x3e;",currentText:"\u4eca\u5929",monthNames:["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708"],monthNamesShort:["\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d","\u4e03","\u516b","\u4e5d","\u5341","\u5341\u4e00","\u5341\u4e8c"],
dayNames:["\u661f\u671f\u65e5","\u661f\u671f\u4e00","\u661f\u671f\u4e8c","\u661f\u671f\u4e09","\u661f\u671f\u56db","\u661f\u671f\u4e94","\u661f\u671f\u516d"],dayNamesShort:["\u5468\u65e5","\u5468\u4e00","\u5468\u4e8c","\u5468\u4e09","\u5468\u56db","\u5468\u4e94","\u5468\u516d"],dayNamesMin:["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],weekHeader:"\u5468",dateFormat:"dd-mm-yy",firstDay:0,isRTL:false,showMonthAfterYear:true,yearSuffix:"\u5e74"};a.datepicker.setDefaults(a.datepicker.regional["zh-HK"])});
jQuery(function(a){a.datepicker.regional["zh-TW"]={closeText:"\u95dc\u9589",prevText:"&#x3c;\u4e0a\u6708",nextText:"\u4e0b\u6708&#x3e;",currentText:"\u4eca\u5929",monthNames:["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708"],monthNamesShort:["\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d","\u4e03","\u516b","\u4e5d","\u5341","\u5341\u4e00","\u5341\u4e8c"],
dayNames:["\u661f\u671f\u65e5","\u661f\u671f\u4e00","\u661f\u671f\u4e8c","\u661f\u671f\u4e09","\u661f\u671f\u56db","\u661f\u671f\u4e94","\u661f\u671f\u516d"],dayNamesShort:["\u5468\u65e5","\u5468\u4e00","\u5468\u4e8c","\u5468\u4e09","\u5468\u56db","\u5468\u4e94","\u5468\u516d"],dayNamesMin:["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],weekHeader:"\u5468",dateFormat:"yy/mm/dd",firstDay:1,isRTL:false,showMonthAfterYear:true,yearSuffix:"\u5e74"};a.datepicker.setDefaults(a.datepicker.regional["zh-TW"])});
