package com.baosight.bsvkkj.mp.wf.vo;


import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 处理多任务
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class DealOne {
	//流程编码
	@NotEmpty(message = "任务ID不能空")
	public String taskId;
	//目标人
	public String targetUserCode;
	//跳转节点
	public String activityKey;
	//跳转目标人
	public String userLabelM;
	//流程参数
	public String processVariable;
}
