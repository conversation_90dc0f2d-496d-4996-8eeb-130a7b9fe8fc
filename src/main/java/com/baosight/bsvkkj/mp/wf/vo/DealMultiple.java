package com.baosight.bsvkkj.mp.wf.vo;


import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 处理多任务
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class DealMultiple {
	//流程编码
	@NotEmpty(message = "流程编码不能空")
	public String processKey;
	//任务节点编码
	@NotEmpty(message = "任务节点编码不能空")
	public String taskDefKey;
	//被替换人
	@NotEmpty(message = "被替换人不能空")
	public String sourceUserCode;
	//目标人
	@NotEmpty(message = "目标人不能空")
	public String targetUserCode;
}
