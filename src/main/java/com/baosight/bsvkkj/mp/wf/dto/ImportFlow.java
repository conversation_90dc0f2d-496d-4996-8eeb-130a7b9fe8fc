package com.baosight.bsvkkj.mp.wf.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ImportFlow{
	@Excel(name = "FLOW_CODE")
	private String flowCode;
	
	@Excel(name = "BUSINESS_ID")
	private String businessId;
	
	@Excel(name = "BUSINESS_TYPE")
	private String businessType;
	
	@Excel(name = "BUSINESS_NAME")
	private String businessName;
		
	@Excel(name = "START_OPERATOR")
	private String startOperator;
		
	@Excel(name = "CURRENT_OPERATOR")
	private String currentOperator;
	
	@Excel(name = "CURRENT_ACTIVITY")
	private String currentActivity;
	
	@Excel(name = "CURRENT_ACTIVITY_NAME")
	private String currentActivityName;
	
}