package com.baosight.bsvkkj.mp.wf.dto;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Getter
@Setter
public class ActivityUserInfo {
    //活动节点Code
    private String activityCode;
    //活动节点名称，多个逗号隔开
    private String activityName;
    //活动节点类型
    private String activityType;
    //用户Code，多个逗号隔开
    private String operatorCode;
    //当前用户名称，多个逗号隔开
    private String operatorName;
    //当前用户（'Code'-'Name'），多个逗号隔开
    private String operator;
    //转移路径
    private String transitionKey;
    /**
     * 节点的扩展属性
     */
    private Map<String, Object> extraParameters;
    /**
     * 流程配置
     */
    private Map<String, Object> flowConfig;
    /**
     * 初始化
     *
     * @param activityCode
     * @param activityName
     * @param activityType
     * @param operatorCode
     * @param operatorName
     */
    public ActivityUserInfo(String activityCode, String activityName, String activityType, String operatorCode, String operatorName, String operator) {
        this.activityCode = activityCode;
        this.activityName = activityName;
        this.activityType = activityType;
        this.operatorCode = operatorCode;
        this.operatorName = operatorName;
        this.operator = operator;
        if (StringUtils.isEmpty(this.operatorCode)) {
            this.operatorCode = "";
            this.operatorName = "";
            this.operator = "";
        }
        if (StringUtils.isEmpty(this.operator)) {
            this.operator = this.operatorCode + "-" + this.operatorName;
        }
    }
}
