package com.baosight.bsvkkj.mp.wf.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class TaskOption implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /*任务ID*/
    private String taskId;
    /*任务名称*/
    private String taskName;
    /*任务类型*/
    private String taskType;
    /*开始时间*/
    private String startTime;
    /*结束时间*/
    private String endTime;
    /*期间*/
    private String duration;
    /*完成人ID*/
    private String completerId;
    /*完成人名称*/
    private String completerFullname;
    /*意见*/
    private String opinion;
    /*受理人ID*/
    private String assigneeId;
    /*受理人名称*/
    private String assigneeFullname;
    /*收货人ID*/
    private String consigneeId;
    /*收货人ID*/
    private String consigneeFullname;
    /*审批结果*/
    private String approvalResult;
    /*状态*/
    private String state;
    /*任务动作类型*/
    private String signType;
    /*实例ID*/
    private String instanceId;
    /*菜单ID*/
    private String nodeType;
    /*菜单名称*/
    private String nodeTypeName;
    /*父菜单ID*/
    private String patentId;
    /*子节点编码*/
    private String taskDefKey;
    /*菜单URL*/
    private String url;
    /*子菜单*/
    private List<TaskOption> children = new ArrayList<TaskOption>();
    /* 操作人所在部门 */
    private String orgName;
    /* 操作人所在部门 */
    private String orgCode;
}
