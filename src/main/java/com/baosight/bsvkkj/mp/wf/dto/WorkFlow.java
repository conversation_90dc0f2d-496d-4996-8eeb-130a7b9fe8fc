package com.baosight.bsvkkj.mp.wf.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class WorkFlow {

    //操作人
    public String operator;
    //业务ID
    public String businessId;
    //业务名称
    public String businessName;
    //业务类型
    public String businessType;
    //业务标签
    public String businessLabel;
    //流程编码
    public String processCode;
    //流程实例ID
    public String processInstanceId;
    //任务ID
    public String taskId;
    //多任务ID 批量提交用
    public String taskIdS;
    //任务类型 host collaborator
    public String taskType;
    //流转意见
    public String comment;
    //转移路线Key 单个转移可以为null，多个转移不能为空
    public String transitionKey;
    //流程参数
	/*
	 	//参数里可以指定节点的参与人
 		List<Map<String,Object>> userS = new ArrayList<>();
		Map<String,Object> user1 = new HashMap<>();
		user1.put("userId", "zhangsan");
		userS.add(user);
		variable.put("Manual1", userS);//指定活动参与人，Manual1为节点code

		json格式：
		"variable": {
	        "Manual1": [
	            {
	                "userId": "zhangsan"
	            }，
	            {
	                "userId": "lisi"
	            }
	        ]
	    }
	 */
    public Map<String, Object> variable;
    //添加任务类型 C:复制(协办) X:转派任务(结束当前人任务) R:传阅任务(和主流程无任何影响) R1:传阅任务(传阅后当前用户的待办挂起，直到其所有的传阅任务被完成)
    public String addTaskType;
    //下一步流转人(code)，多个用逗号隔开，若为null以流程配置为准
    private String userLabelM;
    //子流程参数
    private SubProcessParam[] subProcessParamS;
    //退回节点Code 为空时跳转上一步 注:1.只会退回给节点流转人2.不能退回到开始节点
    private String returnActivityKey;
    //跳转节点Code 跳转时不能为空 会流转给{userLabelM}或者流程配置的用户
    private String jumpActivityKey;
    //流程状态 : ended:结束,active:活动,termination:终止,tempTermination:终止可恢复,suspended:挂起
    private String flowState;
    //当前节点 : CURRENT_ACTIVITY
    private String currentActivity;
    //当前节点名称 : CURRENT_ACTIVITY_NAME
    private String currentActivityName;
    //当前节点类型 : CURRENT_ACTIVITY_TYPE
    private String currentActivityType;
    //当前操作人 : CURRENT_OPERATOR
    private String currentOperator;
    //当前表单 : CURRENT_FORM
    private String currentForm;
    /**
     * 是否是二段式提交
     */
    private Boolean isTx = false;
    /**
     * 流程的扩展属性
     */
    private Map<String, Object> processParameters;
    /**
     * 当前节点扩展属性
     */
    private Map<String, Object> extraParameters;
    /**
     * 下个节点属性
     */
    private List<ActivityUserInfo> nextActivitys;

    public WorkFlow() {
    }

    /**
     * 初始化
     *
     * @param operator
     * @param processCode
     * @param businessType
     * @param businessId
     */
    public WorkFlow(String operator, String processCode, String businessType, String businessId) {
        this.operator = operator;
        this.processCode = processCode;
        this.businessType = businessType;
        this.businessId = businessId;
    }
}
