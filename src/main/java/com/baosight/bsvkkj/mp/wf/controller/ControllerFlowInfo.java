package com.baosight.bsvkkj.mp.wf.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bsvkkj.utils.ServiceUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/mpwf/flowInfo")
public class ControllerFlowInfo extends BaseController {

	/**
	 * 自由流转
	 * @return
	 */
	@PostMapping("/doOther")
	@ResponseBody
	public AjaxResult doOther(@RequestBody Map<String, Object> map) {
		/*
		EiInfo eiInfo = new EiInfo();
		eiInfo.setCell("i", 0, "businessId", map.get("businessId"));
		eiInfo.setCell("i", 0, "taskId", map.get("taskId"));
		eiInfo.setCell("i", 0, "userLabelM", map.get("userLabelM"));
		eiInfo.setCell("i", 0, "comment", map.get("comment"));
		eiInfo.setCell("i", 0, "addTaskType", map.get("addTaskType"));
		*/
		try {
			String operator = UserSession.getLoginName();
			WorkFlow workFlow = BeanUtil.toBean(map, WorkFlow.class);
			workFlow.setOperator(operator);
			if (operator.indexOf(workFlow.getUserLabelM())>-1) {
				return AjaxResult.error("请不要转给自己，您可以在此页面直接流审批！");
			}
			String msg = SWorkFlowUtil.doOther(operator, workFlow);
			return AjaxResult.success(msg);
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 获取下一步操作信息
	 * @return
	 */
	@PostMapping("/getNextSubmitWF")
	@ResponseBody
	public AjaxResult getNextSubmitWF(WorkFlow workFlow) {
		try {
			String operator = UserSession.getLoginName();
			workFlow.setOperator(operator);
			workFlow = SWorkFlowUtil.getNextSubmitWF(workFlow);
			return new AjaxResult(AjaxResult.Type.SUCCESS, "success", workFlow);
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 按钮操作
	 * @return
	 */
	@PostMapping("/doCommonButton")
	@ResponseBody
	public AjaxResult doCommonButton(@RequestBody Map<String, Object> map) {
		try {
			String operator = UserSession.getLoginName();
			String serviceId = (String)map.get("serviceId");
			if (StringUtils.isEmpty(serviceId)) {
				return AjaxResult.error("未配置服务Id(serviceId)");
			}
			WorkFlow workFlow = BeanUtil.toBean(map, WorkFlow.class);
			EiInfo eiInfo = new EiInfo();
			eiInfo.set(EiConstant.serviceId, serviceId);
			eiInfo.set("operator", operator);
			eiInfo.set("buttonName", map.get("buttonName"));
			eiInfo.set("workFlow", workFlow);
			EiInfo rEiInfo = XServiceManager.call(eiInfo);
			if (rEiInfo.getStatus()<0) {
				return AjaxResult.error(rEiInfo.getMsg());
			}
			return AjaxResult.success(rEiInfo.getMsg());
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 撤回
	 * @return
	 */
	@PostMapping("/doUndo")
	@ResponseBody
	public AjaxResult doUndo(@RequestBody Map<String, Object> map) {
		try {
			String operator = UserSession.getLoginName();
			WorkFlow workFlow = BeanUtil.toBean(map, WorkFlow.class);
			workFlow.setOperator(operator);
			String msg = SWorkFlowUtil.doUndo(operator, workFlow);
			return AjaxResult.success(msg);
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 查询子流程
	 */
	@GetMapping ("/childProcess/{businessId}")
	public String childProcess(ModelMap model, @PathVariable("businessId")String businessId) {
		model.put("businessId", businessId);
		return "/component/wfChildProcess";
	}

	/**
	 * 查询子流程
	 */
	@PostMapping("/childProcessData/{businessId}")
	@ResponseBody
	public TableDataInfo childProcessData(ModelMap model, @PathVariable("businessId")String businessId) {
		//String operator = UserSession.getLoginName();
		List<Map<String, Object>> list = SWorkFlowUtil.getHQChildFlowInfoByBusinessId(businessId);
		for (Map<String, Object> stringObjectMap : list) {
			String departmentNo = (String)stringObjectMap.get("departmentNo");
			stringObjectMap.put("departmentNoName", SOrgUtil.getOrgName(departmentNo));
		}
		return getDataTable(list);
	}

	/**
	 * 终止流程
	 * 不可恢复
	 * @return
	 */
	@PostMapping("/endProcessForever")
	@ResponseBody
	public AjaxResult endProcessForever(@RequestBody Map<String, Object> map) {
		try {
			String operator = UserSession.getLoginName();
			WorkFlow workFlow = BeanUtil.toBean(map, WorkFlow.class);
			workFlow.setOperator(operator);
			String msg = SWorkFlowUtil.endProcessForever(operator, workFlow);
			return AjaxResult.success(msg);
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 选人
	 *
	 * @param model
	 * @return
	 */
	@GetMapping("/configUser")
	public String toPage(ModelMap model) {
		model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
		List<Map<String, String>> array = new ArrayList<>();
		String users = paramMap().get("users");
		if (StringUtils.isNotBlank(users)) {
			String[] users_ = users.split(",");
			for (String user : users_) {
				String[] u = user.split("-");
				Map<String, String> jsonObject = new HashMap<>();
				jsonObject.put("userCode", u[0]);
				jsonObject.put("userName", u[1]);
				array.add(jsonObject);
			}
		}
		model.put("userData", array);
		return "/component/wfConfigUser";
	}

	/**
	 * 会签
	 * @return
	 */
	@PostMapping("/doHq")
	@ResponseBody
	public AjaxResult doHq(@RequestBody Map<String, Object> map) {
		try {
			WorkFlow workFlow = BeanUtil.toBean(map, WorkFlow.class);
			if (StringUtils.isBlank(workFlow.getProcessCode() )) {
				return AjaxResult.error("未配置会签流程编码(HQ_PROCESS_CODE)");
			}

			EiInfo eiInfo = new EiInfo();
			eiInfo.set(EiConstant.serviceId, "S_MP_WF_011");
			eiInfo.set("operator", UserSession.getLoginName());
			eiInfo.set("workFlow", workFlow);
			EiInfo rEiInfo = XServiceManager.call(eiInfo);
			if (rEiInfo.getStatus()<0) {
				return AjaxResult.error(rEiInfo.getMsg());
			}
			return AjaxResult.success(rEiInfo.getMsg());
		} catch (Exception e) {
			return AjaxResult.error(e.getMessage());
		}
	}

	@GetMapping("/getMyComment")
	@ResponseBody
	public AjaxResult getMyComment(){
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("operator", UserSession.getLoginName());
		eiInfo.set(EiConstant.serviceId, "S_MP_TY_C1");
		EiInfo rEiInfo = XServiceManager.call(eiInfo);
		if (rEiInfo.getStatus()<0) {
			return AjaxResult.error(rEiInfo.getMsg());
		}
		List<String> list = (List<String>) rEiInfo.get(EiConstant.resultBlock);
		return AjaxResult.success("success", list);
	}

	/***
	 * 获取专家评审待办
	 * @param map
	 * @return
	 */
	@PostMapping("/getMPPSDbList")
	@ResponseBody
	public TableDataInfo queryMPPSDbList(@RequestBody Map<String, Object> map){
		map.put("module","MPTY");
		// 排序
		map.put("displayOrder", " A.UPDATE_DATE desc ");
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo eiInfo1 = ServiceUtil.xLocalManager(eiInfo, "ServiceMPWFFlowInfo", "queryMPPSDbList");
		return getDataTable(eiInfo1);
	}
	/**
	 * 获取专家评审待办
	 *
	 * @param moduleCode 专家评审所属模块编码
	 * @return
	 */
	@GetMapping("/queryMPPSDbList/{moduleCode}")
	@ResponseBody
	public JSONObject queryMPPSDbList(@PathVariable("moduleCode") String moduleCode) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.setCell(EiConstant.queryBlock, 0, "moduleCode", moduleCode);// 专家评审所属模块编码
		eiInfo.set(EiConstant.serviceName, "MPWFFlowInfo");
		eiInfo.set(EiConstant.methodName, "queryMPPSDbList");
		EiInfo call = XLocalManager.call(eiInfo);
		Map<String,Object> rtn = new HashedMap<>();
		rtn.put("status", call.getStatus());
		rtn.put("data", call.getBlock(EiConstant.resultBlock).getRows());
		return (JSONObject) JSON.toJSON(rtn);
	}
}