package com.baosight.bsvkkj.mp.wf.service;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import com.baosight.bsvkkj.mp.wf.business.BusinessFlowInfo;
import com.baosight.bsvkkj.mp.wf.dto.TaskOption;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ServiceMPWFFlowInfo extends PageService {
	Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private BusinessFlowInfo businessFlowInfo;

	/**
	 * 流程退回
	 *
	 * @param inInfo
	 * @return
	 */
	public EiInfo returnFlow(EiInfo inInfo) {
		try {
			String msg = "";
			Map row = inInfo.getBlock("i").getRow(0);
			String businessGuid = (String) row.get("businessGuid");
			String taskId = (String) row.get("taskId");
			String activityKey = (String) row.get("activityKey");
			String nextUser = "";
			if (StrUtil.isNotBlank(businessGuid) && StrUtil.isNotBlank(taskId)) {
				String comment = "";
				if (row.get("comment") != null) {
					comment = (String) row.get("comment");
				}
				nextUser = SWorkFlowUtil.doReturn(UserSession.getLoginName(), businessGuid, taskId, activityKey, comment);
				if (StrUtil.isNotBlank(nextUser)) {
					msg = "退回给：" + nextUser;
				} else {
					msg = "退回成功!";
				}
				inInfo.setMsg(msg);
				inInfo.setStatus(EiConstant.STATUS_SUCCESS);
			}
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if (null != e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}
	
	/**
	 * 自由流转
	 *
	 * @param inInfo
	 * @return
	 */
	public EiInfo doOther(EiInfo inInfo) {
		try {
			String msg = "";
			Map row = inInfo.getBlock("i").getRow(0);
			String businessId = (String) row.get("businessId");
			String taskId = (String) row.get("taskId");
			String userLabelM = (String) row.get("userLabelM");
			String addTaskType = (String) row.get("addTaskType");
			String comment = (String) row.get("comment");
			
			msg = SWorkFlowUtil.doOther(UserSession.getLoginName(), businessId, taskId, userLabelM, comment, addTaskType);
			inInfo.setMsg(msg);
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if (null != e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}

	/**
	 * 获取当前登录用户
	 * 专家评审
	 * 业务类型代办数目列表
	 * 
	 * @param
	 * @return
	 */
	public EiInfo queryMPPSDbList(EiInfo inInfo) {
		try {
			String moduleCode = inInfo.getCellStr(EiConstant.queryBlock, 0, "moduleCode");
			List<Map<String, Object>> list = businessFlowInfo.queryMPPSBusinessTypeDbList(UserSession.getLoginName(),moduleCode);
			inInfo.addRows(EiConstant.resultBlock, list);

			inInfo.setMsg("success");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if(null!=e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}
	
	/**
	 * 获取当前登录用户业务类型代办列表
	 * 
	 * @param
	 * @return
	 */
	public EiInfo queryBusinessTypeDbList(EiInfo inInfo) {
		try {
			String businessType = inInfo.getCellStr(EiConstant.queryBlock, 0, "businessType");
			List<Map<String, Object>> list = businessFlowInfo.queryBusinessTypeDbList(UserSession.getLoginName(),
					businessType);
			inInfo.addRows(EiConstant.resultBlock, list);

			inInfo.setMsg("success");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if(null!=e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}


	/**
	 * 获取当前登录用户业务类型和流程代码的代办列表
	 *
	 * @param
	 * @return
	 */
	public EiInfo queryBusinessTypeAndFlowCodesDbList(EiInfo inInfo) {
		try {
			String businessType = inInfo.getCellStr(EiConstant.queryBlock, 0, "businessType");
			String flowCodes = inInfo.getCellStr(EiConstant.queryBlock,0,"flowCodes");
			List<Map<String, Object>> list = businessFlowInfo.queryBusinessTypeAndFlowCodesDbList(UserSession.getLoginName(),
					businessType,flowCodes);
			inInfo.addRows(EiConstant.resultBlock, list);

			inInfo.setMsg("success");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if(null!=e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}
	/**
	 * 取当前登陆用户的待办
	 * 
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryDB(EiInfo inInfo) {
		try {
			Map<String, Object> queryData = getQueryData(inInfo);
			// 获取数据
			TableDataInfo tdi = businessFlowInfo.queryDB(UserSession.getLoginName(), queryData);
			setPage(inInfo, tdi);
			
			inInfo.setMsg("success");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if(null!=e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}

	/**
	 * 查询已办
	 * 
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryYB(EiInfo inInfo) {
		try {
			Map<String, Object> queryData = getQueryData(inInfo);
			// 获取数据
			TableDataInfo tdi = businessFlowInfo.queryYB(UserSession.getLoginName(), queryData);
			setPage(inInfo, tdi);
			
			inInfo.setMsg("success");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if(null!=e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}
	
	/**
	 * 流程意见
	 * @param inInfo
	 * @return
	 */
	public EiInfo listComment(EiInfo inInfo) {
		String instanceId = (String) inInfo.getAttr().get("instanceId");
		List<TaskOption> tasks = SWorkFlowUtil.getCommentList(instanceId);
		inInfo.set("tasks", tasks);
        return inInfo;
	}



	/**
	 * 获取当前登录用户所有业务类型代办列表
	 *
	 * @param
	 * @return
	 */
	public EiInfo queryDbList(EiInfo inInfo) {
		try {

			List<Map<String, Object>> list = businessFlowInfo.queryDbList(UserSession.getLoginName());
			inInfo.set("dbList",list);
			inInfo.setMsg("success");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if(null!=e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}
}
