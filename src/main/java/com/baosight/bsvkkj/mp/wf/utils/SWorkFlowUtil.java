package com.baosight.bsvkkj.mp.wf.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.baosight.bsvkkj.mp.ad.dto.ADUser;
import com.baosight.bsvkkj.mp.wf.dto.SubProcessParam;
import com.baosight.bsvkkj.mp.wf.dto.TaskOption;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程相关微服务版
 * ServiceMPWFWorkFlow.java
 * <p>
 * S_MP_WF_01 启动流程   			startProcess
 * S_MP_WF_02 启动并跳转指定节点		startProcessToActivity
 * S_MP_WF_03 提交					submit
 * S_MP_WF_04 退回					doReturn
 * S_MP_WF_05 跳转					doJump
 * S_MP_WF_06 终止流程				endProcess
 * S_MP_WF_07 自由流转				doOther
 * S_MP_WF_08 挂起流转				suspendProcess
 * S_MP_WF_09 激活流转				activeProcess
 * S_MP_WF_10 获取后续转移			getNextTransition
 * S_MP_WF_11 获取回退节点			getReturnsActivities
 * S_MP_WF_12 获取流程意见			getCommentList
 * S_MP_WF_13 获取所有流程			getProcessInfo
 * S_MP_WF_14 获取流程所有节点		getActivityInfoByProcessKey
 * S_MP_WF_15 根据流程ID获取流程参数	getVariableByProcessInstanceId
 * S_MP_WF_16 根据流程ID修改流程参数	updateVariableByProcessInstanceId
 * S_MP_WF_17 根据业务ID获取主流程信息	getMainFlowInfoByBusinessId
 * S_MP_WF_18 根据业务ID修改流程信息	updateFlowInfoByBusinessId
 * S_MP_WF_19 根据业务ID和组织编码获取子流程信息 getChildFlowInfoByBusinessIdAndOrgCode
 * S_MP_WF_20 根据任务ID修改流程意见	updateComment
 * S_MP_WF_21 获取对应节点配置的角色值	getRoleValue
 * S_MP_WF_22 根据业务ID获取子流程信息	getChildFlowInfoByBusinessId
 * S_MP_WF_23 根据任务ID获取流程意见	getComment
 * S_MP_WF_24 根据任务ID获取流程信息	getWorkFlowByTaskId
 * S_MP_WF_25 获取提交下一步的信息	    getNextSubmitInfo
 * S_MP_WF_26 关闭任务	    		closeTask
 * S_MP_WF_27 启动流程并提交下一步	    startProcessAndSubmit
 * S_MP_WF_28 获取流程履历3	    	getCommentList3
 * S_MP_WF_29 结束流程	    		finshProecess
 * S_MP_WF_30 插入一条完成的流程履历   addCompletedTask
 * S_MP_WF_31 获取任务   			getTask
 *
 * <AUTHOR>
 */
@Component("SWorkFlowUtil")
public class SWorkFlowUtil {

    static Logger logger = LoggerFactory.getLogger(SWorkFlowUtil.class);

    /**
     * 启动流程
     *
     * @param operator 一般为当前登陆人
     * @param workFlow 业务ID
     * @return
     */
    public static String startProcess(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_01");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 返回流转给谁
     *
     * @param operator         一般为当前登陆人
     * @param businessId       业务ID
     * @param businessName     业务名称
     * @param businessType     模块编码
     * @param processCode      流程编码
     * @param comment          流程意见，有就写
     * @param userLabelM       流转给谁 不填直接 流转给流程配的用户
     * @param subProcessParamS 子流程参数
     * @param variable         流程参数，有就写
     * @param transitionKey    就一个转移可以为null
     * @return
     */
    @Deprecated
    public static String startProcess(String operator, String businessId, String businessName, String businessType,
                                      String processCode, String comment, String userLabelM, SubProcessParam[] subProcessParamS,
                                      Map<String, Object> variable, String transitionKey) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setBusinessName(businessName);
        workFlow.setBusinessType(businessType);
        workFlow.setProcessCode(processCode);
        workFlow.setComment(comment);
        workFlow.setSubProcessParamS(subProcessParamS);
        workFlow.setUserLabelM(userLabelM);
        workFlow.setVariable(variable);
        workFlow.setTransitionKey(transitionKey);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_01");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 返回流转给谁
     *
     * @param operator      一般为当前登陆人
     * @param businessId    业务ID
     * @param businessName  业务名称
     * @param businessType  模块编码
     * @param processCode   流程编码
     * @param comment       流程意见，有就写
     * @param userLabelM    流转给谁 不填直接 流转给流程配的用户
     * @param variable      流程参数，有就写
     * @param transitionKey 就一个转移可以为null
     * @return
     */
    @Deprecated
    public static String startProcess(String operator, String businessId, String businessName, String businessType,
                                      String processCode, String comment, String userLabelM, Map<String, Object> variable, String transitionKey) {
        return startProcess(operator, businessId, businessName, businessType, processCode, comment, userLabelM, null,
                variable, transitionKey);
    }

    /**
     * 返回流转给谁
     *
     * @param operator      一般为当前登陆人
     * @param businessId    业务ID
     * @param businessName  业务名称
     * @param businessType  模块编码
     * @param processCode   流程编码
     * @param comment       流程意见，有就写
     * @param variable      流程参数，有就写
     * @param transitionKey 就一个转移可以为null
     * @return
     */
    @Deprecated
    public static String startProcess(String operator, String businessId, String businessName, String businessType,
                                      String processCode, String comment, Map<String, Object> variable, String transitionKey) {
        return startProcess(operator, businessId, businessName, businessType, processCode, comment, null, null,
                variable, transitionKey);
    }

    /**
     * 启动并跳转指定节点
     *
     * @param operator      一般为当前登陆人
     * @param businessId    业务ID
     * @param businessName  业务名称
     * @param businessType  模块编码
     * @param processCode   流程编码
     * @param activityKey   节点编码
     * @param comment       流程意见，有就写
     * @param userLabelM    流转给谁 不填直接 流转给流程配的用户
     * @param variables     流程参数，有就写
     * @param transitionKey 就一个转移可以为null
     * @return
     */
    public static String startProcessToActivity(String operator, String businessId, String businessName,
                                                String businessType, String processCode, String activityKey, String comment, String userLabelM,
                                                Map variables, String transitionKey) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setBusinessName(businessName);
        workFlow.setBusinessType(businessType);
        workFlow.setProcessCode(processCode);
        workFlow.setComment(comment);
        workFlow.setUserLabelM(userLabelM);
        workFlow.setTransitionKey(transitionKey);
        workFlow.setJumpActivityKey(activityKey);
        workFlow.setVariable(variables);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_02");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 启动流程并提交下一步 (一般为了退回能修改用) 也就是流程会停在第二个活动节点
     * <p>
     * 启动流程到第一个节点时，直接给操作人
     * <p>
     * 第二节点按照规则来 userLabelM > 流程配置
     * 参数transitionKey、userLabelM、subProcessParamS、variable 第二个节点才起作用
     *
     * @param operator 一般为当前登陆人
     * @param workFlow workflow
     * @return
     */
    public static String startProcessAndSubmit(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_27");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 提交
     *
     * @param operator
     * @param workFlow
     * @return
     */
    public static String submit(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_03");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 提交
     *
     * @param operator         操作用户 一般为当前登陆人
     * @param businessId       业务ID
     * @param taskId           任务ID
     * @param transitionKey    转移路线Key
     * @param comment          意见
     * @param userLabelM       流转给谁 不填直接 流转给流程配的用户
     * @param subProcessParamS 子流程参数
     * @param variables        流程参数，有就写
     * @return
     */
    @Deprecated
    public static String submit(String operator, String businessId, String taskId, String transitionKey, String comment,
                                String userLabelM, SubProcessParam[] subProcessParamS, Map<String, Object> variables) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setTaskId(taskId);
        workFlow.setComment(comment);
        workFlow.setUserLabelM(userLabelM);
        workFlow.setTransitionKey(transitionKey);
        workFlow.setSubProcessParamS(subProcessParamS);
        workFlow.setVariable(variables);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_03");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 退回
     *
     * @param operator
     * @param workFlow
     * @return
     */
    public static String doReturn(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_04");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 退回到指定节点
     *
     * @param operator    操作用户 一般为当前登陆人
     * @param businessId  业务ID
     * @param taskId      任务ID
     * @param activityKey 退回到指定活动Code 若为空 就退回上一步
     * @param comment     意见
     * @return
     */
    @Deprecated
    public static String doReturn(String operator, String businessId, String taskId, String activityKey,
                                  String comment) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setTaskId(taskId);
        workFlow.setComment(comment);
        workFlow.setReturnActivityKey(activityKey);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_04");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 跳转
     *
     * @param operator
     * @param workFlow
     * @return
     */
    public static String doJump(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_05");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 跳转到指定节点 可以不按流程配置的流转
     *
     * @param operator    操作用户 一般为当前登陆人
     * @param businessId  业务ID
     * @param taskId      任务ID
     * @param activityKey 指定活动节点编码(只能是人工活动)
     * @param comment     意见
     * @param userLabelM  流转给谁 不填直接 流转给流程配的用户
     * @param variables   流程参数
     * @return
     */
    @Deprecated
    public static String doJump(String operator, String businessId, String taskId, String activityKey, String comment,
                                String userLabelM, Map<String, Object> variables) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setTaskId(taskId);
        workFlow.setComment(comment);
        workFlow.setUserLabelM(userLabelM);
        workFlow.setJumpActivityKey(activityKey);
        workFlow.setVariable(variables);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_05");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 中止流程
     *
     * @param operator 操作用户 一般为当前登陆人
     * @param workFlow
     * @return
     */
    public static String endProcess(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_06");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 中止流程
     *
     * @param operator   操作用户 一般为当前登陆人
     * @param businessId 操作用户 一般为当前登陆人
     * @param comment    意见
     * @return
     */
    @Deprecated
    public static String endProcess(String operator, String businessId, String comment) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setComment(comment);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_06");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 永久终止流程
     *
     * @param operator 操作用户 一般为当前登陆人
     * @param workFlow
     * @return
     */
    public static String endProcessForever(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_061");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 结束流程
     *
     * @param operator 操作用户 一般为当前登陆人
     * @param workFlow
     * @return
     */
    public static String finshProcess(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_29");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 自由流转(传阅)
     *
     * @param operator 操作用户 一般为当前登陆人
     * @param workFlow
     * @return
     */
    public static String doOther(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_07");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 自由流转(传阅)
     *
     * @param operator    操作用户 一般为当前登陆人
     * @param businessId  操作用户 一般为当前登陆人
     * @param taskId      任务ID
     * @param userLabelM  流转人 多个逗号隔开
     * @param comment     意见
     * @param addTaskType 自由流转
     * @return
     */
    @Deprecated
    public static String doOther(String operator, String businessId, String taskId, String userLabelM, String comment,
                                 String addTaskType) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setTaskId(taskId);
        workFlow.setComment(comment);
        workFlow.setUserLabelM(userLabelM);
        workFlow.setAddTaskType(addTaskType);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_07");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 挂起流程 只适用主流程
     *
     * @param operator
     * @param businessId
     */
    @Deprecated
    public static String suspendProcess(String operator, String businessId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_08");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 挂起流程 只适用主流程
     *
     * @param operator
     * @param workFlow
     */
    public static String suspendProcess(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_08");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 激活流程 只适用主流程
     *
     * @param operator
     * @param businessId
     */
    @Deprecated
    public static String activeProcess(String operator, String businessId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_09");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 激活流程 只适用主流程
     *
     * @param operator
     * @param workFlow
     */
    public static String activeProcess(String operator, WorkFlow workFlow) {
        if (StringUtils.isEmpty(workFlow.getOperator())) {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_09");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 获取流程后续转移
     *
     * @param taskId
     * @param variables
     * @return
     */
    public static List<Map<String, String>> getNextTransition(String taskId, Map<String, Object> variables) {
        if (StringUtils.isBlank(taskId)) {
            return new ArrayList<>();
        }
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setTaskId(taskId);
        workFlow.setVariable(variables);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_10");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, String>> rtn = (List<Map<String, String>>) outInfo.get(EiConstant.resultBlock);
        for (Map<String, String> map : rtn) {
            String nodeUser = map.get("nodeUser");
            if (StringUtils.isNotBlank(nodeUser)) {
                List<ADUser> listUser = JSONArray.parseArray(nodeUser, ADUser.class);
                if (null != listUser && !listUser.isEmpty()) {
                    map.put("nodeName",
                            map.get("nodeName") + "("
                                    + listUser.stream().filter(Objects::nonNull).map(user -> user.getUserCode() + "-" + user.getUserName())
                                    .collect(Collectors.joining(","))
                                    + ")");
                }
            }
        }
        return rtn;
    }

    /**
     * 获取流程后续转移
     *
     * @param processCode     流程编码
     * @param currentActivity 当前节点
     * @param variables       参数
     * @return
     */
    public static List<Map<String, String>> getNextTransition(String processCode, String currentActivity,
                                                              Map<String, Object> variables) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessCode(processCode);
        workFlow.setCurrentActivity(currentActivity);
        workFlow.setVariable(variables);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_10");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, String>> rtn = (List<Map<String, String>>) outInfo.get(EiConstant.resultBlock);
        for (Map<String, String> map : rtn) {
            String nodeUser = map.get("nodeUser");
            if (StringUtils.isNotBlank(nodeUser)) {
                List<ADUser> listUser = JSONArray.parseArray(nodeUser, ADUser.class);
                if (null != listUser && !listUser.isEmpty()) {
                    map.put("nodeName",
                            map.get("nodeName") + "("
                                    + listUser.stream().map(user -> user.getUserCode() + "-" + user.getUserName())
                                    .collect(Collectors.joining(","))
                                    + ")");
                }
            }
        }
        return rtn;
    }

    /**
     * 获取任务对应的回退节点配置 禁止回退时，长度为0
     *
     * @param taskId
     * @return "result": [{ "nodeName": "部门经理审批", "nodeKey": "deptManagerApproval"
     * }],
     */
    public static List<Map<String, String>> getReturnsActivities(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return new ArrayList<>();
        }
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setTaskId(taskId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_11");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, String>> rtn = (List<Map<String, String>>) outInfo.get(EiConstant.resultBlock);
        if (ObjectUtil.isEmpty(rtn)) {
            return new ArrayList<>();
        }
        return rtn;
    }

    /**
     * 获取任务对应的回退节点配置 禁止回退时，长度为0
     *
     * @param processCode
     * @param currentActivity
     * @return "result": [{ "nodeName": "部门经理审批", "nodeKey": "deptManagerApproval"
     * }],
     */
    public static List<Map<String, String>> getReturnsActivities(String processCode, String currentActivity) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessCode(processCode);
        workFlow.setCurrentActivity(currentActivity);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_11");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, String>> rtn = (List<Map<String, String>>) outInfo.get(EiConstant.resultBlock);
        if (ObjectUtil.isEmpty(rtn)) {
            return new ArrayList<>();
        }
        return rtn;
    }

    /**
     * 获取流程意见
     *
     * @param processInstanceId
     * @return
     */
    public static List<TaskOption> getCommentList(String processInstanceId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessInstanceId(processInstanceId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_12");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Object> commentList = (List<Object>) outInfo.get(EiConstant.resultBlock);
        if (commentList != null && !commentList.isEmpty()) {
            return commentList.stream().map(map -> BeanUtil.toBean(map, TaskOption.class)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取流程意见
     *
     * @param processInstanceId 流程实例ID
     * @param activityCode      节点编码
     * @return
     */
    public static List<TaskOption> getCommentList(String processInstanceId, String activityCode) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessInstanceId(processInstanceId);
        workFlow.setCurrentActivity(activityCode);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_12");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Object> commentList = (List<Object>) outInfo.get(EiConstant.resultBlock);
        if (commentList != null && !commentList.isEmpty()) {
            return commentList.stream().map(map -> BeanUtil.toBean(map, TaskOption.class)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取流程审批意见，去掉关闭(非主动提交)和待办记录
     * /component/wfCommentList2.html
     *
     * @param processInstanceId
     * @return
     */
    public static List<TaskOption> getNotClosedCommentList(String processInstanceId) {
        List<TaskOption> commentList = getCommentList(processInstanceId);
        return commentList.stream().filter(c -> !"closed".equals(c.getState()) && !"open".equals(c.getState()))
                .collect(Collectors.toList());
    }

    /**
     * 获取流程审批意见，去掉关闭(非主动提交)和待办记录
     * 根据业务ID
     * /component/wfCommentList3.html
     *
     * @param businessId
     * @return
     */
    public static List<TaskOption> getCommentList3(String businessId) {
        EiInfo inInfo = new EiInfo();
        inInfo.set("businessId", businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_28");
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Object> commentList = (List<Object>) outInfo.get("tasks");
        if (commentList != null && !commentList.isEmpty()) {
            return commentList.stream().map(map -> BeanUtil.toBean(map, TaskOption.class)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 根据流程业务ID
     * 获取流程审批意见，去掉关闭(非主动提交)
     * 树格式展示，子流程作为一个任务
     * /component/wfCommentList4.html
     *
     * @param businessId
     * @return
     */
    public static List<TaskOption> getCommentList4(String businessId) {
        EiInfo inInfo = new EiInfo();
        inInfo.set("businessId", businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_281");
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Object> commentList = (List<Object>) outInfo.get("tasks");
        if (commentList != null && !commentList.isEmpty()) {
            /*
            //过滤传阅的记录
            return commentList.stream().map(map -> BeanUtil.toBean(map, TaskOption.class))
                    .filter(taskOption -> !"collaborator".equals(taskOption.getTaskType()))
                    .collect(Collectors.toList());
             */
            return commentList.stream().map(map -> BeanUtil.toBean(map, TaskOption.class))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 根据流程实例ID
     * 获取流程审批意见，去掉关闭(非主动提交)
     * 树格式展示，子流程作为一个任务
     * /component/wfCommentList4.html
     *
     * @param processInstanceId
     * @return
     */
    public static List<TaskOption> getCommentList5(String processInstanceId) {
        EiInfo inInfo = new EiInfo();
        inInfo.set("processInstanceId", processInstanceId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_281");
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Object> commentList = (List<Object>) outInfo.get("tasks");
        if (commentList != null && !commentList.isEmpty()) {
            return commentList.stream().map(map -> BeanUtil.toBean(map, TaskOption.class))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取流程意见
     *
     * @param processInstanceId 流程实例ID
     * @param activityCode      节点编码
     * @return
     */
    public static TaskOption getComment(String processInstanceId, String activityCode) {
        List<TaskOption> commentList = getCommentList(processInstanceId, activityCode);
        Optional<TaskOption> taskOption = commentList.stream().filter(c -> "completed".equals(c.getState()))
                .findFirst();
        return taskOption.orElseGet(TaskOption::new);
    }

    /**
     * 获取流程定义
     *
     * @return map map.put("categoryInfo", categoryInfo);//目录信息
     * map.put("processInfo", processInfo);//流程信息
     */
    public static Map<String, Object> getProcessInfo() {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_13");
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        Map<String, Object> rtn = (Map<String, Object>) outInfo.get(EiConstant.resultBlock);
        return rtn;
    }

    /**
     * 获取流程所有节点
     *
     * @param processKey
     * @return { "nodeName": "人工活动2", "nodeKey": "Manual2" }
     */
    public static List<Map<String, Object>> getActivityInfoByProcessKey(String processKey) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessCode(processKey);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_14");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, Object>> rtn = (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
        return rtn;
    }

    /**
     * 根据流程ID获取流程参数
     *
     * @param processInstanceId
     * @return
     */
    public static Map<String, Object> getVariableByProcessInstanceId(String processInstanceId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessInstanceId(processInstanceId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_15");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        Map<String, Object> rtn = (Map<String, Object>) outInfo.get(EiConstant.resultBlock);
        return rtn;
    }

    /**
     * 根据流程ID修改流程参数
     *
     * @param processInstanceId
     * @param variable
     * @return
     */
    public static boolean updateVariableByProcessInstanceId(String processInstanceId, Map<String, Object> variable) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setVariable(variable);
        workFlow.setProcessInstanceId(processInstanceId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_16");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return true;
    }

    /**
     * 根据业务ID获取主流程信息
     *
     * @param businessId
     * @return
     */
    public static WorkFlow getMainFlowInfoByBusinessId(String businessId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setBusinessId(businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_17");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        WorkFlow rtn = null;
        Object obj = outInfo.get(EiConstant.resultBlock);
        if (obj instanceof Map) {
            rtn = BeanUtil.toBean(obj, WorkFlow.class);
        } else {
            rtn = (WorkFlow) outInfo.get(EiConstant.resultBlock);
        }
        return rtn;
    }

    /**
     * 根据流程ID获取流程信息
     *
     * @param processInstanceId
     * @return
     */
    public static WorkFlow getFlowInfoByProcessId(String processInstanceId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessInstanceId(processInstanceId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_171");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        WorkFlow rtn = null;
        Object obj = outInfo.get(EiConstant.resultBlock);
        if (obj instanceof Map) {
            rtn = BeanUtil.toBean(obj, WorkFlow.class);
        } else {
            rtn = (WorkFlow) outInfo.get(EiConstant.resultBlock);
        }
        return rtn;
    }

    /**
     * 根据业务ID修改业务名称
     *
     * @param operator     操作人
     * @param businessId   业务ID
     * @param businessName 业务名称
     * @return
     */
    public static boolean updateFlowInfoByBusinessId(String operator, String businessId, String businessName) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        workFlow.setBusinessName(businessName);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_18");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return true;
    }

    /**
     * 根据业务ID修改流程相关信息
     *
     * @param operator 操作人
     * @param workFlow 流程信息
     * @return
     */
    public static boolean updateFlowInfoByBusinessId(String operator, WorkFlow workFlow) {
        EiInfo inInfo = new EiInfo();
        if (StringUtils.isEmpty(workFlow.getOperator())) {
            workFlow.setOperator(operator);
        }
        inInfo.set(EiConstant.serviceId, "S_MP_WF_18");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return true;
    }

    /**
     * 根据业务ID和组织编码获取子流程信息
     *
     * @param businessId
     * @param orgCode
     * @return
     */
    public static WorkFlow getChildFlowInfoByBusinessIdAndOrgCode(String businessId, String orgCode) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setBusinessId(businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_19");
        inInfo.set("workFlow", workFlow);
        inInfo.set("orgCode", orgCode);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        WorkFlow rtn = null;
        Object obj = outInfo.get(EiConstant.resultBlock);
        if (obj instanceof Map) {
            rtn = BeanUtil.toBean(obj, WorkFlow.class);
        } else {
            rtn = (WorkFlow) outInfo.get(EiConstant.resultBlock);
        }
        return rtn;
    }

    /**
     * 根据任务ID修改流程意见
     *
     * @param taskId
     * @param comment
     * @return
     */
    public static boolean updateComment(String taskId, String comment) {
        if (comment == null || comment.trim().length() <= 0)
            return true;
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setTaskId(taskId);
        workFlow.setComment(comment);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_20");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return true;
    }

    /**
     * 根据任务ID获取流程意见
     *
     * @param taskId
     * @return
     */
    public static String getComment(String taskId) {
        if (StringUtils.isEmpty(taskId))
            return null;
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setTaskId(taskId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_23");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getString(EiConstant.resultBlock);
    }

    /**
     * 获取对应节点配置的角色值
     *
     * @param processCode
     * @param currentActivity
     * @return
     */
    public static String getRoleValue(String processCode, String currentActivity) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessCode(processCode);
        workFlow.setCurrentActivity(currentActivity);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_21");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getString(EiConstant.resultBlock);
    }

    /**
     * 根据业务ID获取子流程信息
     *
     * @param businessId
     * @return
     */
    public static List<Map<String, Object>> getChildFlowInfoByBusinessId(String businessId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setBusinessId(businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_22");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, Object>> rtn = (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
        return rtn;
    }

    /**
     * 根据业务ID获取会签子流程信息
     *
     * @param businessId
     * @return
     */
    public static List<Map<String, Object>> getHQChildFlowInfoByBusinessId(String businessId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setBusinessId(businessId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_22");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, Object>> rtn = (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
        if (rtn!=null) {
            rtn = rtn.stream().filter(map -> (map.get("departmentNo") != null && map.get("departmentNo").toString().length() != 6)).collect(Collectors.toList());
        }
        return rtn;
    }

    /**
     * 根据流程实例ID获取子流程信息
     *
     * @param processInstanceId
     * @return
     */
    public static List<Map<String, Object>> getChildFlowInfoByProcessInstanceId(String processInstanceId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessInstanceId(processInstanceId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_221");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        List<Map<String, Object>> rtn = (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
        return rtn;
    }


    /**
     * 获取流程后续转移 为了wfSubmitOne.html定制
     *
     * @param taskId
     * @param variables
     * @return
     */
    public static List<Map<String, String>> getNextTransitionForSubmitOne(String taskId,
                                                                          Map<String, Object> variables) {
        List<Map<String, String>> list = getNextTransition(taskId, variables);
        if (null == list || list.size() == 0) {
            return list;
        }
        String returnFlag = "jumpReturnNode";
        Map<String, String> returnNode = null;
        Map<String, String> submitTransition = null;// 取其中一个
        for (Map<String, String> map : list) {
            String transitionKey = map.get("transitionKey");
            if (transitionKey.startsWith(returnFlag)) {
                returnNode = map;
            } else {
                submitTransition = map;
            }
        }
        List<Map<String, String>> rtn = new ArrayList<>();
        if (list.size() <= 1) {
            submitTransition.put("transitionKey", "");// 修改按钮标签为空
            submitTransition.put("transitionName", "提交");// 修改按钮名字
            rtn.add(submitTransition);
            return list;
        }
        if (returnNode != null && list.size() == 2) {// 有退回节点
            submitTransition.put("transitionKey", "");// 修改按钮标签为空
            submitTransition.put("transitionName", "提交");// 修改按钮名字
            rtn.add(submitTransition);
            rtn.add(returnNode);
            return rtn;
        } else {
            submitTransition.put("transitionKey", "");// 修改按钮标签为空
            submitTransition.put("transitionName", "提交");// 修改按钮名字
            submitTransition.put("nodeKey", "");
            String nodeName = list.stream().filter(map -> !map.get("transitionKey").startsWith(returnFlag))
                    .map(map -> map.get("nodeName")).collect(Collectors.joining("<br>"));
            // String nodeName = "下一步";
            submitTransition.put("nodeName", nodeName);

            if (null != returnNode) {
                rtn.add(returnNode);
                rtn.add(submitTransition);
            } else {
                rtn.add(submitTransition);
            }
        }
        return rtn;
    }

    /**
     * 根据任务ID获取流程信息
     *
     * @param taskId
     * @return
     */
    public static WorkFlow getWorkFlowByTaskId(String taskId) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setTaskId(taskId);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_24");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        WorkFlow rtn = null;
        Object obj = outInfo.get(EiConstant.resultBlock);
        if (obj instanceof Map) {
            rtn = BeanUtil.toBean(obj, WorkFlow.class);
        } else {
            rtn = (WorkFlow) outInfo.get(EiConstant.resultBlock);
        }
        return rtn;
    }

    /**
     * 根据业务ID和流程编码获取流程信息
     *
     * @param businessId
     * @param processCode
     * @return
     */
    public static WorkFlow getWorkFlowByBusinessIdAndProcessCode(String businessId, String processCode) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setBusinessId(businessId);
        workFlow.setProcessCode(processCode);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_241");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        WorkFlow rtn = null;
        Object obj = outInfo.get(EiConstant.resultBlock);
        if (obj instanceof Map) {
            rtn = BeanUtil.toBean(obj, WorkFlow.class);
        } else {
            rtn = (WorkFlow) outInfo.get(EiConstant.resultBlock);
        }
        return rtn;
    }

    /**
     * 根据流程编码和节点编码获取节点的扩展属性
     *
     * @param processCode
     * @param currentActivity
     * @return
     */
    public static Map<String, Object> getActExtraParameters(String processCode, String currentActivity) {
        EiInfo inInfo = new EiInfo();
        WorkFlow workFlow = new WorkFlow();
        workFlow.setProcessCode(processCode);
        workFlow.setCurrentActivity(currentActivity);
        inInfo.set(EiConstant.serviceId, "S_MP_WF_242");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return (Map<String, Object>)outInfo.get(EiConstant.resultBlock);
    }

    /**
     * 获取提交下一步的信息
     *
     * @param workFlow
     * @return
     */
    public static String getNextSubmitInfo(WorkFlow workFlow) {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_25");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        Object result = outInfo.get(EiConstant.resultBlock);
        if (null != result) {
            workFlow = BeanUtil.toBean(result, WorkFlow.class);
            return workFlow.getCurrentActivityName() + ":" + workFlow.getCurrentOperator();
        } else {
            return null;
        }
    }

    /**
     * 获取流程开始并提交后下一步信息
     *
     * @param workFlow
     * @return
     */
    public static WorkFlow getNextStartAndSubmitWF(WorkFlow workFlow) {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_251");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        Object result = outInfo.get(EiConstant.resultBlock);
        if (null != result) {
            workFlow = BeanUtil.toBean(result, WorkFlow.class);
            if (workFlow.getNextActivitys()!=null && workFlow.getNextActivitys().size()==1 && workFlow.getNextActivitys().get(0)==null) {
                workFlow.setNextActivitys(null);
            }
            return workFlow;
        } else {
            return null;
        }
    }

    /**
     * 关闭任务
     *
     * @param workFlow
     * @return
     */
    public static String closeTask(WorkFlow workFlow) {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_26");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 插入一条已经完成的流程履历
     * 协办 且已完成
     *
     * @param operator 操作用户 一般为当前登陆人
     * @param workFlow
     * @return
     */
    public static String addCompletedTask(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_30");
        inInfo.set("workFlow", workFlow);
        inInfo.set("operator", operator);
        EiInfo outInfo = null;
        if (workFlow.getIsTx()) {
            outInfo = XServiceManager.callTx(inInfo);
        } else {
            outInfo = XServiceManager.call(inInfo);
        }
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }

    /**
     * 获取流程参数对应的值
     *
     * @param businessId
     * @param key
     * @return
     */
    public static Object getVariableValue(String businessId, String key) {
        WorkFlow workFlow = getMainFlowInfoByBusinessId(businessId);
        if (workFlow!=null) {
            try {
                return workFlow.getVariable().get(key);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    /**
     * 获取提交下一步的信息
     *
     * @param workFlow
     * @return
     */
    public static WorkFlow getNextSubmitWF(WorkFlow workFlow) {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_25");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        Object result = outInfo.get(EiConstant.resultBlock);
        if (null != result) {
            workFlow = BeanUtil.toBean(result, WorkFlow.class);
            if (workFlow.getNextActivitys()!=null && workFlow.getNextActivitys().size()==1 && workFlow.getNextActivitys().get(0)==null) {
                workFlow.setNextActivitys(null);
            }
            return workFlow;
        } else {
            return null;
        }
    }

    /**
     * 获取用户能撤回的目标任务
     *
     * @param operator
     * @param businessId
     * @return
     */
    public static List<Map<String, Object>> getUndoTask(String operator, String businessId) {
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId(businessId);
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_31");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
        }
        Object result = outInfo.get(EiConstant.resultBlock);
        if (null != result) {
            return (List<Map<String, Object>>)result;
        } else {
            return null;
        }
    }

    /**
     * 获取用户能撤回的目标任务
     *
     * @param businessId
     * @return
     */
    public static List<Map<String, Object>> getUndoTask(String businessId) {
        return getUndoTask(UserSession.getLoginName(), businessId);
    }

    /**
     * 撤回
     *
     * @param workFlow
     * @return
     */
    public static String doUndo(String operator, WorkFlow workFlow) {
        if (StringUtils.isNotEmpty(workFlow.getOperator())) {
            operator = workFlow.getOperator();
        } else {
            workFlow.setOperator(operator);
        }
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_WF_32");
        inInfo.set("workFlow", workFlow);
        EiInfo outInfo = XServiceManager.call(inInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo.getMsg();
    }
}
