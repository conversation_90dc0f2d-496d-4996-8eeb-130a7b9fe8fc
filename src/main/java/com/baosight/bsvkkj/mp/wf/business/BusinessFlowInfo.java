package com.baosight.bsvkkj.mp.wf.business;

import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BusinessFlowInfo extends BusinessBase {
	
	/**
	 * 获取用户业务类型待办数目（根据节点分类）
	 * @param operator
	 * @param businessType 多个逗号分割
	 * @return
	 */
	public List<Map<String, Object>> queryBusinessTypeDbList(String operator, String businessType){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(StringUtils.isEmpty(businessType)) {
			throw new PlatException("businessType不能为空");
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("userLabel", operator);
		if(businessType.indexOf(",")>-1) {
			param.put("businessTypes",businessType.split(","));
		}else {
			param.put("businessType", businessType);		
		}
		param.put("displayOrder", " A.FLOW_CODE asc ");
		return dao.query ("MPWFFLowInfo.queryBusinessTypeDbList", param);
	}

	/**
	 * 获取用户业务类型和流程编号待办数目（根据节点分类）
	 * @param operator
	 * @param businessType
	 * @return
	 */
	public List<Map<String, Object>> queryBusinessTypeAndFlowCodesDbList(String operator, String businessType,String flowCodes){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(StringUtils.isEmpty(businessType)) {
			throw new PlatException("businessType不能为空");
		}
		if(StringUtils.isEmpty(flowCodes)) {
			throw new PlatException("flowCodes不能为空");
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("userLabel", operator);
		param.put("businessType", businessType);
		param.put("processCodes",flowCodes);
		return dao.query ("MPWFFLowInfo.queryBusinessTypeDbList", param);
	}

	/**
	 * 获取用户所有待办
	 * @param operator
	 * @return
	 */
	public List<Map<String, Object>> queryDbList(String operator){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("userLabel", operator);
		return dao.query ("MPWFFLowInfo.queryDbList", param);
	}



	/**
	 * @param param
	 * @return List
	 */
	public TableDataInfo queryDB(String operator, Map<String,Object> param){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(param==null) {
			param = new HashMap<String, Object>();
		}
		param.put("userLabel", operator);
		param.put("displayOrder", "A.LAST_TIME DESC");
		return getPage("MPWFFLowInfo.queryDB", param);
	}
	public List<Map> queryDBList(String operator, Map<String,Object> param){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(param==null) {
			param = new HashMap<String, Object>();
		}
		param.put("userLabel", operator);
		param.put("displayOrder", "A.LAST_TIME DESC");
		return dao.query("MPWFFLowInfo.queryDB", param);
	}
	
	/**
	 * @param param
	 * @return List
	 */
	public TableDataInfo queryYB(String operator, Map<String,Object> param){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(param==null) {
			param = new HashMap<String, Object>();
		}
		param.put("userLabel", operator);
		return getPage("MPWFFLowInfo.queryYB", param);
	}
	
	/**
	 * 获取用户业务类型待办数目（根据节点分类）
	 * @param operator
	 * @param businessType
	 * @param moduleCode 专家评审评审所属模块
	 * @return
	 */
	public List<Map<String, Object>> queryMPPSBusinessTypeDbList(String operator, String moduleCode){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(StringUtils.isEmpty(moduleCode)) {
			throw new PlatException("moduleCode不能为空");
		}


		String[] split = moduleCode.split(",");
		String moduleCodeInSql = "'" + Arrays.asList(split).stream().collect(Collectors.joining("','")) + "'";

		Map<String, Object> param = new HashMap<String, Object>();
		param.put("userLabel", operator);
		param.put("moduleCodeInSql", moduleCodeInSql);
		param.put("displayOrder", "p.MODULE_CODE");

		return dao.query ("MPWFFLowInfo.queryMPPSBusinessTypeDbList", param);
	}
	/**
	 * 获取用户业务类型待办数目（根据节点分类）-效益
	 * @param operator
	 * @param businessType
	 * @param moduleCode 效益
	 * @return
	 */
	public List<Map<String, Object>> queryMPPSBusinessTypeDbListToXy(String moduleCode){

		if(StringUtils.isEmpty(moduleCode)) {
			throw new PlatException("moduleCode不能为空");
		}
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("userLabel", UserSession.getLoginName());

		String collect = Arrays.stream(moduleCode.split(",")).collect(Collectors.joining("','"));

		param.put("moduleCodeIn","'"+collect+"'");
		List<Map<String,Object>> query = dao.query("MPWFFLowInfo.queryMPPSBusinessTypeDbListToXy", param);

		return query;
	}
	/**
	 * @param param
	 * @return List
	 */
	public TableDataInfo queryZGSJ(String operator, Map<String,Object> param){
		if(StringUtils.isEmpty(operator)) {
			throw new PlatException("operator不能为空");
		}
		if(param==null) {
			param = new HashMap<String, Object>();
		}
		param.put("userLabel", operator);
		param.put("displayOrder", "A.LAST_TIME DESC");
		return getPage("tkyxmBmzgxmsj.queryZGSJ", param);
	}

	public void deleteByBusinessGuid(String businessGuid) {
		if(StringUtils.isNotEmpty(businessGuid))
			dao.delete("MPWFFLowInfo.deleteByBusinessGuid", businessGuid);
	}
}
