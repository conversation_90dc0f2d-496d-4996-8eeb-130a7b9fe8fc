<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="MPWFFLowInfo">
	<!-- 业务类型待办列表 -->
	<select id="queryBusinessTypeDbList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			A.FLOW_CODE as "processCode" ,
			C.PROCESS_NAME AS "processName" ,
			A.CURRENT_ACTIVITY as "currentActivity" ,
			A.CURRENT_ACTIVITY_NAME as "currentActivityName" ,
			COUNT(A.CURRENT_ACTIVITY) as "todoNum",
			A.BUSINESS_TYPE as "businessType"
		FROM 
			${ggmkSchema}.T_MPWF_FLOW_INFO A ,${platSchema}.TEWPT00 B, ${platSchema}.TEWPD01 C
		WHERE 
		A.FLOW_CODE = C.PROCESS_KEY
		AND C.PROCESS_VERSION = (SELECT MAX(CAST(D.PROCESS_VERSION AS integer)  ) 
			FROM  ${platSchema}.TEWPD01 D WHERE D.PROCESS_KEY = C.PROCESS_KEY AND D.STATE = 'published')
		AND A.FLOW_ID = B.PROCESS_INSTANCE_ID
		AND B.STATE = 'open'
		AND A.FLOW_STATE = 'active'
		and A.FLOW_CODE not in ('MPPS_members_review','MPPS_leaderReview','YWZT_KJXY')
		<isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE =  #processCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processCodes">A.FLOW_CODE in($processCodes$) </isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessType">BUSINESS_TYPE =  #businessType#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessTypes"> 
				BUSINESS_TYPE IN
				<iterate property="businessTypes" conjunction="," open="(" close=")">#businessTypes[]#</iterate>
			</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessName">BUSINESS_NAME =  #businessName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessNameLike">BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY =  #currentActivity#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityName">A.CURRENT_ACTIVITY_NAME =  #currentActivityName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityNameLike">A.CURRENT_ACTIVITY_NAME like '%$currentActivityNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
		GROUP BY A.FLOW_CODE, C.PROCESS_NAME, A.CURRENT_ACTIVITY ,A.CURRENT_ACTIVITY_NAME,A.BUSINESS_TYPE
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>


	<select id="queryDbList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		COUNT(A.CURRENT_ACTIVITY) as "todoNum",
		BUSINESS_TYPE as "businessType"
		FROM
		${ggmkSchema}.T_MPWF_FLOW_INFO A ,${platSchema}.TEWPT00 B, ${platSchema}.TEWPD01 C
		WHERE
		A.FLOW_CODE = C.PROCESS_KEY
		AND C.PROCESS_VERSION = (SELECT MAX(D.PROCESS_VERSION )
		FROM  ${platSchema}.TEWPD01 D WHERE D.PROCESS_KEY = C.PROCESS_KEY AND D.STATE = 'published')
		AND A.FLOW_ID = B.PROCESS_INSTANCE_ID
		AND B.STATE = 'open'
		AND A.FLOW_STATE = 'active'
		and BUSINESS_TYPE!='DEMO'
		<isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE =  #processCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processCodes">A.FLOW_CODE in($processCodes$) </isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessType">BUSINESS_TYPE =  #businessType#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessName">BUSINESS_NAME =  #businessName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessNameLike">BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY =  #currentActivity#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityName">A.CURRENT_ACTIVITY_NAME =  #currentActivityName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityNameLike">A.CURRENT_ACTIVITY_NAME like '%$currentActivityNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
		GROUP BY BUSINESS_TYPE
		order by "todoNum" desc
	</select>
	
	<!-- 取当前登陆用户的待办 -->
	<select id="queryDB"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
	        A.FLOW_CODE             AS "processCode"        ,
	        A.CURRENT_ACTIVITY      AS "currentActivity"    ,
	        A.CURRENT_ACTIVITY_NAME AS "currentActivityName",
	        A.FLOW_ID   			AS "flowId"             ,	     
	        A.PARENT_FLOW_ID   	    AS "parentFlowId"       ,   
	        A.BUSINESS_ID           AS "businessId"         ,
	        A.BUSINESS_NAME         AS "businessName"       ,
	        A.LAST_OPERATOR_NAME    AS "lastOperatorName"   ,
	        A.LAST_TIME             AS "lastTime"           ,
	        A.DEPARTMENT_NO         AS "departmentNo"       ,
	        A.BUSINESS_TYPE 		AS "businessType"		,
	        B.FORM                  AS "pageNo"             ,
	        B.PROCESS_INSTANCE_ID   AS "processInstanceId"  ,
	        B.TASK_NAME             AS "taskName"           ,
	        B.TASK_ID               AS "taskId"		        ,
	        B.TASK_TYPE				AS "taskType"
		FROM
	        ${ggmkSchema}.T_MPWF_FLOW_INFO A,
	        ${platSchema}.TEWPT00 B
		WHERE
		  	A.FLOW_ID = B.PROCESS_INSTANCE_ID
		  	AND B.STATE = 'open'
		<isNotEmpty prepend=" AND " property="businessType">A.BUSINESS_TYPE =  #businessType#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID =  #userLabel#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="flowCode">A.FLOW_CODE =  #flowCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY =  #currentActivity#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessNameLike">A.BUSINESS_NAME LIKE '%$businessNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessIdStr">A.BUSINESS_ID in ($businessIdStr$)</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE =  #processCode#</isNotEmpty>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<!-- 取用户的已办 -->
	<select id="queryYB"  parameterClass="hashmap" resultClass="hashmap">
	SELECT 
		FLOW_CODE             AS "processCode"        ,
		FLOW_ID   			  AS "flowId"             ,
        CURRENT_ACTIVITY      AS "currentActivity"    ,
        CURRENT_ACTIVITY_NAME AS "currentActivityName",
        BUSINESS_ID           AS "businessId"         ,
        BUSINESS_NAME         AS "businessName"       ,
        CURRENT_OPERATOR      AS "currentOperator"    ,
        PROCESS_INSTANCE_ID   AS "processInstanceId"  ,
        TASK_NAME             AS "taskName"           ,
        TASK_ID               AS "taskId"		      ,
		TASK_TYPE             AS "taskType"
	FROM
		${ggmkSchema}.V_MPWF_YB
	WHERE ASSIGNEE_ID = #userLabel#
		<isNotEmpty prepend=" AND " property="businessType">BUSINESS_TYPE =  #businessType#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="flowCode">FLOW_CODE =  #flowCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivity">CURRENT_ACTIVITY =  #currentActivity#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessNameLike">BUSINESS_NAME like  '%$businessNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<!-- 获取专家评审待办数 -->
	<select id="queryMPPSBusinessTypeDbList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			A.FLOW_CODE as "processCode" ,
			C.PROCESS_NAME AS "processName" ,
			A.CURRENT_ACTIVITY as "currentActivity" ,
			A.CURRENT_ACTIVITY_NAME as "currentActivityName" ,
			p.MODULE_CODE      as "moduleCode",
			min(m.BUSINES_NAME)   as "moduleName",
			COUNT(A.CURRENT_ACTIVITY) as "todoNum"
		FROM
			${ggmkSchema}.T_MPWF_FLOW_INFO A ,
			${platSchema}.TEWPT00 B,
			${platSchema}.TEWPD01 C,
			${ggmkSchema}.T_MPPS_REVIEW_REQ P ,
			${ggmkSchema}.T_MPPS_MODULE M
		WHERE 
		A.FLOW_CODE = C.PROCESS_KEY
		AND A.FLOW_ID = B.PROCESS_INSTANCE_ID
		AND B.ACT_PROCESS_DEF_ID=C.ACT_PROC_DEF_ID
		AND A.BUSINESS_ID = P.REVIEWREQ_ID
		AND B.STATE = 'open'
		AND A.FLOW_STATE = 'active'
		and p.MODULE_CODE=m.BUSINES_CODE
		<isNotEmpty prepend=" AND " property="moduleCode">P.MODULE_CODE =  #moduleCode#</isNotEmpty>

		<isNotEmpty prepend=" AND " property="moduleCodeInSql">P.MODULE_CODE in ($moduleCodeInSql$)</isNotEmpty>

		<isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE =  #processCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processCodes">A.FLOW_CODE in($processCodes$) </isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessType">BUSINESS_TYPE =  #businessType#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessName">BUSINESS_NAME =  #businessName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessNameLike">BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY =  #currentActivity#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityName">A.CURRENT_ACTIVITY_NAME =  #currentActivityName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityNameLike">A.CURRENT_ACTIVITY_NAME like '%$currentActivityNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
		GROUP BY p.MODULE_CODE, A.FLOW_CODE, C.PROCESS_NAME, A.CURRENT_ACTIVITY ,A.CURRENT_ACTIVITY_NAME
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>


	<!-- 获取效益评审待办数 -->
	<select id="queryMPPSBusinessTypeDbListToXy"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		A.FLOW_CODE as "processCode" ,
		C.PROCESS_NAME AS "processName" ,
		A.CURRENT_ACTIVITY as "currentActivity" ,
		A.CURRENT_ACTIVITY_NAME as "currentActivityName" ,
		COUNT(A.CURRENT_ACTIVITY) as "todoNum"
		FROM
		${ggmkSchema}.T_MPWF_FLOW_INFO A ,
		${platSchema}.TEWPT00 B,
		${platSchema}.TEWPD01 C,
		${ggmkSchema}.T_MPXY_SPB S
		WHERE
		A.FLOW_CODE = C.PROCESS_KEY
		AND B.ACT_PROCESS_DEF_ID=C.ACT_PROC_DEF_ID
		AND A.FLOW_ID = B.PROCESS_INSTANCE_ID
		AND A.BUSINESS_ID = S.RECORD_ID
		AND B.STATE = 'open'
		AND A.FLOW_STATE = 'active'
		<isNotEmpty prepend=" AND " property="moduleCode">S.EXTRA2 =  #moduleCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="moduleCodeIn">S.EXTRA2 in ($moduleCodeIn$)</isNotEmpty>

		<isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE =  #processCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="processCodes">A.FLOW_CODE in($processCodes$) </isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessType">BUSINESS_TYPE =  #businessType#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessName">BUSINESS_NAME =  #businessName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="businessNameLike">BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY =  #currentActivity#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityName">A.CURRENT_ACTIVITY_NAME =  #currentActivityName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="currentActivityNameLike">A.CURRENT_ACTIVITY_NAME like '%$currentActivityNameLike$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
		GROUP BY A.FLOW_CODE, C.PROCESS_NAME, A.CURRENT_ACTIVITY ,A.CURRENT_ACTIVITY_NAME
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<delete id="deleteByBusinessGuid" parameterClass="string">
		DELETE FROM  ${ggmkSchema}.T_MPWF_FLOW_INFO
		WHERE 		BUSINESS_ID=#value# 	
	</delete>

	<!--查看所有推广移植年度计划活动的节点关闭-->
	<select id="queryFlowKttg"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		FLOW_ID as "flowId",
		FLOW_CODE as "flowCode",
		FLOW_STATE as "flowState",
		BUSINESS_ID as "businessId",
		BUSINESS_TYPE as "businessType",
		BUSINESS_NAME as "businessName",
		LAST_OPERATOR as "lastOperator",
		LAST_OPERATOR_NAME as "lastOperatorName",
		LAST_TIME as "lastTime",
		LAST_ACT as "lastAct",
		CURRENT_OPERATOR as "operator",
		CURRENT_ACTIVITY as "activity",
		CURRENT_ACTIVITY_NAME as "activityName"
		 FROM ggmk.T_MPWF_FLOW_INFO
		 WHERE FLOW_CODE ='KTTG_NDJH' AND FLOW_STATE ='active' and CURRENT_ACTIVITY_NAME not in ('终止')
	</select>
</sqlMap>