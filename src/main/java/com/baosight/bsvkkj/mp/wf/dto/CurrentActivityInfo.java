package com.baosight.bsvkkj.mp.wf.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CurrentActivityInfo {
    //流程编码
    private String processKey;
    //当前活动节点Code，多个逗号隔开
    private String currentActivityCode;
    //当前活动节点名称，多个逗号隔开
    private String currentActivityName;
    //当前活动节点类型
    private String currentActivityType;
    //当前用户Code，多个逗号隔开
    private String currentOperatorCode;
    //当前用户名称，多个逗号隔开
    private String currentOperatorName;
    //当前用户（'Code'-'Name'），多个逗号隔开
    private String currentOperator;

}
