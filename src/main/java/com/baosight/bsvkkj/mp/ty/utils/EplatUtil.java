package com.baosight.bsvkkj.mp.ty.utils;

import com.baosight.bsvkkj.utils.HttpClientUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.util.ArrayList;
import java.util.List;

public class EplatUtil {
	
	//输入参数key
	public static String INPUT_OBEJCT_KEY = "inputObjectKey";
	
	//输出参数key
	public static String OUTPUT_OBEJCT_KEY = "outputObjectKey";
	
	/**
	 * 调用eplat service
	 * @param serviceName
	 * @param methodName
	 * @param object
	 * @return
	 */
	public static Object callEplat(String serviceName, String methodName, Object object) {		
		EiInfo eiInfo = new EiInfo();
		eiInfo.set(EiConstant.serviceName, serviceName);
		eiInfo.set(EiConstant.methodName, methodName);
		eiInfo.addBlock(EiConstant.queryBlock).set(INPUT_OBEJCT_KEY, object);
		eiInfo = XLocalManager.call(eiInfo);
		if(null == eiInfo.getBlock(EiConstant.resultBlock)) {
			return null;
		}
		return eiInfo.getBlock(EiConstant.resultBlock).get(OUTPUT_OBEJCT_KEY);
	}

	/**
	 * 获取共享服务token
	 * @return
	 */
	public static String getToken() throws Exception{
		CloseableHttpClient client;
		String domain = PlatApplicationContext.getProperty("eplat.domain.service");
		//合包环境应用上下文为eplat，分包环境上下文为base-security-service
		String app = "base-security-service";
		//url = 域名 + 应用 + /oauth/token
		String url = domain+app+"/oauth/token";

		if (url.indexOf("https") == 0) {
			SSLContext sslcontext = HttpClientUtil.createIgnoreVerifySSL();
			// 设置协议http和https对应的处理socket链接工厂的对象
			Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
					.<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.INSTANCE)
					.register("https", new SSLConnectionSocketFactory(sslcontext)).build();
			PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(
					socketFactoryRegistry);
			HttpClients.custom().setConnectionManager(connManager);
			client = HttpClients.custom().setConnectionManager(connManager).build();
		} else {
			client = HttpClients.createDefault();
		}
		HttpPost post = new HttpPost(url);
		post.setHeader("Content-Type", "application/x-www-form-urlencoded");
		List<NameValuePair> list = new ArrayList<>();
		String clientId = PlatApplicationContext.getProperty("jtfw-mp.clientId");
		String clientSecret = PlatApplicationContext.getProperty("jtfw-mp.clientSecret");
		list.add(new BasicNameValuePair("client_id", clientId));
		list.add(new BasicNameValuePair("client_secret", clientSecret));
		list.add(new BasicNameValuePair("grant_type", "client_credentials"));
		list.add(new BasicNameValuePair("scope", "read"));
		HttpEntity entity = new UrlEncodedFormEntity(list, "utf-8");
		post.setEntity(entity);
		CloseableHttpResponse response = client.execute(post);
		if (response.getStatusLine().getStatusCode() == 200) {
			JSONObject jsonObject = JSONObject.fromObject(EntityUtils.toString(response.getEntity()));
			String access_token = (String) jsonObject.get("access_token");
			System.out.println(access_token);
			return access_token;
		}
		return null;
	}
}
