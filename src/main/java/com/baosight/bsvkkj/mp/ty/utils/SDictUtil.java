package com.baosight.bsvkkj.mp.ty.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component("SDictUtil")
public class SDictUtil {

    private static final String DICT_CACHE_KEY = "jtfw-mp:MPTY:dict";

    /**
     * 获取相关小代码
     *
     * @param businessType 模块
     * @param dictCode     代码
     * @return
     */
    public static List<Map<String, Object>> getDictList(String businessType, String dictCode) {
        Map dictCache = CacheManager.getCache(DICT_CACHE_KEY);

        List<Map<String, Object>> rtn = new ArrayList<Map<String, Object>>();
        if (null != dictCache.get(businessType + "_" + dictCode)) {
            rtn = (List<Map<String, Object>>) dictCache.get(businessType + "_" + dictCode);
        } else {
            EiInfo inInfo = new EiInfo();
            inInfo.set(EiConstant.serviceId, "S_MP_TY_07");
            inInfo.set("businessType", businessType);
            inInfo.set("dictCode", dictCode);
            EiInfo outInfo = XServiceManager.call(inInfo);
            rtn = (List<Map<String, Object>>) outInfo.get("dictList");
            if (rtn != null && !rtn.isEmpty()) {
                dictCache.put(businessType + "_" + dictCode, rtn);
            }

        }
        return rtn;
    }

    /**
     * 获取小代码子类
     *
     * @param businessType 模块
     * @param dictCode     代码
     * @param dictValue    代码值
     * @return
     */
    public static Map<String, Object> getDict(String businessType, String dictCode, String dictValue) {
        List<Map<String, Object>> list = getDictList(businessType, dictCode);
        if (list != null && !list.isEmpty()) {
            if (StrUtil.isNotBlank(dictValue)) {
                String[] split = dictValue.split(",");
                List<String> strings = Arrays.asList(split);
                List<Map<String, Object>> rtnList = list.stream().filter(dict -> strings.contains(dict.get("dictValue"))).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(rtnList)) {
                    return rtnList.get(0);
                }
            }
        }
        return null;
    }

    /**
     * 获取小代码名称
     *
     * @param businessType 模块
     * @param dictCode     代码
     * @param dictValue    代码值
     * @return
     */
    public static String getDictName(String businessType, String dictCode, String dictValue) {
        Map<String, Object> rtn = getDict(businessType, dictCode, dictValue);
        if (null != rtn) {
            return (String) rtn.get("dictName");
        }
        return dictValue;
    }

    public static String getDictNames(String businessType, String dictCode, String dictValues) {
        if(StringUtils.isBlank(dictValues)) {
            return "";
        }
        String [] dictValue = dictValues.split(",");
        if(dictValue.length==1){
            return getDictName(businessType, dictCode, dictValue[0]);
        }
        List<String> strings = Arrays.asList(dictValue);
        List<String> dictNames = new ArrayList<>();
        for (String str : strings) {
            Map<String, Object> rtn = getDict(businessType, dictCode, str);
            if (null != rtn) {
                String dictName =  (String) rtn.get("dictName");
                dictNames.add(dictName);
            }
        }
        return dictNames.stream().collect(Collectors.joining(","));
    }
    /**
     * 获取小代码值
     *
     * @param businessType 模块
     * @param dictCode     代码
     * @return
     */
    public static String getDictValue(String businessType, String dictCode) {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_TY_10");
        inInfo.set("businessType", businessType);
        inInfo.set("dictCode", dictCode);
        EiInfo outInfo = XServiceManager.call(inInfo);
        Map<String, Object> dict = (Map<String, Object>) outInfo.get("dict");
        if (dict != null) {
            return (String) dict.get("dictValue");
        }
        return null;
    }

    /**
     * 获取小代码
     *
     * @param businessType 模块
     * @param dictCode     代码
     * @return
     */
    public static Map<String, Object> getDict(String businessType, String dictCode) {
        Map dictCache = CacheManager.getCache(DICT_CACHE_KEY);
        if (null != dictCache.get(businessType + "_dict_" + dictCode)) {
            return (Map<String, Object>) dictCache.get(businessType + "_dict_" + dictCode);
        } else {
            EiInfo inInfo = new EiInfo();
            inInfo.set(EiConstant.serviceId, "S_MP_TY_10");
            inInfo.set("businessType", businessType);
            inInfo.set("dictCode", dictCode);
            EiInfo outInfo = XServiceManager.call(inInfo);
            Map<String, Object> dict = (Map<String, Object>) outInfo.get("dict");
            if (dict != null) {
                dictCache.put(businessType + "_dict_" + dictCode, dict);
                return dict;
            }
        }
        return null;
    }


    /**
     * 根据dictValue 获取引导图对应节点
     *
     * @param businessType
     * @param dictCode
     * @param dictValue
     * @return
     */
    public static Map<String, Object> getYDTBYValue(String businessType, String dictCode, String dictValue) {
        EiInfo inInfo = new EiInfo();
        inInfo.set(EiConstant.serviceId, "S_MP_TY_09");
        inInfo.set("businessType", businessType);
        inInfo.set("dictCode", dictCode);
        inInfo.set("dictValue", dictValue);
        EiInfo outInfo = XServiceManager.call(inInfo);
        List<Map<String, Object>> dictList = (List<Map<String, Object>>) outInfo.get("dictList");
        if (dictList != null && !dictList.isEmpty()) {
            return dictList.get(0);
        }
        return null;
    }

    /**
     * 自定义数据 查询名称
     *
     * @param data
     * @param key
     * @param nameKey
     * @param values
     * @return
     */
    public static String getNameByData(Object data, String key, String nameKey, String values) {
        if (StrUtil.isNotBlank(values) && data != null && StrUtil.isNotBlank(key) && StrUtil.isNotBlank(nameKey)) {
            String[] split = values.split(",");
            List<String> strings = Arrays.asList(split);
            if (data instanceof List) {
                List<Object> list = new ArrayList<>((List) data);
                String collect = list.stream().map(map -> BeanUtil.beanToMap(map)).filter(map -> {
                    Map<String, Object> dataMap = BeanUtil.beanToMap(map);
                    Object o = dataMap.get(key);
                    return strings.contains(o);
                }).map(map -> (String) map.get(nameKey)).collect(Collectors.joining(","));

                return collect;
            }

        }
        return values;
    }


    public static Object getValueByKey(Object obj, String key) {
        Map<String, Object> map = BeanUtil.beanToMap(obj);
        return map.get(key);

    }

}
