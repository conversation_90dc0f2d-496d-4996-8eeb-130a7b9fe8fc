package com.baosight.bsvkkj.mp.ty.controller;

import com.alibaba.fastjson.JSONObject;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.mp.ty.dto.AttachmentMap;
import com.baosight.bsvkkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.eplat.upload.service.FileDownloadManager;
import com.baosight.iplat4j.core.ProjectInfo;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.eu.dm.PlatFileUploadUtils;
import com.baosight.iplat4j.eu.dm.util.CheckUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 附件相关
 */
@RestController
@RequestMapping(value = "/attachment")
public class ControllerAttachment extends BaseController {

	private static final String STORE_TYPE = "s3";//remote

	/**
	 * 可以同时上传多个
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/upload", method = RequestMethod.POST)
	public JSONObject upload(HttpServletRequest request, HttpServletResponse response) {
		JSONObject returnJSON = new JSONObject();
		try {
			String operator = UserSession.getUserId();
			CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
					request.getSession().getServletContext());
			if (multipartResolver.isMultipart(request)) {
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					MultipartFile multipartFile = multiRequest.getFile(iter.next());
					if (!multipartFile.isEmpty()) {
						String fileName = multipartFile.getOriginalFilename();

						/**
						 * 校验
						 */
						String fileType = fileName.substring(fileName.lastIndexOf(".")+1, fileName.length());
						CheckUtils.checkUploadFileExtension(fileType);

						Map inMap = new HashMap();
						inMap.put("groupId", ProjectInfo.getComponentName());// 文件分组标识
						inMap.put("configPathDefine", SAttachmentUtil.getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

						String md5Hash = DigestUtils.md5Hex(new ByteArrayInputStream(multipartFile.getBytes()));
						Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(multipartFile, inMap);
						returnJSON = (JSONObject) JSONObject.toJSON(stringStringMap);
						System.out.println(returnJSON);

						String attachmentId = stringStringMap.get("docId");
						if ("-1".equals(stringStringMap.get("status"))) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						if (StringUtils.isBlank(attachmentId)) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						String docUrl = stringStringMap.get("docUrl");
						SAttachmentUtil.addAttachment(operator, attachmentId, fileName, STORE_TYPE, docUrl,
								String.valueOf(multipartFile.getSize()), md5Hash);

						returnJSON.put("fileid", attachmentId);
						returnJSON.put("attachmentName", fileName);
						returnJSON.put("fileSize", String.valueOf(multipartFile.getSize()));
						returnJSON.put("success", true);
						returnJSON.put("msg", "");
					}
				}
			} else {
				returnJSON.put("success", false);
				returnJSON.put("msg", "没有附件");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			returnJSON.put("success", false);
			returnJSON.put("msg", e.getMessage());
		}
		return returnJSON;
	}




	/**
	 * 下载
	 *
	 * @param request
	 * @param response
	 * @throws Exception
	 * @throws ParseException
	 */
	@RequestMapping(value = "/download/{attachmentId}", method = RequestMethod.GET)
	public void download(HttpServletRequest request, HttpServletResponse response,
						 @PathVariable("attachmentId") String attachmentId) throws ParseException, Exception {
		UserSession.web2Service(request);
		//TmptyAttachment attachment = (TmptyAttachment) EplatUtil.callEplat("MPTYAttachment", "query", attachmentId);
		String operator = UserSession.getUserId();
		Map<String,Object> attachment = SAttachmentUtil.downAttachment(operator, attachmentId);
		if (attachment != null) {
			String docUrl = (String)attachment.get("docUrl");
			if(StringUtils.isNotEmpty(docUrl)) {//对象存储返回的url
				InputStream in = null;
				ServletOutputStream sos = null;
				try {
					URL url = new URL(docUrl);
					HttpURLConnection conn = (HttpURLConnection)url.openConnection();
					//设置超时间为3秒
					conn.setConnectTimeout(3*1000);
					//防止屏蔽程序抓取而返回403错误
					conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
					//得到输入流
					in = conn.getInputStream();
					response.setContentType("application/x-unknown;charset=GBK");
					String name = new String(attachment.get("attachmentName").toString().getBytes("GBK"), "ISO8859_1");
					response.setHeader("Content-Disposition", "attachment; filename=" + name);

					sos = response.getOutputStream();
					int i = 0;
					byte[] bt = new byte[8 * 1024];
					while ((i = in.read(bt)) != -1) {
						sos.write(bt, 0, i);
						sos.flush();
					}
				} catch (Exception e) {
					e.printStackTrace();
					throw new PlatException("附件下载失败:" + e.getMessage(), e);
				} finally {
					if (in != null) {
						try {
							in.close();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					if (sos != null) {
						try {
							sos.flush();
							sos.close();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
				}
			} else {
				InputStream in = null;
				ServletOutputStream sos = null;
				try {
					response.setContentType("application/x-unknown;charset=GBK");
					String name = new String(attachment.get("attachmentName").toString().getBytes("GBK"), "ISO8859_1");
					response.setHeader("Content-Disposition", "attachment; filename=" + name);

					Map inMap = new HashMap();
					inMap.put("docId", attachmentId);
					in = FileDownloadManager.downloadFile(inMap);
					sos = response.getOutputStream();
					int i = 0;
					byte[] bt = new byte[8 * 1024];
					while ((i = in.read(bt)) != -1) {
						sos.write(bt, 0, i);
						sos.flush();
					}
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					throw new PlatException("附件下载失败:" + e.getMessage(), e);
				} finally {
					if (in != null) {
						try {
							in.close();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					if (sos != null) {
						try {
							sos.flush();
							sos.close();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
				}
			}
		}else {
			response.setCharacterEncoding("GBK");
			response.getWriter().println("附件不存在！");
		}
		try {
			response.flushBuffer();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		SAttachmentUtil.addAttachmentLog( operator,  attachment,  request);
	}

	/**
	 * 匿名下载
	 *
	 * @param request
	 * @param response
	 * @throws Exception
	 * @throws ParseException
	 */
	@RequestMapping(value = {"/nmDownload/{attachmentId}","/nmDownload/{attachmentId}/{flag}/{fileName}"}, method = RequestMethod.GET)
	public void nmDownload(HttpServletRequest request, HttpServletResponse response,
						   @PathVariable("attachmentId") String attachmentId,
						   @PathVariable(value="flag",required = false) String flag,
						   @PathVariable(value="fileName",required = false) String fileName) throws ParseException, Exception {
		String operator = "nmDownload";
		if(null!=request.getParameter("isMobile")) {
			operator = request.getParameter("operator");
		}
		Map<String,Object> attachment = SAttachmentUtil.downAttachment(operator, attachmentId);
		if (attachment != null) {
			String docUrl = (String)attachment.get("docUrl");
			if(StringUtils.isNotEmpty(docUrl)) {//对象存储返回的url
				String isMobile = request.getParameter("isMobile");
				if("true".equals(isMobile)) {//移动端不能重定向
					InputStream in = null;
					ServletOutputStream sos = null;
					try {
						URL url = new URL(docUrl);
						HttpURLConnection conn = (HttpURLConnection)url.openConnection();
						//设置超时间为3秒
						conn.setConnectTimeout(3*1000);
						//防止屏蔽程序抓取而返回403错误
						conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
						//得到输入流
						in = conn.getInputStream();
						response.setContentType("application/x-unknown;charset=GBK");
						String name = new String(attachment.get("attachmentName").toString().getBytes("GBK"), "ISO8859_1");
						response.setHeader("Content-Disposition", "attachment; filename=" + name);

						sos = response.getOutputStream();
						int i = 0;
						byte[] bt = new byte[8 * 1024];
						while ((i = in.read(bt)) != -1) {
							sos.write(bt, 0, i);
							sos.flush();
						}
					} catch (Exception e) {
						e.printStackTrace();
						throw new PlatException("附件下载失败:" + e.getMessage(), e);
					} finally {
						if (in != null) {
							try {
								in.close();
							} catch (IOException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
						if (sos != null) {
							try {
								sos.flush();
								sos.close();
							} catch (IOException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						}
					}
				} else {
					response.sendRedirect(docUrl);
				}
			}else {
				InputStream in = null;
				ServletOutputStream sos = null;
				try {
					response.setContentType("application/x-unknown;charset=GBK");
					String name = new String(attachment.get("attachmentName").toString().getBytes("GBK"), "ISO8859_1");
					response.setHeader("Content-Disposition", "attachment; filename=" + name);

					Map inMap = new HashMap();
					inMap.put("docId", attachmentId);
					in = FileDownloadManager.downloadFile(inMap);
					sos = response.getOutputStream();
					int i = 0;
					byte[] bt = new byte[8 * 1024];
					while ((i = in.read(bt)) != -1) {
						sos.write(bt, 0, i);
						sos.flush();
					}
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					throw new PlatException("附件下载失败:" + e.getMessage(), e);
				} finally {
					if (in != null) {
						try {
							in.close();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					if (sos != null) {
						try {
							sos.flush();
							sos.close();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
				}
			}
		}else {
			response.setCharacterEncoding("GBK");
			response.getWriter().println("附件不存在！");
		}
		try {
			response.flushBuffer();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}


	/**
	 * 预览
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/preview/{attachmentId}", method = RequestMethod.GET)
	@CrossOrigin
	public void preview(HttpServletRequest request, HttpServletResponse response,
						@PathVariable("attachmentId") String attachmentId) {
		try {
			UserSession.web2Service(request);
			String operator = UserSession.getUserId();

			AttachmentMap attachment = new AttachmentMap();
			attachment.setOperator(operator);
			attachment.setAttachmentId(attachmentId);

			EiInfo inInfo = new EiInfo();
			inInfo.set(EiConstant.serviceId, "S_MP_TY_26");
			inInfo.set("attachment", attachment);
			inInfo.set("operator", operator);
			EiInfo call = XServiceManager.call(inInfo);
			if (call.getStatus() == 1) {
				String preViewUrl = call.getString("fileurl");
				response.sendRedirect(preViewUrl);
			} else {
				throw new PlatException("附件预览失败!" + call.getMsg());
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new PlatException(e.getMessage(), e);
		}
	}

	@RequestMapping(value = "/delete/{attachmentId}", method = RequestMethod.GET)
	public JSONObject delete(HttpServletRequest request, HttpServletResponse response,
							 @PathVariable("attachmentId") String attachmentId) {
		JSONObject returnJSON = new JSONObject();

		try {
			if (!StringUtils.isBlank(attachmentId)) {
				UserSession.web2Service(request);
				String operator = UserSession.getUserId();
				SAttachmentUtil.deleteAttachment(operator, attachmentId);
			}
			returnJSON.put("fileid", attachmentId);
			returnJSON.put("success", true);
			returnJSON.put("msg", "");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			returnJSON.put("success", false);
			returnJSON.put("msg", e.getMessage());
		}

		return returnJSON;
	}

	/**
	 * 获取附件列表
	 *
	 * @throws IOException
	 */
	@RequestMapping(value = "/list/{bizId}/{bizType}", method = RequestMethod.GET)
	public List<AttachmentMap> list(HttpServletRequest request, HttpServletResponse response,
									@PathVariable("bizId") String bizId, @PathVariable("bizType") String bizType) throws IOException {
		List<AttachmentMap> fileWRMOList;
		try {
			if (bizId.equals(null)||bizId.equals("undefined")) {
				fileWRMOList = null;
			} else {
				fileWRMOList = SAttachmentUtil.getAttachmentBySourceId(bizId, bizType);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new PlatException(e.getMessage(), e);
		}
		return fileWRMOList;
	}

	/**
	 * 获取附件列表(加入sourceLable)
	 *
	 * @throws IOException
	 */
	@RequestMapping(value = "/list/{bizId}/{bizType}/{sourceLabel1}", method = RequestMethod.GET)
	public List<AttachmentMap> listBySourceLabel(HttpServletRequest request, HttpServletResponse response,
									@PathVariable("bizId") String bizId, @PathVariable("bizType") String bizType, @PathVariable("sourceLabel1") String sourceLabel1) throws IOException {
		List<AttachmentMap> fileWRMOList;
		try {
			if (bizId.equals(null)||bizId.equals("undefined")) {
				fileWRMOList = null;
			} else {
				if ("undefined".equals(sourceLabel1)){
					sourceLabel1 = null;
				}
				fileWRMOList = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(bizId, bizType, sourceLabel1, null, null);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new PlatException(e.getMessage(), e);
		}
		return fileWRMOList;
	}

	/**
	 * ckeditor 上传图片返回json
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/ckeditorUpload", method = RequestMethod.POST)
	public JSONObject ckeditorUpload(HttpServletRequest request, HttpServletResponse response) {

		JSONObject returnJSON = new JSONObject();
		try {
			UserSession.web2Service(request);
			String operator = UserSession.getUserId();

			CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
					request.getSession().getServletContext());
			if (multipartResolver.isMultipart(request)) {
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					MultipartFile multipartFile = multiRequest.getFile(iter.next());
					if (!multipartFile.isEmpty()) {
						String fileName = multipartFile.getOriginalFilename();

						Map inMap = new HashMap();
						inMap.put("groupId", ProjectInfo.getComponentName());// 文件分组标识
						inMap.put("configPathDefine", SAttachmentUtil.getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

						String md5Hash = DigestUtils.md5Hex(new ByteArrayInputStream(multipartFile.getBytes()));
						Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(multipartFile, inMap);
						returnJSON = (JSONObject) JSONObject.toJSON(stringStringMap);
						System.out.println(returnJSON);

						String attachmentId = stringStringMap.get("docId");
						if ("-1".equals(stringStringMap.get("status"))) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						if (StringUtils.isBlank(attachmentId)) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						String docUrl = stringStringMap.get("docUrl");
						SAttachmentUtil.addAttachment(operator, attachmentId, fileName, STORE_TYPE, docUrl,
								String.valueOf(multipartFile.getSize()), md5Hash);

						returnJSON.put("fileid", attachmentId);
						returnJSON.put("attachmentName", fileName);
						returnJSON.put("fileSize", String.valueOf(multipartFile.getSize()));
						returnJSON.put("success", true);
						returnJSON.put("msg", "");

						//ckeditor当文件成功上传时的JSON响应：
						returnJSON.put("uploaded",1);//设置为1。
						returnJSON.put("fileName",fileName);//上传文件的名称
						returnJSON.put("url",docUrl);//返回的附件下载url

					}
				}
			} else {
				returnJSON.put("success", false);
				returnJSON.put("msg", "没有附件");

				//ckeditor当文件失败的JSON响应：
				returnJSON.put("uploaded", 0);//设置为1。
				returnJSON.put("error.message", "没有附件");//要显示给用户的错误消息
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			returnJSON.put("success", false);
			returnJSON.put("msg", e.getMessage());

			//ckeditor当文件失败的JSON响应：
			returnJSON.put("uploaded", 0);//设置为1。
			returnJSON.put("error.message", e.getMessage());//要显示给用户的错误消息
		}
		return returnJSON;
	}


	@RequestMapping(value = "/uploadImg", method = RequestMethod.POST)
	public String uploadImg(HttpServletRequest request, HttpServletResponse response) {
		try {

			String operator = UserSession.getUserId();
			CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
					request.getSession().getServletContext());
			if (multipartResolver.isMultipart(request)) {
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					MultipartFile multipartFile = multiRequest.getFile(iter.next());
					if (!multipartFile.isEmpty()) {
						String fileName = multipartFile.getOriginalFilename();

						Map inMap = new HashMap();
						inMap.put("groupId", ProjectInfo.getComponentName());// 文件分组标识
						inMap.put("configPathDefine", SAttachmentUtil.getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

						String md5Hash = DigestUtils.md5Hex(new ByteArrayInputStream(multipartFile.getBytes()));
						Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(multipartFile, inMap);


						String attachmentId = stringStringMap.get("docId");
						if ("-1".equals(stringStringMap.get("status"))) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						if (StringUtils.isBlank(attachmentId)) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						String docUrl = stringStringMap.get("docUrl");
						SAttachmentUtil.addAttachment(operator, attachmentId, fileName, STORE_TYPE, docUrl,
								String.valueOf(multipartFile.getSize()), md5Hash);

						return attachmentId;
					}
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();

		}
		return null;
	}


	@GetMapping("/fwb")
	public void fwb(HttpServletResponse response){
		try {
			response.sendRedirect(request.getServletContext().getContextPath()+"/ueditor/jsp/config.json");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	@PostMapping("/fwb")
	public Map<String, Object> fwb(HttpServletRequest request, HttpServletResponse response){
		JSONObject upload = upload(request, response);
		Map<String, Object> map = new HashMap<>();
		map.put("url",request.getServletContext().getContextPath()+"/attachment/download/"+upload.get("docId"));
		map.put("state","SUCCESS");
		return map;
	}

	/**
	 * 上传ehr文件到指定位置 /8PFTP/besHr
	 *
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/uploadBesEhr", method = RequestMethod.POST)
	public JSONObject uploadBesEhr(HttpServletRequest request, HttpServletResponse response) {
		JSONObject returnJSON = new JSONObject();
		try {
			String besFilePath = "/8PFTP/besHr";
			CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
					request.getSession().getServletContext());
			String attachmentName = "";
			if (multipartResolver.isMultipart(request)) {
				String bakFilePath = DateUtil.curDateTimeStr14();
				File bakFileFloder = new File(besFilePath+"/"+bakFilePath);
				if(!bakFileFloder.exists()) {
					bakFileFloder.mkdir();
				}
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					MultipartFile multipartFile = multiRequest.getFile(iter.next());
					if (!multipartFile.isEmpty()) {
						String fileName = multipartFile.getOriginalFilename();
						File destFile = new File(besFilePath+"/"+bakFilePath+"/"+fileName);//备份路径
						multipartFile.transferTo(destFile);//上传到备份下
						File file = new File(besFilePath+"/"+fileName);//目标路径
						FileUtils.copyFile(destFile, file);//复制到目标路径下
						attachmentName += fileName+",";
					}
				}
				returnJSON.put("attachmentName", attachmentName);
				returnJSON.put("success", true);
				returnJSON.put("msg", "上传成功");
			} else {
				returnJSON.put("success", false);
				returnJSON.put("msg", "没有附件");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			returnJSON.put("success", false);
			returnJSON.put("msg", e.getMessage());
		}
		return returnJSON;
	}

	/**
	 * 移动端上传附件
	 * 返回附件ID及相关信息
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/mobileUpload", method = RequestMethod.POST)
	public JSONObject mobileUpload(HttpServletRequest request, HttpServletResponse response) {
		JSONObject returnJSON = new JSONObject();
		try {
			String operator = "mobile";
			CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
					request.getSession().getServletContext());
			if (multipartResolver.isMultipart(request)) {
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					MultipartFile multipartFile = multiRequest.getFile(iter.next());
					if (!multipartFile.isEmpty()) {
						String fileName = multipartFile.getOriginalFilename();

						/**
						 * 校验
						 */
						String fileType = fileName.substring(fileName.lastIndexOf(".")+1, fileName.length());
						CheckUtils.checkUploadFileExtension(fileType);

						Map inMap = new HashMap();
						inMap.put("groupId", ProjectInfo.getComponentName());// 文件分组标识
						inMap.put("configPathDefine", SAttachmentUtil.getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

						String md5Hash = DigestUtils.md5Hex(new ByteArrayInputStream(multipartFile.getBytes()));
						Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(multipartFile, inMap);
						returnJSON = (JSONObject) JSONObject.toJSON(stringStringMap);
						System.out.println(returnJSON);

						String attachmentId = stringStringMap.get("docId");
						if ("-1".equals(stringStringMap.get("status"))) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						if (StringUtils.isBlank(attachmentId)) {
							throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
						}
						String docUrl = stringStringMap.get("docUrl");
						SAttachmentUtil.addAttachment(operator, attachmentId, fileName, STORE_TYPE, docUrl,
								String.valueOf(multipartFile.getSize()), md5Hash);

						returnJSON.put("fileid", attachmentId);
						returnJSON.put("attachmentName", fileName);
						returnJSON.put("fileSize", String.valueOf(multipartFile.getSize()));
						returnJSON.put("success", true);
						returnJSON.put("msg", "");
						returnJSON.put("status", 1);
					}
				}
			} else {
				returnJSON.put("success", false);
				returnJSON.put("status", -1);
				returnJSON.put("msg", "没有附件");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			returnJSON.put("success", false);
			returnJSON.put("status", -1);
			returnJSON.put("msg", e.getMessage());
		}
		return returnJSON;
	}
}
