package com.baosight.bsvkkj.mp.ty.controller;

import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.mp.ty.utils.SearchViewUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/configSearch")
public class ControllerConfigSearch {

    @GetMapping("/saveConfigUser")
    @ResponseBody
    public AjaxResult saveConfigUser(String pageNo, String fieldCodes) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("pageNo", pageNo);
        eiInfo.set("userGh", UserSession.getLoginName());
        eiInfo.set("fieldInfo", fieldCodes);
        eiInfo.set(EiConstant.serviceId, "S_MP_TY_29");
        EiInfo call = XServiceManager.call(eiInfo);
        int status = call.getStatus();
        if (EiConstant.STATUS_FAILURE == status) {
            throw new BusinessException(call.getMsg());
        }
        SearchViewUtil.removeSearchCache(pageNo);//清空搜索项缓存

        return AjaxResult.info(call);
    }


    /**
     * 获取小代码列表
     * @param businessType
     * @param dictCode
     * @return
     */
    @GetMapping("/gridGetDictList")
    @ResponseBody
    public AjaxResult gridGetDictList(String businessType, String dictCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessType", businessType);
        eiInfo.set("dictCode", dictCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_TY_07");
        EiInfo call = XServiceManager.call(eiInfo);
        int status = call.getStatus();
        if (EiConstant.STATUS_FAILURE == status) {
            throw new BusinessException(call.getMsg());
        }

        return AjaxResult.success(call.get("dictList"));
    }
}
