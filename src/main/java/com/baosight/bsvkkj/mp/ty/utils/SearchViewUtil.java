package com.baosight.bsvkkj.mp.ty.utils;

import com.baosight.bsvkkj.utils.MyUserSession;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component("SearchViewUtil")
public class SearchViewUtil {

    private static final String SEARCH_SESSION_KEY = "SearchViewUtil";


    /**
     * 获取配置信息
     * @param pageNo
     * @param configType
     * @return
     */
    public static List<Map<String, Object>> getPageConfigDataList(String pageNo, String configType) {

        List<Map<String, Object>> rtn = (List<Map<String, Object>>) MyUserSession.getValue(SEARCH_SESSION_KEY + ":SearchItemInfo:" + UserSession.getLoginName()+pageNo);
        if (null == rtn) {
            try {
                EiInfo inInfo = new EiInfo();
                inInfo.set("configType", configType);
                inInfo.set(EiConstant.serviceId, "S_MP_TY_28");
                inInfo.set("pageNo", pageNo);
                inInfo.set("userGh", UserSession.getLoginName());
                EiInfo outInfo = XServiceManager.call(inInfo);
                rtn = (List<Map<String, Object>>) outInfo.get("fieldList");
                if (null == rtn) {//远程异常
                    rtn = new ArrayList<>();
                }

            } catch (Exception e) {

            }
            MyUserSession.setValue(SEARCH_SESSION_KEY + ":SearchItemInfo:" + UserSession.getLoginName()+pageNo, rtn);
        }
        return rtn;
    }

    /**
     * 清空收索项缓存
     */
    public static void removeSearchCache(String pageNo) {
        MyUserSession.setValue(SEARCH_SESSION_KEY + ":SearchItemInfo:" + UserSession.getLoginName()+pageNo, null);
    }

}
