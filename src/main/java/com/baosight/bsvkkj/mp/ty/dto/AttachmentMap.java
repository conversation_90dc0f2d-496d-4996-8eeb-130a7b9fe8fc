package com.baosight.bsvkkj.mp.ty.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AttachmentMap {

    public String id;
	//操作人
	public String operator;
	//附件ID
	public String attachmentId;
	//附件名称
	public String attachmentName;
	//附件大小
	public String attachmentSize;
	//来源ID
    private String sourceId ;	
    //来源模块
    private String sourceModule ;	
    //来源标记1
    private String sourceLabel1 ;	
    //来源标记2
    private String sourceLabel2 ;	
    //来源标记3
    private String sourceLabel3 ;
    //附件集合ids
    private List<String> attachmentIds;
    private String attachmentItem;
    private String docUrl;
    private Integer version;
    private String createUserLabel;

    private String updateUserLabel;
    private String  extra2;

    private String createDate;

    private String createUserName;

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getAttachmentItem() {
        return attachmentItem;
    }

    public void setAttachmentItem(String attachmentItem) {
        this.attachmentItem = attachmentItem;
    }

    public String getDocUrl() {
        return docUrl;
    }

    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentSize() {
        return attachmentSize;
    }

    public void setAttachmentSize(String attachmentSize) {
        this.attachmentSize = attachmentSize;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceModule() {
        return sourceModule;
    }

    public void setSourceModule(String sourceModule) {
        this.sourceModule = sourceModule;
    }

    public String getSourceLabel1() {
        return sourceLabel1;
    }

    public void setSourceLabel1(String sourceLabel1) {
        this.sourceLabel1 = sourceLabel1;
    }

    public String getSourceLabel2() {
        return sourceLabel2;
    }

    public void setSourceLabel2(String sourceLabel2) {
        this.sourceLabel2 = sourceLabel2;
    }

    public String getSourceLabel3() {
        return sourceLabel3;
    }

    public void setSourceLabel3(String sourceLabel3) {
        this.sourceLabel3 = sourceLabel3;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreateUserLabel() {
        return createUserLabel;
    }

    public void setCreateUserLabel(String createUserLabel) {
        this.createUserLabel = createUserLabel;
    }

    public String getUpdateUserLabel() {
        return updateUserLabel;
    }

    public void setUpdateUserLabel(String updateUserLabel) {
        this.updateUserLabel = updateUserLabel;
    }
}
