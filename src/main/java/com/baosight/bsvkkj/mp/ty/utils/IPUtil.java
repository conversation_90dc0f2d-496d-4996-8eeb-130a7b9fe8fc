package com.baosight.bsvkkj.mp.ty.utils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;

public class IPUtil {


    public static String getRequestIp(HttpServletRequest request) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // 从获取RequestAttributes中获歌HttpServletRequest的信息
//        HttpServletRequest request =
//                (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        String ip = request .getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 ||  "unknown".equalsIgnoreCase(ip))
            ip = request.getHeader("Proxy-Client-IP");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
            ip = request.getHeader("WL-Proxy-Client_IP") ;
        if (ip == null || ip.length() ==0 ||  "unknown".equalsIgnoreCase(ip))
            ip = request.getHeader("HTTP_CLIENT_IP") ;
        if (ip == null  || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
            ip = request.getHeader("HTTP_X_FORWARDED_FOR") ;
        if (ip == null  || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
            ip = request.getRemoteAddr() ;
        return ip;
    }



}