package com.baosight.bsvkkj.mp.ty.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.bsvkkj.mp.ty.dto.AttachmentMap;
import com.baosight.bsvkkj.mp.ty.dto.TmptyAttachmentLog;
import com.baosight.iplat4j.core.ProjectInfo;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.eu.dm.PlatFileUploadUtils;
import com.baosight.iplat4j.eu.dm.util.CheckUtils;
import com.google.common.io.Files;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 附件相关微服务版
 * 
 * <AUTHOR>
 *
 *         service MPTYAttachmentMap 
 *         S_MP_TY_01 设置附件关系 addAttachmentMap
 *         S_MP_TY_02 根据来源ID删除附件关系 deleteAttachmentMapBySourceId 
 *         S_MP_TY_03 根据来源ID获取相关附件 getAttachmentBySourceId 
 *         S_MP_TY_04 根据附件ID查询附件 getAttachment 
 *         S_MP_TY_05 根据附件ID集合查询附件集合 getAttachmentList 
 *         S_MP_TY_06 根据来源ID删除附件关系 deleteAttachmentMapBySourceIdAndSourceLabel
 * 
 *         service MPTYAttachment 
 *         S_MP_TY_11 新增附件信息 addAttachment 
 *         S_MP_TY_12 下载附件 downAttachment 
 *         S_MP_TY_13 删除附件信息 deleteAttachment
 *         S_MP_TY_14 根据来源ID和标识获取相关附件 getAttachmentBySourceIdAndSourceLabel
 *         S_MP_TY_30 添加日志
 */
@Component("SAttachmentUtil")
public class SAttachmentUtil {

	public static final String MOBILE_PDF = "mobilePdf";

	/**
	 * 新增关系，一般
	 * 
	 * @param sourceGuid    来源guid，一般记录主键
	 * @param attachmentIdS 附件ID 多个样","隔开 必须
	 * @param sourceModel   模块标识 必须
	 */
	public static void addAttachmentMaps(String sourceGuid, String attachmentIdS, String sourceModel) {
		String operator = UserSession.getLoginName();
		if (attachmentIdS != null && attachmentIdS.trim().length() > 0) {
			String[] attachmentId = attachmentIdS.split(",");
			for (int i = 0; i < attachmentId.length; i++) {
				String string = attachmentId[i];
				if ("undefined".equals(string)) {
					continue;
				}
				addAttachmentMap(operator, string.trim(), sourceGuid, sourceModel);
			}
		}
	}

	/**
	 * 新增关系，一般
	 * 
	 * @param sourceGuid    来源guid，一般记录主键
	 * @param attachmentIdS 附件ID 多个样","隔开 必须
	 * @param sourceModel   模块标识 必须
	 */
	public static void addAttachmentMapsBySourceGuid(String sourceGuid, String attachmentIdS, String sourceModel) {
		String operator = UserSession.getLoginName();
		deleteAttachmentMapBySourceId(operator, sourceGuid, sourceModel);// 根据业务ID清除所有附件
		if (attachmentIdS != null && attachmentIdS.trim().length() > 0) {
			String[] attachmentId = attachmentIdS.split(",");
			for (int i = 0; i < attachmentId.length; i++) {
				String string = attachmentId[i];
				if ("undefined".equals(string)) {
					continue;
				}
				addAttachmentMap(operator, string.trim(), sourceGuid, sourceModel);
			}
		}
	}

	/**
	 * * 新增关系，一般 多个标识
	 * 
	 * @param sourceGuid    来源guid，一般记录主键
	 * @param attachmentIdS 附件ID 多个样","隔开 必须
	 * @param sourceModel   模块标识 必须
	 * @param sourceLabel1  标识1
	 * @param sourceLabel2  标识2
	 * @param sourceLabel3  标识3
	 */
	public static void addAttachmentMapsBySourceGuidAndSourceLabel(String sourceGuid, String attachmentIdS,
			String sourceModel, String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		String operator = UserSession.getLoginName();
		deleteAttachmentMapBySourceIdAndSourceLabel(operator, sourceGuid, sourceModel, sourceLabel1, sourceLabel2,
				sourceLabel3);// 根据业务ID和标识清除所有附件
		if (attachmentIdS != null && attachmentIdS.trim().length() > 0) {
			String[] attachmentId = attachmentIdS.split(",");
			for (int i = 0; i < attachmentId.length; i++) {
				String string = attachmentId[i];
				if ("undefined".equals(string)) {
					continue;
				}
				addAttachmentMap(operator, string.trim(), sourceGuid, sourceModel, sourceLabel1, sourceLabel2,
						sourceLabel3);
			}
		}
	}

	/**
	 * * 新增关系，一般 多个标识
	 *
	 * @param operator      操作人
	 * @param sourceGuid    来源guid，一般记录主键
	 * @param attachmentIdS 附件ID 多个样","隔开 必须
	 * @param sourceModel   模块标识 必须
	 * @param sourceLabel1  标识1
	 * @param sourceLabel2  标识2
	 * @param sourceLabel3  标识3
	 */
	public static void addAttachmentMapsBySourceGuidAndSourceLabel(String operator, String sourceGuid, String attachmentIdS,
																   String sourceModel, String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		deleteAttachmentMapBySourceIdAndSourceLabel(operator, sourceGuid, sourceModel, sourceLabel1, sourceLabel2,
				sourceLabel3);// 根据业务ID和标识清除所有附件
		if (attachmentIdS != null && attachmentIdS.trim().length() > 0) {
			String[] attachmentId = attachmentIdS.split(",");
			for (int i = 0; i < attachmentId.length; i++) {
				String string = attachmentId[i];
				if ("undefined".equals(string)) {
					continue;
				}
				addAttachmentMap(operator, string.trim(), sourceGuid, sourceModel, sourceLabel1, sourceLabel2,
						sourceLabel3);
			}
		}
	}

	public static void tjAttachmentMapsBySourceGuidAndSourceLabelOperator(String sourceGuid, String attachmentIdS,
																   String sourceModel, String sourceLabel1, String sourceLabel2, String sourceLabel3,String operator) {
		deleteAttachmentMapBySourceIdAndSourceLabel(operator, sourceGuid, sourceModel, sourceLabel1, sourceLabel2,
				sourceLabel3);// 根据业务ID和标识清除所有附件
		if (attachmentIdS != null && attachmentIdS.trim().length() > 0) {
			String[] attachmentId = attachmentIdS.split(",");
			for (int i = 0; i < attachmentId.length; i++) {
				String string = attachmentId[i];
				if ("undefined".equals(string)) {
					continue;
				}
				addAttachmentMap(operator, string.trim(), sourceGuid, sourceModel, sourceLabel1, sourceLabel2,
						sourceLabel3);
			}
		}
	}
	/**
	 * 设置附件关系
	 * 
	 * @param operator
	 * @param attachmentId
	 * @param sourceId
	 * @param sourceModule
	 */
	public static void addAttachmentMap(String operator, String attachmentId, String sourceId, String sourceModule) {
		// attachmentUtil.businessAttachmentMap.addAttachmentMap(operator, attachmentId,
		// sourceId, sourceModule);
		EiInfo inInfo = new EiInfo();

		AttachmentMap attachment = new AttachmentMap();
		attachment.setOperator(operator);
		attachment.setAttachmentId(attachmentId);
		attachment.setSourceId(sourceId);
		attachment.setSourceModule(sourceModule);
		inInfo.set("attachment", attachment);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}

	/**
	 * 设置附件关系
	 * 
	 * @param operator
	 * @param attachmentId
	 * @param sourceId
	 * @param sourceModule
	 * @param sourceLabel1
	 * @param sourceLabel2
	 * @param sourceLabel3
	 */
	public static void addAttachmentMap(String operator, String attachmentId, String sourceId, String sourceModule,
			String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		// attachmentUtil.businessAttachmentMap.addAttachmentMap(operator, attachmentId,
		// sourceId, sourceModule, sourceLabel1, sourceLabel2, sourceLabel3);
		EiInfo inInfo = new EiInfo();

		AttachmentMap attachment = new AttachmentMap();
		attachment.setOperator(operator);
		attachment.setAttachmentId(attachmentId);
		attachment.setSourceId(sourceId);
		attachment.setSourceModule(sourceModule);
		attachment.setSourceLabel1(sourceLabel1);
		attachment.setSourceLabel2(sourceLabel2);
		attachment.setSourceLabel3(sourceLabel3);
		inInfo.set("attachment", attachment);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}

	/**
	 * 根据来源ID删除附件关系
	 * 
	 * @param operator
	 * @param sourceId
	 */
	public static void deleteAttachmentMapBySourceId(String operator, String sourceId, String sourceModule) {
		// attachmentUtil.businessAttachmentMap.deleteAttachmentMapBySourceId(operator,
		// sourceId, sourceModule);
		EiInfo inInfo = new EiInfo();
		AttachmentMap attachment = new AttachmentMap();
		attachment.setOperator(operator);
		attachment.setSourceId(sourceId);
		attachment.setSourceModule(sourceModule);
		inInfo.set("attachment", attachment);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_02");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}

	/**
	 * 根据来源ID获取相关附件
	 * 
	 * @param sourceId
	 * @param sourceModule
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentBySourceId(String sourceId, String sourceModule) {
		EiInfo inInfo = new EiInfo();

		AttachmentMap attachment = new AttachmentMap();
		attachment.setSourceId(sourceId);
		attachment.setSourceModule(sourceModule);
		inInfo.set("attachment", attachment);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_03");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String,Object>> rows = (List<Map<String, Object>>) outInfo.getBlock(EiConstant.resultBlock).getRows();
		if(rows!=null && !rows.isEmpty()){
			return rows.stream().map(map-> BeanUtil.toBean(map,AttachmentMap.class)).collect(Collectors.toList());
		}
		return null;
	}

	/**
	 * 根据附件ID查询附件关系
	 * 
	 * @param attachmentId
	 * @return
	 */
	public static AttachmentMap getAttachment(String attachmentId) {
		EiInfo inInfo = new EiInfo();

		AttachmentMap attachment = new AttachmentMap();
		attachment.setAttachmentId(attachmentId);
		inInfo.set("attachment", attachment);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_04");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		return BeanUtil.toBean(outInfo.getBlock(EiConstant.resultBlock).getRow(0),AttachmentMap.class);
	}

	/**
	 * 根据附件ID查询附件关系
	 *
	 * @param attachmentId
	 * @return
	 */
	public static String getAttachmentName(String attachmentId) {
		EiInfo inInfo = new EiInfo();
		AttachmentMap attachmentMap = new AttachmentMap();
		if(StringUtils.isNotBlank(attachmentId)){
			AttachmentMap attachment = new AttachmentMap();
			attachment.setAttachmentId(attachmentId);
			inInfo.set("attachment", attachment);

			inInfo.set(EiConstant.serviceId, "S_MP_TY_04");
			EiInfo outInfo = XServiceManager.call(inInfo);
			if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				throw new PlatException(outInfo.getMsg());
			}
			attachmentMap = BeanUtil.toBean(outInfo.getBlock(EiConstant.resultBlock).getRow(0),AttachmentMap.class);

		}
		return attachmentMap==null ? "": attachmentMap.getAttachmentName();
	}
	/**
	 * 根据附件ID集合查询附件集合
	 * 
	 * @param attachmentIdList
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentList(List<String> attachmentIdList) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("attachmentIdList", attachmentIdList);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_05");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String,Object>> rows = (List<Map<String, Object>>) outInfo.getBlock(EiConstant.resultBlock).getRows();
		if(rows!=null && !rows.isEmpty()){
			return rows.stream().map(map-> BeanUtil.toBean(map,AttachmentMap.class)).collect(Collectors.toList());
		}
		return null;
	}
	/**
	 * 合同历史版本信息
	 *
	 * @param attachmentId
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentListByAttIdAndExtra2(String attachmentId,String version) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("attachmentId", attachmentId);
		inInfo.set("version", version);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_051");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		List<Map<String,Object>> rows = (List<Map<String, Object>>) outInfo.getBlock(EiConstant.resultBlock).getRows();
		if(rows!=null && !rows.isEmpty()){
			return rows.stream().sorted(Comparator.comparing(
					map -> Integer.valueOf((String) map.get("version")),
					Comparator.reverseOrder()
			)).map(map-> BeanUtil.toBean(map,AttachmentMap.class)).collect(Collectors.toList());
		}
		return null;
	}
	/**
	 * 根据附件ID集合查询附件集合
	 * 多个用逗号隔开
	 * @param attachmentIdS
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentListByString(String attachmentIdS) {
		if (StringUtils.isBlank(attachmentIdS)) {
			return new ArrayList<>();
		}
		String[] temp = attachmentIdS.split(",");
		return getAttachmentList(Arrays.stream(temp).collect(Collectors.toList()));
	}

	/**
	 * 根据来源ID和标识删除附件关系
	 * 
	 * @param operator
	 * @param sourceId
	 */
	public static void deleteAttachmentMapBySourceIdAndSourceLabel(String operator, String sourceId,
			String sourceModule, String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		EiInfo inInfo = new EiInfo();

		AttachmentMap attachment = new AttachmentMap();
		attachment.setOperator(operator);
		attachment.setSourceId(sourceId);
		attachment.setSourceModule(sourceModule);
		attachment.setSourceLabel1(sourceLabel1);
		attachment.setSourceLabel2(sourceLabel2);
		attachment.setSourceLabel3(sourceLabel3);
		inInfo.set("attachment", attachment);

		inInfo.set(EiConstant.serviceId, "S_MP_TY_06");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}

	/**
	 * 根据来源ID和标识获取相关附件
	 * 
	 * @param sourceId
	 * @param sourceModule
	 * @param sourceLabel1
	 * @param sourceLabel2
	 * @param sourceLabel3
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentBySourceIdAndSourceLabel(String sourceId, String sourceModule,
			String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		if (StrUtil.isNotBlank(sourceId) && StrUtil.isNotBlank(sourceModule)) {
			EiInfo inInfo = new EiInfo();

			AttachmentMap attachment = new AttachmentMap();
			attachment.setSourceId(sourceId);
			attachment.setSourceModule(sourceModule);
			attachment.setSourceLabel1(sourceLabel1);
			attachment.setSourceLabel2(sourceLabel2);
			attachment.setSourceLabel3(sourceLabel3);
			inInfo.set("attachment", attachment);

			inInfo.set(EiConstant.serviceId, "S_MP_TY_14");
			EiInfo outInfo = XServiceManager.call(inInfo);
			if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				throw new PlatException(outInfo.getMsg());
			}
			List<Map<String,Object>> rows = (List<Map<String, Object>>) outInfo.get("attachmentList");
			if(rows!=null && !rows.isEmpty()){
				return rows.stream().map(map-> BeanUtil.toBean(map,AttachmentMap.class)).collect(Collectors.toList());
			}

		}
		return null;
	}

	/**
	 * 根据来源ID和标识获取相关附件
	 * CREATE_DATE DESC
	 * @param sourceId
	 * @param sourceModule
	 * @param sourceLabel1
	 * @param sourceLabel2
	 * @param sourceLabel3
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentBySourceIdAndSourceLabelDesc(String sourceId, String sourceModule,
																				String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		if (StrUtil.isNotBlank(sourceId) && StrUtil.isNotBlank(sourceModule)) {
			EiInfo inInfo = new EiInfo();

			AttachmentMap attachment = new AttachmentMap();
			attachment.setSourceId(sourceId);
			attachment.setSourceModule(sourceModule);
			attachment.setSourceLabel1(sourceLabel1);
			attachment.setSourceLabel2(sourceLabel2);
			attachment.setSourceLabel3(sourceLabel3);
			inInfo.set("attachment", attachment);

			inInfo.set(EiConstant.serviceId, "S_MP_TY_141");
			EiInfo outInfo = XServiceManager.call(inInfo);
			if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				throw new PlatException(outInfo.getMsg());
			}
			List<Map<String, Object>> rows = (List<Map<String, Object>>) outInfo.get("attachmentList");
			if (rows != null && !rows.isEmpty()) {
				return rows.stream().map(map -> BeanUtil.toBean(map, AttachmentMap.class)).collect(Collectors.toList());
			}

		}
		return null;
	}

	/**
	 * 根据来源ID和标识获取相关附件
	 *
	 * @param sourceModule
	 * @param sourceLabel1
	 * @return
	 */
	public static List<AttachmentMap> getAttachmentBySourceModuleAndSourceLabel(String sourceModule,String sourceLabel1) {
		if (StrUtil.isNotBlank(sourceLabel1) && StrUtil.isNotBlank(sourceModule)) {
			EiInfo inInfo = new EiInfo();

			AttachmentMap attachment = new AttachmentMap();
			attachment.setSourceModule(sourceModule);
			attachment.setSourceLabel1(sourceLabel1);
			inInfo.set("attachment", attachment);
			inInfo.set(EiConstant.serviceId, "S_MP_TY_27");
			EiInfo outInfo = XServiceManager.call(inInfo);
			if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				throw new PlatException(outInfo.getMsg());
			}
			List<Map<String,Object>> rows = (List<Map<String, Object>>) outInfo.get("attachmentList");
			if(rows!=null && !rows.isEmpty()){
				return rows.stream().map(map-> BeanUtil.toBean(map,AttachmentMap.class)).collect(Collectors.toList());
			}

		}
		return null;
	}
	/**
	 * 根据来源ID和标识获取相关附件
	 * 
	 * @param sourceId
	 * @param sourceModule
	 * @param sourceLabel1
	 * @param sourceLabel2
	 * @param sourceLabel3
	 * @return
	 */
	public static Map<String, Object> getAttachmentBySourceIdAndSourceLabelToMap(String sourceId, String sourceModule,
			String sourceLabel1, String sourceLabel2, String sourceLabel3) {
		Map<String, Object> map = new HashMap<>();
		if (StrUtil.isNotBlank(sourceId) && StrUtil.isNotBlank(sourceModule)) {
			List<AttachmentMap> attachmentBySourceIdAndSourceLabel = getAttachmentBySourceIdAndSourceLabel(sourceId,
					sourceModule, sourceLabel1, sourceLabel2, sourceLabel3);
			String collect="";
			if(attachmentBySourceIdAndSourceLabel!=null && !attachmentBySourceIdAndSourceLabel.isEmpty()){
				collect = attachmentBySourceIdAndSourceLabel.stream().map(attachment ->attachment.getAttachmentId()).collect(Collectors.joining(","));
			}
			map.put("attachmentList", attachmentBySourceIdAndSourceLabel);
			map.put("attachmentIds", collect);
		} else {
			map.put("attachmentList", null);
			map.put("attachmentIds", "");
		}
		return map;
	}

	/**
	 * 新增附件信息
	 * 
	 * @param operator       操作人
	 * @param attachmentId   附件ID
	 * @param attachmentName 附件名称
	 * @param storeType      存储类型
	 * @param docUrl         附件下载Url
	 * @param fileSize       附件大小
	 * @param hashValue      文件hash值
	 * @param storeType 附件类型
	 */
	public static boolean addAttachment(String operator, String attachmentId, String attachmentName, String storeType,
			String docUrl,String fileSize,String hashValue) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("operator", operator);
		inInfo.set("attachmentId", attachmentId);
		inInfo.set("attachmentName", attachmentName);
		inInfo.set("storeType", storeType);
		inInfo.set("docUrl", docUrl);
		inInfo.set("fileSize", fileSize);
		inInfo.set("hashValue", hashValue);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_11");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		return true;
	}
	public static boolean addAttachment(String operator, String attachmentId, String attachmentName, String storeType,
										String docUrl,String fileSize,String hashValue,String recordVersion) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("operator", operator);
		inInfo.set("attachmentId", attachmentId);
		inInfo.set("attachmentName", attachmentName);
		inInfo.set("storeType", storeType);
		inInfo.set("docUrl", docUrl);
		inInfo.set("fileSize", fileSize);
		inInfo.set("hashValue", hashValue);
		inInfo.set("recordVersion", recordVersion);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_11");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		return true;
	}
	/**
	 * 附件下载，次数加一
	 * 
	 * @param operator
	 * @param attachmentId
	 * @return
	 */
	public static Map<String, Object> downAttachment(String operator, String attachmentId) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("operator", operator);
		inInfo.set("attachmentId", attachmentId);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_12");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		return (Map<String, Object>) outInfo.getAttr().get("attachment");
	}

	/**
	 * 删除附件信息 和 附件关系表
	 * 
	 * @param operator
	 * @param attachmentId
	 * @return
	 */
	public static boolean deleteAttachment(String operator, String attachmentId) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("operator", operator);
		inInfo.set("attachmentId", attachmentId);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_13");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		return true;
	}
	
	/**
	 * 根据文件流上传附件
	 * @param operator
	 * @param inputStream
	 * @param attachmentName
	 * @return
	 * @throws Exception
	 */
	public static String uploadByInputStream(String operator,InputStream inputStream, String attachmentName) throws Exception {
		try {
			Map inMap = new HashMap();
			inMap.put("groupId", ProjectInfo.getComponentName());// 传入文件分组标识
			inMap.put("fileName", attachmentName);
			inMap.put("configPathDefine", getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

			String md5Hash = DigestUtils.md5Hex(inputStream);

			String fileSuffix = CheckUtils.checkFileSuffix(attachmentName);
			byte[] buffer = new byte[inputStream.available()];
			inputStream.read(buffer);
			String uuid = UUIDHexIdGenerator.generate().toString();
			String newFileName = uuid + "." + fileSuffix;
			File file = new File(newFileName);
			Files.write(buffer, file);
			//s3
			Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(file, inMap);
			
			String attachmentId = stringStringMap.get("docId");
			if ("-1".equals(stringStringMap.get("status"))) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			if (StringUtils.isBlank(attachmentId)) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			String docUrl = stringStringMap.get("docUrl");
			SAttachmentUtil.addAttachment(operator, attachmentId, attachmentName, "s3", docUrl, 
					String.valueOf(inputStream.available()), md5Hash);
			
			return attachmentId;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new Exception("上传文件流失败：" + e.getMessage());
		}finally {
			if(null!=inputStream) {
				inputStream.close();
			}
		}
	}
	
	/**
	 * 根据附件url上传附件
	 * @param operator
	 * @param strUrl
	 * @param attachmentName
	 * @return
	 * @throws Exception
	 */
	public static String uploadByUrl(String operator,String strUrl,String attachmentName) throws Exception {
		InputStream fileInputStream=null;
		try {
			Map inMap = new HashMap();
			inMap.put("groupId", ProjectInfo.getComponentName());// 传入文件分组标识
			inMap.put("fileName", attachmentName);
			inMap.put("configPathDefine", getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

			String fileSuffix = CheckUtils.checkFileSuffix(attachmentName);
			String uuid = UUIDHexIdGenerator.generate().toString();

//			Setting setting = new Setting("gfbes/app-file-share.properties");
//			String newFileName = setting.get("app-file-share.exportExcelPath")+uuid + "." + fileSuffix;
			String newFileName = uuid + "." + fileSuffix;
			File file = new File(newFileName);

			HttpUtil.downloadFile(strUrl,file);
			fileInputStream = new FileInputStream(file);
			String md5Hash = DigestUtils.md5Hex(fileInputStream);
			//s3
			Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(file, inMap);

			String attachmentId = stringStringMap.get("docId");
			if ("-1".equals(stringStringMap.get("status"))) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			if (StringUtils.isBlank(attachmentId)) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			String docUrl = stringStringMap.get("docUrl");
			SAttachmentUtil.addAttachment(operator, attachmentId, attachmentName, "s3", docUrl,
					stringStringMap.get("fileSize"), md5Hash);

			return attachmentId;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new Exception("上传文件流失败：" + e.getMessage());
		}finally {
			if(null!=fileInputStream) {
				fileInputStream.close();
			}
		}
	}
	public static String uploadByUrl1(String operator,String strUrl,String attachmentName) throws Exception {
		InputStream fileInputStream=null;
		try {
			Map inMap = new HashMap();
			inMap.put("groupId", ProjectInfo.getComponentName());// 传入文件分组标识
			inMap.put("fileName", attachmentName);
			inMap.put("configPathDefine", getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

			File file = new File(strUrl);
			fileInputStream = new FileInputStream(file);
			String md5Hash = DigestUtils.md5Hex(fileInputStream);
			//s3
			Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(file, inMap);

			String attachmentId = stringStringMap.get("docId");
			if ("-1".equals(stringStringMap.get("status"))) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			if (StringUtils.isBlank(attachmentId)) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			String docUrl = stringStringMap.get("docUrl");
			SAttachmentUtil.addAttachment(operator, attachmentId, attachmentName, "s3", docUrl,
					stringStringMap.get("fileSize"), md5Hash);

			return attachmentId;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new Exception("上传文件流失败：" + e.getMessage());
		}finally {
			if(null!=fileInputStream) {
				fileInputStream.close();
			}
		}
	}
	public static String uploadByUrl2(String operator,String strUrl,String attachmentName,String version) throws Exception {
		InputStream fileInputStream=null;
		try {
			Map inMap = new HashMap();
			inMap.put("groupId", ProjectInfo.getComponentName());// 传入文件分组标识
			inMap.put("fileName", attachmentName);
			inMap.put("configPathDefine", getPathDefine());// system后面扩展的路径（若不填写，则不扩展）

			File file = new File(strUrl);
			fileInputStream = new FileInputStream(file);
			String md5Hash = DigestUtils.md5Hex(fileInputStream);
			//s3
			Map<String, String> stringStringMap = PlatFileUploadUtils.uploadFile(file, inMap);

			String attachmentId = stringStringMap.get("docId");
			if ("-1".equals(stringStringMap.get("status"))) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			if (StringUtils.isBlank(attachmentId)) {
				throw new PlatException("远程存储失败。" + stringStringMap.get("msg"));
			}
			String docUrl = stringStringMap.get("docUrl");
			SAttachmentUtil.addAttachment(operator, attachmentId, attachmentName, "s3", docUrl,
					stringStringMap.get("fileSize"), md5Hash,version);

			return attachmentId;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw new Exception("上传文件流失败：" + e.getMessage());
		}finally {
			if(null!=fileInputStream) {
				fileInputStream.close();
			}
		}
	}
	/**
	 * 获取当前时间第几周
	 * 如202105_2:2021年5月第2周
	 * @return
	 */
	public static String getPathDefine() {
		Calendar cal = Calendar.getInstance();
		try {
			int i = cal.get(Calendar.WEEK_OF_MONTH);
			String dateString = DateUtil.calendarToString(cal, "YYYY-MM-dd");			
			return dateString.substring(0, 4) + dateString.substring(5, 7)+"_"+i;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	
	/**
     * 复制文件关联关系至目标id
     *
     * @param operator 操作人
     * @param sourceModel 模块标识
     * @param sourceId    来源id
     * @param targetId    目标id
     */
    public static void copyAttachment(String operator, String sourceModel, String sourceId, String targetId) {
        List<AttachmentMap> list = getAttachmentBySourceId(sourceId, sourceModel);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_TY_01");
        for (AttachmentMap attachmentMap : list) {
            attachmentMap.setOperator(operator);
            attachmentMap.setSourceId(targetId);
            eiInfo.set("attachment", attachmentMap);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                throw new PlatException(outInfo.getMsg());
            }
        }
    }

	/**
	 * 复制文件关联关系至目标id
	 * @param operator 操作人
	 * @param sourceModel 模块标识
	 * @param targetModel 目标模块标识
	 * @param sourceId    来源id
	 * @param targetId    目标id
	 */
	public static void copyAttachment(String operator, String sourceModel,String targetModel, String sourceId, String targetId) {
		List<AttachmentMap> list = getAttachmentBySourceId(sourceId, sourceModel);
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		EiInfo eiInfo = new EiInfo();
		eiInfo.set(EiConstant.serviceId, "S_MP_TY_01");
		for (AttachmentMap attachmentMap : list) {
			attachmentMap.setSourceModule(targetModel);
			attachmentMap.setOperator(operator);
			attachmentMap.setSourceId(targetId);
			eiInfo.set("attachment", attachmentMap);
			EiInfo outInfo = XServiceManager.call(eiInfo);
			if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				throw new PlatException(outInfo.getMsg());
			}
		}
	}

	/**
	 * 删除附件关系
	 *
	 * @param attachmentIds
	 * @param sourceId
	 * @return
	 */
	public static boolean deleteAttachmentMapByAttachmentIdsAndSourceId(List<String> attachmentIds, String sourceId) {
		EiInfo inInfo = new EiInfo();
		AttachmentMap attachment = new AttachmentMap();
		attachment.setAttachmentIds(attachmentIds);
		attachment.setSourceId(sourceId);
		inInfo.set("attachment", attachment);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_16");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
		return true;
	}
	public static void addAttachmentLog(String operator, Map<String,Object> attachment, HttpServletRequest request) {
		EiInfo inInfo = new EiInfo();
		TmptyAttachmentLog tmptyAttachmentLog = getAttmentLog( operator, attachment,  request);
		inInfo.set("attmentLog", tmptyAttachmentLog);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_30");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}
	public static TmptyAttachmentLog getAttmentLog(String operator, Map<String,Object> attachment, HttpServletRequest request){
		TmptyAttachmentLog tmptyAttachmentLog = new TmptyAttachmentLog();
		tmptyAttachmentLog.setAttmentId((String) attachment.get("attachmentId"));
		tmptyAttachmentLog.setFileType((String) attachment.get("attachmentType"));
		tmptyAttachmentLog.setFileName((String) attachment.get("attachmentName"));
		tmptyAttachmentLog.setIpAddress(IPUtil.getRequestIp(request));
		tmptyAttachmentLog.setUserId(operator);
		tmptyAttachmentLog.setUserName(SUserUtil.getUserName(operator));
		return tmptyAttachmentLog;
	}
	public static void updateAttmentId(String attachmentId, String newAttachmentId) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("attachmentId", attachmentId);
		inInfo.set("newAttachmentId", newAttachmentId);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_31");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}
	//合同编号套用后需要修改之前的文件extra2 改为新的文件ID
	public static void updateAttmentIdExtra2(String attachmentId, String newAttachmentId) {
		EiInfo inInfo = new EiInfo();
		inInfo.set("attachmentId", attachmentId);
		inInfo.set("newAttachmentId", newAttachmentId);
		inInfo.set(EiConstant.serviceId, "S_MP_TY_32");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
			throw new PlatException(outInfo.getMsg());
		}
	}
}
