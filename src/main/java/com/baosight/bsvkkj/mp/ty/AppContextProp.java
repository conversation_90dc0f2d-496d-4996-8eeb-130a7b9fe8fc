package com.baosight.bsvkkj.mp.ty;

import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

@Component("AppContextProp")
@Getter
@Setter
public class AppContextProp {
	private String ctxGGMK = PlatApplicationContext.getProperty("app-context.ctxGGMK");

	private String ctxKY = PlatApplicationContext.getProperty("app-context.ctxKY");

	private String ctxZZZC = PlatApplicationContext.getProperty("app-context.ctxZZZC");
}
