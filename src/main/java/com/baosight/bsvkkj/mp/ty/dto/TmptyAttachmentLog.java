package com.baosight.bsvkkj.mp.ty.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baosight.bsvkkj.common.domain.AbstractDomain;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Size;


/**
 * 【请填写功能名称】对象 t_mpty_attachment_log
 * 
 * <AUTHOR>
 * @date 2024-01-16
 */
@Getter
@Setter
@ToString
public class TmptyAttachmentLog extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     * ATTMENT_LOG_ID
     */
    private String attmentLogId;

    /**
     * $column.columnComment
     * USER_ID
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String userId;

    /**
     * $column.columnComment
     * USER_NAME
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String userName;

    /**
     * $column.columnComment
     * IP_ADDRESS
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 50, message = "${comment}最大为50位字符")
    private String ipAddress;

    /**
     * $column.columnComment
     * ATTMENT_ID
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 50, message = "${comment}最大为50位字符")
    private String attmentId;

    /**
     * $column.columnComment
     * FILE_TYPE
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String fileType;

    /**
     * $column.columnComment
     * FILE_NAME
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 100, message = "${comment}最大为100位字符")
    private String fileName;

    /**
     * $column.columnComment
     * ID_CARD
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String idCard;

    /**
     * $column.columnComment
     * EXTRA1
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 50, message = "${comment}最大为50位字符")
    private String extra1;

    /**
     * $column.columnComment
     * EXTRA2
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 50, message = "${comment}最大为50位字符")
    private String extra2;

    /**
     * $column.columnComment
     * EXTRA3
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 50, message = "${comment}最大为50位字符")
    private String extra3;

    /**
     * $column.columnComment
     * EXTRA4
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 128, message = "${comment}最大为128位字符")
    private String extra4;

    /**
     * $column.columnComment
     * EXTRA5
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 128, message = "${comment}最大为128位字符")
    private String extra5;

    /**
     * $column.columnComment
     * EXTRA6
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 128, message = "${comment}最大为128位字符")
    private String extra6;

}
