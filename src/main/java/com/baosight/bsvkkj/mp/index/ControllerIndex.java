package com.baosight.bsvkkj.mp.index;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.mp.ad.dto.ADUser;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.bsvkkj.mp.ty.AppContextProp;
import com.baosight.bsvkkj.mp.ty.utils.SDictUtil;
import com.baosight.iplat4j.core.FrameworkInfo;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/index")
public class ControllerIndex extends BaseController {

    @GetMapping({"", "/"})
    public String init(ModelMap model) {
        model.put("projectCname", FrameworkInfo.getProjectCname());
        String requestURL = request.getRequestURL().toString();
        AppContextProp app = (AppContextProp) SpringUtil.getBean(AppContextProp.class);
        if(app.getCtxGGMK().indexOf("baocloud.cn")>=0&&requestURL.indexOf("localhost")==-1){//非本地环境跳转首页
            String url = app.getCtxGGMK()+"web/MPINDEX";
            return redirect(url);
        }else{
            String loginName = UserSession.getLoginName();
            model.put("loginName", loginName);
            model.put("loginCName", SUserUtil.getUserByUserCode(loginName).getUserName());
            model.put("isAdmin", SUserUtil.isAdmin(loginName));
            return "/index";
        }
    }

    @GetMapping("/index")
    public String index(ModelMap model) {
        model.put("projectCname", FrameworkInfo.getProjectCname());
        String loginName = UserSession.getLoginName();
        model.put("loginName", loginName);
        ADUser userByUserCode = SUserUtil.getUserByUserCode(loginName);
        if (userByUserCode != null) {
            model.put("loginCName", userByUserCode.getUserName());
        }
        model.put("isAdmin", SUserUtil.isAdmin(loginName));
        return "index";
    }

    @GetMapping("/indexEG")
    public String indexEG(ModelMap model) {
        model.put("projectCname", FrameworkInfo.getProjectCname());
        String loginName = UserSession.getLoginName();
        model.put("loginName", loginName);
        ADUser userByUserCode = SUserUtil.getUserByUserCode(loginName);
        if (userByUserCode != null) {
            model.put("loginCName", userByUserCode.getUserName());
        }
        model.put("isAdmin", SUserUtil.isAdmin(loginName));
        return "index";
    }

    @GetMapping("/moduleIndex")
    public String moduleIndex(String moduleCode, String moduleUrl, String urlName, String rootUrl, String menuCode, String tabMenuCode,
                              ModelMap model, HttpServletRequest request, RedirectAttributes attr) {
        model.put("projectCname", FrameworkInfo.getProjectCname());
        //跳其他应用
        if (StrUtil.isNotBlank(rootUrl) && !(request.getContextPath().substring(1)).equals(rootUrl)) {
            String origin = request.getHeader("Origin");
            if (StrUtil.isBlank(origin)) {
                origin = request.getHeader("Host");
            }
            String url = request.getScheme() + "://" + origin + "/" + rootUrl + "/web/MPID01";
            attr.addAttribute("moduleCode", moduleCode);
            attr.addAttribute("moduleUrl", moduleUrl);
            attr.addAttribute("rootUrl", rootUrl);
            attr.addAttribute("menuCode", menuCode);
            attr.addAttribute("tabMenuCode", tabMenuCode);
            return redirect(url);
        }

        //本应用
        String url = "/index/menuIndex";
        attr.addAttribute("moduleCode", moduleCode);
        attr.addAttribute("moduleUrl", moduleUrl);
        attr.addAttribute("rootUrl", rootUrl);
        attr.addAttribute("menuCode", menuCode);
        attr.addAttribute("tabMenuCode", tabMenuCode);
        return redirect(url);
    }

    /**
     *
     * @param moduleCode 模块名
     * @param moduleUrl 默认打开的tab的url
     * @param urlName 默认打开的tab名称
     * @param menuCode 定制的顶层菜单名称
     * @param tabMenuCode 定制的tab页菜单编码(menuUrl为空时起作用)
     * @param model
     * @return
     */
    @GetMapping("/menuIndex")
    public String menuIndex(String moduleCode, String moduleUrl, String urlName, String menuCode, String tabMenuCode, ModelMap model) {
        model.put("projectCname", FrameworkInfo.getProjectCname());
        //菜单类别
        String menuKind = "";
        //模块名称
        String moduleName = "";
        String loginName = UserSession.getLoginName();
        model.put("loginName", loginName);
        model.put("loginCName", SUserUtil.getUserByUserCode(loginName).getUserName());
        model.put("isAdmin", SUserUtil.isAdmin(loginName));
        if(StringUtils.isNotEmpty(moduleCode)) {
            Map<String, Object> dict = SDictUtil.getDict("MP", "BUSINESS_TYPE", moduleCode);
            if (null != dict) {
                moduleName = (String)dict.get("dictName");
                if (StringUtils.isEmpty(menuCode)) {
                    menuCode = (String) dict.get("menuCode");
                }
                if (StringUtils.isNotBlank(menuCode)) {
                    moduleCode = menuCode;
                }
                menuKind = (String) dict.get("menuKind");
                if (StringUtils.isEmpty(moduleUrl)) {
                    moduleUrl = (String)dict.get("componentUrl");
                }
                if (StringUtils.isEmpty(urlName)) {
                    urlName = (String)dict.get("dictName");
                }
            }
        } else if (StringUtils.isNotEmpty(menuCode)) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_MP_TY_MR2");
            eiInfo.set("menuCode", menuCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (outInfo.getStatus()>0) {
                Map<String, Object> hrefMenu = (Map<String, Object>)outInfo.get(EiConstant.resultBlock);
                if (hrefMenu!=null) {
                    moduleName = (String)hrefMenu.get("menuName");
                }
            }
        }

        if (StringUtils.isNotEmpty(tabMenuCode)) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_MP_TY_MR2");
            eiInfo.set("menuCode", tabMenuCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (outInfo.getStatus()>0) {
                Map<String, Object> hrefMenu = (Map<String, Object>)outInfo.get(EiConstant.resultBlock);
                if (hrefMenu!=null) {
                    moduleUrl = (String)hrefMenu.get("menuUrl");;
                    urlName = (String)hrefMenu.get("menuName");
                }
            }
        }

        model.put("moduleUrl", moduleUrl);
        model.put("moduleCode", menuCode);
        if (StringUtils.isEmpty(menuKind)) {
            menuKind = "fw";
        }
        model.put("menuKind", menuKind);
        if (StringUtils.isEmpty(moduleName)) {
            moduleName = "合同管理";
        }
        model.put("moduleName", moduleName);
        model.put("urlName", urlName);
        model.put("menuCode", menuCode);

        String url = "/moduleIndex";
        return url;
    }

    /***
     * 获取菜单数据
     * @return
     */
    @GetMapping("/getChildMenu/{menuCode}")
    @ResponseBody
    public List<Map> getChildMenu(@PathVariable("menuCode") String menuCode) {
        List<Map> result = new ArrayList<Map>();
        try {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_XS_02");
            eiInfo.set("loginName", UserSession.getLoginName());
            eiInfo.set("p", menuCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            result = (List<Map>) outInfo.get("result");
            for (Map map : result) {
                map.put("children", null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /***
     * 获取菜单数据
     * @return
     */
    @GetMapping("/getMyChildMenu/{menuCode}")
    @ResponseBody
    public List<Map> getMyChildMenu(@PathVariable("menuCode") String menuCode) {
        List<Map> menuList = new ArrayList<>();
        try {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_MP_TY_MR1");
            String loginName = UserSession.getLoginName();
            eiInfo.set("operator", UserSession.getLoginName());
            eiInfo.set("rightCodes", SUserUtil.getUserRightInfo(loginName));
            eiInfo.set("menuCode", menuCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            List<Object> result = (List<Object>) outInfo.get(EiConstant.resultBlock);
            Map<String, Object> menu;
            Map map;
            for (Object obj : result) {
                map = BeanUtil.toBean(obj, Map.class);
                menu = new HashMap();
                menu.put("parent", menuCode);
                menu.put("isAuth", "");
                menu.put("imagePath", map.get("menuImage"));
                menu.put("nodeUrl", map.get("menuUrl")==null?"":map.get("menuUrl"));
                menu.put("label", map.get("menuCode"));
                menu.put("text", map.get("menuName"));
                menu.put("leaf", map.get("isLeaf"));
                menu.put("children", null);
                menuList.add(menu);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return menuList;
    }

    @GetMapping("/toDbXx")
    public String toDbXx(String menuCode){
        if(StrUtil.isBlank(menuCode)){
            throw new PlatException("参数为空!");
        }
        String url = "/index/moduleIndex?moduleCode="+menuCode;
        Map<String, Object> dict = SDictUtil.getDict("MP", "BUSINESS_TYPE", menuCode);
        if(null!=dict) {
            if(null!=dict.get("menuCode")) {
                url = "/index/moduleIndex?moduleCode="+dict.get("menuCode");
            }
            if(null==dict.get("componentUrl")||null==dict.get("componentEname")) {
                throw new PlatException("该待办找不到链接,请联系管理员到小代码[业务模块]里配置!");
            } else {
                String componentUrl = (String)dict.get("componentUrl");
                if(componentUrl.indexOf("/")!=0) {
                    componentUrl = "/"+componentUrl;
                }
                url+="&moduleUrl="+componentUrl+"&rootUrl="+dict.get("componentEname");
            }
        }else {
            throw new PlatException("该待办找不到链接,请联系管理员到小代码[业务模块]里配置!");
        }
        return redirect(url);
    }
}
