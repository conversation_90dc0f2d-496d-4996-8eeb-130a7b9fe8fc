package com.baosight.bsvkkj.mp.index;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 数据菜单
 */
@Service("indexMenu")
public class ServiceIndexMenu {

    /**
     * 获取菜单数据
     *
     * @param menuCode
     * @return
     */
    public List<Map> getChildMenu(String menuCode) {
        return getChildMenu(UserSession.getLoginName(), menuCode);
    }

    /***
     * 获取菜单数据
     * @return
     */
    public List<Map> getChildMenu(String operator, String menuCode) {
        List<Map> result = new ArrayList<Map>();
        try {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_XS_02");
            eiInfo.set("loginName", operator);
            eiInfo.set("p", menuCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            List<Map> maps = (List<Map>) outInfo.get("result");
            for (Map map : result) {
                map.put("children", null);
            }
            return maps;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /***
     * 获取菜单数据
     * @return
     */
    public List<Map> getMyChildMenu(String menuCode) {
        List<Map> menuList = new ArrayList<>();
        try {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_MP_TY_MR1");
            String loginName = UserSession.getLoginName();
            eiInfo.set("operator", UserSession.getLoginName());
            eiInfo.set("rightCodes", SUserUtil.getUserRightInfo(loginName));
            eiInfo.set("menuCode", menuCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            List<Object> result = (List<Object>) outInfo.get(EiConstant.resultBlock);
            if (result!=null) {
                Map<String, Object> menu;
                Map map;
                for (Object obj : result) {
                    map = BeanUtil.toBean(obj, Map.class);
                    menu = new HashMap();
                    menu.put("parent", menuCode);//合同起草
                    menu.put("isAuth", "");
                    menu.put("imagePath", map.get("menuImage"));
                    menu.put("nodeUrl", map.get("menuUrl")==null?"":map.get("menuUrl"));
                    menu.put("label", map.get("menuCode"));
                    menu.put("text", map.get("menuName"));
                    menu.put("leaf", map.get("isLeaf"));
                    menu.put("children", null);
                    menuList.add(menu);
                }
            }
            return menuList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return menuList;
    }
}
