package com.baosight.bsvkkj.mp.index;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.bsvkkj.utils.EncryptUtil;
import com.baosight.bsvkkj.utils.RSAUtil;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 单点登录相关
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/sso")
public class ContrllerSSO {

    private static final int TIME_OUT = 30;//分钟
    private static final Logger logger = LoggerFactory.getLogger(ContrllerSSO.class);

    @RequestMapping(value = "/{loginName}", method = RequestMethod.GET)
    public void sso(HttpServletRequest request, HttpServletResponse response,
                    @PathVariable("loginName") String loginName) throws IOException {
        String user = EncryptUtil.decrypt(loginName);
        logger.info("====user====" + user);
        String[] userS = user.split("_");
        String userLabel = userS[0];
        String timestr = userS[1];
        logger.info("====userLabel====" + userLabel);
        logger.info("====timestr====" + timestr);

        Long time = Long.parseLong(timestr);
        Long currentTime = System.currentTimeMillis();
        if (time > currentTime + TIME_OUT * 60 * 1000 || time < currentTime - 100 * 60 * 1000) {
            throw new PlatException("密钥超时");
        } else {
            String userName = SUserUtil.getUserName(userLabel);
            String targetUrl = request.getParameter("targetUrl");
            String url;
            if (ObjectUtil.isEmpty(targetUrl)) {
                targetUrl = "/index.jsp";
                String path = request.getRequestURL().toString() + targetUrl;
                path = path.replace("/sso/" + loginName, "");
                logger.info("====path====" + path);
                url = RSAUtil.getEplatSSOUrl(path, userLabel, userName);
            } else {
                url = RSAUtil.getEplatSSOUrl(targetUrl, userLabel, userName);
            }
            logger.info("====url====" + url);
            System.out.println("====url====" + url);
            response.sendRedirect(url);
        }
    }

    @RequestMapping(value = "/admin/{jumpUser}", method = RequestMethod.GET)
    @ResponseBody
    public JSONObject ssoAdmin(HttpServletRequest request, HttpServletResponse response,
                               @PathVariable("jumpUser") String jumpUser) throws IOException {
        JSONObject jsonObject = new JSONObject();

        String loginName = UserSession.getLoginName();
        if (SUserUtil.isAdmin(loginName)) {
            String targetUrl = "/index.jsp";
            //String userName = UserUtil.getUserName(jumpUser);
            String path = request.getRequestURL().toString() + targetUrl;
            path = path.replace("/sso/admin/" + jumpUser, "");
            logger.info("====path====" + path);
            String url = RSAUtil.getEplatSSOUrl(path, jumpUser, jumpUser);
            jsonObject.put("ssoUrl", url);
            jsonObject.put("msg", "请先登出，然后在浏览器输入此ssoUrl");
        } else {
            jsonObject.put("msg", "超级管理才能使用");
        }
        return jsonObject;
    }
}
