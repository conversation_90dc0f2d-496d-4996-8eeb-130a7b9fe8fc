package com.baosight.bsvkkj.mp.ad.controller;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.domain.Ztree;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.dto.ADTree;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.utils.ServiceUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/mpad/org")
public class ControllerOrg extends BaseController {

    private String prefix = "/mpty/attachmentMap";

    @GetMapping("/A000002")
    public String A000001() {
        return prefix + "/A000002";
    }

    @GetMapping("/editUserRole")
    public String editUserRole(String orgCode,String loginName,ModelMap map1) {
        map1.put("orgCode",orgCode);
        map1.put("loginName",loginName);
        return prefix + "/editUserRole";
    }
    /**
     * 删除禁入单位
     */
    @PostMapping("/saveUserRole")
    @ResponseBody
    public AjaxResult saveUserRole(String ids,String userCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("guid", ids);
        eiInfo.set("userCode", userCode);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceMPADOrgUserRole", "saveRoleUser");
    }

    /**
     * 查询组织树
     *
     * @param orgCode    组织  默认BSVK 
     * @param parentCode 父组织
     * @param level      最高层级
     * @param values     回显值 ，隔开
     * @param excludeOrgCode 排除的组织 ，隔开
     * @return
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Ztree> treeData(String orgCode, String parentCode, String level, String values, String excludeOrgCode) {
        Integer i=null;
        try {
            i = Integer.parseInt(level);
        }catch (Exception  e){

        }
        return SOrgUtil.getOrgAllZtree(orgCode, parentCode, i, values, excludeOrgCode);
    }

    /**
     * 查询组织树
     *
     * @param orgCode    组织  默认BSVK 
     * @param parentCode 父组织
     * @param level      最高层级
     * @param values     回显值 ，隔开
     * @return
     */
    @GetMapping("/getADTree")
    @ResponseBody
    public List<ADTree> getADTree(String orgCode, String parentCode, String level, String values, String excludeOrgCode) {
        Integer i=null;
        try {
            i = Integer.parseInt(level);
        }catch (Exception  e){

        }
        return SOrgUtil.getADTree(orgCode, parentCode, i, values, excludeOrgCode);
    }

    /**
     * 查询公司组织树
     *
     * @param orgCode    组织
     * @param parentCode 父组织
     * @param level      最高层级
     * @param values     回显值 ，隔开
     * @param excludeOrgCode 排除的组织 ，隔开
     * @return
     */
    @GetMapping("/getCompanyADTree")
    @ResponseBody
    public List<ADTree> getCompanyADTree(String orgCode, String parentCode, String level, String values, String excludeOrgCode) {
        ADOrg company = SOrgUtil.getUserDirCompany(UserSession.getLoginName());
        if (company!=null) {
            return SOrgUtil.getADTree(company.getOrgCode(), parentCode, null, values, excludeOrgCode);
        }
        return new ArrayList<>();
    }

    /**
     * 查询商标注册-异议（子）
     他人对宝钢异议子列表
     */
    @PostMapping("/queryUser")
    @ResponseBody
    public TableDataInfo queryUser(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceMPADOrgUserRole", "queryUser");
        return getDataTable(query);
    }
    /**
     * 查询组织下的角色
     * orgCode
     */
    @PostMapping("/queryRole")
    @ResponseBody
    public TableDataInfo queryRole(@RequestBody Map<String, Object> map,String orgCode,String loginName) {
        map.put("orgCode",orgCode);
        map.put("loginName",loginName);
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceMPADOrgUserRole", "queryRole");
        return getDataTable(query);
    }

    /**
     * 选择组织
     *
     * @param selectType S:单选 M:多选 默认S
     * @param orgCode      组织id  默认BSVK 
     * @param level      最高层级
     * @param values     回显值 ，隔开
     * @param mmap
     * @return
     */
    @GetMapping("/selectOrgList")
    public String selectOrgList(String selectType, String orgCode, Integer level, String values,Integer showLevel, String callback, ModelMap mmap) {
        mmap.put("selectType", selectType);
        mmap.put("level", level);
        if (StrUtil.isBlank(orgCode)) {
            orgCode = "BSVK";
        }
        mmap.put("orgCode", orgCode);
        mmap.put("orgName", SOrgUtil.getOrgName(values));
        mmap.put("values", values);
        mmap.put("showLevel", showLevel);
        mmap.put("callback", callback);

        return "/mpad/selectOrg";
    }

    /**
     * 选择组织2
     * 支持动态传参数
     * @param model
     * @return
     */
    @GetMapping("/selectOrgList2")
    public String selectOrgList2(ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return "/mpad/selectOrg2";
    }

    /**
     * 选择组织3
     * @param model
     * @return
     */
    @GetMapping("/selectOrgList3")
    public String selectOrgList3(ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return "/mpad/selectOrg3";
    }

    /**
     * 子组织列表
     * @param model
     * @return
     */
    @GetMapping("/selectSubOrg/{topOrgCode}")
    public String selectSubOrg(ModelMap model, @PathVariable("topOrgCode")String topOrgCode) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return "/mpad/selectSubOrg";
    }

    /**
     *

     */
    @PostMapping("/subOrg/{topOrgCode}")
    @ResponseBody
    public TableDataInfo roleUserList(@PathVariable("topOrgCode")String topOrgCode, @RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock,map);
        eiInfo.set("orgCode", topOrgCode);
        EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_O3");
        List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
        String filterOrgCode = (String)map.get("filterOrgCode");
        if (StringUtils.isNotEmpty(filterOrgCode)) {
            String[] temps = filterOrgCode.split(",");
            if (StringUtils.isNotEmpty(filterOrgCode)) {
                orgList = orgList.stream().filter(o -> !Arrays.asList(temps).contains(o.get("orgCode"))).collect(Collectors.toList());
            }
        }
        return getDataTable(orgList);
    }

    /**
     * 获取组织下子组织相关角色的用户
     * @param model
     * @return
     */
    @GetMapping("/selectRoleUserOrg/{parentOrgCode}/{roleCode}")
    public String selectRoleUserOrg(ModelMap model, @PathVariable("parentOrgCode")String parentOrgCode,
                                    @PathVariable("roleCode")String roleCode) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return "/mpad/selectRoleUserOrg";
    }

    /**
     * 获取组织下子组织相关角色的用户
     */
    @PostMapping("/listRoleUserOrg/{parentOrgCode}/{roleCode}")
    @ResponseBody
    public TableDataInfo listRoleUserOrg(@PathVariable("parentOrgCode")String parentOrgCode,
                                           @PathVariable("roleCode")String roleCode, @RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock,map);
        eiInfo.set("parentOrgCode", parentOrgCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_R2");
        List<Map<String, Object>> userOrgList = (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
        String filterOrgCode = (String)map.get("filterOrgCode");
        if (StringUtils.isNotEmpty(filterOrgCode)) {
            String[] temps = filterOrgCode.split(",");
            if (StringUtils.isNotEmpty(filterOrgCode)) {
                userOrgList = userOrgList.stream().filter(o ->  !Arrays.asList(temps).contains(o.get("orgCode"))).collect(Collectors.toList());
            }
        }
        for (Map<String, Object> userOrg : userOrgList) {
            userOrg.put("orgUser", userOrg.get("orgCode")+":"+userOrg.get("userCode"));
        }
        return getDataTable(userOrgList);
    }
}
