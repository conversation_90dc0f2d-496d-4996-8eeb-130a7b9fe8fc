package com.baosight.bsvkkj.mp.ad.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ADOrg implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 组织别名(简称)
     */
    private String orgBriefName;
    /**
     * 组织路径全称
     */
    private String orgPathCode;
    /**
     * 组织路径全称
     */
    private String orgPathName;
    /**
     * 组织别名(简称)全称
     */
    private String orgBriefNamePath;
    /**
     * 组织类型
     */
    private String orgType;
    /**
     * 组织层级
     */
    private String orgLevel;
    /**
     * 父组织编码
     */
    private String parentCode;
    /**
     * 父组织名称
     */
    private String parentName;
}
