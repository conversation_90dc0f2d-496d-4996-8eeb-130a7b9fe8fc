package com.baosight.bsvkkj.mp.ad.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.dto.ADUser;
import com.baosight.bsvkkj.utils.MyUserSession;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component("SUserUtil")
public class SUserUtil {

    private static final String SESSION_KEY_SUSER = "SUserUtil";

    private static final ThreadLocal<Map<String, ADUser>> threadLocal = new ThreadLocal();

    private static Map<String, ADUser> getData() {
        Map<String, ADUser> map = threadLocal.get();
        if (map == null) {
            map = new ConcurrentHashMap();
            threadLocal.set(map);
        }
        return map;
    }

    /**
     * 返回当前登录人
     * @return
     */
    public static String getLoginName () {
        return UserSession.getLoginName();
    }

    /**
     * 根据用户编码获取用户信息
     *
     * @param userCode
     * @return
     */
    public static ADUser getUserByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        Map<String, ADUser> threadData = getData();
        ADUser user = threadData.get(userCode);
        if (user == null) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceId, "S_MP_AD_08");
            eiInfo.set("userCode", userCode);
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                throw new PlatException("用户编码获取用户信息失败:" + outInfo.getMsg());
            }
            Object object = outInfo.get("result");
            if (object instanceof ADUser) {
                user = (ADUser) object;
            } else {
                user = BeanUtil.toBean(object, ADUser.class);
            }
            //user = mapToUser(orgMap);
            if (user != null) {
                threadData.put(userCode, user);
            }
        }
        return user;
    }

    /**
     * 判断用户是否为系统管理员
     *
     * @param userCode
     * @return
     */
    public static boolean isAdmin(String userCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_XS_32");
        eiInfo.set("loginName", userCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return EiConstant.STATUS_SUCCESS == outInfo.getStatus();
    }

    /**
     * 判断用户是否为系统管理员
     *
     * @return
     */
    public static boolean isAdmin() {
        String userCode = UserSession.getLoginName();
        return isAdmin(userCode);
    }

    /**
     * 获取当前在线人数
     *
     * @return
     */
    public static int getOnlineUserNumbers() {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_XS_30");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return (int) outInfo.get("result");
    }

    /**
     * 根据工号获取名称（,隔开）
     *
     * @param userCode
     * @return
     */
    public static String getUserName(String userCode) {
        if (StrUtil.isBlank(userCode)) {
            return null;
        }
        String[] split = userCode.split(",");
        List<String> list = new ArrayList<>();
        for (String code : split) {
            ADUser userByUserCode = getUserByUserCode(code);
            if (userByUserCode != null) {
                list.add(userByUserCode.getUserName());
            }
        }
        if (!list.isEmpty()) {
            return list.stream().collect(Collectors.joining(","));
        }
        return userCode;
    }

    /**
     * 根据组织编码获取下级用户信息(直属)
     *
     * @param orgCode
     * @return
     */
    public static List<ADUser> getOrgUser(String orgCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("orgCode", orgCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_U2");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码获取下级用户信息(直属):" + outInfo.getMsg());
        }
        if (null == outInfo.get("result")) {
            return new ArrayList<>();
        }
        List<Object> userList = (List<Object>) outInfo.get("result");
        return listToUser(userList);
    }

    private static List<ADUser> listToUser(List<Object> userList) {
        if (userList == null) {
            return null;
        }
        List<ADUser> rtn = new ArrayList<>();
        for (Object data : userList) {
            if (data != null) {
                rtn.add(BeanUtil.toBean(data, ADUser.class));
            }
        }
        return rtn;
    }

    /**
     * 获取用户相关组织角色信息
     * @param userCode
     * @return
     */
    public static List<String> getUserRightInfo(String userCode){
        List<String> listRight = (List<String>) MyUserSession.getValue(SESSION_KEY_SUSER + ":UserRightInfo:" + userCode);
        if (listRight==null) {
            listRight = new ArrayList<>();
            //自动的全员属性
            listRight.add("RX_ALL");
            //用户属性
            listRight.add("U_"+userCode);
            //组织属性
            List<ADOrg> listOrg = SOrgUtil.getOrgByUserCode(userCode);
            if (listOrg!=null && listOrg.size()>0) {
                for (ADOrg org : listOrg) {
                    listRight.add("O_" + org.getOrgCode());
                    //组织包含子组织属性
                    String[] orgCodes = org.getOrgPathCode().split("/");
                    for (String orgCode : orgCodes) {
                        if (StringUtils.isNotEmpty(orgCode)) {
                            listRight.add("OX_" + orgCode);
                        }
                    }
                }
            }
            List<String> listRole = SRoleUtil.getUserAllRole(userCode);
            for (String roleCode : listRole) {
                if (StringUtils.isNotEmpty(roleCode)) {
                    listRight.add("R_"+roleCode);
                }
            }
            MyUserSession.setValue(SESSION_KEY_SUSER + ":UserRightInfo:" + userCode, listRight);
        }
        return listRight;
    }

    /**
     * 获取用户相关组织信息
     * @param userCode
     * @return
     */
    public static List<String> getUserOrgInfo(String userCode){
        List<String> listOrgCode = (List<String>) MyUserSession.getValue(SESSION_KEY_SUSER + ":UserOrgInfo:" + userCode);
        if (listOrgCode==null) {
            Set<String> setOrg = new HashSet<>();
            //组织属性
            List<ADOrg> listOrg = SOrgUtil.getOrgByUserCode(userCode);
            if (listOrg!=null && listOrg.size()>0) {
                for (ADOrg org : listOrg) {
                    setOrg.add(org.getOrgCode());
                    //组织包含子组织属性
                    String[] orgCodes = org.getOrgPathCode().split("/");
                    for (String orgCode : orgCodes) {
                        if (StringUtils.isNotEmpty(orgCode)) {
                            setOrg.add(orgCode);
                        }
                    }
                }
            }
            listOrgCode = new ArrayList<>(setOrg);
            MyUserSession.setValue(SESSION_KEY_SUSER + ":UserOrgInfo:" + userCode, listOrgCode);
        }
        return listOrgCode;
    }
}
