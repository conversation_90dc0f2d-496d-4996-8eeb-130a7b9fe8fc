package com.baosight.bsvkkj.mp.ad.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.domain.Ztree;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.dto.ADTree;
import com.baosight.bsvkkj.mp.ad.dto.ADUser;
import com.baosight.bsvkkj.utils.MyUserSession;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Component("SOrgUtil")
public class SOrgUtil {

    private static final String SESSION_KEY_SORG = "SOrgUtil";

    /**
     * 公司类型
     */
    private static final String TYPE_COMPANY = "28";

    /**
     * 部门类型
     */
    private static final String TYPE_DEPT = "21";

    private static final ThreadLocal<Map<String, Object>> threadLocal = new ThreadLocal();
    private static final String TOP_COMPANY = "BSVK";

    private static Map<String, Object> getData() {
        Map<String, Object> map = threadLocal.get();
        if (map == null) {
            map = new ConcurrentHashMap();
            threadLocal.set(map);
        }
        return map;
    }

    /**
     * 根据组织编码获取组织信息
     *
     * @param orgCode
     * @return
     */
    public static ADOrg getOrgByOrgCode(String orgCode) {
        if (StringUtils.isEmpty(orgCode))
            return null;
        Map<String, Object> threadData = getData();
        ADOrg org = (ADOrg) threadData.get("_org_" + orgCode);
        if (org == null) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("orgCode", orgCode);
            eiInfo.set(EiConstant.serviceId, "S_MP_AD_O1");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                return null;
            }
            org = BeanUtil.toBean(outInfo.get("result"), ADOrg.class);
            if (org == null) {
                return org;
            }
            threadData.put("_org_" + orgCode, org);
        }

        return org;
    }

    /**
     * 根据组织名称获取组织信息
     *
     * @param orgName
     * @return
     */
    public static ADOrg getOrgByOrgName(String orgName) {
        if (StringUtils.isEmpty(orgName))
            return null;
        Map<String, Object> threadData = getData();
        ADOrg org = (ADOrg) threadData.get("_org_" + orgName);
        if (org == null) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("orgName", orgName);
            eiInfo.set(EiConstant.serviceId, "S_MP_AD_10");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                //throw new PlatException("根据组织编码获取组织信息失败:"+outInfo.getMsg());
                return null;
            }
            List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
            if (ObjectUtil.isEmpty(orgList)) {
                return null;
            } else if (orgList.size() > 1) {
                throw new PlatException("根据组织名称【" + orgName + "】获取到多个组织");
            } else {
                org = BeanUtil.toBean(orgList.get(0), ADOrg.class);
                if (org == null) {
                    return org;
                }
                threadData.put("_org_" + orgName, org);
            }
        }
        return org;
    }

    /**
     * 查询所有组织
     *
     * @param orgCode
     * @return
     */
    public static ADOrg[] getAllParentOrg(String orgCode) {
        if (StringUtils.isEmpty(orgCode))
            return null;
        Map<String, Object> threadData = getData();
        ADOrg[] allParentOrg = (ADOrg[]) threadData.get("_allParentOrg_" + orgCode);
        if (allParentOrg == null) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("orgCode", orgCode);
            eiInfo.set(EiConstant.serviceId, "S_MP_AD_O8");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                return null;
            }
            List<Object> objList = (List<Object>) outInfo.get("result");
            List<ADOrg> orgList = objList.stream().map(org -> BeanUtil.toBean(org, ADOrg.class)).sorted(new OrgComprator()).collect(Collectors.toList());
            allParentOrg = new ADOrg[orgList.size()];
            orgList.toArray(allParentOrg);
            threadData.put("_allParentOrg_" + orgCode, allParentOrg);
        }
        return allParentOrg;
    }

    /**
     * 查询指定层级组织
     *
     * @param orgCode
     * @param level   层级
     * @return
     */
    public static ADOrg getLevelOrg(String orgCode, int level) {
        ADOrg[] orgS = getAllParentOrg(orgCode);
        if (orgS == null || orgS.length < level) {
            return null;
        }
        if (orgS.length == level) {
            return getOrgByOrgCode(orgCode);
        }
        if (orgS.length > level)
            return orgS[level];
        return null;
    }

    /**
     * 根据组织编码获取父组织信息
     *
     * @param orgCode
     * @return
     */
    public static ADOrg getParentOrgByOrgCode(String orgCode) {
        Map<String, Object> threadData = getData();
        ADOrg pOrg = (ADOrg) threadData.get("_parent_" + orgCode);
        if (pOrg == null) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("orgCode", orgCode);
            eiInfo.set(EiConstant.serviceId, "S_MP_AD_O2");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                return null;
            }
            pOrg = BeanUtil.toBean(outInfo.get("result"), ADOrg.class);
            if (pOrg == null) {
                return pOrg;
            }
            threadData.put("_parent_" + orgCode, pOrg);
        }
        return pOrg;
    }

    /**
     * 根据组织编码获取下级组织信息(直属)
     *
     * @param orgCode
     * @return
     */
    public static List<ADOrg> getSubOrgByOrgCode(String orgCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("orgCode", orgCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_O3");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码获取下级组织信息(直属):" + outInfo.getMsg());
        }
        if (null == outInfo.get("result")) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
        return listToOrg(orgList);
    }

    /**
     * 根据组织编码获取下级用户信息(直属)
     *
     * @param orgCode
     * @return
     */
    public static List<ADUser> getUserByOrgCode(String orgCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("orgEname", orgCode);
        eiInfo.set(EiConstant.serviceId, "S_XS_60");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            //throw new PlatException("根据组织编码获取下级用户信息(直属)失败:"+outInfo.getMsg());
            return null;
        }
        List<ADUser> rtn = new ArrayList<ADUser>();
        List<Map<String, Object>> userList = (List<Map<String, Object>>) outInfo.get("result");
        for (Map<String, Object> userMap : userList) {
            ADUser user = new ADUser();
            user.setUserCode((String) userMap.get("loginName"));
            user.setUserName((String) userMap.get("userName"));
            rtn.add(user);
        }
        return rtn;
    }

    /**
     * 根据用户编码获取组织信息
     *
     * @param userCode
     * @return
     */
    public static List<ADOrg> getOrgByUserCode(String userCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("userCode", userCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_O7");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            //throw new PlatException("根据用户编码获取组织信息:"+outInfo.getMsg());
            return null;
        }
        List<ADOrg> rtn = new ArrayList<>();
        List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
        for (Map<String, Object> map : orgList) {
            rtn.add(BeanUtil.toBean(map, ADOrg.class));
        }
        return rtn;
    }

    private static List<ADOrg> listToOrg(List<Map<String, Object>> orgList) {
        if (orgList == null) {
            return null;
        }
        List<ADOrg> rtn = new ArrayList<>();
        for (Map<String, Object> map : orgList) {
            if (map != null) {
                String orgCode = (String) map.get("orgEname");
                if (StringUtils.isBlank(orgCode)) {
                    orgCode = (String) map.get("orgCode");
                }
                rtn.add(getOrgByOrgCode(orgCode));
            }
        }
        return rtn;
    }

    /**
     * 获取用户的主组织
     *
     * @param userCode
     * @return
     */
    public static ADOrg getMainOrgByUserCode(String userCode) {
        //从session获取
        ADOrg org = (ADOrg) MyUserSession.getValue(SESSION_KEY_SORG + ":MainOrg:" + userCode);
        if (org == null) {
            if ("admin".equals(userCode)) {
                org = SOrgUtil.getOrgByOrgCode("BSVK00");
            } else {
                List<ADOrg> list = getOrgByUserCode(userCode);
                if (list == null || list.size() == 0) {
                    return null;
                }
                org = list.get(0);
            }
            if (org == null) {
                return org;
            }
            MyUserSession.setValue(SESSION_KEY_SORG + ":MainOrg:" + userCode, org);
        }
        return org;
    }

    /**
     * 获取用户最大的部门组织
     *
     * @param userCode
     * @return
     */
    public static ADOrg getMainMaxOrgByUserCode(String userCode) {
        //从session获取
        ADOrg org = (ADOrg) MyUserSession.getValue(SESSION_KEY_SORG + ":MainMaxOrg:" + userCode);
        if (org == null) {
            ADOrg mainOrg = getMainOrgByUserCode(userCode);
            if (TYPE_COMPANY.equals(mainOrg.getOrgType())) {
                org = mainOrg;
            } else {
                if (mainOrg != null) {
                    ADOrg[] orgs = getAllParentOrg(mainOrg.getOrgCode());
                    for (int i = orgs.length - 1; i > 0; i--) {
                        if (TYPE_COMPANY.equals(orgs[i].getOrgType())) {
                            break;
                        }
                        org = orgs[i];
                    }
                }
            }
            MyUserSession.setValue(SESSION_KEY_SORG + ":MainMaxOrg:" + userCode, org);
        }
        return org;
    }

    /**
     * 根据组织编码获取上级公司
     *
     * @param orgCode
     * @return
     */
    public static ADOrg[] getCompany(String orgCode) {
        Map<String, Object> threadData = getData();
        ADOrg[] pCompanyS = (ADOrg[]) threadData.get("_parentCompany_" + orgCode);
        if (pCompanyS == null) {
            ADOrg cOrg = getOrgByOrgCode(orgCode);
            if (null == cOrg) {
                return null;
            }
            ADOrg[] orgs = getAllParentOrg(orgCode);
            if (orgs==null) {
                return null;
            }
            List<ADOrg> orgList = new ArrayList<>();
            for (int i = 0; i < orgs.length; i++) {
                if (TYPE_COMPANY.equals(orgs[i].getOrgType())) {
                    orgList.add(orgs[i]);
                }
            }
            pCompanyS = new ADOrg[orgList.size()];
            orgList.toArray(pCompanyS);
            threadData.put("_parentCompany_" + orgCode, pCompanyS);
        }
        return pCompanyS;
    }

    /**
     * 根据组织编码直属上级公司
     *
     * @param orgCode
     * @return
     */
    public static ADOrg getDirCompany(String orgCode) {
        ADOrg cOrg = getOrgByOrgCode(orgCode);
        if (cOrg != null && TYPE_COMPANY.equals(cOrg.getOrgType())) {
            return cOrg;
        }
        ADOrg[] orgS = getCompany(orgCode);
        if (orgS == null) {
            return null;
        }
        return orgS[orgS.length - 1];
    }

    /**
     * 根据组织编码获取指定上级公司
     *
     * @param orgCode
     * @param level   1级子公司就传1，类推
     * @return
     */
    public static ADOrg getLevelCompany(String orgCode, int level) {
        ADOrg[] orgS = getCompany(orgCode);
        if (orgS == null || orgS.length < level) {
            return null;
        }
        if (orgS.length > level)
            return orgS[level];

        return null;
    }

    /**
     * 判断是否是几级 子公司或者子公司部门
     * 长度为1为一级子公司
     * 长度为2为二级子公司，类推
     *
     * @param orgCode
     * @param level   判断级别
     * @return
     */
    public static Boolean isLevelCompanyDept(String orgCode, int level) {
        ADOrg[] orgS = getCompany(orgCode);
        if (orgS == null) {
            return false;
        }
        return orgS.length - 1 == level;
    }

    /**
     * 获取组织树
     *
     * @param orgCode
     * @param parentCode 父组织
     * @param level      组织树最多层级
     * @param values     回显值 ，隔开
     * @param excludeOrgCode 排除的组织 ，隔开
     * @return
     */
    public static List<Ztree> getOrgAllZtree(String orgCode, String parentCode, Integer level, String values, String excludeOrgCode) {
        if (StrUtil.isBlank(orgCode)) {
            // 
            orgCode = TOP_COMPANY;
        }
        orgCode = "'" + orgCode.replaceAll(",", "','") + "'";
        if (StringUtils.isNotEmpty(excludeOrgCode)) {
            excludeOrgCode = "'" + excludeOrgCode.replaceAll(",", "','") + "'";
        }
        List<Map<String, Object>> query = queryOrgChildByOrgCode(orgCode, level, parentCode, excludeOrgCode);

        List<Ztree> ztrees = new ArrayList<>();
        for (Map<String, Object> map : query) {
            String orgType = (String) map.get("orgType");
            Ztree ztree = new Ztree();
            ztree.setId((String) map.get("orgId"));
            ztree.setpId((String) map.get("parentOrgId"));
            ztree.setCode((String) map.get("orgCode"));
            ztree.setOpen(false);
            ztree.setCode((String) map.get("orgCode"));
            Integer num = (Integer) map.get("num");
            if (num != null && num > 0) {
                ztree.setIsParent(true);
            }
            if (StrUtil.isNotBlank(values)) {
                List<String> asList = Arrays.asList(values.split(","));
                if (asList.contains(ztree.getId())) {
                    ztree.setChecked(true);
                }
            }
            ztree.setName((String) map.get("orgName"));
            ztree.setTitle((String) map.get("orgName"));
            if ("28".equals(orgType)) {
                ztree.setOrgPathName((String) map.get("orgName"));
            } else {
                ztree.setOrgPathName((String) map.get("orgPathName"));
            }
            ztrees.add(ztree);
        }
        return ztrees;
    }

    /**
     * 根据组织id 查询所有子节点
     *
     * @param orgCode
     * @param level      最大层级
     * @param parentCode 父组织
     * @param excludeOrgCode 排除的组织 ，隔开
     * @return
     */
    private static List<Map<String, Object>> queryOrgChildByOrgCode(String orgCode, Integer level, String parentCode, String excludeOrgCode) {
        if (StrUtil.isBlank(orgCode) && StrUtil.isBlank(parentCode) && level == null) {
            return null;
        } else {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("orgCode", orgCode);
            eiInfo.set("level", level);
            eiInfo.set("parentCode", parentCode);
            eiInfo.set("excludeOrgCode", excludeOrgCode);
            eiInfo.set(EiConstant.serviceId, "S_MP_AD_O5");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
            return orgList;
        }
    }

    /**
     * 根据组织编码获取名称（,隔开）
     *
     * @param orgCode
     * @return
     */
    public static String getOrgName(String orgCode) {
        if (StrUtil.isEmpty(orgCode)) {
            return null;
        }
        String[] split = orgCode.split(",");
        List<String> list = new ArrayList<>();
        for (String code : split) {
            ADOrg orgByOrgCode = getOrgByOrgCode(code);
            if (orgByOrgCode != null) {
                list.add(orgByOrgCode.getOrgName());
            }
        }
        if (!list.isEmpty()) {
            return list.stream().collect(Collectors.joining(","));
        }
        return null;
    }

    /**
     * 根据组织编码获取全部名称（,隔开）
     *
     * @param orgCode
     * @return
     */
    public static String getOrgPathName(String orgCode) {
        if (StrUtil.isEmpty(orgCode)) {
            return null;
        }
        String[] split = orgCode.split(",");
        List<String> list = new ArrayList<>();
        for (String code : split) {
            ADOrg orgByOrgCode = getOrgByOrgCode(code);
            if (orgByOrgCode != null) {
                String orgPathName = orgByOrgCode.getOrgPathName();
                list.add(orgPathName);
            }
        }
        if (!list.isEmpty()) {
            return list.stream().collect(Collectors.joining(","));
        }
        return null;
    }

    /**
     * 判断是组织下员工
     *
     * @param userCode
     * @return
     */
    public static boolean isOrgMember(String orgCode, String userCode) {
        ADOrg org = getMainOrgByUserCode(userCode);
        if (null != org) {
            ADOrg[] orgS = getAllParentOrg(org.getOrgCode());
            for (ADOrg adOrg : orgS) {
                if (orgCode.equals(adOrg.getOrgCode())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否parentOrgCode的子组织
     *
     * @param orgCode
     * @param parentOrgCode
     * @return
     */
    public static boolean isChildOrg(String orgCode, String parentOrgCode) {
        ADOrg[] orgS = getAllParentOrg(orgCode);
        if (null != orgS) {
            for (ADOrg adOrg : orgS) {
                if (parentOrgCode.equals(adOrg.getOrgCode())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取用户直属上级公司
     *
     * @param userCode
     * @return
     */
    public static ADOrg getUserDirCompany(String userCode) {
        //从session获取
        ADOrg org = (ADOrg) MyUserSession.getValue(SESSION_KEY_SORG + ":Company:" + userCode);
        if (org == null) {
            ADOrg dept = SOrgUtil.getMainOrgByUserCode(userCode);
            if (dept != null) {
                org = SOrgUtil.getDirCompany(dept.getOrgCode());
            }
            MyUserSession.setValue(SESSION_KEY_SORG + ":Company:" + userCode, org);
        }
        return org;
    }

    /**
     * 向上获取组织，并限制组织代码长度
     *
     * @param org    初始组织
     * @param length 限制组织代码长度
     * @return
     */
    public static ADOrg recursiveGetOrgLimitLength(ADOrg org, int length) {
        if (org == null) {
            throw new BusinessException("用户所在组织异常！");
        }

        if (org.getOrgCode().length() > length) {
            ADOrg parentOrg = getParentOrgByOrgCode(org.getOrgCode());
            return recursiveGetOrgLimitLength(parentOrg, length);
        } else {
            return org;
        }
    }

    /**
     * 根据组织编码获取下级有效组织信息(所有)
     * 最多返回1000
     *
     * @param orgCode
     * @param orgType null 全部, company 公司, dept 部门, 25 作业区, 27 班组
     * @return
     */
    public static List<ADOrg> getAllSubOrgByOrgCode(String orgCode, String orgType) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("orgCode", orgCode);
        eiInfo.set("orgType", orgType);
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_O4");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码获取下级组织信息(所有):" + outInfo.getMsg());
        }
        if (null == outInfo.get("result")) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
        List<ADOrg> rtn = new ArrayList<>();
        for (Map<String, Object> map : orgList) {
            rtn.add(BeanUtil.toBean(map, ADOrg.class));
        }
        return rtn;
    }

    /**
     * 获取传入组织的最大的组织
     * <p>
     * 过滤父子组织，保留父组织
     *
     * @param list
     * @return
     */
    public static List<ADOrg> getMaxOrg(List<ADOrg> list) {
        if (list == null || list.size() <= 1) {
            return list;
        }
        List<ADOrg> rtnOrg = new ArrayList<>();
        //过滤父子组织，保留父组织
        for (ADOrg adOrg : list) {
            if (StringUtils.isBlank(adOrg.getOrgPathCode())) {
                continue;
            }
            //是否需要
            boolean needAdd = true;
            for (ADOrg adOrgChoose : rtnOrg) {
                if (adOrg.getOrgPathCode().indexOf(adOrgChoose.getOrgPathCode()) > -1) {//如果是其子组织忽略
                    needAdd = false;
                    break;
                } else if (adOrgChoose.getOrgPathCode().indexOf(adOrg.getOrgPathCode()) > -1) {//如果是其父组织替换为父组织
                    rtnOrg.remove(adOrgChoose);
                    rtnOrg.add(adOrg);
                    needAdd = false;
                    break;
                }
            }
            if (needAdd) {
                rtnOrg.add(adOrg);
            }
        }
        return rtnOrg;
    }

    public static String getOrgByLoginCode() {
        String userCode = UserSession.getLoginName();
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            return org.getOrgCode();
        }
        ADOrg pOrg = getUserDirCompany(userCode);
        if (pOrg == null) {
            return null;
        }
        return pOrg.getOrgCode();
    }

    public static String getParentOrgByLoginCode() {
        String UserCode = UserSession.getLoginName();
        if ("admin".equals(UserCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            return org.getOrgCode();
        }
        ADOrg loginOrg = getMainOrgByUserCode(UserCode);
        if (loginOrg == null) {
            return null;
        }
        return loginOrg.getOrgCode();
    }

    /**
     * 获取组织树
     *
     * @param orgCode
     * @param parentCode 父节点
     * @param level      组织树最多层级
     * @param values     回显值 ，隔开
     * @param excludeOrgCode 排除的组织 ，隔开
     * @return
     */
    public static List<ADTree> getADTree(String orgCode, String parentCode, Integer level, String values, String excludeOrgCode) {
        if (StrUtil.isBlank(orgCode)) {
            // 
            orgCode = TOP_COMPANY;
        }
        orgCode = "'" + orgCode.replaceAll(",", "','") + "'";
        if (StringUtils.isNotEmpty(excludeOrgCode)) {
            excludeOrgCode = "'" + excludeOrgCode.replaceAll(",", "','") + "'";
        }
        List<Map<String, Object>> query = queryOrgChildByOrgCode(orgCode, level, parentCode, excludeOrgCode);

        List<ADTree> ztrees = new ArrayList<>();
        ADTree adTree;
        List<ADUser> list = SUserUtil.getOrgUser(parentCode);
        if (list != null) {
            for (ADUser adUser : list) {
                adTree = new ADTree();
                adTree.setType("user");
                adTree.setId(adUser.getUserCode());
                adTree.setpId(parentCode);
                adTree.setCode(adUser.getUserCode());
                adTree.setOpen(false);
                adTree.setCode(adUser.getUserCode());
                adTree.setUserCode(adUser.getUserCode());
                adTree.setUserName(adUser.getUserName());
                adTree.setPostCode(adUser.getPostCode());
                adTree.setPostName(adUser.getPostName());
                adTree.setIsParent(false);
                if (StrUtil.isNotBlank(values)) {
                    List<String> asList = Arrays.asList(values.split(","));
                    if (asList.contains(adTree.getId())) {
                        adTree.setChecked(true);
                    }
                }
                adTree.setName(adUser.getUserName());
                adTree.setTitle(adUser.getPostName());
                adTree.setOrgPathName("");
                adTree.setIconSkin("user");
                ztrees.add(adTree);
            }

        }

        for (Map<String, Object> map : query) {
            adTree = new ADTree();
            adTree.setType("org");
            adTree.setId((String) map.get("orgId"));
            adTree.setpId((String) map.get("parentOrgId"));
            adTree.setCode((String) map.get("orgCode"));
            adTree.setOpen(false);
            adTree.setCode((String) map.get("orgCode"));
            //Integer num = (Integer) map.get("num");
            //if (num != null && num > 0) {
            adTree.setIsParent(true);//有用户都是true
            //}
            if (StrUtil.isNotBlank(values)) {
                List<String> asList = Arrays.asList(values.split(","));
                if (asList.contains(adTree.getId())) {
                    adTree.setChecked(true);
                }
            }
            adTree.setName((String) map.get("orgName"));
            adTree.setTitle((String) map.get("orgName"));
            adTree.setOrgPathName((String) map.get("orgPathName"));
            ztrees.add(adTree);
        }
        return ztrees;
    }

    public static ADOrg filed_getOrgByUserCode(String userCode) {
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            return org;
        }
        ADOrg pOrg = getUserDirCompany(userCode);
        return SOrgUtil.getOrgByOrgCode(pOrg.getOrgCode());
    }

    public static ADOrg filed_getParentOrgByUserCode(String userCode) {
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            return org;
        }
        ADOrg loginOrg = getMainOrgByUserCode(userCode);
        return loginOrg;
    }

    /**
     * 获取登录用户的组织
     * 包括映射的
     *
     * @return
     */
    public static List<Map<String, Object>> getOrgByLoginUser() {
        String userCode = UserSession.getLoginName();
        List<ADOrg> list = new ArrayList<>();
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            list.add(org);
        } else {
            list = getOrgByUserCode(userCode);
        }

        //获取组织映射
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceName, "HTSQOrgMap");
        eiInfo.set(EiConstant.methodName, "getDestOrg");
        eiInfo.set("listOrg", list);
        eiInfo = XLocalManager.call(eiInfo);

        list = (List<ADOrg>) eiInfo.get(EiConstant.resultBlock);

        List<Map<String, Object>> mapList = new ArrayList<>();
        for (ADOrg adOrg : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("dictValue", adOrg.getOrgCode());
            map.put("dictName", adOrg.getOrgPathName());
            map.put("sort", 0);
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 获取用户的组织
     * 包括映射的
     * 带账套过滤
     *
     * @return
     */
    public static List<Map<String, Object>> getOrgByUserCode(String userCode, String companyId) {
        List<ADOrg> list = new ArrayList<>();
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            list.add(org);
        } else {
            list = getOrgByUserCode(userCode);
        }

        //获取组织映射
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceName, "HTSQOrgMap");
        eiInfo.set(EiConstant.methodName, "getDestOrg");
        eiInfo.set("listOrg", list);
        eiInfo = XLocalManager.call(eiInfo);

        list = (List<ADOrg>) eiInfo.get(EiConstant.resultBlock);

        List<Map<String, Object>> mapList = new ArrayList<>();
        List<String> temp;
        for (ADOrg adOrg : list) {
            if (StringUtils.isNotEmpty(companyId)) {
                temp = Arrays.asList(adOrg.getOrgPathCode().split("/"));
                if (!temp.contains(companyId)) {
                    continue;
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("dictValue", adOrg.getOrgCode());
            map.put("dictName", adOrg.getOrgPathName());
            map.put("sort", 0);
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 获取登录用户的组织
     * 包括映射的
     * 带账套过滤
     *
     * @return
     */
    public static List<Map<String, Object>> getOrgByLoginUser(String companyId) {
        String userCode = UserSession.getLoginName();
        return getOrgByUserCode(userCode, companyId);
    }

    /**
     * 获取登录用户的组织
     * 包括映射的
     * 带账套过滤
     * noFilterOrgCode 不过滤的组织编码
     *
     * @return
     */
    public static List<Map<String, Object>> getOrgByLoginUser(String companyId, String noFilterOrgCode) {
        String userCode = UserSession.getLoginName();
        List<ADOrg> list = new ArrayList<>();
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            list.add(org);
        } else {
            list = getOrgByUserCode(userCode);
        }

        //获取组织映射
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceName, "HTSQOrgMap");
        eiInfo.set(EiConstant.methodName, "getDestOrg");
        eiInfo.set("listOrg", list);
        eiInfo = XLocalManager.call(eiInfo);

        list = (List<ADOrg>) eiInfo.get(EiConstant.resultBlock);

        List<Map<String, Object>> mapList = new ArrayList<>();
        List<String> temp;
        String[] noFilterOrgCodeS = noFilterOrgCode.split(",");
        for (ADOrg adOrg : list) {
            //是否不需要过滤
            boolean notFilter = false;
            for (String _temp : noFilterOrgCodeS) {
                if (!StringUtils.isBlank(_temp)&&adOrg.getOrgPathCode().indexOf(_temp)>-1) {
                    notFilter = true;
                    break;
                }
            }
            if (!notFilter) {
                temp = Arrays.asList(adOrg.getOrgPathCode().split("/"));
                if (!temp.contains(companyId)) {
                    continue;
                }
            }
            if (StringUtils.isNotEmpty(companyId)) {
                if (StringUtils.isBlank(noFilterOrgCode)||noFilterOrgCode.indexOf(adOrg.getOrgPathCode())>-1) {
                    temp = Arrays.asList(adOrg.getOrgPathCode().split("/"));
                    if (!temp.contains(companyId)) {
                        continue;
                    }
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("dictValue", adOrg.getOrgCode());
            map.put("dictName", adOrg.getOrgPathName());
            map.put("sort", 0);
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 根据工号获取组织;
     * 包括映射的
     * 带账套过滤
     *
     * @return
     */
    public static ADOrg getOrgByUser(String userCode, String companyId) {
        List<ADOrg> list = new ArrayList<>();
        if ("admin".equals(userCode)) {
            ADOrg org = SOrgUtil.getOrgByOrgCode("BSVK00");
            list.add(org);
        } else {
            list = getOrgByUserCode(userCode);
        }

        //获取组织映射
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceName, "HTSQOrgMap");
        eiInfo.set(EiConstant.methodName, "getDestOrg");
        eiInfo.set("listOrg", list);
        eiInfo = XLocalManager.call(eiInfo);

        list = (List<ADOrg>) eiInfo.get(EiConstant.resultBlock);

        ADOrg adOrgRe = new ADOrg();
        List<String> temp;
        for (ADOrg adOrg : list) {
            if (StringUtils.isNotEmpty(companyId)) {
                temp = Arrays.asList(adOrg.getOrgPathCode().split("/"));
                if (!temp.contains(companyId)) {
                    continue;
                }
                adOrgRe = adOrg;
            }
        }
        return adOrgRe;
    }

    /**
     * 获取组织最大部门属性组织
     *
     * @param orgCode
     * @return
     */
    public static ADOrg getMaxDeptOrg(String orgCode) {
        ADOrg org = null;
        ADOrg[] orgs = getAllParentOrg(orgCode);
        for (int i = orgs.length - 1; i > 0; i--) {
            if (TYPE_COMPANY.equals(orgs[i].getOrgType())) {
                break;
            }
            org = orgs[i];
        }
        if (org == null) {
            org = getOrgByOrgCode(orgCode);
            if (org == null || TYPE_COMPANY.equals(org.getOrgType())) {
                return null;
            }
        }
        return org;
    }

    /**
     * 根据用户编码和角色获取所有组织
     *
     * @param userCode
     * @return
     */
    public static List<ADOrg> getOrgByUserCodeAndRoleCode(String userCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("userCode", userCode);
        eiInfo.set("roleCode", roleCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_O9");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        List<ADOrg> rtn = new ArrayList<>();
        List<Map<String, Object>> orgList = (List<Map<String, Object>>) outInfo.get("result");
        for (Map<String, Object> map : orgList) {
            rtn.add(BeanUtil.toBean(map, ADOrg.class));
        }
        return rtn;
    }

    //根据OrgLevel排序
    static class OrgComprator implements Comparator<ADOrg> {
        @Override
        public int compare(ADOrg o1, ADOrg o2) {
            int t1 = Integer.parseInt(o1.getOrgLevel());
            int t2 = Integer.parseInt(o2.getOrgLevel());
            return t1 > t2 ? 1 : -1;
        }
    }
}
