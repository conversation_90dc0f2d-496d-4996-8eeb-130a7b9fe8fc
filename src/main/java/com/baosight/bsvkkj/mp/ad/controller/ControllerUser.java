package com.baosight.bsvkkj.mp.ad.controller;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.bsvkkj.utils.ServiceUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/mpad/user")
public class ControllerUser extends BaseController {

	/**
	 * 

	 */
	@PostMapping("/userList")
	@ResponseBody
	public TableDataInfo userList(@RequestBody Map<String, Object> map) {
		/**
		String orgCode = (String) map.get("orgCode");
		String userCode = (String) map.get("userCode");
		String userName = (String) map.get("userName");
		Integer pageNum = (Integer) map.get("pageNum");
		Integer pageSize = (Integer) map.get("pageSize");
		return SUserUtil.queryAllUserByOrgId(orgCode, userName, userCode, pageNum, pageSize);
		*/
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock,map);
		EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_U1");
		return getDataTable(outInfo);
	}

	/**
	 * 组织下选择人员
	 * @param selectType S:单选 M:多选 默认S
	 * @param orgCode 组织  默认BSVK 
	 * @param values 回显value值 ,隔开
	 * @param excludeOrgCode 排除的组织 ，隔开
	 * @param mmap
	 * @return
	 */
	@GetMapping("/selectUserList")
	public String selectUserList(String selectType,String orgCode,String values,String callback, String excludeOrgCode, ModelMap mmap) {
		mmap.put("selectType", selectType);
		if(StrUtil.isBlank(orgCode)) {
			orgCode="BSVK";
		}
		mmap.put("orgCode", orgCode);
		mmap.put("values", values);
		if(StrUtil.isNotBlank(values)){
			String userName = SUserUtil.getUserName(values);
			mmap.put("userName", userName);
		}
		if (StrUtil.isNotBlank(excludeOrgCode)){
			mmap.put("excludeOrgCode", excludeOrgCode);
		}
		mmap.put("callback", callback);
		return "/mpad/selectUser";
	}

	/**
	 * 选择人员
	 * @param model
	 * @return
	 */
	@GetMapping("/selectUserList2")
	public String selectUserList2(ModelMap model) {
		model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
		return "/mpad/selectUser2";
	}

	/**
	 * 选择角色人员
	 * @param model
	 * @return
	 */
	@GetMapping("/selectRoleUserList/{roleCode}")
	public String selectRoleUserList(@PathVariable("roleCode")String roleCode, ModelMap model) {
		model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
		model.put("roleCode", roleCode);
		return "/mpad/selectRoleUser";
	}

	/**
	 *

	 */
	@PostMapping("/roleUserList/{roleCode}")
	@ResponseBody
	public TableDataInfo roleUserList(@PathVariable("roleCode")String roleCode,@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock,map);
		eiInfo.set("roleCode", roleCode);
		EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_U3");
		return getDataTable(outInfo);
	}

	/**
	 * 同部门下人员
	 * @param model
	 * @return
	 */
	@GetMapping("/selectDeptUser")
	public String selectDeptUser(ModelMap model) {
		model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
		return "/mpad/selectDeptUser";
	}

	/**
	 *
	 */
	@PostMapping("/getDeptUser")
	@ResponseBody
	public TableDataInfo getDeptUser(@RequestBody Map<String, Object> map) {
		if (map == null) {
			map = new HashMap<>();
		}
		List<ADOrg> list = SOrgUtil.getOrgByUserCode(UserSession.getLoginName());
		if (list==null||list.size()==0) {
			return new TableDataInfo();
		}

		//获取组织映射 此处 就ht应用才有
		try {
			EiInfo eiInfoMap = new EiInfo();
			eiInfoMap.set(EiConstant.serviceName, "HTSQOrgMap");
			eiInfoMap.set(EiConstant.methodName, "getDestOrg");
			eiInfoMap.set("listOrg", list);
			eiInfoMap = XLocalManager.callNewTx(eiInfoMap);
			list = (List<ADOrg>) eiInfoMap.get(EiConstant.resultBlock);
		} catch (Exception e) {
			e.printStackTrace();
		}

		List<ADOrg> listMaxDept = new ArrayList<>();
		for (ADOrg org : list) {
			listMaxDept.add(SOrgUtil.getMaxDeptOrg(org.getOrgCode()));
		}
		List<String> orgPathCodeS = new ArrayList<>();
		for (ADOrg org : listMaxDept) {
			if (org!=null&&StringUtils.isNotBlank(org.getOrgPathCode())) {
				orgPathCodeS.add(org.getOrgPathCode());
			}
		}
		if (orgPathCodeS.size()==0) {
			return new TableDataInfo();
		}
		map.put("orgPathCodeS", orgPathCodeS);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("params", map);
		EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_U6");
		TableDataInfo s = getDataTable(outInfo);
		return s;
	}
	/**
	 *

	 */
	@PostMapping("/allUserList")
	@ResponseBody
	public TableDataInfo allUserList(@RequestBody Map<String, Object> map) {
		if ("company".equals(paramMap().get("queryType"))) {//查询本公司
			ADOrg company = SOrgUtil.getUserDirCompany(UserSession.getLoginName());
			if (company!=null) {
				map.put("orgPathCodeLike", company.getOrgPathCode());
			}
		}
		String excludeOrgCode = map.getOrDefault("excludeOrgCode", "").toString();
		if (StringUtils.isNotBlank(excludeOrgCode)) {
			map.put("excludeOrgCode", "'" + excludeOrgCode.replaceAll(",", "','") + "'");
		}
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("params", map);
		EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_U4");
		return getDataTable(outInfo);
	}

	/**
	 * 选择组织下人员
	 * @param model
	 * @return
	 */
	@GetMapping("/selectOrgUserList")
	public String selectOrgUserList(ModelMap model) {
		model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
		return "/mpad/selectOrgUser";
	}

	/**
	 * 获取登录同部门下用户
	 * 组织编码长度为8的
	 */
	@PostMapping("/orgUserList")
	@ResponseBody
	public TableDataInfo orgUserList(@RequestBody Map<String, Object> map) {
		String orgCode = paramMap().get("orgCode");
		ADOrg org = null;
		if (StringUtils.isNotEmpty(orgCode)) {
			org = SOrgUtil.getOrgByOrgCode(orgCode);
		}
		if (org == null) {
			org = SOrgUtil.getMainOrgByUserCode(UserSession.getLoginName());
		}

		if (org!=null) {
			String orgPathCodeLike = "";
			String[] orgCodes = org.getOrgPathCode().split("/");
			for (String _orgCode : orgCodes) {
				if (_orgCode.length()>0 ) {
					orgPathCodeLike += "/" + _orgCode;
					if (_orgCode.length()==8) {
						break;
					}
				}
			}
			map.put("orgPathCodeLike", orgPathCodeLike);
		}
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("params", map);
		EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "S_MP_AD_U4");
		return getDataTable(outInfo);
	}


}
