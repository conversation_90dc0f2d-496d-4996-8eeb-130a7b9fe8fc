package com.baosight.bsvkkj.mp.ad.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.iplat4j.core.data.dao.DaoFactory;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component("SRoleUtil")
public class SRoleUtil {

    private static final ThreadLocal<Map<String, Object>> threadLocal = new ThreadLocal();

    private static Map<String, Object> getData() {
        Map<String, Object> map = threadLocal.get();
        if (map == null) {
            map = new ConcurrentHashMap();
            threadLocal.set(map);
        }
        return map;
    }

    /**
     * 根据组织编码和角色编码获取相关用户
     *
     * @param orgCode  组织编码
     * @param roleCode 角色编码
     * @return
     */
    public static List<String> getUserLabelList(String orgCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_01");
        eiInfo.set("orgCode", orgCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码和角色编码获取相关用户编码:" + outInfo.getMsg());
        }
        return (List<String>) outInfo.get(EiConstant.resultBlock);
    }

    /**
     * 根据组织编码和角色编码获取相关用户
     *
     * @param orgCode  组织编码
     * @param roleCode 角色编码
     * @return
     */
    public static List<Map<String, Object>> getUserMapList(String orgCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_011");
        eiInfo.set("orgCode", orgCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码和角色编码获取相关用户:" + outInfo.getMsg());
        }
        return (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
    }

    /**
     * 根据组织编码和角色编码获取相关用户
     *
     * @param orgCode  组织编码
     * @param roleCode 角色编码
     * @return
     */
    public static List<Map<String, String>> getRoleList(String orgCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_02");
        eiInfo.set("orgCode", orgCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码和角色编码获取相关用户:" + outInfo.getMsg());
        }
        return (List<Map<String, String>>) outInfo.get(EiConstant.resultBlock);
    }

    /**
     * 根据组织编码和角色编码获取相关用户
     *
     * @param orgCode  组织编码
     * @param roleCode 角色编码
     * @return
     */
    public static List<Map<String, String>> getUserList(String orgCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_03");
        eiInfo.set("orgCode", orgCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码和角色编码获取相关用户:" + outInfo.getMsg());
        }
        return (List<Map<String, String>>) outInfo.get(EiConstant.resultBlock);
    }

    /**
     * 根据组织编码和角色编码获取上级(包含本级)配了该角色的组织
     *
     * @param orgCode  组织编码
     * @param roleCode 角色编码
     * @return
     */
    public static ADOrg getOrgByChildOrg(String orgCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_04");
        eiInfo.set("orgCode", orgCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            //throw new PlatException("根据组织编码和角色编码获取上级(包含本级)配了该角色的组织:" + outInfo.getMsg());
            return null;
        }
        ADOrg rtn = null;
        Object obj = outInfo.get(EiConstant.resultBlock);
        if (obj instanceof Map) {
            rtn = BeanUtil.toBean(obj, ADOrg.class);
        } else {
            rtn = (ADOrg) outInfo.get(EiConstant.resultBlock);
        }
        return rtn;
    }

    /**
     * 根据用户编码和角色编码获取用户配的相关组织
     *
     * @param userCode 用户编码
     * @param roleCode 角色编码 多个英文逗号分割
     * @return
     */
    public static List<ADOrg> getOrgByUser(String userCode, String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_05");
        eiInfo.set("userCode", userCode);
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        List<ADOrg> rtn = new ArrayList<>();
        ADOrg org = null;
        List<Object> list = (List<Object>) outInfo.get(EiConstant.resultBlock);
        for (Object object : list) {
            if (object instanceof ADOrg) {
                org = (ADOrg) object;
            } else {
                org = BeanUtil.toBean(object, ADOrg.class);
            }
            rtn.add(org);
        }
        return rtn;
    }

    /**
     * 根据角色编码获取对应的组织
     *
     * @param roleCode 角色编码
     * @return
     */
    public static List<ADOrg> getOrgByRoleCode(String roleCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_06");
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        List<ADOrg> rtn = new ArrayList<>();
        ADOrg org = null;
        List<Object> list = (List<Object>) outInfo.get(EiConstant.resultBlock);
        for (Object object : list) {
            if (object instanceof ADOrg) {
                org = (ADOrg) object;
            } else {
                org = BeanUtil.toBean(object, ADOrg.class);
            }
            rtn.add(org);
        }
        return rtn;
    }

    /**
     * 根据组织编码和角色编码获取相关用户
     *
     * @param orgCode  组织编码
     * @param roleCode 角色编码
     * @param up       是否递归
     * @return
     */
    public static List<String> getUserLabelList(String orgCode, String roleCode, boolean up) {
        List<String> userList = getUserLabelList(orgCode, roleCode);
        if (up) {
            while ((null == userList || userList.size() == 0) && orgCode != null) {
                if (null != SOrgUtil.getParentOrgByOrgCode(orgCode)) {
                    orgCode = SOrgUtil.getParentOrgByOrgCode(orgCode).getOrgCode();
                    userList = getUserLabelList(orgCode, roleCode, true);
                } else {
                    orgCode = null;
                }
            }
        }
        return userList;
    }

    /**
     * 根据角色组织获取用户 多个以逗号分割(支持递归向上级组织查找直至找到为止)
     *
     * @param roleCode
     * @param orgCode
     * @return
     */
    public static String getUserLabel(String roleCode, String orgCode) {
        List<String> listUser = getUserLabelList(orgCode, roleCode, true);
        return StringUtils.join(listUser, ",");
    }

    /**
     * 根据角色获取用户 多个以逗号分割
     *
     * @param roleCode
     * @return
     */
    public static String getUserLabel(String roleCode) {
        if (StringUtils.isEmpty(roleCode))
            return null;
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_09");
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        List<String> list = (List<String>) outInfo.get(EiConstant.resultBlock);
        return list.stream().collect(Collectors.joining(","));
    }

    /**
     * 判断某个用户是否属于某个组织角色
     *
     * @param orgCode
     * @param roleCode
     * @param userCode
     * @return
     */
    public static boolean isRoleMember(String orgCode, String roleCode, String userCode) {
        if (StringUtils.isEmpty(roleCode) || StringUtils.isEmpty(userCode))
            return false;
        List<String> userLabelList = getUserLabelList(orgCode, roleCode);
        if (userLabelList != null) {
            for (String userLabel : userLabelList) {
                if (userLabel.equals(userCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断某个用户是否属于某个角色
     *
     * @param roleCode
     * @param userCode
     * @return
     */
    public static boolean isRoleMember(String roleCode, String userCode) {
        if (StringUtils.isEmpty(roleCode) || StringUtils.isEmpty(userCode))
            return false;
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_09");
        eiInfo.set("roleCode", roleCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return false;
        }
        List<String> list = (List<String>) outInfo.get(EiConstant.resultBlock);
        if (null == list) {
            return false;
        }
        return list.contains(userCode);
    }

    /**
     * 判断用户是否是组织下角色成员
     *
     * @param userCode
     * @param orgCode
     * @param roleCode
     * @return
     */
    public static boolean isRoleOrgMember(String userCode, String orgCode, String roleCode) {
        if (StringUtils.isEmpty(userCode) || StringUtils.isEmpty(roleCode) || StringUtils.isEmpty(orgCode))
            return false;
        List<String> list = getUserLabelList(orgCode, roleCode);
        if (null == list) {
            return false;
        }
        return list.contains(userCode);
    }

    /**
     * 判断是否为系统管理员 S_XS_32 XSUserManage isAdmin
     *
     * @param userCode
     */
    public static boolean isAdmin(String userCode) {
        return SUserUtil.isAdmin(userCode);
    }

    /**
     * 判断是否存在该角色
     *
     * @param roleCode
     * @return
     */
    public static Boolean isExist(String roleCode) {
        Map<String, Object> threadData = getData();
        Boolean bool = (Boolean) threadData.get("_role_isExist_" + roleCode);
        if (null == bool) {
            bool = false;
            Map paramMap = new HashMap<>();
            paramMap.put("groupEname", roleCode);
            List existGroup = DaoFactory.getPlatSqlDao().query("XSUserManage.queryUserGroup", paramMap);
            if (null != existGroup && existGroup.size() > 0) {
                bool = true;
            }
            threadData.put("_role_isExist_" + roleCode, bool);
        }
        return bool;
    }

    /**
     * 创建角色
     *
     * @param operator
     * @param roleCode
     * @param roleName
     */
    public static void createRole(String operator, String roleCode, String roleName) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_XS_18");
        List list = new ArrayList();
        Map map = new HashMap();
        map.put("groupEname", roleCode);
        map.put("groupCname", roleName);
        map.put("groupType", "ROLE");//类型为角色
        map.put("recCreator", operator);
        list.add(map);
        eiInfo.set("list", list);
        XServiceManager.call(eiInfo);
    }

    /**
     * 获取角色名称
     *
     * @param roleCode
     */
    public static String getRoleName(String roleCode) {
        if (StringUtils.isEmpty(roleCode)) {
            return null;
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("groupEname", roleCode);
        eiInfo.set(EiConstant.serviceId, "S_XS_01");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            return null;
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.get(EiConstant.resultBlock);
        if (ObjectUtil.isNotEmpty(list)) {
            return (String) list.get(0).get("groupCname");
        } else {
            return null;
        }
    }

    /**
     * 添加角色成员
     * 新增用户组成员
     * 和组织无关系一般为了使用平台的授权
     *
     * @param operator 操作人
     * @param roleCode 角色代码
     * @param userCode 用户code 多个用,隔开
     * @return
     */
    public static Boolean addRoleMember(String operator, String roleCode, String userCode) {
        if (StringUtils.isEmpty(roleCode)) {
            return false;
        }
        if (StringUtils.isEmpty(userCode)) {
            return false;
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_XS_21");
        List list = new ArrayList();
        String[] userCodeS = userCode.split(",");
        for (String string : userCodeS) {
            if (StringUtils.isNotBlank(string)) {
                EiInfo eiInfo10 = new EiInfo();
                eiInfo10.set(EiConstant.serviceId, "S_XS_10");//判断是否已经是角色成员
                eiInfo10.set("groupEname", roleCode);
                eiInfo10.set("loginName", string);
                EiInfo outInfo10 = XServiceManager.call(eiInfo10);
                List list10 = (List) outInfo10.get("result");
                if (null == list10 || list10.isEmpty()) {
                    Map map = new HashMap();
                    map.put("memberName", string);
                    map.put("memberType", "USER");
                    map.put("parentName", roleCode);
                    map.put("recCreator", operator);
                    list.add(map);
                }
            }
        }
        eiInfo.set("list", list);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return EiConstant.STATUS_SUCCESS == outInfo.getStatus();
    }

    /**
     * 删除角色成员
     * 删除用户组成员
     * 和组织无关系一般为了使用平台的授权
     *
     * @param operator 操作人
     * @param roleCode 角色代码
     * @param userCode 用户code 多个用,隔开
     * @return
     */
    public static Boolean removeRoleMember(String operator, String roleCode, String userCode) {
        if (StringUtils.isEmpty(roleCode)) {
            return false;
        }
        if (StringUtils.isEmpty(userCode)) {
            return false;
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_XS_22");
        List list = new ArrayList();
        String[] userCodeS = userCode.split(",");
        for (String string : userCodeS) {
            if (StringUtils.isNotBlank(string)) {
                EiInfo eiInfo10 = new EiInfo();
                eiInfo10.set(EiConstant.serviceId, "S_XS_10");//判断是否已经是角色成员
                eiInfo10.set("groupEname", roleCode);
                eiInfo10.set("loginName", string);
                EiInfo outInfo10 = XServiceManager.call(eiInfo10);
                List list10 = (List) outInfo10.get("result");
                if (ObjectUtil.isNotEmpty(list10)) {
                    Map map = new HashMap();
                    map.put("memberName", string);
                    map.put("memberType", "USER");
                    map.put("parentName", roleCode);
                    map.put("recRevisor", operator);
                    list.add(map);
                }
            }
        }
        eiInfo.set("list", list);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return EiConstant.STATUS_SUCCESS == outInfo.getStatus();
    }

    /**
     * 获取用户所有角色
     *
     * @param userCode  用户编码
     * @return
     */
    public static List<String> getUserAllRole(String userCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceId, "S_MP_AD_R1");
        eiInfo.set("userCode", userCode);
        EiInfo outInfo = XServiceManager.call(eiInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            throw new PlatException("根据组织编码和角色编码获取相关用户:" + outInfo.getMsg());
        }
        return (List<String>) outInfo.get(EiConstant.resultBlock);
    }
}