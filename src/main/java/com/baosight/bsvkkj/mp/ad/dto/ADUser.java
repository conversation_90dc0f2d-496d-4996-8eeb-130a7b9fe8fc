package com.baosight.bsvkkj.mp.ad.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ADUser implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     * 编码(工号)
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 联系电话
     */
    private String userTel;

    /**
     * 邮件
     */
    private String email;

    /**
     * 籍贯
     */
    private String userNativePlace;

    /**
     * 民族
     */
    private String userNation;

    /**
     * 职务名称
     */
    private String userJob;

    /**
     * 职称
     */
    private String userZhiCheng;

    /**
     * 学历
     */
    private String userEducational;

    /**
     * 学位
     */
    private String userDegree;

    /**
     * 身份证号
     */
    private String userCardId;

    /**
     * 文化程度
     */
    private String userCulture;

    /**
     * 毕业时间
     */
    private String userGraduationDate;

    /**
     * 通讯地址
     */
    private String userAddress;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 岗位类型
     */
    private String postType;

    /**
     * 岗位层级
     */
    private String postLevel;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 人员状态 90退休 91 离职
     */
    private String userStatus;

    /**
     * 薪酬模式名称
     */
    private String salaryName;

    /**
     * 备注
     */
    private String remark;
}
