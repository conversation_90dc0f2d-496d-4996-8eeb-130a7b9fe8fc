package com.baosight.bsvkkj.utils;

import cn.hutool.core.collection.CollUtil;
import com.baosight.iplat4j.core.exception.PlatException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class ValidatorUtils {
    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 单个bean
     */
    public static void validate(Object obj) {
        if (obj != null) {
            StringBuilder errorMessage = new StringBuilder();

            Set<ConstraintViolation<Object>> resultSet = VALIDATOR.validate(obj);
            if (resultSet.size() > 0) {
                //如果存在错误结果，则将其解析并进行拼凑后异常抛出
                List<String> errorMessageList = resultSet.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList());
                errorMessageList.forEach(o -> errorMessage.append(o).append(";"));
            }
            if (errorMessage.length() > 0) {
                throw new PlatException("数据不规范：" + errorMessage);
            }
        }

    }

    /**
     * list
     */
    public static <T> void validateList(List<T> list) {
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(data -> {
                validate(data);
            });
        }

    }

}
