package com.baosight.bsvkkj.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.format.FastDateFormat;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.URLEncoder;

/**
 * 文件处理工具类
 *
 * <AUTHOR>
 */
public class FileUtils {
    public static String FILENAME_PATTERN = "[a-zA-Z0-9_\\-\\|\\.\\u4e00-\\u9fa5]+";

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os       输出流
     * @return
     */
    public static void writeBytes(String filePath, OutputStream os) throws IOException {
        FileInputStream fis = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new FileNotFoundException(filePath);
            }
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int length;
            while ((length = fis.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } catch (IOException e) {
            throw e;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean flag = false;
        File file = new File(filePath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            file.delete();
            flag = true;
        }
        return flag;
    }

    /**
     * 文件名称验证
     *
     * @param filename 文件名称
     * @return true 正常 false 非法
     */
    public static boolean isValidFilename(String filename) {
        return filename.matches(FILENAME_PATTERN);
    }

    /**
     * 下载文件名重新编码
     *
     * @param request  请求对象
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String setFileDownloadHeader(HttpServletRequest request, String fileName)
            throws UnsupportedEncodingException {
        final String agent = request.getHeader("USER-AGENT");
        String filename = fileName;
        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, "utf-8");
            filename = filename.replace("+", " ");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else if (agent.contains("Chrome")) {
            // google浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        } else if (agent.contains("Macintosh")) {
            // Safari浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        }
        return filename;
    }

    /**
     * 根据给定路径返回临时【月】文件夹
     * 在给定目录下创建格式如temp202302
     * 如果不存在就创建文件夹，并同时删除其他文件夹以temp开始的文件夹
     *
     * @param baseDir
     * @return
     */
    public static String getTempMonthDir(String baseDir) {
        String monthString = FastDateFormat.getInstance(DatePattern.SIMPLE_MONTH_PATTERN).format(new DateTime());
        File file1 = new File(baseDir + File.separator + monthString + File.separator);
        if (!file1.exists()) {// 如果文件夹不存在
            //1.先清空baseDir下temp开始的文件夹
            File file2 = new File(baseDir);
            if (!file2.exists()) {// 如果文件夹不存在
                file2.mkdirs();// 创建文件夹
            } else {
                File[] file2List = file2.listFiles();
                for (File temFile : file2List) {
                    if (temFile.getName().indexOf("temp") == 0) {
                        temFile.delete();
                    }
                }
            }
            //2.创建文件夹
            file1.mkdirs();
        }
        return baseDir + File.separator + monthString;
    }
}
