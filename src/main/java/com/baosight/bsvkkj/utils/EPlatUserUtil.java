package com.baosight.bsvkkj.utils;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.json.EiInfo2Json2;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 参照 http://***********:9090/pages/viewpage.action?pageId=55247393 iPlat4J V6
 * 产品文档/04 用户手册/03 安全管理用户手册/02 安全管理-对外接口
 * 
 * <AUTHOR>
 *
 */
@Component("EPlatUserUtil")
public class EPlatUserUtil {

	/**
	 * 获取用户信息
	 * 
	 * @param empCode
	 * @return
	 */
	public static String getUser(String empCode) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set(EiConstant.serviceId, "S_XS_03");
		eiInfo.set("loginName", empCode);
		EiInfo outInfo = XServiceManager.call(eiInfo);
		return EiInfo2Json2.toJsonString(outInfo);
	}

	/**
	 * 新增用户
	 * 
	 * @param empCode  用户工号
	 * @param password
	 * @param userName
	 * @param mobile
	 * @param email
	 * @param operator 操作人工号
	 * @return
	 */
	public static String addUser(String empCode, String password, String userName, String mobile, String email,
			String operator) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set(EiConstant.serviceId, "S_XS_15");
		// 设置参数控制属性，忽略重复数据
		eiInfo.set("ignoreDupicate", "true");
		// 设置用户数据对象list
		List list = new ArrayList();
		Map map = new HashMap();
		map.put("loginName", empCode);
		map.put("password", password);
		map.put("rePass", password);
		map.put("userType", "USER");
		map.put("userName", userName);
		map.put("mobile", mobile);
		map.put("email", email);
		map.put("recCreator", operator);
		list.add(map);
		eiInfo.set("list", list);
		EiInfo outInfo = XServiceManager.call(eiInfo);
		return EiInfo2Json2.toJsonString(outInfo);
	}

	/**
	 * 修改用户
	 * 
	 * @param empCode  用户工号
	 * @param userName
	 * @param mobile
	 * @param email
	 * @param operator 操作人工号
	 * @return
	 */
	public static String updateUserInfo(String empCode, String userName, String mobile, String email, String operator) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set(EiConstant.serviceId, "S_XS_17");
		List list = new ArrayList();
		Map map = new HashMap();
		map.put("loginName", empCode);
		map.put("userType", "USER");
		map.put("userName", userName);
		map.put("mobile", mobile);
		map.put("email", email);
		map.put("recRevisor", operator);
		list.add(map);
		eiInfo.set("list", list);
		EiInfo outInfo = XServiceManager.call(eiInfo);
		return EiInfo2Json2.toJsonString(outInfo);
	}

	/**
	 * 删除用户
	 * 
	 * @param empCode  用户工号
	 * @param operator 操作人工号
	 * @return
	 */
	public static String deleteUser(String empCode, String operator) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set(EiConstant.serviceId, "S_XS_16");
		List list = new ArrayList();
		Map map = new HashMap();
		map.put("loginName", empCode);
		map.put("recRevisor", operator);
		list.add(map);
		eiInfo.set("list", list);
		EiInfo outInfo = XServiceManager.call(eiInfo);
		return EiInfo2Json2.toJsonString(outInfo);
	}

	/**
	 * 锁定用户
	 * 
	 * @param empCode
	 * @return
	 */
	public static String lockUser(String empCode) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("loginName", empCode);
		eiInfo.set(EiConstant.serviceId, "S_XS_102");
		EiInfo outInfo = XServiceManager.call(eiInfo);
		return EiInfo2Json2.toJsonString(outInfo);
	}

	/**
	 * 获取登出路径
	 * 
	 * @return
	 */
	public static String getLogoutUrl() {
		ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = ra.getRequest();
		String logoutUrl = request.getContextPath()+"/login.jsp";
		try {
			if (null != Class.forName("com.baosight.eplat.be.aa.client.config.OauthClientConfig")) {
				logoutUrl = PlatApplicationContext.getProperty("eplat.security.client.logoutUrl");
				/** sdk 2.0.0版本且使用业务交付配置时放开以下注释 */
				if (!StringUtils.isNotEmpty(logoutUrl)) {
					String clientAddrProperty = PlatApplicationContext.getProperty("eplat.security.client.app.url");
					String serverAddrProperty = PlatApplicationContext.getProperty("eplat.security.client.auth.url");
					logoutUrl = serverAddrProperty + "/logout?callback=" + clientAddrProperty + "/logout";
				}			
				logoutUrl = logoutUrl + "&token=" + request.getSession().getAttribute("oauth2_token");
			}
		} catch (ClassNotFoundException ex) {
			// logger.info("未对接认证，使用默认登出链接");
		}
		return logoutUrl;
	}
}
