package com.baosight.bsvkkj.utils;

import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;

public class MobileUtil {

    /**
     * 根据业务ID和链接生产pdf附件
     *
     * @param businessId
     * @param htmlUrl
     * @return
     */
    public static String genPdf(String businessId, String htmlUrl) {
        String ctxPdf = PlatApplicationContext.getProperty("app-context.ctxPdf");
        JSONObject postJson = new JSONObject();
        postJson.put("businessId", businessId);
        postJson.put("htmlUrl", htmlUrl);
        if (ctxPdf.indexOf("run")>-1) {
            postJson.put("app", "run");//生产环境
        }
        JSONObject jsonObject = HttpClientUtil.postJson(ctxPdf, postJson, 25);
        if (null == jsonObject) {
            return null;
        }
        int status = 0;
        try {
            status = (int) jsonObject.get("status");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        if (1 == status) {
            return (String) jsonObject.get("fileid");
        } else {
            System.out.println("=======生成pdf失败===="+jsonObject.get("msg"));
        }
        return null;
    }
}
