package com.baosight.bsvkkj.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class JwtUtil {

    /**
     * 生成指定长度随机大小写字母
     * @param length
     * @return
     */
    public static String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // 可以根据需要修改字符集
            int randomChar = random.nextInt(26) + 'a';
            char letter = (char) randomChar;
            // 随机大小写
            if (random.nextBoolean()) {
                letter = Character.toUpperCase(letter);
            }
            sb.append(letter);
        }
        return sb.toString();
    }

    /**
     * 生成jwt
     * 使用Hs256算法, 私匙使用固定秘钥
     *
     * @param secretKey  jwt秘钥
     * @param expireTime jwt过期时间
     * @param claims     设置的信息
     * @return
     */
    public static String createToken(String secretKey, Date expireTime, Map<String, Object> claims) {
        // 算法
        Key key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));
        // 设置jwt的body
        JwtBuilder builder = Jwts.builder()
                // 如果有私有声明，一定要先设置这个自己创建的私有的声明，这个是给builder的claim赋值，一旦写在标准的声明赋值之后，就是覆盖了那些标准的声明的
                .setClaims(claims)
                // 设置签名使用的签名算法和签名使用的秘钥
                .signWith(key)
                // 设置过期时间
                .setExpiration(expireTime);

        return builder.compact();
    }

    /**
     * 生成jwt
     * 使用Hs256算法, 私匙使用固定秘钥
     *
     * @param secretKey jwt秘钥
     * @param ttlMillis jwt过期时间(毫秒)
     * @param claims    设置的信息
     * @return
     */
    public static String createToken(String secretKey, long ttlMillis, Map<String, Object> claims) {
        return createToken(secretKey, new Date(System.currentTimeMillis() + ttlMillis), claims);
    }

    /**
     * 校验token
     * @param token
     * @param secretKey
     * @return
     */
    public static Claims parseToken(String token, String secretKey) throws Exception{
        return Jwts.parser()
                    .setSigningKey(secretKey.getBytes(StandardCharsets.UTF_8))
                    .parseClaimsJws(token)
                    .getBody();
    }

    public static void main(String[] args) throws Exception{
        String secretKey = generateRandomString(32); // 应该是一个复杂的随机字符串
        System.out.println(secretKey);
        String subject = "SubjectIdentifier";
        long ttlMillis = 7200000; // 2 hour in milliseconds

        Map<String, Object> claims = new HashMap<>();
        claims.put("subject", subject);

        String token = createToken(secretKey, ttlMillis, claims);
        System.out.println("Generated Token: " + token);

        claims = parseToken(token, secretKey);
        System.out.println(claims);
    }
}
