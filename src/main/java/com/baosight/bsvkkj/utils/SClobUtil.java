package com.baosight.bsvkkj.utils;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class SClobUtil {
	static Logger logger = LoggerFactory.getLogger(SWorkFlowUtil.class);
	
	/**
	 * 新增
	 * @param contentMap 内容
	 * @param businessGuid 业务GUID
	 * @param sourceTable 来源表
	 * @param sourceModule 来源模块
	 * @param operator 操作人
	 */
	public static void add(Map<String,Object> contentMap,String businessGuid,String sourceTable,String sourceModule,String operator){
		if(!StringUtils.isEmpty(businessGuid) && !StringUtils.isEmpty(sourceTable) && !StringUtils.isEmpty(sourceModule)) {
			EiInfo inInfo = new EiInfo();
			inInfo.set(EiConstant.serviceId, "S_MP_TY_16");
			inInfo.set("contentMap", contentMap);
			inInfo.set("businessGuid", businessGuid);
			inInfo.set("sourceTable", sourceTable);
			inInfo.set("sourceModule", sourceModule);
			inInfo.set("operator", operator);
			EiInfo outInfo = XServiceManager.call(inInfo);
			if(EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				logger.error(outInfo.toJSONString());
				throw new PlatException(outInfo.getMsg());
			}
		}
	}
	
	/**
	 * 修改
	 * @param businessGuid 业务GUID
	 * @param contentMap 内容
	 * @param operator 操作人
	 */
	public static void update(String businessGuid,Map<String,Object> contentMap,String operator){
		if(!StringUtils.isEmpty(businessGuid)) {
			EiInfo inInfo = new EiInfo();
			inInfo.set(EiConstant.serviceId, "S_MP_TY_17");
			inInfo.set("contentMap", contentMap);
			inInfo.set("businessGuid", businessGuid);
			inInfo.set("operator", operator);
			EiInfo outInfo = XServiceManager.call(inInfo);
			if(EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
				logger.error(outInfo.toJSONString());
				throw new PlatException(outInfo.getMsg());
			}
		}
	}
	/**
	 * 新增
	 * @param contentMap 内容
	 * @param businessGuid 业务GUID
	 * @param sourceTable 来源表
	 * @param sourceModule 来源模块
	 * @param operator 操作人
	 */
	public static void save(Map<String,Object> contentMap,String businessGuid,String sourceTable,String sourceModule,String operator){
		Map<String, Object> contentMap2 = getContentMap(businessGuid);
		if(contentMap2==null)
			add(contentMap, businessGuid, sourceTable, sourceModule, operator);
		else
			update(businessGuid, contentMap, operator);
	}
	/**
	 * 获取内容
	 * @param businessGuid 业务GUID
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String,Object> getContentMap(String businessGuid){
		if(businessGuid==null || businessGuid.trim().length()<=0) return null;
		EiInfo inInfo = new EiInfo();
		inInfo.set(EiConstant.serviceId, "S_MP_TY_18");
		inInfo.set ( "businessGuid", businessGuid );
		EiInfo outInfo = XServiceManager.call ( inInfo );
		if (EiConstant.STATUS_SUCCESS != outInfo.getStatus ()) {
			logger.error ( outInfo.toJSONString () );
			throw new PlatException ( outInfo.getMsg () );
		}
		Map<String, Object> contentMap = (Map<String, Object>) outInfo.get ( "contentMap" );
		//返回null代表是空 走新增方法
		return contentMap;
	}

	/***
	 * 添加或者更新
	 * @param contentMap
	 * @param businessGuid
	 * @param sourceTable
	 * @param sourceModule
	 */
	public static void addOrUpdateadd(Map<String, Object> contentMap, String businessGuid, String sourceTable, String sourceModule, String operator) {
		Map<String, Object> contentMap1 = getContentMap ( businessGuid );
		if (ObjectUtil.isNotEmpty ( contentMap1 )) {
			update ( businessGuid, contentMap, operator );
		} else {
			add ( contentMap, businessGuid, sourceTable, sourceModule, operator );
		}
	}

	/**
	 * demo
	 */
	private void demo() {
		Map<String, Object> contentMap = new HashMap<String, Object> ();
		contentMap.put ( "a", "可视对讲覅偶是大家" );
		contentMap.put ( "b", "ksdjfiosdjfisdjfi" );
		contentMap.put ( "c", "空手道解放斯蒂芬建瓯市低价佛i金额为i" );
		add ( contentMap, BizIdUtil.INSTANCE.nextId (), "t_kyxt_demo", "KYXT", "admin" );
		System.out.println(1);
	}
}
