package com.baosight.bsvkkj.utils.excel;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.export.ExcelExportService;
import cn.afterturn.easypoi.util.PoiPublicUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.setting.Setting;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.utils.FileUtils;
import com.baosight.bsvkkj.utils.ServletUtils;
import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * excelUtil 工具类
 *
 * @ClassName: ExcelUtil
 * @Description:
 * @author: z
 * @Company: xy
 * @date: 2020年11月3日 下午3:52:40
 * @param:
 */
public class ExcelUtil<T> {

    public static final String excelUrl = ExcelUtil.class.getResource("/").getPath() + "excel/";
    public Class<T> clazz;
    private ExportParams exportParams = new ExportParams(null, "sheet1", ExcelType.XSSF);

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }


    public void setTitle(String title) {
        exportParams.setTitle(title);
    }

    public void setSheet(String sheetName) {
        exportParams.setSheetName(sheetName);
    }

    public ExportParams getExportParams() {
        return exportParams;
    }

    public void setExportParams(ExportParams exportParams) {
        this.exportParams = exportParams;
    }

    /***
     * 根据实体类数据 获取对应的wordkbook(需要对应的实体类有excel注解)
     *
     * @param list
     * @return
     */
    public Workbook initExcel(List<T> list) {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clazz, list);
        return workbook;
    }

    /***
     * 根据map数据 获取对应的wordkbook
     *
     * @param map  k:list中map的k,v:表头名称
     * @param list
     * @return
     */
    public Workbook initExcel(Map<String, String> map, List<Map<String, Object>> list) {
        List<ExcelExportEntity> entity = new ArrayList<ExcelExportEntity>();
        int[] i = {0};
        map.forEach((k, v) -> {
            i[0]++;
            ExcelExportEntity excelentity = new ExcelExportEntity(v, k);
            excelentity.setOrderNum(i[0]);
            entity.add(excelentity);
        });
        return ExcelExportUtil.exportExcel(exportParams, entity, list);
    }

    /***
     * 根据map数据 获取对应的wordkbook
     *
     * @param map  k:list中map的k,v:表头名称
     * @param list
     * @return
     */
    public Workbook initExcel(Map<String, String> map, List<Map<String, Object>> list, Map<String, ExcelExportEntity> attrMap) {
        List<ExcelExportEntity> entity = new ArrayList<ExcelExportEntity>();
        int[] i = {0};
        map.forEach((k, v) -> {
            i[0]++;
            ExcelExportEntity excelentity = new ExcelExportEntity(v, k);
            excelentity.setOrderNum(i[0]);
            if (attrMap != null) {
                ExcelExportEntity excelExportEntity = attrMap.get(k);
                if (excelExportEntity != null) {
                    BeanUtil.copyProperties(excelExportEntity, excelentity, "name", "key", "orderNum");
                }

            }
            entity.add(excelentity);
        });
        return ExcelExportUtil.exportExcel(exportParams, entity, list);
    }

    /***
     * 根据map数据 获取对应的wordkbook
     *
     * @param map  k:list中map的k,v:表头名称
     * @param list
     * @param ks 纵向合并 k
     * @return
     */
    public Workbook initExcel(Map<String, String> map, List<Map<String, Object>> list, String... ks) {
        List<ExcelExportEntity> entity = new ArrayList<ExcelExportEntity>();
        int[] i = {0};
        map.forEach((k, v) -> {

            i[0]++;
            ExcelExportEntity excelentity = new ExcelExportEntity(v, k);
            excelentity.setOrderNum(i[0]);
            if (Arrays.asList(ks).contains(k)) {
                excelentity.setMergeVertical(true);
                excelentity.setNeedMerge(true);
            }
            entity.add(excelentity);
        });
        return ExcelExportUtil.exportExcel(exportParams, entity, list);
    }

    /***
     * 模板
     *
     * @param path 模板路径
     * @param map  数据
     * @return
     */
    public Workbook initExcel(String path, Map<String, Object> map) {
        TemplateExportParams params = new TemplateExportParams(path);
        return ExcelExportUtil.exportExcel(params, map);
    }

    /**
     * 导出excel
     *
     * @param
     * @param list<t> 实体类（要导出的字段要有excel 注解）
     * @param list    数据
     * @param path    导出路径
     */
    public void exportExcel(List<T> list, String path) {
        save(initExcel(list), path);
    }

    /***
     * 导出excel
     *
     * @param map  k:list中map的k,v:表头名称
     * @param list
     * @param path 导出路径
     */
    public void exportExcel(Map<String, String> map, List<Map<String, Object>> list, String path) {
        save(initExcel(map, list), path);
    }

    /***
     * 导出excel
     *
     * @param map  k:list中map的k,v:表头名称
     * @param list
     * @param path 导出路径
     * @param attrMap 属性
     */
    public void exportExcel(Map<String, String> map, List<Map<String, Object>> list, String path, Map<String, ExcelExportEntity> attrMap) {
        save(initExcel(map, list, attrMap), path);
    }

    /**
     * 导出excel
     *
     * @param mbpath 模板路径
     * @param map    数据
     * @param path   导出路径
     */
    public void exportExcel(String mbpath, Map<String, Object> map, String path) {
        save(initExcel(mbpath, map), path);
    }

    /**
     * 保存excle
     *
     * @param workbook
     * @param path
     */
    public void save(Workbook workbook, String path) {
        if (workbook != null) {
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(path);
                workbook.write(fos);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /***
     * 设置样式
     *
     * @param workbook
     * @param at        工作薄
     * @param row       行
     * @param cell      列
     * @param cellStyle 样式
     */
    public void setStyle(Workbook workbook, int at, int row, int cell, CellStyle cellStyle) {
        workbook.getSheetAt(at).getRow(row).getCell(cell).setCellStyle(cellStyle);
    }

    /**
     * 设置样式
     *
     * @param cla 此类需要继承AbstractExcelExportStyler
     */
    public void setStyle(Class<?> cla) {
        exportParams.setStyle(cla);
    }

    /**
     * 编码文件名
     */
    public String encodingFilename(String filename) {
        filename = UUID.randomUUID() + "_" + filename + ".xlsx";
        return filename;
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public String getAbsoluteFile(String filename) {
        try {
            //放在class 文件下
            Setting setting = new Setting("ftp/app-file-share.properties");
            String exportPath = setting.get("app-file-share.exportExcelPath");
            if (exportPath == null || exportPath.trim().isEmpty()) {
                // 如果配置文件中没有配置路径，使用默认路径
                exportPath = System.getProperty("java.io.tmpdir") + File.separator + "excel" + File.separator;
            }
            String downloadPath = exportPath + filename;
            File desc = new File(downloadPath);
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
            return downloadPath;
        } catch (Exception e) {
            // 如果配置文件读取失败，使用默认路径
            String defaultPath = System.getProperty("java.io.tmpdir") + File.separator + "excel" + File.separator;
            String downloadPath = defaultPath + filename;
            File desc = new File(downloadPath);
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
            return downloadPath;
        }
    }

    /**
     * 保存 excel 默认位置
     * <p>
     * 实体类（要导出的字段要有excel 注解）
     *
     * @param list 数据
     * @return
     */
    public AjaxResult exportExcel(List<T> list) {
        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        save(initExcel(list), getAbsoluteFile(filename));
        return AjaxResult.success(filename);
    }

    /***
     * 保存 excel 默认位置
     *
     * @param path 模板路径
     * @param map
     * @return
     */
    public AjaxResult exportExcel(String path, Map<String, Object> map) {
        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        save(initExcel(path, map), getAbsoluteFile(filename));
        return AjaxResult.success(filename);
    }

    /***
     * 保存默认位置
     *
     * @param map
     * @param list
     * @return
     */
    public AjaxResult exportExcel(Map<String, String> map, List<Map<String, Object>> list) {
        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        save(initExcel(map, list), getAbsoluteFile(filename));
        return AjaxResult.success(filename);
    }

    /***
     * 保存默认位置
     *
     * @param map
     * @param list
     * @return
     */
    public AjaxResult exportExcel(Map<String, String> map, List<Map<String, Object>> list, Map<String, ExcelExportEntity> attrMap) {
        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        save(initExcel(map, list, attrMap), getAbsoluteFile(filename));
        return AjaxResult.success(filename);
    }

    /***
     * 保存默认位置
     *
     * @param map
     * @param
     * @return
     */
    public AjaxResult exportExcel(Map<String, String> map, List<Map<String, Object>> list, List<ExcelExportEntity> entity) {
        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        save(initExcel(map, list, entity), getAbsoluteFile(filename));
        return AjaxResult.success(filename);
    }

    /***
     * 根据map数据 获取对应的wordkbook
     *
     * @param map  k:list中map的k,v:表头名称
     * @param list
     * @return
     */
    public Workbook initExcel(Map<String, String> map, List<Map<String, Object>> list, List<ExcelExportEntity> entity) {
        List<ExcelExportEntity> newEntity = new ArrayList<ExcelExportEntity>();
        int[] i = {0};
        map.forEach((k, v) -> {
            i[0]++;
            ExcelExportEntity excelentity = new ExcelExportEntity(v, k);
            excelentity.setOrderNum(i[0]);
            Optional<ExcelExportEntity> any = entity.stream().filter(data -> v.equals(data.getName())).findAny();
            if (any.isPresent()) {
                BeanUtil.copyProperties(any.get(), excelentity, "name", "key", "orderNum");
            }
            newEntity.add(excelentity);
        });
        return ExcelExportUtil.exportExcel(exportParams, newEntity, list);

    }

    /***
     * 导出excel 包含图片 超链接
     *
     * @param data {name:列名,code:数据的的key,type:类型 1 文本,2 图片,3超链接}
     * @param list
     */
    public AjaxResult exportExcel(List<Map<String, String>> data, List<Map<String, Object>> list) {

        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        try {
            // 是否有超链接
            boolean isHyperlink = false;
            List<ExcelExportEntity> entity = new ArrayList<ExcelExportEntity>();
            ExcelExportEntity excelentity = null;
            for (Map<String, String> map : data) {
                String type = map.get("type");
                String name = map.get("name");
                String code = map.get("code");

                if (StrUtil.isBlank(code) || StrUtil.isBlank(name) || StrUtil.isBlank(type)) {
                    throw new Exception("参数错误");
                }
                excelentity = new ExcelExportEntity(name, code);
                if ("2".equals(type)) {
                    excelentity.setExportImageType(1);
                    excelentity.setType(2);
                    excelentity.setWidth(40);
                    excelentity.setHeight(80);
                } else if ("3".equals(type)) {
                    excelentity.setHyperlink(true);
                    if (!isHyperlink) {
                        isHyperlink = true;
                    }
                }
                entity.add(excelentity);
            }
            exportParams.setType(ExcelType.XSSF);
            exportParams.setStyle(ExcelExportStylerImpl.class);
            if (isHyperlink) {
                HyperlinkInfo<T> hyperlinkInfo = new HyperlinkInfo<>();
                exportParams.setDataHandler(hyperlinkInfo);
            }
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, entity, list);
            FileOutputStream fos = new FileOutputStream(filename);
            workbook.write(fos);
            fos.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(filename);
    }

    /**
     * 导出excel
     *
     * @param list
     * @param isHyperlink 是否有超链接
     * @return
     */
    public AjaxResult exportExcel(List<T> list, boolean isHyperlink) {
        String sheetName = exportParams.getSheetName();
        String filename = encodingFilename(sheetName);
        exportParams.setType(ExcelType.XSSF);
//		exportParams.setStyle(ExcelExportStylerImpl.class);
        if (isHyperlink) {
            HyperlinkInfo<T> hyperlinkInfo = new HyperlinkInfo<>();
            exportParams.setDataHandler(hyperlinkInfo);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clazz, list);
        FileOutputStream fos;
        try {
            fos = new FileOutputStream(filename);
            workbook.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return AjaxResult.success(filename);
    }

    public void downloadExcel(Workbook workbook, String name) {
        HttpServletResponse response = ServletUtils.getResponse();
        HttpServletRequest request = ServletUtils.getRequest();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            byte[] bytes = os.toByteArray();

            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, name + ".xlsx"));
            ServletOutputStream outputStream = response.getOutputStream();

            outputStream.write(bytes);
            outputStream.flush();
            outputStream.close();

        } catch (Exception e) {
            throw new PlatException("导出异常请联系管理员。错误信息" + e.getMessage());
        }
    }


    /**
     * 动态导出列 Map
     *
     * @param fileData 前端的查询指定列数据 $("#bootstrap-table").bootstrapTable('getVisibleColumns');
     * @param list     导出数据
     * @param attrMap  列的各个属性
     * @return
     */
    public AjaxResult filedExportExcelMap(List<Map<String, Object>> fileData, List<Map<String, Object>> list, Map<String, ExcelExportEntity> attrMap) {
        try {
            Map<String, String> map = new LinkedHashMap<>();
            fileData.forEach(data -> {
                Object field = data.get("field");
                Object title = data.get("title");
                if (ObjectUtil.isNotEmpty(field) && ObjectUtil.isNotEmpty(title)) {
                    if (!"操作".equals(data.get("title"))) {
                        map.put(data.get("field").toString(), data.get("title").toString());
                    }
                }
            });
            return exportExcel(map, list, attrMap);
        } catch (Exception e) {
            throw new BusinessException("导出异常 :" + e.getMessage());
        }

    }

    /**
     * 动态导出列 对象
     *
     * @param fileData 前端的查询指定列数据 $("#bootstrap-table").bootstrapTable('getVisibleColumns');
     * @param list     导出数据
     * @return
     */
    public AjaxResult filedExportExcelDomain(List<Map<String, Object>> fileData, List<T> list) {
        try {
            Map<String, String> map = new LinkedHashMap<>();
            fileData.forEach(data -> {
                Object field = data.get("field");
                Object title = data.get("title");
                if (ObjectUtil.isNotEmpty(field) && ObjectUtil.isNotEmpty(title)) {
                    if (!"操作".equals(data.get("title"))) {
                        map.put(data.get("field").toString(), data.get("title").toString());
                    }
                }
            });


            Field[] fileds = PoiPublicUtil.getClassFields(clazz);
            ExcelTarget etarget = clazz.getAnnotation(ExcelTarget.class);
            String targetId = etarget == null ? null : etarget.value();
            ExcelExportService excelExportService = new ExcelExportService();
            List<ExcelExportEntity> excelParams = new ArrayList<ExcelExportEntity>();
            try {
                excelExportService.getAllExcelField(exportParams.getExclusions(), targetId, fileds, excelParams, clazz,
                        null, null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            List<Map<String, Object>> collect = list.stream().map(data -> BeanUtil.beanToMap(data)).collect(Collectors.toList());
            return exportExcel(map, collect, excelParams);
        } catch (Exception e) {
            throw new BusinessException("导出异常 :" + e.getMessage());
        }

    }

    /**
     * 动态导出列
     *
     * @param filed    字段code
     * @param fileName 字段名称
     * @param list     数据
     * @return
     */
    public AjaxResult filedExportExcel(List<String> filed, List<String> fileName, List<T> list) {
        try {
            Map<String, String> map = new LinkedHashMap<>();
            for (int i = 0; i < filed.size(); i++) {
                String s = filed.get(i);
                if (!StringUtils.isNumeric(s)) {
                    map.put(s, fileName.get(i));
                }
            }
            List<Map<String, Object>> collect = list.stream().map(data -> {
                if (data instanceof Map) {
                    return (Map<String, Object>) data;
                } else {
                    return BeanUtil.beanToMap(data);
                }
            }).collect(Collectors.toList());
            return exportExcel(map, collect);
        } catch (Exception e) {
            throw new BusinessException("导入异常 :" + e.getMessage());
        }

    }

}

