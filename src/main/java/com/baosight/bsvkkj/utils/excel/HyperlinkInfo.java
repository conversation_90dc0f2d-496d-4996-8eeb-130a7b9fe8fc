package com.baosight.bsvkkj.utils.excel;

import cn.afterturn.easypoi.handler.inter.IExcelDataHandler;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Hyperlink;

import java.util.Map;

public class HyperlinkInfo<T> implements IExcelDataHandler<T> {

    @Override
    public Object exportHandler(T t, String name, Object value) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public String[] getNeedHandlerFields() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void setNeedHandlerFields(String[] fields) {
        // TODO Auto-generated method stub

    }

    @Override
    public Object importHandler(T t, String name, Object value) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void setMapValue(Map<String, Object> map, String originKey, Object value) {
        // TODO Auto-generated method stub

    }

    @Override
    public Hyperlink getHyperlink(<PERSON>H<PERSON><PERSON> creationHelper, T t, String name, Object value) {
        System.out.println("---" + name);
        System.out.println("---" + value);
        Hyperlink createHyperlink = creationHelper.createHyperlink(HyperlinkType.URL);
        createHyperlink.setAddress((String) value);
        return createHyperlink;
    }
}
