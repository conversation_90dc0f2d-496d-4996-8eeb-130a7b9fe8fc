package com.baosight.bsvkkj.utils;

import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import java.util.HashMap;
import java.util.Map;

public class MyUserSession {

    //需要在平台EDCC03里设置 iplat.threadlocal.sessionKeys
    private static final String MY_SESSION_KEY = "jtfw:userSession";

    public static Map<String, Object> getMySessionMap() {
        Map<String, Object> mySessionMap = (Map<String, Object>) UserSession.getInSessionProperty(MY_SESSION_KEY);
        if (mySessionMap == null) {
            mySessionMap = new HashMap<>();
            UserSession.setOutSessionProperty(MY_SESSION_KEY, mySessionMap);
        }
        return mySessionMap;
    }

    public static Object getValue(String key) {
        return getMySessionMap().get(key);
    }

    public static void setValue(String key, Object value) {
        getMySessionMap().put(key, value);
    }
}
