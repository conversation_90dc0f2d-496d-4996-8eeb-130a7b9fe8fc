package com.baosight.bsvkkj.utils;

import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-01-18 14:08
 */
public class XyUtil {
    /**
     * 获取效益
     *
     * @param ywId      业务id 必填
     * @param moduleCode 模块code 必填
     * @return
     */
    public static List<Map<String, Object>> getXyList(String ywId, String moduleCode) {
        if (StrUtil.isBlank(ywId)) {
            return null;
        }
        EiInfo eiInfo = new EiInfo();
        //业务id
        eiInfo.set("ywId", ywId);
        //模块code
        eiInfo.set("extra2", moduleCode);

        eiInfo.set("serviceId", "S_MP_XY_03");
        EiInfo call = XServiceManager.call(eiInfo);

        if (EiConstant.STATUS_FAILURE == call.getStatus()) {
            throw new PlatException(call.getMsg());
        }
        return (List<Map<String, Object>>) call.get("list");
    }

    public static void main(String[] args) {
        for (int i=0 ;i<129;i++){
            System.out.println(BizIdUtil.INSTANCE.nextId());
        }

    }
}
