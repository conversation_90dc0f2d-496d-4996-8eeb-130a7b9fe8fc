package com.baosight.bsvkkj.utils;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("WorkFlowUtil")
public class WorkFlowUtil {

    /**
     * 获取租户对应的退回选项
     * @return
     */
    public static List<Map<String, Object>> getReturnOption() {
        List<Map<String, Object>> rtn = new ArrayList<>();
        Map<String, Object> option1 = new HashMap<>();
        option1.put("key", "逐级审批");
        option1.put("value", "0");
        rtn.add(option1);
        Map<String, Object> option2 = new HashMap<>();
        option2.put("key", "直达本节点");
        option2.put("value", "1");
        rtn.add(option2);
        return rtn;
    }

}