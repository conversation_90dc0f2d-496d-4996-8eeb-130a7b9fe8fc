package com.baosight.bsvkkj.utils;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * 通过接口获取所有实现
 *
 * <AUTHOR> 时间：2016年8月23日 下午10:16:53
 */
public class ClassUtil {

    private final Class clazz;
    private final String packagePath;

    public ClassUtil(Class clazz) {
        this.clazz = clazz;
        this.packagePath = clazz.getPackage().getName();
    }

    public ClassUtil(Class clazz, String packagePath) {
        this.clazz = clazz;
        this.packagePath = packagePath;
    }

    /**
     * 通过包名获取所有实现（可以将包名配置到统一配置文件中）
     *
     * @return
     */
    public List<Class> getAllClassByPackage() {
        ArrayList<Class> returnClassList = new ArrayList<Class>();
        try {
            List<Class> allClass = getClasses(packagePath);
            // 判断是否是一个接口
            for (int i = 0; i < allClass.size(); i++) {
                if (clazz.isAssignableFrom(allClass.get(i))) {
                    if (!clazz.equals(allClass.get(i))) {
                        returnClassList.add(allClass.get(i));
                    }
                }
            }
        } catch (Exception e) {
        }
        return returnClassList;
    }

    private List<Class> getClasses(String packageName) throws ClassNotFoundException, IOException {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        String path = packageName.replace(".", "/");
        Enumeration<URL> resources = classLoader.getResources(path);
        List<File> dirs = new ArrayList<File>();
        while (resources.hasMoreElements()) {
            URL resource = resources.nextElement();
            dirs.add(new File(resource.getFile()));
        }
        ArrayList<Class> classes = new ArrayList<Class>();
        for (File directory : dirs) {
            classes.addAll(findClass(directory, packageName));
        }
        return classes;
    }

    private List<Class> findClass(File directory, String packageName) throws ClassNotFoundException {
        List<Class> classes = new ArrayList<Class>();
        if (!directory.exists()) {
            return classes;
        }
        File[] files = directory.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                assert !file.getName().contains(".");
                classes.addAll(findClass(file, packageName + "." + file.getName()));
            } else if (file.getName().endsWith(".class")) {
                classes.add(Class.forName(packageName + "." + file.getName().substring(0, file.getName().length() - 6)));
            }
        }
        return classes;
    }
}
