package com.baosight.bsvkkj.utils;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Logger logger = LoggerFactory.getLogger(JsonUtil.class);

    /**
     * 将json转换为对象 如果对象模版为内部类会出现问题，所以不要使用内部类
     * @param json 要转换的json
     * @param clazz 要映射的对象
     * @return 转换成的目标对象，如果转换失败返回null
     * */
    public static Object json2Object(String json, Class<?> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonParseException e) {
            logger.error("|JsonParseException|异常字符串|" + json, e);
        } catch (JsonMappingException e) {
            logger.error("|JsonMappingException|异常字符串|" + json, e);
        } catch (IOException e) {
            logger.error("|IOException|异常字符串|" + json, e);
        }
        return null;
    }
    /**
     * 将对象转换成json字符串,如果转换失败则返回null
     * @param o 需要转换为json的对象
     * @return String 转换后的json字符串
     *
     *
     * */
    public static String write2JsonStr(Object o) {
        String jsonStr = "";
        try {
            jsonStr = objectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            logger.error("|JsonProcessingException|", e);
        }
        return jsonStr;
    }
    public static Object json2Object1(String json, Class<?> clazz) {
        try {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
            return objectMapper.readValue(json, clazz);
        } catch (JsonParseException e) {
            logger.error("|JsonParseException|异常字符串|" + json, e);
        } catch (JsonMappingException e) {
            logger.error("|JsonMappingException|异常字符串|" + json, e);
        } catch (IOException e) {
            logger.error("|IOException|异常字符串|" + json, e);
        }
        return null;
    }
}
