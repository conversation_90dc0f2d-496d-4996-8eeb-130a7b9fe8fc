package com.baosight.bsvkkj.utils;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class MyThreadPoolUtils {

    //获取线程池
    public static ExecutorService myThreadPool(){
        ThreadFactory threadFactory = new ThreadFactory() {
            //用于创建多个线程时，对进程数字编号
            final AtomicInteger atomic = new AtomicInteger();
            @Override
            public Thread newThread(Runnable r) {
                //每创建一个线程，会附加进程数字编号号返回并增加计数
                return new Thread(r, "MyThread" + this.atomic.getAndIncrement());
            }
        };

        ThreadPoolExecutor executor = null;
        try {
            executor = new ThreadPoolExecutor(
                    10, //核心线程大小
                    50, //线程池最大容量
                    10, //线程空闲时间
                    TimeUnit.SECONDS, //时间单位
                    new LinkedBlockingQueue<>(1000), //任务队列;一个阻塞队列
                    threadFactory, //线程工厂
                    new ThreadPoolExecutor.AbortPolicy() //线程拒绝策略
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
        return executor;
    }
}