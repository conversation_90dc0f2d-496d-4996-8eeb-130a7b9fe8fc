package com.baosight.bsvkkj.utils.expert;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.bsvkkj.mp.ad.utils.SRoleUtil;
import com.baosight.bsvkkj.mp.ty.utils.SDictUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import java.util.*;

/**
 * 专家评审util
 *
 * <AUTHOR>
 * @create 2021-12-23 15:08
 */
public class ExperReviewUtil {

    public static String getVersion(String bizId, String moduleCode, Long approveNum) {
        List<Map<String, Object>> reviewReq = getReviewReq(bizId, moduleCode, approveNum);
        if(CollUtil.isNotEmpty(reviewReq)){
            EiInfo eiInfo = new EiInfo();
            //业务id
            eiInfo.set("processId", reviewReq.get(0).get("processId"));
            eiInfo.set("serviceId", "S_MP_PS_07");
            EiInfo call = XServiceManager.call(eiInfo);
            Map<String,Object> process = (Map<String, Object>) call.get("process");
            if(process!=null){
                return (String)process.get("extra1");
            }

        }
        return null;
    }
    /**
     * 获取评审要求信息
     *
     * @param bizId      业务id 必填
     * @param moduleCode 模块code 必填
     * @param approveNum 当前评审次数
     * @return
     */
    public static List<Map<String, Object>> getReviewReq(String bizId, String moduleCode, Long approveNum) {
        if (StrUtil.isBlank(bizId) || StrUtil.isBlank(moduleCode)) {
            return null;
        }
        EiInfo eiInfo = new EiInfo();
        //业务id
        eiInfo.set("bizId", bizId);
        //模块code
        eiInfo.set("moduleCode", moduleCode);
        eiInfo.set("approveNum", approveNum);
        eiInfo.set("serviceId", "S_MP_PS_02");
        EiInfo call = XServiceManager.call(eiInfo);

        if (EiConstant.STATUS_FAILURE == call.getStatus()) {
            throw new PlatException(call.getMsg());
        }
        return (List<Map<String, Object>>) call.getAttr().get("list");
    }

    /**
     * 获取评审要求信息
     *
     * @param bizId      业务id 必填
     * @param moduleCode 模块code 必填
     * @param approveNum 当前评审次数
     * @return
     */
    public static Boolean getReviewReqGly(String bizId, String moduleCode, Long approveNum) {
        if (StrUtil.isBlank(bizId) || StrUtil.isBlank(moduleCode)) {
            return false;
        }
        String loginName = UserSession.getLoginName();
        if(SRoleUtil.isAdmin(loginName)){
            return true;
        }
        EiInfo eiInfo = new EiInfo();
        //业务id
        eiInfo.set("bizId", bizId);
        //模块code
        eiInfo.set("moduleCode", moduleCode);
        eiInfo.set("approveNum", approveNum);
        eiInfo.set("serviceId", "S_MP_PS_02");
        EiInfo call = XServiceManager.call(eiInfo);

        if (EiConstant.STATUS_FAILURE == call.getStatus()) {
            throw new PlatException(call.getMsg());
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) call.getAttr().get("list");
        if(CollUtil.isNotEmpty(list)){
            for (Map<String, Object> map : list) {
                return loginName.equals(map.get("createUserLabel"));
            }
        }
        return false;
    }

    /**
     * 获取评审信息表（结果信息）
     *
     * @param reviewreqId 评审要求表主键 必填
     * @param userType    成员类型 zz-组长 zy-组员
     * @param reviewUser  评审人员工号
     * @return
     */
    public static List<Map<String, Object>> getReviewInfo(String reviewreqId, String userType, String reviewUser) {
        if (StrUtil.isBlank(reviewreqId)) {
            throw new PlatException("参数为空");
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("reviewreqId", reviewreqId);
        eiInfo.set("userType", userType);
        eiInfo.set("reviewUser", reviewUser);
        eiInfo.set("serviceId", "S_MP_PS_03");
        EiInfo call = XServiceManager.call(eiInfo);

        if (EiConstant.STATUS_FAILURE == call.getStatus()) {
            throw new PlatException(call.getMsg());
        }
        return (List<Map<String, Object>>) call.getAttr().get("list");
    }

    /**
     * 评审信息（所有评审信息）
     *
     * @param reviewreqId 评审要求表主键 必填
     * @param userType    成员类型 zz-组长 zy-组员
     * @param reviewUser  评审人员工号
     * @return
     */
    public static Map<String,Object> getReviewResult(String reviewreqId, String userType, String reviewUser) {
        if (StrUtil.isBlank(reviewreqId)) {
            throw new PlatException("参数为空");
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("reviewreqId", reviewreqId);
        eiInfo.set("userType", userType);
        eiInfo.set("reviewUser", reviewUser);
        eiInfo.set("serviceId", "S_MP_PS_04");
        EiInfo call = XServiceManager.call(eiInfo);

        if (EiConstant.STATUS_FAILURE == call.getStatus()) {
            throw new PlatException(call.getMsg());
        }
        return  call.getAttr();
    }



    public static String getUserTypeName(String userType) {
        if (StrUtil.isNotBlank(userType)) {
            if ("zz".equals(userType)) {
                return "组长评审";
            } else if ("zy".equals(userType)) {
                return "组员评审";
            }
        }
        return null;
    }

    /**
     * 获取评审信息列表（前台展示）
     *
     * @param bizId      业务id 必填
     * @param moduleCode 模块code 必填
     * @param approveNum 当前评审次数
     * @return
     */
    public static List<Map<String, Object>> getReviewToPage(String bizId, String moduleCode, Long approveNum) {
        List<Map<String, Object>> reviewReq = getReviewReq(bizId, moduleCode, approveNum);
        if(reviewReq!=null) {
	        reviewReq.forEach(map -> {
	            List<Map<String, Object>> list = getReviewInfo((String) map.get("reviewreqId"), null, null);
	            String reviewreqId=(String) map.get("reviewreqId");
	            WorkFlow mainFlowInfoByBusinessId = SWorkFlowUtil.getMainFlowInfoByBusinessId(reviewreqId);
                if(mainFlowInfoByBusinessId!=null){
                    String currentActivity = mainFlowInfoByBusinessId.getCurrentActivity();
                    if(!"End".equals(currentActivity)){
                        String currentOperator = mainFlowInfoByBusinessId.getCurrentOperator();
                        if("Manual1".equals(currentActivity)){
                            String groupMembers=(String) map.get("groupMembers");
                            String groupMembersName=(String) map.get("groupMembersName");
                            String[] split = groupMembers.split(",");
                            String[] split2 = groupMembersName.split(",");
                            if(CollUtil.isNotEmpty(list)){
                                for (int i=0;i<split.length;i++) {
                                    String s=split[i];
                                    String name=split2[i];
                                    if(currentOperator.indexOf(s)>-1){
                                        Optional<Map<String, Object>> reviewUser = list.stream().filter(data -> s.equals(data.get("reviewUser"))).findAny();
                                        if(reviewUser.isPresent()){
                                            reviewUser.get().put("psStatus","待评审");
                                        }else{
                                            Map<String, Object> hashMap = new HashMap<>();
                                            hashMap.put("reviewUser",s);
                                            hashMap.put("reviewUserName",name);
                                            hashMap.put("userType","zy");
                                            hashMap.put("psStatus","待评审");
                                            list.add(hashMap);
                                        }
                                    }
                                }
                            }else{
                                for (int i = 0; i < split.length; i++) {
                                    String s = split[i];
                                    String name = split2[i];
                                    Map<String, Object> hashMap = new HashMap<>();
                                    hashMap.put("reviewUser", s);
                                    hashMap.put("reviewUserName", name);
                                    hashMap.put("userType", "zy");
                                    hashMap.put("psStatus", "待评审");
                                    list.add(hashMap);

                                }
                            }


                        }else {
                            String groupLeader=(String) map.get("groupLeader");
                            String groupLeaderName =(String) map.get("groupLeaderName");
                            Optional<Map<String, Object>> reviewUser = list.stream().filter(data -> groupLeader.equals(data.get("reviewUser"))).findAny();
                            if(reviewUser.isPresent()){
                                reviewUser.get().put("psStatus","待评审");
                            }else{
                                Map<String, Object> hashMap = new HashMap<>();
                                hashMap.put("reviewUser",groupLeader);
                                hashMap.put("reviewUserName",groupLeaderName);
                                hashMap.put("userType","zz");
                                hashMap.put("psStatus","待评审");
                                list.add(hashMap);

                            }
                        }
                    }
                }
	            list.forEach(reviewInfo -> {
	                reviewInfo.put("userTypeName", getUserTypeName((String) reviewInfo.get("userType")));
	            });
	
	
	            map.put("reviewInfo", list);
	
	        });
        }
        return reviewReq;
    }

    /**
     * 获取基本信息封装
     *
     * @param moduleCode 模块code 必填
     * @param map        key 列名  value 列值
     * @return
     */
    public static String getReviewInfoEncapsulation(String moduleCode, Map<String, Object> map,String bizGuid) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businesCode", moduleCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_PS_01");
        EiInfo module = XServiceManager.call(eiInfo);
        Map<String, Object> moduleData = (Map<String, Object>) module.get("data");
        LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("业务模块", SDictUtil.getDictName("MPTY","BUSINESS_TYPE",(String)moduleData.get("moduleName")));
        linkedHashMap.put("评审类型", moduleData.get("businesName"));
        linkedHashMap.putAll(map);
        ArrayList<Map<String, Object>> objects = new ArrayList<>();
        linkedHashMap.forEach((k, v) -> {
            Map<String, Object> data = new HashMap<>();
            data.put("name", k);
            data.put("value", v);
            objects.add(data);
        });
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put(bizGuid,objects);
        JSONObject jsonObject = JSONUtil.parseObj(hashMap);
        return Base64.encodeUrlSafe(jsonObject.toJSONString(0));
    }
    
    /**
     * 获取基本信息封装
     *
     * @param moduleCode 模块code 必填
     * @param map        key 列名  value 列值
     * @return
     */
    public static JSONObject getReviewInfoEncapsulationToJsonStr(String moduleCode, Map<String, Object> map,String bizGuid) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businesCode", moduleCode);
        eiInfo.set(EiConstant.serviceId, "S_MP_PS_01");
        EiInfo module = XServiceManager.call(eiInfo);
        Map<String, Object> moduleData = (Map<String, Object>) module.get("data");
        LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("业务模块", SDictUtil.getDictName("MPTY","BUSINESS_TYPE",(String)moduleData.get("moduleName")));
        linkedHashMap.put("评审类型", moduleData.get("businesName"));
        linkedHashMap.putAll(map);
        ArrayList<Map<String, Object>> objects = new ArrayList<>();
        linkedHashMap.forEach((k, v) -> {
            Map<String, Object> data = new HashMap<>();
            data.put("name", k);
            data.put("value", v);
            objects.add(data);
        });
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put(bizGuid,objects);
        return JSONUtil.parseObj(hashMap);
      
    }




}
