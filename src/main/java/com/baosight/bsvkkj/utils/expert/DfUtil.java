package com.baosight.bsvkkj.utils.expert;


import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DfUtil {

    public static List<Map<String,Object>> getDfList(String bizId, String userCode){
        if(StrUtil.isBlank(bizId)){
            return new ArrayList<>();
        }
        EiInfo eiInfo = new EiInfo();
        //业务id
        eiInfo.set("bizId", bizId);
        eiInfo.set("userCode",userCode);
        eiInfo.set("serviceId","S_MP_DF_001");
        EiInfo call = XServiceManager.call(eiInfo);
        List<Map<String,Object>> list = (List<Map<String, Object>>) call.get("list");
        return list;
    }

    public static String getDfByColumnId(String bizId, String userCode,String columnId){
        if(StrUtil.isBlank(columnId)){
            return null;
        }
        List<Map<String, Object>> dfList = getDfList(bizId, userCode);
        List<String> list = dfList.stream().filter(data->columnId.equals(data.get("columnId"))).map(data->(String)data.get("reviewResult")).collect(Collectors.toList());
        if(list.size()>0)
            return list.get(0);

        return null;
    }

    public static void updateDfAndDfSum(String bizId, String userCode,String columnId,String columnValue){
        List<Map<String, Object>> dfList = getDfList(bizId, userCode);
        String sumColumSumId="";
        BigDecimal sum=BigDecimal.ZERO;

        for (Map<String, Object> data : dfList) {
            String cId = (String) data.get("columnId");
            String reviewResult = (String) data.get("reviewResult");
            if(columnId.equals(cId)){
                reviewResult=columnValue;
            }
            if(StrUtil.isNotBlank(columnValue) && !"dfsum".equals(data.get("columnType"))){
                sum=sum.add(new BigDecimal(reviewResult));
            }
            if("dfsum".equals(data.get("columnType"))){
                sumColumSumId=cId;
            }

        }
        updateDF(bizId,userCode,columnId,columnValue);
        updateDF(bizId,userCode,sumColumSumId,sum.toString());
    }



    public static void updateDF(String bizId, String userCode,String columnId,String columnValue){
        EiInfo eiInfo = new EiInfo();
        //业务id
        eiInfo.set("bizId", bizId);
        eiInfo.set("userCode",userCode);
        eiInfo.set("columnId",columnId);
        eiInfo.set("columnValue",columnValue);
        eiInfo.set("serviceId","S_MP_DF_002");
        EiInfo call = XServiceManager.call(eiInfo);
    }

}
