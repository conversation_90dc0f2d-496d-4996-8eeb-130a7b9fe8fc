package com.baosight.bsvkkj.utils;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baosight.bsvkkj.mp.ty.utils.SDictUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021-12-27 15:27
 */
@Component
public class IndexUtil {
    /**
     * 获取当前时间
     * @return
     */
    public static String getDate(){
        Date date = new Date();
        String name="";

        int i = DateUtil.thisDayOfWeek()-1;
        if(i==0){
            name="日";
        }else{
            name=NumberChineseFormatter.format(i,false);
        }

        return DateUtil.formatChineseDate(date,false,false)+" 星期"+name;
    }

    public static String getToDate(){

        return DateUtil.today();
    }

    /**
     * 获取登录名
     * @return
     */
    public static String getLoginName(){

        return UserSession.getLoginName();
    }

    /**
     * 获取用户名称
     * @return
     */
    public static String getUserName(){
        return UserSession.getLoginCName();
    }

    /**
     * 获取年
     * @param date
     * @return
     */
    public static int getYear(String date){
        DateTime parse = DateUtil.parse(date);
        return DateUtil.year(parse);
    }

    /**
     * 获取月日
     * @param date
     * @return
     */
    public static String getYR(String date){
        DateTime parse = DateUtil.parse(date);
        return DateUtil.format(parse,"MM/dd");
    }

    public static List<Map<String, Object>>  getDb(){
        List<Map<String, Object>> list = (List<Map<String, Object>>) ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceMPWFFlowInfo", "queryDbList", "dbList");
        List<Map<String, Object>> dictList = SDictUtil.getDictList("MPTY", "BUSINESS_TYPE");
        if(list!=null && !list.isEmpty()){

            list.forEach(map->{
                Optional<Map<String, Object>> any = dictList.stream().filter(dict -> map.get("businessType").equals(dict.get("dictValue"))).findAny();
                if(any.isPresent()){
                    String dictName = (String) any.get().get("dictName");
                    map.put("businessTypeName", dictName);
                }else{
                    map.put("businessTypeName",  map.get("businessType")+"无名称");
                }
            });

            Optional<Map<String, Object>> kxcpAny = list.stream().filter(data -> data.get("businessType").equals("KXCP")).findAny();
            Optional<Map<String, Object>> kyxmAny = list.stream().filter(data -> data.get("businessType").equals("KYXM")).findAny();

            List<Map<String, Object>> collect = list.stream().filter(data -> !data.get("businessType").equals("KXCP")).collect(Collectors.toList());
            collect.forEach(map->{
                if(kxcpAny.isPresent()){
                    if("KYXM".equals(map.get("businessType"))){
                        map.put("todoNum",(Integer)map.get("todoNum")+ (Integer)kxcpAny.get().get("todoNum"));
                    }
                }
            });

            if(kxcpAny.isPresent()){
                if(!kyxmAny.isPresent()){
                    Map<String, Object> map = kxcpAny.get();
                    map.put("businessType","KYXM");
                    map.put("businessTypeName","科研项目");
                    collect.add(map);
                }
            }
            List<Map<String, Object>> todoNum = collect.stream().sorted(Comparator.comparing(o -> (Integer) o.get("todoNum"), Comparator.reverseOrder())).collect(Collectors.toList());
            return todoNum;
        }
        return null;
    }

    /**
     * 导航显示的模块
     * @return
     */
    public static List<Map<String, Object>> getDictList(){
        List<Map<String, Object>> dictList = SDictUtil.getDictList("MP", "BUSINESS_TYPE");
        List<Map<String, Object>> extra2 = dictList.stream().filter(data -> "1".equals(data.get("extra2"))).collect(Collectors.toList());
        return extra2;
    }

}
