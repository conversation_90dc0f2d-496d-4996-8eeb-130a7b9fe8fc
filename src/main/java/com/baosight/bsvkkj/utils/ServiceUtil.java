package com.baosight.bsvkkj.utils;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

/**
 * eiInfo 调用
 */
public class ServiceUtil {


    /**
     * 当前模板调用service
     *
     * @param eiInfo
     * @param serviceName
     * @param methodName
     * @return EiInfo
     */
    public static EiInfo xLocalManager(EiInfo eiInfo, String serviceName, String methodName) {
        if (eiInfo == null || StrUtil.isBlank(serviceName) || StrUtil.isBlank(methodName)) {
            return null;
        }
        eiInfo.set(EiConstant.serviceName, serviceName.replace("Service", ""));
        eiInfo.set(EiConstant.methodName, methodName);
        EiInfo call = XLocalManager.call(eiInfo);
        int status = call.getStatus();
        if (EiConstant.STATUS_FAILURE == status) {
            throw new BusinessException(call.getMsg());
        }
        return call;
    }

    /**
     * 当前模板调用service
     *
     * @param eiInfo
     * @param serviceId
     * @return EiInfo
     */
    public static EiInfo xLocalManager(EiInfo eiInfo, String serviceId) {
        if (eiInfo == null || StrUtil.isBlank(serviceId)) {
            return null;
        }
        eiInfo.set(EiConstant.serviceId, serviceId);
        EiInfo call = XServiceManager.call(eiInfo);
        int status = call.getStatus();
        if (EiConstant.STATUS_FAILURE == status) {
            throw new BusinessException(call.getMsg());
        }
        return call;
    }

    /**
     * 当前模板调用service返回状态码和消息
     *
     * @param eiInfo
     * @param serviceName
     * @param methodName
     * @return AjaxResult
     */
    public static AjaxResult xLocalManagerToAjaxResult(EiInfo eiInfo, String serviceName, String methodName) {
        EiInfo info = xLocalManager(eiInfo, serviceName, methodName);
        int status = info.getStatus();
        AjaxResult ajaxResult = null;
        if (EiConstant.STATUS_SUCCESS == status) {
            ajaxResult = new AjaxResult(AjaxResult.Type.SUCCESS, info.getMsg());
        } else if (EiConstant.STATUS_FAILURE == status) {
            ajaxResult = new AjaxResult(AjaxResult.Type.ERROR, info.getMsg());
        } else {
            ajaxResult = new AjaxResult(AjaxResult.Type.WARN, info.getMsg());
        }

        return ajaxResult;
    }
    /***
     * 当前模板调用service返回状态码和消息与数据
     * @param eiInfo
     * @param serviceName
     * @param methodName
     * @param key
     * @return
     */
    public static AjaxResult xLocalManagerToAjaxResult(EiInfo eiInfo, String serviceName, String methodName, String key) {
        EiInfo info = xLocalManager ( eiInfo, serviceName, methodName );
        int status = info.getStatus ();
        AjaxResult ajaxResult = null;
        if (EiConstant.STATUS_SUCCESS == status) {
            ajaxResult = new AjaxResult ( AjaxResult.Type.SUCCESS, info.getMsg (), info.get ( key ) );
        } else if (EiConstant.STATUS_FAILURE == status) {
            ajaxResult = new AjaxResult ( AjaxResult.Type.ERROR, info.getMsg () );
        } else {
            ajaxResult = new AjaxResult(AjaxResult.Type.WARN, info.getMsg());
        }

        return ajaxResult;
    }
    /**
     * 当前模板调用service返回对象
     *
     * @param eiInfo
     * @param serviceName
     * @param methodName
     * @param key         eiinfo 的key
     * @return Object    key的值
     */
    public static Object xLocalManagerToData(EiInfo eiInfo, String serviceName, String methodName, String key) {
        EiInfo info = xLocalManager(eiInfo, serviceName, methodName);
        return info.get(key);
    }


}
