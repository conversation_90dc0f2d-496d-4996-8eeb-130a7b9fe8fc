package com.baosight.bsvkkj.utils;

import org.apache.http.HttpResponse;
import org.apache.http.client.ResponseHandler;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

public class CustomResponseHandler implements ResponseHandler<String> {
    private String charset;

    public CustomResponseHandler(String charset) {
        this.charset = charset;
    }

    @Override
    public String handleResponse(HttpResponse response) throws IOException {
        return EntityUtils.toString(response.getEntity(), charset);
    }
}