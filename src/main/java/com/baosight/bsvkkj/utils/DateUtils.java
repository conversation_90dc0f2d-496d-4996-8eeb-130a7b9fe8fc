package com.baosight.bsvkkj.utils;

import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy" ;

    public static String MM = "MM" ;

    public static String YYYY_MM = "yyyy-MM" ;

    public static String YYYY_MM_DD = "yyyy-MM-dd" ;

    public static String YYYYMMDD = "yyyyMMdd" ;

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss" ;

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss" ;

    public static String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss SSS";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前年份, 默认格式为yyyy
     *
     * @return String
     */
    public static String getYear() {
        return dateTimeNow(YYYY);
    }


    public static String getMonth() {
        return dateTimeNow(MM);
    }

    /**
     * 获取当前年份, 默认格式为yyyy
     *
     * @return String
     */
    public static String getYear(int amount) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, -1);
        Date y = c.getTime();
        String year = format.format(y);
        return year;
    }

    /**
     * 得到当前年份的（上n年或下n年 的年份）
     * 如果当前是2020年
     * sum 上一年 -1 或者 下一年 1
     * return 2019 或者  2021
     */
    public static String getToYear(int sum) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, sum);
        String first = format.format(calendar.getTime());
        return first;
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String getMsec() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS_SSS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟" ;
    }

    /**
     * 获取月份
     * sum 上月 -1 或者 下月 1
     * @return 201908
     */
    public static String getToMonth(int sum) {
        SimpleDateFormat sdf = new SimpleDateFormat("MM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, sum);
        date = calendar.getTime();
        return sdf.format(date);
    }

    /**
     * 获取两个日期的相差的年数
     * 向上取整
     * 若其中一个为空就返回999
     * @param startDateStr yyyy-MM-dd
     * @param endDateStr yyyy-MM-dd
     * @return
     */
    public static int getDifferYearNum(String startDateStr, String endDateStr){
        try {
            if (StringUtils.isAnyBlank(endDateStr, endDateStr)) {
                return 999;
            }
            Date endDate = com.baosight.iplat4j.core.util.DateUtils.toDate(endDateStr, com.baosight.iplat4j.core.util.DateUtils.DATE10_PATTERN);
            Date startDate = com.baosight.iplat4j.core.util.DateUtils.toDate(startDateStr, com.baosight.iplat4j.core.util.DateUtils.DATE10_PATTERN);
            if (startDate.getTime()>endDate.getTime()) {
                throw new PlatException("开始日期:"+startDateStr+"大于"+"结束日期:"+endDateStr);
            }

            Calendar cEnd = Calendar.getInstance();
            cEnd.setTime(endDate);

            Calendar cStart = Calendar.getInstance();
            cStart.setTime(startDate);

            int yE = cEnd.get(Calendar.YEAR);
            int yS = cStart.get(Calendar.YEAR);

            int diff = 1;
            if (yE > yS) {
                diff = yE - yS;
                int mE = cEnd.get(Calendar.MONTH);
                int mS = cStart.get(Calendar.MONTH);
                if (mE==mS) {
                    int dE = cEnd.get(Calendar.DAY_OF_MONTH);
                    int dS = cStart.get(Calendar.DAY_OF_MONTH);
                    if (dE>dS) {
                        diff ++;
                    }
                } else if(mE>mS) {
                    diff ++;
                }
            }
            return diff;
        } catch (Exception e) {
            e.printStackTrace();
            return 999;
        }
    }
}
