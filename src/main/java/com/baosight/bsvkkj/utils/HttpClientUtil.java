package com.baosight.bsvkkj.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

public class HttpClientUtil {

    /**
     * post json
     *
     * @param url
     * @param postJson
     * @return
     */
    public static JSONObject postJson(String url, JSONObject postJson) {
        // 第一步：创建HttpClient对象
        CloseableHttpClient httpClient = null;
        ResponseHandler<String> responseHandler = new CustomResponseHandler(StandardCharsets.UTF_8.name());;
        try {
            //https
            if (url.indexOf("https") == 0) {
                SSLContext sslcontext = createIgnoreVerifySSL();
                // 设置协议http和https对应的处理socket链接工厂的对象
                Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                        .<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.INSTANCE)
                        .register("https", new SSLConnectionSocketFactory(sslcontext)).build();
                PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(
                        socketFactoryRegistry);
                HttpClients.custom().setConnectionManager(connManager);
                httpClient = HttpClients.custom().setConnectionManager(connManager).build();
            } else {
                httpClient = HttpClients.createDefault();
            }
            // 第二步：创建httpPost对象
            HttpPost httpPost = new HttpPost(url);
            RequestConfig config = RequestConfig.custom()
                    .setConnectionRequestTimeout(3000)
                    .setSocketTimeout(10000)
                    .setConnectTimeout(10000)
                    .build();
            httpPost.setConfig(config);

            // 第三步：给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(postJson.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            // 第四步：发送HttpPost请求，获取业务返回值
            String returnValue = httpClient.execute(httpPost, responseHandler);
            return JSONObject.parseObject(returnValue);
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("message", "httpClient=>" + e.getMessage());
            return jsonObject;
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * post json
     *
     * @param url
     * @param postJson
     * @return
     */
    public static JSONObject postJson(String url, JSONObject postJson, int timeOutSec) {
        // 第一步：创建HttpClient对象
        CloseableHttpClient httpClient = null;
        ResponseHandler<String> responseHandler = new CustomResponseHandler(StandardCharsets.UTF_8.name());
        try {
            //https
            if (url.indexOf("https") == 0) {
                SSLContext sslcontext = createIgnoreVerifySSL();
                // 设置协议http和https对应的处理socket链接工厂的对象
                Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                        .<ConnectionSocketFactory>create().register("http", PlainConnectionSocketFactory.INSTANCE)
                        .register("https", new SSLConnectionSocketFactory(sslcontext)).build();
                PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(
                        socketFactoryRegistry);
                HttpClients.custom().setConnectionManager(connManager);
                httpClient = HttpClients.custom().setConnectionManager(connManager).build();
            } else {
                httpClient = HttpClients.createDefault();
            }
            // 第二步：创建httpPost对象
            HttpPost httpPost = new HttpPost(url);
            RequestConfig config = RequestConfig.custom()
                    .setConnectionRequestTimeout(3000)
                    .setSocketTimeout(timeOutSec * 1000)
                    .setConnectTimeout(timeOutSec * 1000)
                    .build();
            httpPost.setConfig(config);

            // 第三步：给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(postJson.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            // 第四步：发送HttpPost请求，获取业务返回值
            String returnValue = httpClient.execute(httpPost, responseHandler);
            return JSONObject.parseObject(returnValue);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 绕过验证
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("SSLv3");

        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                                           String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                                           String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

}
