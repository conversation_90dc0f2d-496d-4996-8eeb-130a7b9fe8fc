package com.baosight.bsvkkj.utils;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.cache.redis.PlatRedisUtils;
import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:25
 */
public class XplatTokenUtil {
    public final static String XPLAT_TOKEN_UEL  = SpringUtil.getProperty("XPLAT_TOKEN_UEL");

    public static String getXplatToken() {
        Jedis jedis = PlatRedisUtils.getResource();
        try {
            String accessTokenKey = "getXplatToken_access_token";
            String access_token = jedis.get(accessTokenKey);
            if (StringUtils.isNotBlank(access_token)) {
                return access_token;
            }
            String post = HttpUtil.post(XPLAT_TOKEN_UEL, "", 100000);
            JSONObject jsonObject = JSONUtil.parseObj(post);
            access_token = jsonObject.getStr("access_token", "");
            if (StringUtils.isBlank(access_token)) {
                throw new PlatException("获取access_token失败！");
            }
            Long expires_in = jsonObject.getLong("expires_in", 60 * 60L);//默认1小时
            jedis.set(accessTokenKey, access_token, "NX", "EX", expires_in);
            return access_token;
        } catch (Exception ignored) {
            throw new PlatException("获取access_token服务异常！");
        } finally {
            PlatRedisUtils.returnResource(jedis);
        }
    }
}
