package com.baosight.bsvkkj.utils;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021-12-31 15:23
 */

public class MessageUtil {

    /**
     * 发送消息
     *
     * @param title       标题
     * @param content     内容
     * @param userId      发送人
     * @param receiveUser 接收人 多个逗号隔开
     * @param urlDetail   详细地址 全路径
     * @return
     */
    public static EiInfo saveMessage(String title, String content, String userId, String receiveUser, String urlDetail) {
        Map<String, Object> map = new HashMap<>();
        map.put("title", title);
        map.put("content", content);
        map.put("userId", userId);
        map.put("receiveUser", receiveUser);
        map.put("urlDetail", urlDetail);

        //未读
        map.put("msgStatus", "1");
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("message", map);
        eiInfo.set("serviceId", "S_MP_TY_XX01");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return outInfo;
    }

}
