package com.baosight.bsvkkj.utils;

import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.iplat4j.core.exception.PlatException;

import java.sql.SQLException;

public class ExceptionUtil {
	
	/**
	 * 根据异常获取具体出错信息
	 * @param e
	 * @return
	 */
	public static String getMsg(BusinessException e) {
		if(e.getMessage().indexOf("SQLCODE=-302")>0) {
			return "字段超长，请联系管理员"+getColumMsg(e.getMessage());
		} else if (e.getMessage().indexOf("SQLCODE=-420")>0) {
			return "字段类型转换错误，请联系管理员"+getColumMsg(e.getMessage());
		}
		return "sql异常:" + e.getMessage();
	}
	
	/**
	 * 根据异常获取具体出错信息
	 * @param e
	 * @return
	 */
	public static String getMsg(PlatException e) {
		if(e.getMessage().indexOf("SQLCODE=-302")>0) {
			return "字段超长，请联系管理员";
		} else if (e.getMessage().indexOf("SQLCODE=-420")>0) {
			return "字段类型转换错误，请联系管理员"+getColumMsg(e.getMessage());
		}
		return "sql异常:" + e.getMessage();
	}

	/**
	 * 根据异常获取具体出错信息
	 * @param e
	 * @return
	 */
	public static String getMsg(SQLException e) {		
		if(e.getMessage().indexOf("SQLCODE=-302")>0) {
			return "字段超长，请联系管理员" + getColumMsg(e.getMessage());
		}
		return "sql异常:" + e.getMessage();
	}
	
	/**
	 * 根据异常获取具体出错信息
	 * @param e
	 * @return
	 */
	public static String getMsg(NullPointerException e) {
		StackTraceElement[] s = e.getStackTrace();
		StringBuffer erro = new StringBuffer();
		if(s!=null&&s.length>0) {
			StackTraceElement stackTraceElement = s[0];
			erro.append(stackTraceElement.getClassName()+"=>");
			erro.append(stackTraceElement.getMethodName()+"=>");
			erro.append(stackTraceElement.getLineNumber()+"行");
			return "空指针异常:" + erro;
		}
		return "空指针异常:" + e.getMessage();
	}
	
	private static String getColumMsg(String erroMsg) {
		String TBSPACEID = null, TABLEID = null, COLNO = null;
		String columMsg = "";
		if(erroMsg.indexOf("TBSPACEID")>=0) {
			String[] ss = erroMsg.split("TBSPACEID");			
			TBSPACEID = ss[1].substring(0, ss[1].indexOf(","));
			System.out.println("TBSPACEID==="+TBSPACEID);
		}
		if(erroMsg.indexOf("TABLEID")>=0) {
			String[] ss = erroMsg.split("TABLEID");
			TABLEID = ss[1].substring(0, ss[1].indexOf(","));
			System.out.println("TABLEID==="+TABLEID);
		}
		if(erroMsg.indexOf("COLNO")>=0) {
			String[] ss = erroMsg.split("COLNO");
			COLNO = ss[1].substring(0, ss[1].indexOf(" "));
			System.out.println("COLNO==="+COLNO);
		}
		if(null!=TBSPACEID&&null!=TABLEID&&null!=COLNO) {
			columMsg = erroMsg;
			/*
			try {
				BusinessMPTYTable businessMPTYTable = SpringUtil.getBean ( BusinessMPTYTable.class );
				Map<String,Object> colum = businessMPTYTable.queryColums(TBSPACEID, TABLEID, COLNO);
				if(null!=colum) {
					columMsg += (String)colum.get("tabname")+"=>"+colum.get("colname")+"=>"+colum.get("remarks")+"=>长度:"+colum.get("length")+".";
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			*/
		}
		return columMsg;
	}
	
	public static void main(String[] args) {
		String erroMsg = "=====TBSPACEID=2, TABLEID=1070, COLNO=38 ";
		if(erroMsg.indexOf("TBSPACEID")>=0) {
			String[] ss = erroMsg.split("TBSPACEID");
			
			String TBSPACEID = ss[1].substring(0, ss[1].indexOf(","));
			System.out.println("TBSPACEID==="+TBSPACEID);
		}
		if(erroMsg.indexOf("TBSPACEID")>=0) {
			String[] ss = erroMsg.split("TABLEID");
			
			String TBSPACEID = ss[1].substring(0, ss[1].indexOf(","));
			System.out.println("TABLEID==="+TBSPACEID);
		}
		if(erroMsg.indexOf("COLNO")>=0) {
			String[] ss = erroMsg.split("COLNO");
			
			String TBSPACEID = ss[1].substring(0, ss[1].indexOf(" "));
			System.out.println("COLNO==="+TBSPACEID);
		}
		System.out.println(erroMsg);
	}
}
