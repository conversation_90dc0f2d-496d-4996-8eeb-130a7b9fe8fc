package com.baosight.bsvkkj.utils;


import org.apache.commons.codec.binary.Base64;
import org.apache.log4j.Logger;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

public class RSAUtil {
    public static final String KEY_ALGORITHM = "RSA";
    private static final Logger logger = Logger.getLogger(RSAUtil.class);
    private static final String ENCODE_TYPE = "RSA/ECB/PKCS1Padding";

    private static final String PUBLIC_KEY = "RSAPublicKey";

    private static final String PRIVATE_KEY = "RSAPrivateKey";

    private static final int MAX_ENCRYPT_LENGTH = 117;

    private static final int MAX_DECRYPT_LENGTH = 128;

    private static final int INIT_KEY_SIZE = 1024;

    /**
     * eplat 公钥
     */
    private static final String EPLAT_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCfTBifZhETOt12PgKAMW2aN/p4iynz0dDHN2iMgVvTCtxq9txCpJ+BfRfavLHWB7pLxRQC/UlGJjpBeK6eheRR1moRY129pketVS5PKT0StJdJUGeTNcxnimR7TAaOVRV6N3889cosBEiRUO23iU4/dlfrpUfttyVUjt45XrtThQIDAQAB";


    public static KeyPair getKePair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGenerator.initialize(INIT_KEY_SIZE);
        return keyPairGenerator.generateKeyPair();
    }


    public static Map<String, Key> initKeyMap() {
        KeyPair keyPair = null;
        try {
            keyPair = getKePair();
        } catch (NoSuchAlgorithmException e) {
            logger.error(e.getMessage());
        }
        Map<String, Key> keyMap = null;
        if (keyPair != null) {
            keyMap = new HashMap();
            keyMap.put(PUBLIC_KEY, keyPair.getPublic());
            keyMap.put(PRIVATE_KEY, keyPair.getPrivate());
        }
        return keyMap;
    }

    public static String getPublicKey(Map<String, Key> keyMap) {
        return Base64.encodeBase64String(keyMap.get(PUBLIC_KEY).getEncoded());
    }

    public static String getPrivateKey(Map<String, Key> keyMap) {
        return Base64.encodeBase64String(keyMap.get(PRIVATE_KEY).getEncoded());
    }

    /**
     * 用公钥加密
     *
     * @param data      明文
     * @param publicKey 公钥字符串
     * @return 加密后的byte[]
     */
    public static String encryptWithPublicKey(String data, String publicKey) throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException, IOException {
        //获取公钥
        PublicKey key = string2PublicKey(publicKey);
        Cipher cipher = Cipher.getInstance(ENCODE_TYPE);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
        return handleStepEncryptOrDecrypt(dataBytes, MAX_ENCRYPT_LENGTH, cipher, true);
    }

    /**
     * 用私钥解密
     *
     * @param data       密文
     * @param privateKey 私钥字符串
     * @return 解密后byte[]
     * @throws Exception
     */
    public static String decryptWithPrivateKey(String data, String privateKey) throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException, IOException {
        PrivateKey key = string2PrivateKey(privateKey);
        Cipher cipher = Cipher.getInstance(ENCODE_TYPE);
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] content = Base64.decodeBase64(data);
        return handleStepEncryptOrDecrypt(content, MAX_DECRYPT_LENGTH, cipher, false);
    }

    private static PublicKey string2PublicKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        return KeyFactory.getInstance(KEY_ALGORITHM).generatePublic(new X509EncodedKeySpec(Base64.decodeBase64(key)));
    }

    private static PrivateKey string2PrivateKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        return KeyFactory.getInstance(KEY_ALGORITHM).generatePrivate(new PKCS8EncodedKeySpec(Base64.decodeBase64(key)));
    }

    public static PublicKey str2PublicKey(String key) throws CertificateException {
        byte[] keyBytes = Base64.decodeBase64(key);
        CertificateFactory fact = CertificateFactory.getInstance("X.509");
        X509Certificate cer = (X509Certificate) fact.generateCertificate(new ByteArrayInputStream(keyBytes));
        return cer.getPublicKey();
    }

    private static String handleStepEncryptOrDecrypt(byte[] dataBytes, int maxLength, Cipher cipher, boolean isEncrypt) throws BadPaddingException, IllegalBlockSizeException, IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        int inputDataLength = dataBytes.length;
        int offset = 0;
        byte[] cache;
        int i = 0;
        while (inputDataLength - offset > 0) {
            if (inputDataLength - offset > maxLength) {
                cache = cipher.doFinal(dataBytes, offset, maxLength);
            } else {
                cache = cipher.doFinal(dataBytes, offset, inputDataLength - offset);
            }
            outputStream.write(cache, 0, cache.length);
            i++;
            offset = i * maxLength;
        }
        byte[] result = outputStream.toByteArray();
        outputStream.close();
        return isEncrypt ? Base64.encodeBase64URLSafeString(result) : new String(result, StandardCharsets.UTF_8);
    }

    public static final String getMD5(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(data.getBytes(StandardCharsets.UTF_8));
            return encodeHex(digest.digest());
        } catch (Exception var2) {
            throw new IllegalStateException(var2.getMessage());
        }
    }

    public static final String encodeHex(byte[] bytes) {
        StringBuffer buf = new StringBuffer(bytes.length * 2);

        for (int i = 0; i < bytes.length; ++i) {
            if ((bytes[i] & 255) < 16) {
                buf.append("0");
            }

            buf.append(Long.toString(bytes[i] & 255, 16));
        }

        return buf.toString();
    }

    /**
     * @param targetUrl
     * @param userLabel
     * @param userName
     * @return
     */
    public static String getEplatSSOUrl(String targetUrl, String userLabel, String userName) {
        String encryptString = "";
        try {
            encryptString = RSAUtil.encryptWithPublicKey(userLabel + "," + userName + "," + System.currentTimeMillis() + ",BWOA", EPLAT_PUBLIC_KEY);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            throw new RuntimeException("eplat加密异常:" + e.getMessage());
        }
        if (targetUrl.indexOf("?") < 0) {
            targetUrl += "?ePlatUser=" + encryptString;
        } else {
            targetUrl += "&ePlatUser=" + encryptString;
        }
        return targetUrl;
    }

}

