package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.domain.Ztree;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLMaintainTecharea;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainTecharea;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class ServiceKIZLMaintainTecharea extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessKIZLMaintainTecharea businessKIZLMaintainTecharea;

    public EiInfo tableData(EiInfo inInfo) {
        List<Ztree> ztrees = new ArrayList<Ztree>();
        Map map = new HashMap();
        map.put("fullIdLike", "6CACD8FD513C459B48257B19001C8770");
        int offset = 0;
        int limit = 1000;
        List<TkizlMaintainTecharea> list = new ArrayList<TkizlMaintainTecharea>();
        boolean flag = true;
        while (flag) {
            List<TkizlMaintainTecharea> query = businessKIZLMaintainTecharea.queryListPlus(map, offset, limit);
            if (query == null || query.size() <= 0)
                flag = false;
            list.addAll(query);
            offset = offset + limit;
        }

        if (list.size() > 0) {
            for (TkizlMaintainTecharea row : list) {
                Ztree ztree = new Ztree();
                ztree.setId(row.getAreaId());
                ztree.setpId(row.getParentId());
                ztree.setCode(row.getAreaId());
                ztree.setOpen(false);

				/*if (StrUtil.isNotBlank(values)) {
					List<String> asList = Arrays.asList(values.split(","));
					if (asList.contains(ztree.getId())) {
						ztree.setChecked(true);
					}
				}*/
                ztree.setName(row.getAreaName());
                ztree.setTitle(row.getFullName());
                ztrees.add(ztree);
            }
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("nodeInfo", ztrees);
        return outInfo;
    }


	/**
	*
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TkizlMaintainTecharea bean = businessKIZLMaintainTecharea.initBean(loginName);
            inInfo.set("maintainTecharea", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlMaintainTecharea> queryList = businessKIZLMaintainTecharea.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessKIZLMaintainTecharea.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkizlMaintainTecharea bean = BeanUtil.toBean(row, TkizlMaintainTecharea.class);
            businessKIZLMaintainTecharea.save(UserSession.getLoginName(), bean);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String areaId = (String) inInfo.get("areaId");
            TkizlMaintainTecharea query = businessKIZLMaintainTecharea.query(areaId);
            inInfo.set("maintainTecharea", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String areaId = (String) inInfo.get("areaId");
	        if (StrUtil.isNotBlank(areaId)) {
	            for (String id : areaId.split(",")) {
	                businessKIZLMaintainTecharea.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
}
