package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainAttachment;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_维护_附件信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLMaintainAttachment extends BusinessBase {

    private static final String NAME_SPACE = "tkizlMaintainAttachment";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlMaintainAttachment initBean(String operator) {
        TkizlMaintainAttachment maintainAttachment = new TkizlMaintainAttachment();
        return maintainAttachment;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlMaintainAttachment> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlMaintainAttachment> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param attId
     * @return
     */
    public TkizlMaintainAttachment query(String attId) {
        if (StringUtils.isBlank(attId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("attId", attId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainAttachment) query.get(0) : null;
    }

    /**
     * 根据附件编码查询
     *
     * @param attCode
     * @return
     */
    public TkizlMaintainAttachment queryByAttCode(String attCode) {
        if (StringUtils.isBlank(attCode)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("attCode", attCode);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainAttachment) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param maintainAttachment
     * @return
     */
    public TkizlMaintainAttachment insert(String operator, TkizlMaintainAttachment maintainAttachment) {
        maintainAttachment.initAdd(operator);
        if (StringUtils.isEmpty(maintainAttachment.getAttId())) {
            maintainAttachment.setAttId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", maintainAttachment);
        return maintainAttachment;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param maintainAttachment
     * @return
     */
    public TkizlMaintainAttachment update(String operator, TkizlMaintainAttachment maintainAttachment) {
        maintainAttachment.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", maintainAttachment);
        return maintainAttachment;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param attId
     * @return
     */
    public TkizlMaintainAttachment deleteLogin(String operator, String attId) {
        TkizlMaintainAttachment maintainAttachment = new TkizlMaintainAttachment();
        maintainAttachment.setAttId(attId);
        maintainAttachment.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", maintainAttachment);
        return maintainAttachment;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param attId
     * @return
     */
    public int delete(String operator, String attId) {
        return dao.delete(NAME_SPACE + ".delete", attId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param maintainAttachment
     * @return
     */
    public TkizlMaintainAttachment save(String operator, TkizlMaintainAttachment maintainAttachment) {
        if (StringUtils.isEmpty(maintainAttachment.getAttId())) {
            return insert(operator, maintainAttachment);
        } else {
            if (null == query(maintainAttachment.getAttId())) {
                return insert(operator, maintainAttachment);
            }
            return update(operator, maintainAttachment);
        }
    }

}
