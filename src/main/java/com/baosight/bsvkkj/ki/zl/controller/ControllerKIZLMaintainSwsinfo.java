package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainSwsinfo;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 境内专利_维护_事务所Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Controller
@RequestMapping("/kizl/maintainSwsinfo")
public class ControllerKIZLMaintainSwsinfo extends BaseController{
    private String prefix = "/kizl/maintainSwsinfo";
    
    @GetMapping()
    public String maintainSwsinfo(){
        return prefix + "/maintainSwsinfoList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询境内专利_维护_事务所列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainSwsinfo", "page");
		return getDataTable(query);
	}

    /**
     * 新增境内专利_维护_事务所
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("maintainSwsinfo", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLMaintainSwsinfo", "initLoad", "maintainSwsinfo"));
        return prefix + "/maintainSwsinfo";
    }

    /**
     * 修改境内专利_维护_事务所
     */
    @GetMapping("/edit/{swsId}")
    public String edit(@PathVariable("swsId") String swsId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("swsId", swsId);

        modelMap.put("maintainSwsinfo", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainSwsinfo", "load", "maintainSwsinfo"));
        return prefix + "/maintainSwsinfo";
    }

    /**
     * 保存境内专利_维护_事务所
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlMaintainSwsinfo maintainSwsinfo){
        EiInfo eiInfo = getEiInfo("i", maintainSwsinfo);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainSwsinfo", "save");
    }
    
    /**
     * 删除境内专利_维护_事务所
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("swsId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainSwsinfo", "remove");
	}

    /**
     * 导出境内专利_维护_事务所数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkizlMaintainSwsinfo> list = (List<TkizlMaintainSwsinfo>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainSwsinfo", "query", "list");
        ExcelUtil<TkizlMaintainSwsinfo> util = new ExcelUtil<>(TkizlMaintainSwsinfo.class);
        String title = "数据导出表";
		util.setSheet("境内专利_维护_事务所");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出境内专利_维护_事务所模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TkizlMaintainSwsinfo> listExcel = new ArrayList<>();
        ExcelUtil<TkizlMaintainSwsinfo> util = new ExcelUtil<>(TkizlMaintainSwsinfo.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }

    /**
     * 根据管理组织代码查询事务所信息
     * 用于前端下拉选择
     */
    @GetMapping("/queryByGldwCode/{gldwCode}")
    @ResponseBody
    public AjaxResult queryByGldwCode(@PathVariable("gldwCode") String gldwCode) {
        EiInfo eiInfo = new EiInfo();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("gldwCode", gldwCode);
        eiInfo.set(EiConstant.queryBlock, queryMap);
        
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainSwsinfo", "query");
        List<TkizlMaintainSwsinfo> list = (List<TkizlMaintainSwsinfo>) query.get("list");
        
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 获取所有事务所信息
     * 用于前端下拉选择
     */
    @GetMapping("/getAllSwsinfo")
    @ResponseBody
    public AjaxResult getAllSwsinfo() {
        EiInfo eiInfo = new EiInfo();
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainSwsinfo", "query");
        List<TkizlMaintainSwsinfo> list = (List<TkizlMaintainSwsinfo>) query.get("list");
        
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据swsGuid获取事务所详细信息
     * 用于前端自动填充代理人信息
     */
    @GetMapping("/getSwsDetailInfo/{swsGuid}")
    @ResponseBody
    public AjaxResult getSwsDetailInfo(@PathVariable("swsGuid") String swsGuid) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("swsId", swsGuid);
        
        TkizlMaintainSwsinfo swsInfo = (TkizlMaintainSwsinfo) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainSwsinfo", "load", "maintainSwsinfo");
        
        if (swsInfo != null) {
            return AjaxResult.success("查询成功", swsInfo);
        } else {
            return AjaxResult.error("未找到对应的事务所信息");
        }
    }
}
