package com.baosight.bsvkkj.ki.zl.strategy;

import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;

/**
 * 工作流活动策略接口
 * 用于处理不同工作流活动节点的业务逻辑
 * 
 * <AUTHOR>
 */
public interface WorkflowActivityStrategy {
    
    /**
     * 处理工作流活动
     * 
     * @param operator 操作人
     * @param beanEx 申请基本信息扩展对象
     * @return 工作流对象
     */
    WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx);
    
    /**
     * 获取支持的活动代码
     * 
     * @return 活动代码
     */
    String getSupportedActivity();
} 