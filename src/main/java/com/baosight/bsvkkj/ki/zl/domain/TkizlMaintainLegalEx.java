package com.baosight.bsvkkj.ki.zl.domain;

import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;
import lombok.Data;

@Data
public class TkizlMaintainLegalEx extends TkizlApplySqr {

    /** 节点ID */
    private String id;

    /** 节点父ID */
    private String pId;

    /** 节点名称 */
    private String name;

    /** 节点标题 */
    private String title;

    /** 是否勾选 */
    private boolean checked = false;

    /** 是否展开 */
    private boolean open = false;

    /** 是否能勾选 */
    private boolean nocheck = false;
}
