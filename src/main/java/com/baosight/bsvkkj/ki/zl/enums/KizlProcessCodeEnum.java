package com.baosight.bsvkkj.ki.zl.enums;

import java.util.Arrays;

/***
 * 流程相关信息
 */
public enum KizlProcessCodeEnum {
    KIZL_SB("KIZL","KIZLSB01","","/kizl/applyBaseinfo")
    ;

    private String businessType;
    private String processCode;
    private String processName;
    private String pathCode;

    KizlProcessCodeEnum(String businessType, String processCode, String processName, String pathCode) {
        this.businessType = businessType;
        this.processCode = processCode;
        this.processName = processName;
        this.pathCode = pathCode;
    }

    public String getPathCode() {
        return pathCode;
    }
    public void setPathCode(String pathCode) {
        this.pathCode = pathCode;
    }
    public String getBusinessType() {
        return businessType;
    }
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getProcessName() {
        return processName;
    }
    public void setProcessName(String processName) {
        this.processName = processName;
    }
    public String getProcessCode() {
        return processCode;
    }
    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    /**
     * 根据流程编码获取流程枚举
     *
     * @param processCode 流程编码
     * @return 流程枚举
     */
    public static KizlProcessCodeEnum getProcessCode(String processCode) {
        return Arrays.stream(values())
                .filter(processCodeEnum -> processCodeEnum.getProcessCode().equals(processCode))
                .findFirst()
                .orElse(null);
    }

}
