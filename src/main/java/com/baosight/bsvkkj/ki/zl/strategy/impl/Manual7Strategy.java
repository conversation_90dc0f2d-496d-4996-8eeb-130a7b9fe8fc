package com.baosight.bsvkkj.ki.zl.strategy.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLPatentInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Manual7活动节点策略实现
 * 交底
 * 
 * <AUTHOR>
 */
@Component
public class Manual7Strategy implements WorkflowActivityStrategy {
    
    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;
    
    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {

        TkizlPatentInfo patentInfo = beanEx.getPatentInfo();
        if (ObjectUtil.isNotEmpty(patentInfo)) {
            patentInfo.setPatentStatus(KIZLConstants.PatentStatus.IN_AGENCY);
            // 交底：保存专利信息
            businessKIZLPatentInfo.save(operator, beanEx.getPatentInfo());
        }
        return beanEx.getWorkFlow();
    }
    
    @Override
    public String getSupportedActivity() {
        return KizlWorkflowActivityEnum.MANUAL7.getCode();
    }
} 