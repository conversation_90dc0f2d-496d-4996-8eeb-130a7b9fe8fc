package com.baosight.bsvkkj.ki.zl.enums;

/**
 * 显示类型枚举
 */
public enum KizlDisplayTypeEnum {
    TYPE1("type1", "类型1", "申请部门、申报名称、联系人、当前状态、任务到达时间"),
    TYPE2("type2", "类型2", "接收编号、申报名称、主管部门操作人、下级部门、联系人");

    private final String code;
    private final String name;
    private final String description;

    KizlDisplayTypeEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
} 