package com.baosight.bsvkkj.ki.zl.strategy.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLPatentInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;

import javax.annotation.Resource;

/**
 * Manual10活动节点策略实现
 * 处理Manual10活动逻辑
 *
 * <AUTHOR>
 */
public class Manual10Strategy implements WorkflowActivityStrategy {

    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;

    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;

    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        // 处理Manual10活动逻辑
// 1. 保存申请基本信息
        this.businessKIZLApplyBaseinfo.doSave(operator, beanEx);
        // 2. 保存/更新专利信息
        TkizlPatentInfo patentInfo = beanEx.getPatentInfo();
        if (ObjectUtil.isNotEmpty(patentInfo)) {
            patentInfo.setPatentStatus(KIZLConstants.PatentStatus.AUTH);
            businessKIZLPatentInfo.save(operator, patentInfo);
        }
        // 返回处理后的工作流对象
        return beanEx.getWorkFlow();
    }
    @Override
    public String getSupportedActivity() {
        // 返回支持的活动代码
        return KizlWorkflowActivityEnum.MANUAL10.getCode();
    }
}
