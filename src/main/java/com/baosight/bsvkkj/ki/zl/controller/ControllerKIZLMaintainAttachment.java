package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainAttachment;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 境内专利_维护_附件信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Controller
@RequestMapping("/kizl/maintainAttachment")
public class ControllerKIZLMaintainAttachment extends BaseController{
    private String prefix = "/kizl/maintainAttachment";
    
    @GetMapping()
    public String maintainAttachment(){
        return prefix + "/maintainAttachmentList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询境内专利_维护_附件信息列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainAttachment", "page");
		return getDataTable(query);
	}

    /**
     * 新增境内专利_维护_附件信息
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("maintainAttachment", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLMaintainAttachment", "initLoad", "maintainAttachment"));
        return prefix + "/maintainAttachment";
    }

    /**
     * 修改境内专利_维护_附件信息
     */
    @GetMapping("/edit/{attId}")
    public String edit(@PathVariable("attId") String attId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("attId", attId);

        modelMap.put("maintainAttachment", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainAttachment", "load", "maintainAttachment"));
        return prefix + "/maintainAttachment";
    }

    /**
     * 保存境内专利_维护_附件信息
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlMaintainAttachment maintainAttachment){
        EiInfo eiInfo = getEiInfo("i", maintainAttachment);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainAttachment", "save");
    }
    
    /**
     * 删除境内专利_维护_附件信息
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("attId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainAttachment", "remove");
	}

    /**
     * 导出境内专利_维护_附件信息数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkizlMaintainAttachment> list = (List<TkizlMaintainAttachment>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainAttachment", "query", "list");
        ExcelUtil<TkizlMaintainAttachment> util = new ExcelUtil<>(TkizlMaintainAttachment.class);
        String title = "数据导出表";
		util.setSheet("境内专利_维护_附件信息");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出境内专利_维护_附件信息模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TkizlMaintainAttachment> listExcel = new ArrayList<>();
        ExcelUtil<TkizlMaintainAttachment> util = new ExcelUtil<>(TkizlMaintainAttachment.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }

    /**
     * 根据附件编码获取附件信息
     */
    @GetMapping("/getByAttCode/{attCode}")
    @ResponseBody
    public AjaxResult getByAttCode(@PathVariable("attCode") String attCode) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("attCode", attCode);
        EiInfo result = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainAttachment", "getByAttCode");
        
        if (result.getStatus() == EiConstant.STATUS_SUCCESS) {
            return AjaxResult.success(result.getMsg(), result.get("attachment"));
        } else {
            return AjaxResult.error(result.getMsg());
        }
    }

    /**
     * 获取所有附件信息列表
     */
    @GetMapping("/getAllAttachments")
    @ResponseBody
    public AjaxResult getAllAttachments() {
        EiInfo eiInfo = new EiInfo();
        EiInfo result = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainAttachment", "getAllAttachments");
        
        if (result.getStatus() == EiConstant.STATUS_SUCCESS) {
            return AjaxResult.success(result.getMsg(), result.get("attachments"));
        } else {
            return AjaxResult.error(result.getMsg());
        }
    }
}
