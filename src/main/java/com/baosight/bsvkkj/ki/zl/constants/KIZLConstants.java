package com.baosight.bsvkkj.ki.zl.constants;


public class KIZLConstants {

    /**
     * 申请单配置
     */
    public static final String BUSINESS_TYPE = "KIZL";

    /**
     * 专利类型-发明
     */
    public static final String FM = "FM";

    public static final String IS_STATR_PROCESS = "1";


    /**不同意交底*/
    public static final String TRANSITION13 = "Transition13";

    public static final String TRANSITION5 = "Transition5";

    public static final String TRANSITION8 = "Transition8";

    public static final String KIZL_ZLPS = "kizl_zlps";

    //删除状态（0存在，1删除）
    public static final String DELSTATUS_FLASE_0 = "0";

    /**
     * 主管部门操作意见
     */
    public static final class ZgbmOperation {
        /** 同意 */
        public static final String AGREE = "1";
        /** 不同意 */
        public static final String DISAGREE = "0";
    }
    
    /**
     * 流程状态
     */
    public static final class FlowStatus {
        /**
         * 终止
         */
        public static final String STOP = "stop";
        /**
         * 活跃
         */
        public static final String ACTIVE = "active";
        /**
         * 草稿
         */
        public static final String DRAFT = "draft";
    }


    
    /**
     * 法律状态
     */
    public static final class LegalStatus {
        /** 终止 */
        public static final String TERMINATED = "09";
        /** 活跃 */
        public static final String ACTIVE = "01";
        /** 待交底 */
        public static final String PENDING_DISCLOSURE = "03";
    }
    
    /**
     * 专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权
     */
    public static final class PatentStatus {
        /** 申请中 */
        public static final String APPLYING = "01";
        /** 待交底 */
        public static final String PENDING_DISCLOSURE = "02";
        /** 已交底 */
        public static final String DISCLOSED = "03";
        /** 代理中 */
        public static final String IN_AGENCY = "04";
        /** 已受理 */
        public static final String ACCEPTED = "05";
        /** 已授权 */
        public static final String AUTH = "06";
    }
    
    /**
     * 有效性标识
     */
    public static final class ValidFlag {
        /** 有效 */
        public static final String VALID = "1";
        /** 无效 */
        public static final String INVALID = "0";
    }
    
    /**
     * 人员类型
     */
    public static final class PersonType {
        /** 宝武人员 */
        public static final String BAOWU = "01";
        /** 非宝武人员 */
        public static final String NON_BAOWU = "02";
    }

    public static final String KIZL_ALL_READALL = "KIZL_ALL_READALL";

    // 按优先级检查各种角色权限
    public static String[] roleHierarchy = {
            "KIZL_ZH_ADMIN",
            "KIZL_DW_ADMIN",
            "KIZL_DW_LEADER",
            "KIZL_ZH_LEADER"
    };

}
