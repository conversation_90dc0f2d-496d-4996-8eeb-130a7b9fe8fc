package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlApplyZjps;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_申请_专家评审
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLApplyZjps extends BusinessBase {

    private static final String NAME_SPACE = "tkizlApplyZjps";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlApplyZjps initBean(String operator) {
        TkizlApplyZjps applyZjps = new TkizlApplyZjps();
        return applyZjps;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlApplyZjps> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlApplyZjps> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param ryxxId
     * @return
     */
    public TkizlApplyZjps query(String ryxxId) {
        if (StringUtils.isBlank(ryxxId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("ryxxId", ryxxId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlApplyZjps) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param applyZjps
     * @return
     */
    public TkizlApplyZjps insert(String operator, TkizlApplyZjps applyZjps) {
        applyZjps.initAdd(operator);
        if (StringUtils.isEmpty(applyZjps.getRyxxId())) {
            applyZjps.setRyxxId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", applyZjps);
        return applyZjps;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param applyZjps
     * @return
     */
    public TkizlApplyZjps update(String operator, TkizlApplyZjps applyZjps) {
        applyZjps.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", applyZjps);
        return applyZjps;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param ryxxId
     * @return
     */
    public TkizlApplyZjps deleteLogin(String operator, String ryxxId) {
        TkizlApplyZjps applyZjps = new TkizlApplyZjps();
        applyZjps.setRyxxId(ryxxId);
        applyZjps.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", applyZjps);
        return applyZjps;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param ryxxId
     * @return
     */
    public int delete(String operator, String ryxxId) {
        return dao.delete(NAME_SPACE + ".delete", ryxxId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param applyZjps
     * @return
     */
    public TkizlApplyZjps save(String operator, TkizlApplyZjps applyZjps) {
        if (StringUtils.isEmpty(applyZjps.getRyxxId())) {
            return insert(operator, applyZjps);
        } else {
            if (null == query(applyZjps.getRyxxId())) {
                return insert(operator, applyZjps);
            }
            return update(operator, applyZjps);
        }
    }

}
