package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 境内专利_申请_人员信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Controller
@RequestMapping("/kizl/applyRyxx")
public class ControllerKIZLApplyRyxx extends BaseController{
    private String prefix = "/kizl/applyRyxx";
    
    @GetMapping()
    public String applyRyxx(){
        return prefix + "/applyRyxxList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询境内专利_申请_人员信息列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLApplyRyxx", "page");
		return getDataTable(query);
	}

    /**
     * 新增境内专利_申请_人员信息
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("applyRyxx", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLApplyRyxx", "initLoad", "applyRyxx"));
        return prefix + "/applyRyxx";
    }

    /**
     * 修改境内专利_申请_人员信息
     */
    @GetMapping("/edit/{ryxxId}")
    public String edit(@PathVariable("ryxxId") String ryxxId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("ryxxId", ryxxId);

        modelMap.put("applyRyxx", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyRyxx", "load", "applyRyxx"));
        return prefix + "/applyRyxx";
    }

    /**
     * 保存境内专利_申请_人员信息
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlApplyRyxx applyRyxx){
        EiInfo eiInfo = getEiInfo("i", applyRyxx);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLApplyRyxx", "save");
    }
    
    /**
     * 删除境内专利_申请_人员信息
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("ryxxId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLApplyRyxx", "remove");
	}

    /**
     * 导出境内专利_申请_人员信息数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkizlApplyRyxx> list = (List<TkizlApplyRyxx>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyRyxx", "query", "list");
        ExcelUtil<TkizlApplyRyxx> util = new ExcelUtil<>(TkizlApplyRyxx.class);
        String title = "数据导出表";
		util.setSheet("境内专利_申请_人员信息");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出境内专利_申请_人员信息模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TkizlApplyRyxx> listExcel = new ArrayList<>();
        ExcelUtil<TkizlApplyRyxx> util = new ExcelUtil<>(TkizlApplyRyxx.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }
}
