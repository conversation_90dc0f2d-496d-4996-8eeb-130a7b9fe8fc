package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLWorkFlow;
import com.baosight.bsvkkj.ki.zl.enums.KizlDisplayTypeEnum;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ServiceKIZLWorkFlow extends PageService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BusinessKIZLWorkFlow businessKIZLWorkFlow;

    /**
     * 模块代办列表
     * @return
     */
    public EiInfo queryModuleDbList(EiInfo inInfo) {
        try {
            String operator = inInfo.getString("operator");
            if (StringUtils.isEmpty(operator)) {
                operator = UserSession.getLoginName();
            }
            String businessType = inInfo.getCellStr(EiConstant.queryBlock, 0, "businessType");
            List<Map<String, Object>> list = businessKIZLWorkFlow.queryModuleDbList(businessType, operator);
            inInfo.set("businessType", businessType);
            inInfo.addRows(EiConstant.resultBlock, list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getStackTrace());
            throw new PlatException(e.getMessage());
        }
        return inInfo;
    }


    public EiInfo pageDB(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("assigneeId", loginName);
            queryData.put("businessType", "'"+queryData.get("businessType")+"'");
            queryData.put("displayOrder", "A.UPDATE_DATE DESC");
            TableDataInfo queryPage = businessKIZLWorkFlow.queryPage(queryData);
            
            // 根据流程节点设置显示字段
            String currentActivity = (String) queryData.get("currentActivity");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rows = (List<Map<String, Object>>) queryPage.getRows();
            if (rows != null) {
                for (Map<String, Object> row : rows) {
                    String activity = (String) row.get("currentActivity");
                    if (activity != null) {
                        // 使用枚举设置显示字段标识
                        if (KizlWorkflowActivityEnum.isType1Display(activity)) {
                            row.put("displayType", KizlDisplayTypeEnum.TYPE1.getCode());
                        } else if (KizlWorkflowActivityEnum.isType2Display(activity)) {
                            row.put("displayType", KizlDisplayTypeEnum.TYPE2.getCode());
                        } else {
                            // 默认显示类型1
                            row.put("displayType", KizlDisplayTypeEnum.TYPE1.getCode());
                        }
                    }
                }
            }
            
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 获取下一步提交的工作流信息
     * @param inInfo 输入信息，包含工作流信息
     * @return 更新后的输入信息，包含下一步工作流信息
     */
    public EiInfo getNextSubmitWF(EiInfo inInfo) {
        try {
            WorkFlow workFlow = (WorkFlow) inInfo.get("workFlow");
            if (StringUtils.isEmpty(workFlow.getTaskId())) {
                workFlow = SWorkFlowUtil.getNextStartAndSubmitWF(workFlow);
            } else {
                workFlow = SWorkFlowUtil.getNextSubmitWF(workFlow);
            }
            inInfo.set("workFlow", workFlow);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 流程退回
     * @param inInfo 输入信息，包含工作流信息
     * @return 更新后的输入信息，包含操作结果
     */
    public EiInfo returnWF(EiInfo inInfo) {
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            WorkFlow workFlow = (WorkFlow) inInfo.get("workFlow");
            inInfo.setMsg( SWorkFlowUtil.doReturn(operator, workFlow));
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

}
