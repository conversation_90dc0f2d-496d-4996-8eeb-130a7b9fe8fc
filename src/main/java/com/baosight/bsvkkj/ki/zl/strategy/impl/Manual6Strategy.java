package com.baosight.bsvkkj.ki.zl.strategy.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLPatentInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Manual6活动节点策略实现
 * 待交底
 * 
 * <AUTHOR>
 */
@Component
public class Manual6Strategy implements WorkflowActivityStrategy {
    
    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    
    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;
    
    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        WorkFlow workFlow = beanEx.getWorkFlow();
        TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(workFlow.getBusinessId());
        TkizlPatentInfo patentInfo = beanEx.getPatentInfo();
        // 待交底
        if (ObjectUtil.isNotEmpty(beanEx.getWorkFlow()) && 
            StrUtil.equals(beanEx.getWorkFlow().getTransitionKey(), KIZLConstants.TRANSITION13)) {
            // 流程终止，更新申请表的flowStatus为终止
            query.setFlowStatus(KIZLConstants.FlowStatus.STOP);
            patentInfo.setFlzt(KIZLConstants.LegalStatus.TERMINATED); // 法律状态：09-终止
            patentInfo.setFlowStatus(KIZLConstants.FlowStatus.STOP); // 流程状态：终止
            patentInfo.setPatentStatus(KIZLConstants.PatentStatus.PENDING_DISCLOSURE); // 专利状态：03-待交底
            patentInfo.setIsvalid(KIZLConstants.ValidFlag.INVALID); // 专利信息无效
        } else {
            workFlow.setTransitionKey(KIZLConstants.TRANSITION8);
        }
        businessKIZLApplyBaseinfo.update(operator, query);
        businessKIZLPatentInfo.save(operator, patentInfo);
        
        return workFlow;
    }
    
    @Override
    public String getSupportedActivity() {
        return KizlWorkflowActivityEnum.MANUAL6.getCode();
    }
} 