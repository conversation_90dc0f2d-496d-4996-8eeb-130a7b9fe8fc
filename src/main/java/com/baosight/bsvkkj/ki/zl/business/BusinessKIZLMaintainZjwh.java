package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainZjwh;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_维护_专家维护
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLMaintainZjwh extends BusinessBase {

    private static final String NAME_SPACE = "tkizlMaintainZjwh";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlMaintainZjwh initBean(String operator) {
        TkizlMaintainZjwh maintainZjwh = new TkizlMaintainZjwh();
        return maintainZjwh;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlMaintainZjwh> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlMaintainZjwh> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param moneyId
     * @return
     */
    public TkizlMaintainZjwh query(String moneyId) {
        if (StringUtils.isBlank(moneyId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("moneyId", moneyId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainZjwh) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param maintainZjwh
     * @return
     */
    public TkizlMaintainZjwh insert(String operator, TkizlMaintainZjwh maintainZjwh) {
        maintainZjwh.initAdd(operator);
        if (StringUtils.isEmpty(maintainZjwh.getMoneyId())) {
            maintainZjwh.setMoneyId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", maintainZjwh);
        return maintainZjwh;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param maintainZjwh
     * @return
     */
    public TkizlMaintainZjwh update(String operator, TkizlMaintainZjwh maintainZjwh) {
        maintainZjwh.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", maintainZjwh);
        return maintainZjwh;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param moneyId
     * @return
     */
    public TkizlMaintainZjwh deleteLogin(String operator, String moneyId) {
        TkizlMaintainZjwh maintainZjwh = new TkizlMaintainZjwh();
        maintainZjwh.setMoneyId(moneyId);
        maintainZjwh.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", maintainZjwh);
        return maintainZjwh;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param moneyId
     * @return
     */
    public int delete(String operator, String moneyId) {
        return dao.delete(NAME_SPACE + ".delete", moneyId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param maintainZjwh
     * @return
     */
    public TkizlMaintainZjwh save(String operator, TkizlMaintainZjwh maintainZjwh) {
        if (StringUtils.isEmpty(maintainZjwh.getMoneyId())) {
            return insert(operator, maintainZjwh);
        } else {
            if (null == query(maintainZjwh.getMoneyId())) {
                return insert(operator, maintainZjwh);
            }
            return update(operator, maintainZjwh);
        }
    }

}
