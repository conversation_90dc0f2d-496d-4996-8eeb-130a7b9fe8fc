package com.baosight.bsvkkj.ki.zl.business;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.exception.BusinessException;

/**
 * 境内专利_专利信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLPatentInfo extends BusinessBase {

    private static final String NAME_SPACE = "tkizlPatentInfo";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlPatentInfo initBean(String operator) {
        TkizlPatentInfo patentInfo = new TkizlPatentInfo();
        return patentInfo;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlPatentInfo> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlPatentInfo> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param patentId
     * @return
     */
    public TkizlPatentInfo query(String patentId) {
        if (StringUtils.isBlank(patentId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("patentId", patentId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlPatentInfo) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param patentInfo
     * @return
     */
    public TkizlPatentInfo insert(String operator, TkizlPatentInfo patentInfo) {
        patentInfo.initAdd(operator);
        if (StringUtils.isEmpty(patentInfo.getPatentId())) {
            patentInfo.setPatentId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", patentInfo);
        return patentInfo;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param patentInfo
     * @return
     */
    public TkizlPatentInfo update(String operator, TkizlPatentInfo patentInfo) {
        patentInfo.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", patentInfo);
        return patentInfo;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param patentId
     * @return
     */
    public TkizlPatentInfo deleteLogin(String operator, String patentId) {
        TkizlPatentInfo patentInfo = new TkizlPatentInfo();
        patentInfo.setPatentId(patentId);
        patentInfo.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", patentInfo);
        return patentInfo;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param patentId
     * @return
     */
    public int delete(String operator, String patentId) {
        return dao.delete(NAME_SPACE + ".delete", patentId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param patentInfo
     * @return
     */
    public TkizlPatentInfo save(String operator, TkizlPatentInfo patentInfo) {
        if (StringUtils.isEmpty(patentInfo.getPatentId())) {
            return insert(operator, patentInfo);
        } else {
            if (null == query(patentInfo.getPatentId())) {
                return insert(operator, patentInfo);
            }
            return update(operator, patentInfo);
        }
    }

    /**
     * 根据申请ID查询专利信息
     *
     * @param applyId 申请ID
     * @return 专利信息对象
     */
    public TkizlPatentInfo queryByApplyId(String applyId) {
        if (StringUtils.isBlank(applyId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("applyId", applyId);
        List<TkizlPatentInfo> query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? query.get(0) : null;
    }

    /**
     * 保存专利信息
     * 将申请基本信息转换并保存到专利信息表
     *
     * @param operator 操作人
     * @param beanEx 申请基本信息扩展对象
     * @return 保存的专利信息对象
     */
    public TkizlPatentInfo savePatentInfo(String operator, TkizlApplyBaseinfo beanEx) {
        // 检查是否已存在专利信息记录
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("applyId", beanEx.getApplyId());
        List<TkizlPatentInfo> existingList = this.queryList(queryParam);
        
        TkizlPatentInfo patentInfo;
        if (ObjectUtil.isNotEmpty(existingList)) {
            // 如果已存在，更新现有记录
            patentInfo = existingList.get(0);
        } else {
            // 如果不存在，创建新记录
            patentInfo = new TkizlPatentInfo();
            patentInfo.setPatentId(BizIdUtil.INSTANCE.nextId());
        }
        // 从申请信息复制基本字段到专利信息
        BeanUtil.copyProperties(beanEx, patentInfo);
        patentInfo.setPatentName(beanEx.getApplyName());
        // 设置专利状态为申请中
        patentInfo.setPatentStatus(KIZLConstants.PatentStatus.APPLYING); // 01-申请中
        // 设置法律状态为申请中
        patentInfo.setFlzt(KIZLConstants.PatentStatus.APPLYING); // 01-申请中
        // 设置有效状态
        patentInfo.setIsvalid(KIZLConstants.ValidFlag.VALID); // 1-有效
        // 保存专利信息
        if (ObjectUtil.isNotEmpty(existingList)) {
            return this.update(operator, patentInfo);
        } else {
            return this.insert(operator, patentInfo);
        }
    }

    /**
     * 专利综合查询 - 分页查询
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    public TableDataInfo queryComprehensivePage(Map<String, Object> param) {
        return getPage("KIZLPatenInfo.queryPatentAll", param);
    }

    public List<Map<String, Object>> queryComprehensiveList(Map<String, Object> param) {
        return dao.query( "KIZLPatenInfo.queryPatentAll", param);
    }

}
