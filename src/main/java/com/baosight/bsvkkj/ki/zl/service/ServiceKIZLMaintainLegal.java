package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;
import com.baosight.bsvkkj.ki.zl.domain.TkizlMaintainLegalEx;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLMaintainLegal;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainLegal;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 境内专利_维护_申请人信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class ServiceKIZLMaintainLegal extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessKIZLMaintainLegal businessKIZLMaintainLegal;

	/**
	*
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TkizlMaintainLegal bean = businessKIZLMaintainLegal.initBean(loginName);
            inInfo.set("maintainLegal", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlMaintainLegal> queryList = businessKIZLMaintainLegal.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessKIZLMaintainLegal.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkizlMaintainLegal bean = BeanUtil.toBean(row, TkizlMaintainLegal.class);
            businessKIZLMaintainLegal.save(UserSession.getLoginName(), bean);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String legalId = (String) inInfo.get("legalId");
            TkizlMaintainLegal query = businessKIZLMaintainLegal.query(legalId);
            inInfo.set("maintainLegal", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String legalId = (String) inInfo.get("legalId");
	        if (StrUtil.isNotBlank(legalId)) {
	            for (String id : legalId.split(",")) {
	                businessKIZLMaintainLegal.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo tableData(EiInfo inInfo) {
        List<TkizlMaintainLegalEx> newList = new ArrayList<TkizlMaintainLegalEx>();
        List<TkizlMaintainLegal> query = businessKIZLMaintainLegal.queryList(new HashMap());
        if (query.size() > 0) {
            //第一层组织
            TkizlMaintainLegalEx firstQuery = new TkizlMaintainLegalEx();
            firstQuery.setId(query.get(0).getDeptFirst());
            firstQuery.setPId("00");
            firstQuery.setOpen(false);
            firstQuery.setName(query.get(0).getDeptFirstName());
            firstQuery.setTitle(query.get(0).getDeptFirstName());
            newList.add(firstQuery);
//            for (TkizlMaintainLegal row : query) {
//                if (row.getDeptSecond() != null) {
//                    //第二层组织
//                    TkizlMaintainLegalEx zquery = new TkizlMaintainLegalEx();
//                    zquery.setId(row.getDeptSecond());
//                    zquery.setPId(row.getDeptFirst());
//                    zquery.setOpen(false);
//                    zquery.setName(row.getDeptSecondName());
//                    zquery.setTitle(row.getDeptSecondName());
//                    newList.add(zquery);
//                    if (row.getDeptThird() != null) {
//                        //第三层组织
//                        zquery.setId(row.getDeptThird());
//                        zquery.setPId(row.getDeptSecond());
//                        zquery.setOpen(false);
//                        zquery.setName(row.getDeptThirdName());
//                        zquery.setTitle(row.getDeptThirdName());
//                        newList.add(zquery);
//                    }
//                }
//            }
        }
        List<TkizlMaintainLegalEx> ztrees = newList.stream().distinct().collect(Collectors.toList());
        EiInfo outInfo = new EiInfo();
        outInfo.set("nodeInfo", ztrees);
        return outInfo;
    }

    public EiInfo lxrData(EiInfo inInfo) {
        Map map = (Map) inInfo.get("map");
        String treeId = map.get("treeId")+"";
        //获取申请人通讯地址、邮编、付费联系人、联系电话
        Map nodeInfo = new HashMap();
        Map param = new HashMap();
        param.put("extra1", treeId);
        List<TkizlMaintainLegal> list = businessKIZLMaintainLegal.queryList(param);
        if(list.size()>0) {
            nodeInfo.put("mailAddress", list.get(0).getMailAddress());
            nodeInfo.put("postOffice", list.get(0).getPostOffice());
            nodeInfo.put("lxrMoney", list.get(0).getLxrMoney());
            nodeInfo.put("lxrPhone", list.get(0).getLxrPhone());
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("nodeInfo", nodeInfo);
        return outInfo;
    }

}
