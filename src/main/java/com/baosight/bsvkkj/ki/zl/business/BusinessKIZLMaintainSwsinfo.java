package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainSwsinfo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_维护_事务所
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLMaintainSwsinfo extends BusinessBase {

    private static final String NAME_SPACE = "tkizlMaintainSwsinfo";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlMaintainSwsinfo initBean(String operator) {
        TkizlMaintainSwsinfo maintainSwsinfo = new TkizlMaintainSwsinfo();
        return maintainSwsinfo;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlMaintainSwsinfo> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlMaintainSwsinfo> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param swsId
     * @return
     */
    public TkizlMaintainSwsinfo query(String swsId) {
        if (StringUtils.isBlank(swsId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("swsId", swsId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainSwsinfo) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param maintainSwsinfo
     * @return
     */
    public TkizlMaintainSwsinfo insert(String operator, TkizlMaintainSwsinfo maintainSwsinfo) {
        maintainSwsinfo.initAdd(operator);
        if (StringUtils.isEmpty(maintainSwsinfo.getSwsId())) {
            maintainSwsinfo.setSwsId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", maintainSwsinfo);
        return maintainSwsinfo;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param maintainSwsinfo
     * @return
     */
    public TkizlMaintainSwsinfo update(String operator, TkizlMaintainSwsinfo maintainSwsinfo) {
        maintainSwsinfo.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", maintainSwsinfo);
        return maintainSwsinfo;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param swsId
     * @return
     */
    public TkizlMaintainSwsinfo deleteLogin(String operator, String swsId) {
        TkizlMaintainSwsinfo maintainSwsinfo = new TkizlMaintainSwsinfo();
        maintainSwsinfo.setSwsId(swsId);
        maintainSwsinfo.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", maintainSwsinfo);
        return maintainSwsinfo;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param swsId
     * @return
     */
    public int delete(String operator, String swsId) {
        return dao.delete(NAME_SPACE + ".delete", swsId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param maintainSwsinfo
     * @return
     */
    public TkizlMaintainSwsinfo save(String operator, TkizlMaintainSwsinfo maintainSwsinfo) {
        if (StringUtils.isEmpty(maintainSwsinfo.getSwsId())) {
            return insert(operator, maintainSwsinfo);
        } else {
            if (null == query(maintainSwsinfo.getSwsId())) {
                return insert(operator, maintainSwsinfo);
            }
            return update(operator, maintainSwsinfo);
        }
    }

}
