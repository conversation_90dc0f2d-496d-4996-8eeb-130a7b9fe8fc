package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.Enumeration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Date;
import java.text.SimpleDateFormat;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SRoleUtil;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletRequest;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import java.util.LinkedHashMap;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;

/**
 * 境内专利_专利信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Controller
@RequestMapping("/kizl/patentInfo")
public class ControllerKIZLPatentInfo extends BaseController {
    private final SRoleUtil sRoleUtil;
    private String prefix = "/kizl/patentInfo";

    public ControllerKIZLPatentInfo(SRoleUtil sRoleUtil) {
        super();
        this.sRoleUtil = sRoleUtil;
    }


    @GetMapping()
    public String patentInfo() {
        return prefix + "/patentInfoList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix + "/" + pageNo;
    }

    /**
     * 查询-专利状态维护-专利列表
     */
    @PostMapping("/statusMaintenanceList")
    @ResponseBody
    public TableDataInfo statusMaintenanceList(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        List<ADOrg> adOrgs = SRoleUtil.getOrgByUser(UserSession.getLoginName(), "KIZL_ZHLADMIN");
        if (ObjectUtil.isNotEmpty(adOrgs)) {
            StringBuffer dynSql = new StringBuffer("(");
            for (ADOrg adOrg : adOrgs) {
                if (dynSql.length() > 1) {
                    dynSql.append(" OR ");
                }
                dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
            }
            dynSql.append(")");
            map.put("dynSql", dynSql.toString());
        }
        String dynSql = MapUtil.getStr(map, "dynSql");
        map.put("dynSql", StrUtil.isNotBlank(dynSql) ? dynSql + " AND FLZT IN ('02','20','10')" : "FLZT IN ('02','20','10')");
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLPatentInfo", "page");
        return getDataTable(query);
    }

    /**
     * 查询境内专利_专利信息列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLPatentInfo", "page");
        return getDataTable(query);
    }

    /**
     * 新增境内专利_专利信息
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap) {
        modelMap.put("patentInfo", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLPatentInfo", "initLoad", "patentInfo"));
        return prefix + "/patentInfo";
    }

    /**
     * 修改境内专利_专利信息
     */
    @GetMapping("/edit/{patentId}")
    public String edit(@PathVariable("patentId") String patentId, ModelMap modelMap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("patentId", patentId);

        modelMap.put("patentInfo", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLPatentInfo", "load", "patentInfo"));
        return prefix + "/patentInfo";
    }


    /**
     * 保存境内专利_专利信息
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlPatentInfo patentInfo) {
        EiInfo eiInfo = getEiInfo("i", patentInfo);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLPatentInfo", "save");
    }

    /**
     * 删除境内专利_专利信息
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String patentIds) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("patentId", patentIds);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLPatentInfo", "remove");
    }

    /**
     * 导出境内专利_专利信息数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
        // 获取选择的导出列
        String columnsParam = (String) map.get("columns");
        List<String> selectedColumns = new ArrayList<>();
        if (StrUtil.isNotBlank(columnsParam)) {
            selectedColumns = Arrays.asList(columnsParam.split(","));
        }

        // 如果有选择的列，使用综合查询导出
        if (!selectedColumns.isEmpty()) {
            comprehensiveExportInternal(map, selectedColumns, response);
            return;
        }

        // 默认导出方式
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        List<TkizlPatentInfo> list = (List<TkizlPatentInfo>) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLPatentInfo", "query", "list");
        ExcelUtil<TkizlPatentInfo> util = new ExcelUtil<>(TkizlPatentInfo.class);
        String title = "专利信息导出";
        util.setSheet("境内专利_专利信息");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 综合查询页面导出专利信息数据
     */
    @RequestMapping(value = "/comprehensiveExport", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public void comprehensiveExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取查询参数
        Map<String, Object> params = new HashMap<>();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            if (!"columns".equals(paramName)) {
                params.put(paramName, request.getParameter(paramName));
            }
        }

        // 获取选择的导出列
        String columnsParam = request.getParameter("columns");
        List<String> selectedColumns = new ArrayList<>();
        if (StrUtil.isNotBlank(columnsParam)) {
            selectedColumns = Arrays.asList(columnsParam.split(","));
        }

        comprehensiveExportInternal(params, selectedColumns, response);
    }

    /**
     * 内部综合导出方法
     */
    private void comprehensiveExportInternal(Map<String, Object> params, List<String> selectedColumns, HttpServletResponse response) throws IOException {
        // 查询数据
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, params);
        List<Map<String, Object>> list = (List<Map<String, Object>>) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLPatentInfo", "queryComprehensive", "list");

        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("专利综合查询结果");

        // 创建标题行样式
        XSSFCellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        XSSFFont headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 数据行样式
        XSSFCellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 定义列映射
        Map<String, String> columnMapping = getColumnMapping();

        // 如果没有选择列，默认选择所有列
        if (selectedColumns.isEmpty()) {
            selectedColumns = new ArrayList<>(columnMapping.keySet());
        }

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        int colIndex = 0;
        for (String column : selectedColumns) {
            if (columnMapping.containsKey(column)) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(columnMapping.get(column));
                cell.setCellStyle(headerStyle);
            }
        }

        // 填充数据
        if (list != null && !list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> data = list.get(i);
                Row dataRow = sheet.createRow(i + 1);
                colIndex = 0;

                for (String column : selectedColumns) {
                    if (columnMapping.containsKey(column)) {
                        Cell cell = dataRow.createCell(colIndex++);
                        Object value = getColumnValue(data, column, i + 1);
                        if (value != null) {
                            cell.setCellValue(value.toString());
                        }
                        cell.setCellStyle(dataStyle);
                    }
                }
            }
        }

        // 自动调整列宽
        for (int i = 0; i < selectedColumns.size(); i++) {
            sheet.autoSizeColumn(i);
            // 设置最大宽度限制
            int columnWidth = sheet.getColumnWidth(i);
            if (columnWidth > 15000) {
                sheet.setColumnWidth(i, 15000);
            }
        }

        // 设置响应头 - 兼容exportExcelBit方法
        String filename = "专利综合查询结果_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(filename, "UTF-8"));
        response.setHeader("download-filename", java.net.URLEncoder.encode(filename, "UTF-8"));

        // 输出文件
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 获取列映射关系
     */
    private Map<String, String> getColumnMapping() {
        Map<String, String> columnMapping = new LinkedHashMap<>();
        columnMapping.put("index", "序号");
        columnMapping.put("jsbh", "接收编号");
        columnMapping.put("egbh", "鄂钢编号");
        columnMapping.put("patentName", "专利申报名称");
        columnMapping.put("patentStatus", "最新状态");
        columnMapping.put("patentType", "专利类型");
        columnMapping.put("patentNo", "申请号");
        columnMapping.put("slrq", "申请日");
        columnMapping.put("sqrq", "授权日");
        columnMapping.put("updateDate", "最新处理日期");
        columnMapping.put("fromNo", "项目编号");
        columnMapping.put("fromType", "项目类别");
        columnMapping.put("fromName", "项目名称");
        columnMapping.put("flzt", "法律状态");
        columnMapping.put("inventorNames", "发明人");
        columnMapping.put("inventorCoefficients", "发明人系数");
        columnMapping.put("firstDeptName", "申请部门");
        columnMapping.put("useDeptDept", "实施区域");
        columnMapping.put("useFirstdate", "实施初始时间");
        columnMapping.put("swsName", "代理事务所");
        columnMapping.put("swsdlr", "代理人");
        columnMapping.put("applicantNames", "申请人");
        return columnMapping;
    }

    /**
     * 获取列值
     */
    private Object getColumnValue(Map<String, Object> data, String column, int index) {
        if ("index".equals(column)) {
            return index;
        }
        return data.get(column);
    }

    /**
     * 查看专利详情
     */
    @GetMapping("/viewDetail")
    public String viewDetail(@RequestParam Map<String, Object> map, ModelMap model) {
        String businessId = MapUtil.getStr(map, "applyId");
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", businessId);
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryDBDetail", "applyBaseinfo");
        model.put("applyBaseinfo", beanEx);
        model.put("businessId", businessId);
        return prefix + "/KIZLZLDT";
    }
}
