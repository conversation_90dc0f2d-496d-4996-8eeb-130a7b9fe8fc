package com.baosight.bsvkkj.ki.zl.controller;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Controller
@RequestMapping("/kizl/c")
public class ControllerKIZLRouter extends BaseController {

	private String prefix = "/kizl/";

	//到页面 toPage
	@GetMapping("/p")
	public String toList(HttpServletRequest req, ModelMap map) {
		//1、参数
		Map<String, Object> param = getParamMap(req);
		
		//2、返回值
		map.put("loginName", UserSession.getLoginName());
		map.putAll(param); 
		
		//3、跳转路径
		String pagePath = req.getParameter("pagePath");
		String pageNo = req.getParameter("pageNo");
		return prefix + pagePath + "/" +pageNo;
	}

	//到编辑页面loadDetail toEdit or toDetail
	@GetMapping("/l")
	public String toEdit(HttpServletRequest req, ModelMap map) {
		//1、参数
		String pageNo = req.getParameter("pageNo");
		Map<String, Object> param = getParamMap(req);
		
		//2、调用服务
		EiInfo inInfo = new EiInfo();
		inInfo.addRow("i", param);
		inInfo.set("pageNo", pageNo);
	    inInfo.set(EiConstant.serviceName, req.getParameter("serviceName")); //设置服务名
	    inInfo.set(EiConstant.methodName, req.getParameter("methodName"));//设置方法名
	    EiInfo outInfo = XLocalManager.call(inInfo);

	    //3、返回值
	    map.put("applyBaseinfo", outInfo.get("applyBaseinfo"));

	    //4、跳转路径
		String pagePath = req.getParameter("pagePath");
		return prefix + pagePath + "/" +pageNo;
	}

	//获取分页数据
	@PostMapping("/d")
	@ResponseBody
	public TableDataInfo pageList(HttpServletRequest req, @RequestBody Map<String, Object> map) {
		//1、参数
		Map<String, Object> param = getParamMap(req);
		param.putAll(map);
		
		//2、调用服务
		EiInfo inInfo = new EiInfo();
		inInfo.addRow("i", param);
	    inInfo.set(EiConstant.serviceName, req.getParameter("serviceName")); //设置服务名
	    inInfo.set(EiConstant.methodName, req.getParameter("methodName"));//设置方法名
	    EiInfo outInfo = XLocalManager.call(inInfo);
	    
	    //3、返回值
	    //pageData 简称pd
	    TableDataInfo tdi = (TableDataInfo) outInfo.get("pd");
	    if(tdi==null) {
	    	tdi = getDataTable(outInfo);
	    }
		return tdi;
	}

	//保存 提交(新增 删除 修改)数据
	@PostMapping("/s")
	@ResponseBody
	public AjaxResult save(HttpServletRequest req, ModelMap map) {
		//1、参数
		Map<String, Object> param = getParamMap(req);
		param.putAll(map);
		
		//2、调用服务
		EiInfo inInfo = new EiInfo();
		inInfo.addRow("i", param);
		inInfo.set("pageNo", req.getParameter("pageNo"));
	    inInfo.set(EiConstant.serviceName, req.getParameter("serviceName")); //设置服务名
	    inInfo.set(EiConstant.methodName, req.getParameter("methodName"));//设置方法名
	    EiInfo outInfo = XLocalManager.callNewTx(inInfo);
	    
	    //3、返回值
	    int status = outInfo.getStatus();
	    if(status==EiConstant.STATUS_SUCCESS)
	    	status = 0;
	    AjaxResult ar = new AjaxResult(status, outInfo.getMsg(), outInfo);
	    
		return ar;
	}
	
	//校验、提示、查询数据
	@PostMapping("/v")
	@ResponseBody
	public AjaxResult validate(HttpServletRequest req, ModelMap map) {
		//1、参数
		Map<String, Object> param = getParamMap(req);
		
		//2、调用服务
		EiInfo inInfo = new EiInfo();
		inInfo.addRow("i", param);
		inInfo.set("pageNo", req.getParameter("pageNo"));
	    inInfo.set(EiConstant.serviceName, req.getParameter("serviceName")); //设置服务名
	    inInfo.set(EiConstant.methodName, req.getParameter("methodName"));//设置方法名
	    EiInfo outInfo = XLocalManager.call(inInfo);
	    
	    //3、返回值
	    int status = outInfo.getStatus();
	    if(status==EiConstant.STATUS_SUCCESS)
	    	status = 0;
	    AjaxResult ar = new AjaxResult(status, outInfo.getMsg(), outInfo);
	    
		return ar;
	}

	//私有方法
	private Map<String, Object> getParamMap(HttpServletRequest req) {
		Map<String, Object> param = new HashMap<String, Object>(); 
		Map<String, String[]> parameterMap = req.getParameterMap();
		Set<String> keySet = parameterMap.keySet();
		for (String key : keySet) {
			if(parameterMap.get(key).length==1) {
				param.put(key, parameterMap.get(key)[0]);
			}else {
				param.put(key, StringUtils.joinWith(",", parameterMap.get(key)));
			}
		}
		return param;
	}
	
	//导出excel
	@PostMapping("/ee")
	@ResponseBody
	public AjaxResult exportExcel(HttpServletRequest req,@RequestBody Map<String, Object> map) {
		//1、参数
		Map<String, Object> param = getParamMap(req);
		param.putAll(map);
		String exportField = (String)map.get("exportField");
		if(StringUtils.isEmpty(exportField)) {
			return null;
		}

		//2、调用服务
		EiInfo inInfo = new EiInfo();
		inInfo.addRow("i", param);
		inInfo.set(EiConstant.serviceName, req.getParameter("serviceName")); //设置服务名
		inInfo.set(EiConstant.methodName, req.getParameter("methodName"));//设置方法名
		EiInfo outInfo = XLocalManager.call(inInfo);

		//3、返回excel
		ExcelUtil<Map> util = new ExcelUtil<Map>(Map.class);
		util.setSheet(req.getParameter("fileName"));
		List<Map<String,Object>> object = new ArrayList<Map<String,Object>>();
		Map<String,String> mapTitle = new LinkedHashMap<String,String>();
		String[] efs = exportField.split(",");
		for (String ef : efs) {
			if(StringUtils.isEmpty(ef) || ef.indexOf("undefined")>-1)
				continue;
			String[] f = ef.split("_");
			mapTitle.put(f[0], f[1]);
		}

		// 获取导出数据，处理可能的空指针异常
		List<Map<String, Object>> rows = new ArrayList<>();
		if (outInfo.getBlock("r") != null && outInfo.getBlock("r").getRows() != null) {
			rows = outInfo.getBlock("r").getRows();
		}
		
		return util.exportExcel(mapTitle, rows);
	}


}

