package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlProcessCodeEnum;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.dto.ADUser;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bsvkkj.utils.expert.ExperReviewUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLPatentInfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 境内专利_申请_基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class ServiceKIZLApplyBaseinfo extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    @Resource
    private ServiceKIZLApplyRyxx serviceKIZLApplyRyxx;
    @Resource
    private ServiceKIZLApplySqr serviceKIZLApplySqr;
    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;
    @Autowired
    private SOrgUtil sOrgUtil;


    public EiInfo doSave(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("beanEx");
            if (StrUtil.isNotBlank(beanEx.getFirstDeptCode())) {
                ADOrg orgByOrgCode = SOrgUtil.getOrgByOrgCode(beanEx.getFirstDeptCode());
                if (ObjectUtil.isNotEmpty(orgByOrgCode)) {
                    beanEx.setFirstDeptPath(orgByOrgCode.getOrgPathCode());
                }
            }
            EiInfo retInfo = this.businessKIZLApplyBaseinfo.doSave(operator, beanEx);
            inInfo.setMsg(retInfo.getMsg());
            inInfo.setCell("i", 0, "applyId", retInfo.getBlock("i").getRow(0).get("applyId"));
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

	/**
	*
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TkizlApplyBaseinfo bean = businessKIZLApplyBaseinfo.initBean(loginName);
            TkizlApplyBaseinfoEx beanEx = TkizlApplyBaseinfoEx.initParent(bean);
            beanEx.setLxrCode(UserSession.getLoginName());
            beanEx.setLxrName(UserSession.getLoginCName());
            beanEx.setApplyDate(DateUtil.today());
            beanEx.setUseMethod("1");
            // 用户与部门
            ADOrg adOrg = SOrgUtil.getMainOrgByUserCode(loginName);
            if (ObjectUtil.isNotEmpty(adOrg)) {
                beanEx.setFirstDeptCode(adOrg.getOrgCode());
                beanEx.setFirstDeptName(adOrg.getOrgPathName());
                beanEx.setUseDept(adOrg.getOrgCode());
            }
            inInfo.set("applyBaseinfo", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlApplyBaseinfo> queryList = businessKIZLApplyBaseinfo.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessKIZLApplyBaseinfo.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkizlApplyBaseinfo bean = BeanUtil.toBean(row, TkizlApplyBaseinfo.class);
            businessKIZLApplyBaseinfo.save(UserSession.getLoginName(), bean);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String applyId = (String) inInfo.get("applyId");
            TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(applyId);
            inInfo.set("applyBaseinfo", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String applyId = (String) inInfo.get("applyId");
	        if (StrUtil.isNotBlank(applyId)) {
	            for (String id : applyId.split(",")) {
	                businessKIZLApplyBaseinfo.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo queryDBDetail(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String businessId = inInfo.getString("businessId");
            TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(businessId);
            TkizlApplyBaseinfoEx beanEx = TkizlApplyBaseinfoEx.initParent(query);
            //流程属性
            String taskId = inInfo.getString("taskId");
            WorkFlow workFlow;
            if (StringUtils.isBlank(taskId)) {
                workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessId);
                if (ObjectUtil.isEmpty(workFlow)) {
                    KizlProcessCodeEnum processCodeEnum = KizlProcessCodeEnum.getProcessCode(KizlProcessCodeEnum.KIZL_SB.getProcessCode());
                    workFlow = new WorkFlow(operator, processCodeEnum.getProcessCode(), processCodeEnum.getBusinessType(), beanEx.getApplyId());
                }
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            beanEx.setWorkFlow(workFlow);
            //发明人信息
            beanEx.setRyxxList(serviceKIZLApplyRyxx.queryListByApplyId(businessId));
            //申请人信息
            beanEx.setSqrList(serviceKIZLApplySqr.queryListByApplyId(businessId));
            //专利信息
            beanEx.setPatentInfo(businessKIZLPatentInfo.queryByApplyId(businessId));
            inInfo.set("applyBaseinfo", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo doSubmit(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("beanEx");
            WorkFlow workFlow = this.businessKIZLApplyBaseinfo.doSubmit(operator, beanEx);
            if (ObjectUtil.isNotEmpty(beanEx.getWorkFlow()) && (StrUtil.equalsAny(beanEx.getWorkFlow().getTransitionKey(), KIZLConstants.TRANSITION13,KIZLConstants.TRANSITION5))) {
                inInfo.setMsg(SWorkFlowUtil.suspendProcess(operator, workFlow));
            } else {
                inInfo.setMsg(SWorkFlowUtil.submit(operator, workFlow));
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 保存专利信息
     */
    public EiInfo savePatentInfo(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("beanEx");
            TkizlPatentInfo patentInfo = beanEx.getPatentInfo();
            TkizlPatentInfo query = this.businessKIZLPatentInfo.query(patentInfo.getPatentId());
            // 设置待交底信息
            if (StrUtil.isBlank(patentInfo.getDjdPerson()) && StrUtil.isBlank(query.getDjdPerson())) {
                patentInfo.setDjdPerson(operator);
                patentInfo.setDjdDate(DateUtil.today());
            }
            // 保存专利信息
            businessKIZLPatentInfo.save(operator, patentInfo);
            this.businessKIZLApplyBaseinfo.doSave(operator, beanEx);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查看详细信息（只查看，不处理工作流）
     */
    /**
     * 获取发明人组织信息
     */
    public EiInfo getInventorOrg(EiInfo inInfo) {
        try {
            String userCode = (String) inInfo.get("userCode");
            if (StringUtils.isBlank(userCode)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("用户工号不能为空");
                return inInfo;
            }
            
            ADOrg adOrg = SOrgUtil.getMainOrgByUserCode(userCode);
            if (ObjectUtil.isNotEmpty(adOrg)) {
                Map<String, Object> orgInfo = BeanUtil.beanToMap(adOrg);
                inInfo.set("orgInfo", orgInfo);
                inInfo.setMsg("获取组织信息成功");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } else {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("未找到该人员的组织信息");
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取组织信息失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryViewDetail(EiInfo inInfo) {
        try {
            String businessId = inInfo.getString("businessId");
            TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(businessId);
            TkizlApplyBaseinfoEx beanEx = TkizlApplyBaseinfoEx.initParent(query);
            
            //发明人信息
            beanEx.setRyxxList(serviceKIZLApplyRyxx.queryListByApplyId(businessId));
            //申请人信息
            beanEx.setSqrList(serviceKIZLApplySqr.queryListByApplyId(businessId));
            //专利信息
            beanEx.setPatentInfo(businessKIZLPatentInfo.queryByApplyId(businessId));

            inInfo.set("applyBaseinfo", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 获取同来源相关信息（项目信息、产品信息、专利列表、技术秘密列表）
     */
    public EiInfo getRelatedInfo(EiInfo inInfo) {
        try {
            String fromNo = inInfo.getString("fromNo");
            if (StrUtil.isBlank(fromNo)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("来源编号不能为空");
                return inInfo;
            }
            // 获取同来源相关信息
            Map<String, Object> relatedInfo = businessKIZLApplyBaseinfo.getRelatedInfo(fromNo);
            inInfo.set("data", relatedInfo);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取成功");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取同来源相关信息失败：" + e.getMessage());
            logger.error("获取同来源相关信息失败", e);
        }
        return inInfo;
    }

    /**
     * 申请跟踪查询
     * @param inInfo
     * @return
     */
    public EiInfo queryApplyTracking(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 处理权限控制
            businessKIZLApplyBaseinfo.processApplyTrackingPermissions(param, operator);
            
            // 查询申请跟踪数据
            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryApplyTracking(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryApplyTrackingDept(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 处理权限控制
            businessKIZLApplyBaseinfo.processApplyTrackingPermissions(param, operator);

            // 查询申请跟踪数据
            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryApplyTrackingDept(param);

            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryApplyTrackingDept2(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }
            // 处理权限控制
            businessKIZLApplyBaseinfo.processApplyTrackingPermissions(param, operator);
            // 查询申请跟踪数据
            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryApplyTrackingDept2(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryApplyTrackingProject(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 处理权限控制
            businessKIZLApplyBaseinfo.processApplyTrackingPermissions(param, operator);

            // 查询申请跟踪数据
            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryApplyTrackingProject(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查询我的申请 - 本人作为发明人的专利申请
     */
    public EiInfo queryMyApplications(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }
            
            // 设置当前用户作为提出人的查询条件
            param.put("lxrCode", operator);
            
            // 设置排序条件 - 按专利名称排序
            param.put("displayOrder", "A.APPLY_NAME ASC");
            
            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryMyApplications(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryMyRelated(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 设置当前用户作为发明人的查询条件
            param.put("inventorEmpId", operator);

            // 设置排序条件 - 按专利名称排序
            param.put("displayOrder", "A.APPLY_NAME ASC");

            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryMyApplications(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryMyProcessing(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 设置当前用户作为查询条件
            param.put("userLabel", operator);
            // 设置排序条件 - 时间排序
            param.put("displayOrder", "A.LAST_TIME DESC");
            List<String> list = new ArrayList<>();
            list.add(KizlProcessCodeEnum.KIZL_SB.getProcessCode());
            param.put("flowCode",list);

            TableDataInfo tdi = businessKIZLApplyBaseinfo.queryMyProcessing(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo getKizlPsxx(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }
            String base64 = base64BaseApplyInfo(operator, MapUtil.getStr(param, "bizGuid"));
            inInfo.set("jbxx", base64);
            inInfo.setMsg("获取专利信息成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public String base64BaseApplyInfo(String operator, String bizGuids) {
        if (StrUtil.isNotBlank(bizGuids)) {
            JSONObject jsonObject = new JSONObject();
            String[] splits = bizGuids.split(",");
            for (String bizGuid : splits) {
                TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(bizGuid);
                Map<String, Object> map = new HashMap();
                map.put("专利申报名称", query.getApplyName());
                jsonObject = ExperReviewUtil.getReviewInfoEncapsulationToJsonStr(KIZLConstants.KIZL_ZLPS, map, bizGuid);
            }
            System.out.println(jsonObject.toJSONString(0));
            return Base64.encodeUrlSafe(jsonObject.toJSONString(0));
        }
        return null;
    }

    /**
     * 根据来源编号查询有效专利列表
     *
     * @param inInfo 输入信息，projectCode查询参数
     * @return 有效专利列表信息list
     */
    public EiInfo queryValidPatentsByFromNo(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            
            // 检查必要参数
            String fromNo = (String) queryData.get("projectCode");
            if (StrUtil.isBlank(fromNo)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("来源编号不能为空");
                return inInfo;
            }
            
            // 查询有效专利列表
            List<Map<String, Object>> validPatentList = businessKIZLApplyBaseinfo.queryValidPatentsByFromNo(fromNo);
            
            inInfo.set("list", validPatentList);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询有效专利失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据来源编号查询有效专利列表（分页）
     *
     * @param inInfo 输入信息，包含fromNo等查询参数
     * @return 有效专利分页列表信息
     */
    public EiInfo queryValidPatentsByFromNoPage(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            
            // 检查必要参数
            String fromNo = (String) queryData.get("fromNo");
            if (StrUtil.isBlank(fromNo)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("来源编号不能为空");
                return inInfo;
            }
            
            // 查询有效专利列表（分页）
            TableDataInfo queryPage = businessKIZLApplyBaseinfo.queryValidPatentsByFromNoPage(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询有效专利失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据条件查询有效专利列表
     *
     * @param inInfo 输入信息，包含查询条件
     * @return 有效专利列表信息
     */
    public EiInfo queryValidPatentsByConditions(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            
            // 查询有效专利列表
            List<Map<String, Object>> validPatentList = businessKIZLApplyBaseinfo.queryValidPatentsByConditions(queryData);
            
            inInfo.set("list", validPatentList);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询有效专利失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

}
