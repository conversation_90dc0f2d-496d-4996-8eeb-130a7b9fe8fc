package com.baosight.bsvkkj.ki.zl.strategy.impl;

import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Manual3活动节点策略实现
 * 部门领导审批，生成接收编号
 * 
 * <AUTHOR>
 */
@Component
public class Manual3Strategy implements WorkflowActivityStrategy {
    
    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    
    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        WorkFlow workFlow = beanEx.getWorkFlow();
        TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(workFlow.getBusinessId());
        
        // 部门领导审批，生成接收编号
        businessKIZLApplyBaseinfo.generateJsbh(query);
        businessKIZLApplyBaseinfo.update(operator, query);
        
        return workFlow;
    }
    
    @Override
    public String getSupportedActivity() {
        return KizlWorkflowActivityEnum.MANUAL3.getCode();
    }
} 