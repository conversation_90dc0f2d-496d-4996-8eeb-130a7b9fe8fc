package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLMaintainAttachment;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainAttachment;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 境内专利_维护_附件信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class ServiceKIZLMaintainAttachment extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessKIZLMaintainAttachment businessKIZLMaintainAttachment;

	/**
	*
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TkizlMaintainAttachment bean = businessKIZLMaintainAttachment.initBean(loginName);
            inInfo.set("maintainAttachment", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlMaintainAttachment> queryList = businessKIZLMaintainAttachment.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "attNameLike"))) {
                queryData.put("dynSql", "ATT_NAME like '%" + MapUtil.getStr(queryData, "attNameLike") + "%'");
            }
            TableDataInfo queryPage = businessKIZLMaintainAttachment.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkizlMaintainAttachment bean = BeanUtil.toBean(row, TkizlMaintainAttachment.class);
            businessKIZLMaintainAttachment.save(UserSession.getLoginName(), bean);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String attId = (String) inInfo.get("attId");
            TkizlMaintainAttachment query = businessKIZLMaintainAttachment.query(attId);
            inInfo.set("maintainAttachment", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String attId = (String) inInfo.get("attId");
	        if (StrUtil.isNotBlank(attId)) {
	            for (String id : attId.split(",")) {
	                businessKIZLMaintainAttachment.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    /**
     * 根据附件编码获取附件信息
     */
    public EiInfo getByAttCode(EiInfo inInfo) {
        try {
            String attCode = (String) inInfo.get("attCode");
            if (StrUtil.isBlank(attCode)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("附件编码不能为空");
                return inInfo;
            }
            
            TkizlMaintainAttachment attachment = businessKIZLMaintainAttachment.queryByAttCode(attCode);
            if (attachment != null) {
                inInfo.set("attachment", attachment);
                inInfo.setMsg("获取成功");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } else {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("未找到对应的附件信息");
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 获取所有附件信息列表（用于前端获取附件名称映射）
     */
    public EiInfo getAllAttachments(EiInfo inInfo) {
        try {
            List<TkizlMaintainAttachment> attachments = businessKIZLMaintainAttachment.queryList(new HashMap<>());
            inInfo.set("attachments", attachments);
            inInfo.setMsg("获取成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
}
