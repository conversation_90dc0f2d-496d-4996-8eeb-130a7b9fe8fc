package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLWorkSheets;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.flow.FlowKizlApply;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作表格
 */
@Service
public class ServiceKIZLWorkSheets {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    BusinessKIZLWorkSheets businessKIZLWorkSheets;
    @Resource
    BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;

    /**
     * 技术交底清单
     * @return TableDataInfo
     */
    public EiInfo queryjsjdqd(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            List<Map<String, Object>> list = businessKIZLWorkSheets.queryjsjdqd(param);
            // 处理评审意见 - 交底状态和主管部门内部意见
            if (list != null && !list.isEmpty()) {
                for (Map<String, Object> map : list) {
                    String flowStatus = MapUtil.getStr(map, "flowStatus");
                    String zgbmNc = MapUtil.getStr(map, "zgbmNc");
                    // 设置评审意见
                    String extra1 = flowStatus != null ? flowStatus.toString() : "";

                    if (zgbmNc != null && !zgbmNc.isEmpty()) {
                        extra1 = extra1 + "," + zgbmNc;
                    }
                    map.put("extra1", extra1);
                }
            }
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setRows(list);
            tableDataInfo.setTotal(list.size());
            inInfo.set("pd", tableDataInfo);
            
            // 如果是导出请求，同时设置 r 块数据
            if (param.containsKey("exportField")) {
                if (list != null && !list.isEmpty()) {
                    for (Map<String, Object> row : list) {
                        inInfo.addRow("r", row);
                    }
                } else {
                    // 如果没有数据，也要添加一个空的r块
                    inInfo.addBlock("r");
                }
            }
            
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryApplyThisYear(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 处理权限控制
            businessKIZLApplyBaseinfo.processApplyTrackingPermissions(param, operator);

            // 查询申请跟踪数据
            TableDataInfo tdi = businessKIZLWorkSheets.queryApplyThisYear(param);
            inInfo.set("pd", tdi);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查询专利交底发明人信息
     * @param inInfo
     * @return
     */
    public EiInfo queryPatentInventorInfo(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = inInfo.getRow("i", 0);
            if (param == null) {
                param = new HashMap<>();
            }

            // 处理权限控制
            businessKIZLApplyBaseinfo.processApplyTrackingPermissions(param, operator);

            // 查询专利交底发明人信息数据
            TableDataInfo tdi = businessKIZLWorkSheets.queryPatentInventorInfo(param);

            // 处理发明人显示模式
            processInventorDisplayMode(tdi, param);

            inInfo.set("pd", tdi);

            // 如果是导出请求，同时设置 r 块数据
            if (param.containsKey("exportField")) {
                List<?> rows = tdi.getRows();
                if (rows != null && !rows.isEmpty()) {
                    for (Object row : rows) {
                        inInfo.addRow("r", (Map<String, Object>) row);
                    }
                } else {
                    // 如果没有数据，也要添加一个空的r块
                    inInfo.addBlock("r");
                }
            }

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 处理发明人显示模式
     * @param tdi 表格数据
     * @param param 查询参数
     */
    private void processInventorDisplayMode(TableDataInfo tdi, Map<String, Object> param) {
        String showJobNumber = MapUtil.getStr(param, "showJobNumber", "true");

        // 如果不显示工号，处理发明人列表数据
        if ("false".equals(showJobNumber)) {
            List<?> rows = tdi.getRows();
            if (ObjectUtil.isNotEmpty(rows)) {
                for (Object rowObj : rows) {
                    if (rowObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> row = (Map<String, Object>) rowObj;
                        String inventorList = MapUtil.getStr(row, "inventorList");
                        if (StrUtil.isNotBlank(inventorList)) {
                            // 移除工号部分 - 移除括号及其中的内容
                            String inventorListWithoutJobNumber = inventorList.replaceAll("\\([^)]*\\)", "");
                            row.put("inventorList", inventorListWithoutJobNumber);
                        }
                    }
                }
            }
        }
    }
}
