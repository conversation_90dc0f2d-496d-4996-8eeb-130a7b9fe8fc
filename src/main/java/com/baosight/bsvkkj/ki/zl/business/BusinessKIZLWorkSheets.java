package com.baosight.bsvkkj.ki.zl.business;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Component;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class BusinessKIZLWorkSheets extends BusinessBase {

    private static final String NAME_SPACE = "KIZLPatenInfo";

    /**
     * 查询技术交底清单
     * @param param 查询参数
     * @return TableDataInfo
     */
    public List<Map<String,Object>> queryjsjdqd(Map<String, Object> param) {

        return dao.query(NAME_SPACE + ".queryjsjdqd", param);
    }

    public TableDataInfo queryApplyThisYear(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }

        // 查询申请跟踪数据
        return getPage(NAME_SPACE + ".queryApplyThisYear", param);
    }

    /**
     * 查询专利交底发明人信息
     * @param param 查询参数
     * @return 分页数据
     */
    public TableDataInfo queryPatentInventorInfo(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }

        // 如果是导出请求，不使用分页
        if (param.containsKey("exportField")) {
            List<Map<String, Object>> list = dao.query("KIZLPatenInfo.queryPatentInventorInfo", param);
            TableDataInfo tdi = new TableDataInfo();
            tdi.setRows(list);
            tdi.setTotal(list.size());
            return tdi;
        }

        // 查询专利交底发明人信息数据
        return getPage(NAME_SPACE + ".queryPatentInventorInfo", param);
    }
}
