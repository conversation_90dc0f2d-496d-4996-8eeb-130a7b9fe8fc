package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainZjwh;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 境内专利_维护_专家维护Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Controller
@RequestMapping("/kizl/maintainZjwh")
public class ControllerKIZLMaintainZjwh extends BaseController{
    private String prefix = "/kizl/maintainZjwh";
    
    @GetMapping()
    public String maintainZjwh(){
        return prefix + "/maintainZjwhList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询境内专利_维护_专家维护列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainZjwh", "page");
		return getDataTable(query);
	}

    /**
     * 新增境内专利_维护_专家维护
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("maintainZjwh", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLMaintainZjwh", "initLoad", "maintainZjwh"));
        return prefix + "/maintainZjwh";
    }

    /**
     * 修改境内专利_维护_专家维护
     */
    @GetMapping("/edit/{moneyId}")
    public String edit(@PathVariable("moneyId") String moneyId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("moneyId", moneyId);

        modelMap.put("maintainZjwh", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainZjwh", "load", "maintainZjwh"));
        return prefix + "/maintainZjwh";
    }

    /**
     * 保存境内专利_维护_专家维护
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlMaintainZjwh maintainZjwh){
        EiInfo eiInfo = getEiInfo("i", maintainZjwh);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainZjwh", "save");
    }
    
    /**
     * 删除境内专利_维护_专家维护
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("moneyId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainZjwh", "remove");
	}

    /**
     * 导出境内专利_维护_专家维护数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkizlMaintainZjwh> list = (List<TkizlMaintainZjwh>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainZjwh", "query", "list");
        ExcelUtil<TkizlMaintainZjwh> util = new ExcelUtil<>(TkizlMaintainZjwh.class);
        String title = "数据导出表";
		util.setSheet("境内专利_维护_专家维护");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出境内专利_维护_专家维护模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TkizlMaintainZjwh> listExcel = new ArrayList<>();
        ExcelUtil<TkizlMaintainZjwh> util = new ExcelUtil<>(TkizlMaintainZjwh.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }
}
