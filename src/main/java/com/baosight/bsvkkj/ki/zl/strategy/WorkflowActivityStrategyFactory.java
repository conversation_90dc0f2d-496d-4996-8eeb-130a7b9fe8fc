package com.baosight.bsvkkj.ki.zl.strategy;

import com.baosight.bsvkkj.ki.zl.strategy.impl.DefaultStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流活动策略工厂
 * 用于管理和获取不同的工作流活动策略实现
 * 
 * <AUTHOR>
 */
@Component
public class WorkflowActivityStrategyFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowActivityStrategyFactory.class);
    
    @Autowired
    private List<WorkflowActivityStrategy> strategies;
    
    @Autowired
    private DefaultStrategy defaultStrategy;
    
    private Map<String, WorkflowActivityStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        for (WorkflowActivityStrategy strategy : strategies) {
            String activityCode = strategy.getSupportedActivity();
            if (!"DEFAULT".equals(activityCode)) { // 排除默认策略
                strategyMap.put(activityCode, strategy);
                logger.info("注册工作流活动策略: {} -> {}", activityCode, strategy.getClass().getSimpleName());
            }
        }
        logger.info("工作流活动策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());
    }
    
    /**
     * 根据活动代码获取对应的策略
     * 如果没有找到对应的策略，则返回默认策略
     * 
     * @param activityCode 活动代码
     * @return 策略实现，永远不会返回null
     */
    public WorkflowActivityStrategy getStrategy(String activityCode) {
        WorkflowActivityStrategy strategy = strategyMap.get(activityCode);
        if (strategy == null) {
            logger.warn("未找到活动代码 {} 对应的策略，使用默认策略", activityCode);
            return defaultStrategy;
        }
        return strategy;
    }
    
    /**
     * 检查是否支持指定的活动代码
     * 
     * @param activityCode 活动代码
     * @return true表示支持，false表示不支持
     */
    public boolean isSupported(String activityCode) {
        return strategyMap.containsKey(activityCode);
    }
    
    /**
     * 获取所有已注册的活动代码
     * 
     * @return 活动代码集合
     */
    public java.util.Set<String> getSupportedActivities() {
        return strategyMap.keySet();
    }
} 