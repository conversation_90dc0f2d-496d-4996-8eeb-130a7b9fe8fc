package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import cn.hutool.core.map.MapUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.domain.TkizlMaintainLegalEx;
import com.baosight.bsvkkj.ki.zl.util.KIZLCommonUtil;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 境内专利_申请_基本信息Controller

 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Controller
@RequestMapping("/kizl/applyBaseinfo")
public class ControllerKIZLApplyBaseinfo extends BaseController{
    private String prefix = "/kizl/applyBaseinfo";
    @GetMapping()
    public String applyBaseinfo(){
        return prefix + "/KIZLSBMN";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询境内专利_申请_基本信息列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
        map.put("createUserLabel",UserSession.getLoginName());
        map.put("flowStatus", KIZLConstants.FlowStatus.DRAFT);
        map.put("delStatus", KIZLConstants.DELSTATUS_FLASE_0);
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLApplyBaseinfo", "page");
		return getDataTable(query);
	}

    /**
     * 新增境内专利_申请_基本信息
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        KIZLCommonUtil.isOrg(UserSession.getLoginName());
        modelMap.put("applyBaseinfo", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLApplyBaseinfo", "initLoad", "applyBaseinfo"));
        modelMap.put("stationList", KIZLCommonUtil.getApplyNum());//获取申请人数
        return prefix + "/KIZLSB00";
    }

    /**
     * 修改境内专利_申请_基本信息
     */
    @GetMapping("/edit/{applyId}")
    public String edit(@PathVariable("applyId") String applyId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("businessId", applyId);

        modelMap.put("applyBaseinfo", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryDBDetail", "applyBaseinfo"));
        return prefix + "/KIZLSB00";
    }

    @PostMapping("/doSave")
    @ResponseBody
    public AjaxResult doSave(@Validated TkizlApplyBaseinfoEx beanEx) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("beanEx", beanEx);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLApplyBaseinfo", "doSave");
        Map row = query.getBlock("i").getRow(0);
        return AjaxResult.success(query.getMsg(), row);
    }


    @PostMapping("/doSubmit")
    @ResponseBody
    public AjaxResult doSubmit(@Validated TkizlApplyBaseinfoEx beanEx) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("beanEx", beanEx);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLApplyBaseinfo", "doSubmit");
    }

    /**
     * 保存专利信息
     */
    @PostMapping("/savePatentInfo")
    @ResponseBody
    public AjaxResult savePatentInfo(@Validated TkizlApplyBaseinfoEx beanEx) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("beanEx", beanEx);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLApplyBaseinfo", "savePatentInfo");
        return AjaxResult.success(query.getMsg());
    }

    /**
     * 保存境内专利_申请_基本信息
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlApplyBaseinfo applyBaseinfo){
        EiInfo eiInfo = getEiInfo("i", applyBaseinfo);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLApplyBaseinfo", "save");
    }
    
    /**
     * 删除境内专利_申请_基本信息
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("applyId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLApplyBaseinfo", "remove");
	}

    /**
     * 导出境内专利_申请_基本信息数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkizlApplyBaseinfo> list = (List<TkizlApplyBaseinfo>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "query", "list");
        ExcelUtil<TkizlApplyBaseinfo> util = new ExcelUtil<>(TkizlApplyBaseinfo.class);
        String title = "数据导出表";
		util.setSheet("境内专利_申请_基本信息");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出境内专利_申请_基本信息模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TkizlApplyBaseinfo> listExcel = new ArrayList<>();
        ExcelUtil<TkizlApplyBaseinfo> util = new ExcelUtil<>(TkizlApplyBaseinfo.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }


    /**
     * 选择-申请人页面
     *
     * @return
     */
    @GetMapping("/selectList")
    public String selectList(String selectType, String values, String callback, ModelMap mmap) {
        mmap.put("selectType", selectType);
        mmap.put("values", values);
        mmap.put("callback", callback);
        return prefix + "/legalSelect";
    }


    /**
     * 选择页面-查询-共同申请人信息
     *
     * @return
     */
    @GetMapping("/tableData")
    @ResponseBody
    public List<TkizlMaintainLegalEx> tableData() {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceName, "KIZLMaintainLegal");
        eiInfo.set(EiConstant.methodName, "tableData");
        EiInfo outInfo = XLocalManager.call(eiInfo);
        List<TkizlMaintainLegalEx> query = (List<TkizlMaintainLegalEx>) outInfo.get("nodeInfo");
        return query;
    }

    /**
     * 查询-共同申请人地址、联系人等信息
     *
     * @return
     */
    @GetMapping("/lxrData/{treeId}")
    @ResponseBody
    public AjaxResult lxrData(@PathVariable("treeId") String treeId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set(EiConstant.serviceName, "KIZLMaintainLegal");
        eiInfo.set(EiConstant.methodName, "lxrData");
        Map map = new HashMap();
        map.put("treeId", treeId);
        eiInfo.set("map", map);
        EiInfo outInfo = XLocalManager.call(eiInfo);
        Map query = (Map) outInfo.get("nodeInfo");
        return AjaxResult.success("success", query);
    }

    /**
     * 查看详细信息
     */
    @GetMapping("/queryDT")
    public String queryDT(@RequestParam Map<String, Object> map, ModelMap model) {
        //业务id
        String businessId = MapUtil.getStr(map, "businessId");
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", businessId);
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryDBDetail", "applyBaseinfo");
        model.put("applyBaseinfo", beanEx);
        model.put("businessId",businessId);
        return prefix + "/KIZLSBDT";
    }

    /**
     * 查看专利受理信息（代理中信息）
     */
    @GetMapping("/viewPatentInfo/{applyId}")
    public String viewPatentInfo(@PathVariable("applyId") String applyId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", applyId);
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryViewDetail", "applyBaseinfo");
        model.put("applyBaseinfo", beanEx);
        return prefix + "/KIZLSBDLZDT";
    }

    /**
     * 查看授权通知信息
     */
    @GetMapping("/viewAuthNoticeInfo/{applyId}")
    public String viewAuthNoticeInfo(@PathVariable("applyId") String applyId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", applyId);
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryViewDetail", "applyBaseinfo");
        model.put("applyBaseinfo", beanEx);
        return prefix + "/KIZLSBSQTZDT";
    }

    /**
     * 查看授权信息
     */
    @GetMapping("/viewAuthInfo/{applyId}")
    public String viewAuthInfo(@PathVariable("applyId") String applyId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", applyId);
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryViewDetail", "applyBaseinfo");
        model.put("applyBaseinfo", beanEx);
        return prefix + "/KIZLSBSQXXDT";
    }

    /**
     * 获取同来源专利和技术秘密信息
     */
    @GetMapping("/related")
    @ResponseBody
    public AjaxResult getRelatedInfo(@RequestParam("fromNo") String fromNo) {
        try {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("fromNo", fromNo);
            return AjaxResult.success(ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "getRelatedInfo", "data"));
        } catch (Exception e) {
            logger.error("获取同来源专利和技术秘密信息失败", e);
            return AjaxResult.error("获取同来源专利和技术秘密信息失败：" + e.getMessage());
        }
    }
}
