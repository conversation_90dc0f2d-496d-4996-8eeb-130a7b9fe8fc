package com.baosight.bsvkkj.ki.zl.business;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainLegal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_维护_申请人信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLMaintainLegal extends BusinessBase {

    private static final String NAME_SPACE = "tkizlMaintainLegal";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlMaintainLegal initBean(String operator) {
        TkizlMaintainLegal maintainLegal = new TkizlMaintainLegal();
        return maintainLegal;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlMaintainLegal> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlMaintainLegal> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param legalId
     * @return
     */
    public TkizlMaintainLegal query(String legalId) {
        if (StringUtils.isBlank(legalId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("legalId", legalId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainLegal) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param maintainLegal
     * @return
     */
    public TkizlMaintainLegal insert(String operator, TkizlMaintainLegal maintainLegal) {
        maintainLegal.initAdd(operator);
        if (StringUtils.isEmpty(maintainLegal.getLegalId())) {
            maintainLegal.setLegalId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", maintainLegal);
        return maintainLegal;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param maintainLegal
     * @return
     */
    public TkizlMaintainLegal update(String operator, TkizlMaintainLegal maintainLegal) {
        maintainLegal.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", maintainLegal);
        return maintainLegal;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param legalId
     * @return
     */
    public TkizlMaintainLegal deleteLogin(String operator, String legalId) {
        TkizlMaintainLegal maintainLegal = new TkizlMaintainLegal();
        maintainLegal.setLegalId(legalId);
        maintainLegal.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", maintainLegal);
        return maintainLegal;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param legalId
     * @return
     */
    public int delete(String operator, String legalId) {
        return dao.delete(NAME_SPACE + ".delete", legalId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param maintainLegal
     * @return
     */
    public TkizlMaintainLegal save(String operator, TkizlMaintainLegal maintainLegal) {
        ADOrg org = SOrgUtil.getOrgByOrgCode(maintainLegal.getExtra1());
        // 处理部门路径
        String orgPathCode = org.getOrgPathCode().replaceFirst("/", "").replace("BSTA/BSSG/", "");
        String[] code = orgPathCode.split("/");
        for (int i = 0; i < code.length; i++) {
            if (i == 0) {
                maintainLegal.setDeptFirst(code[i]);
                maintainLegal.setDeptFirstName(SOrgUtil.getOrgName(code[i]));
                maintainLegal.setExtra1(code[i]);
            } else if (i == 1) {
                maintainLegal.setDeptSecond(code[i]);
                maintainLegal.setDeptSecondName(SOrgUtil.getOrgName(code[i]));
                maintainLegal.setExtra1(code[i]);
            } else if (i > 1) {
                if (i == code.length - 1) {
                    maintainLegal.setDeptThird(code[i]);
                    maintainLegal.setDeptThirdName(SOrgUtil.getOrgName(code[i]));
                    maintainLegal.setExtra1(code[i]);
                }
            }
        }
        if (StringUtils.isEmpty(maintainLegal.getLegalId())) {
            return insert(operator, maintainLegal);
        } else {
            if (null == query(maintainLegal.getLegalId())) {
                return insert(operator, maintainLegal);
            }
            return update(operator, maintainLegal);
        }
    }

}
