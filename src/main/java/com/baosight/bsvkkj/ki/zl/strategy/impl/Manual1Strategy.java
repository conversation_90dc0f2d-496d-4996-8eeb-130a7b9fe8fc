package com.baosight.bsvkkj.ki.zl.strategy.impl;

import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Manual1活动节点策略实现
 * 编辑申请单
 * 
 * <AUTHOR>
 */
@Component
public class Manual1Strategy implements WorkflowActivityStrategy {
    
    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    
    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        // 编辑申请单：保存申请信息
        businessKIZLApplyBaseinfo.doSave(operator, beanEx);
        return beanEx.getWorkFlow();
    }
    
    @Override
    public String getSupportedActivity() {
        return KizlWorkflowActivityEnum.MANUAL1.getCode();
    }
} 