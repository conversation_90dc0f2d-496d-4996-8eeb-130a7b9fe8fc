package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlApplyConfirm;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_申请_确认信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLApplyConfirm extends BusinessBase {

    private static final String NAME_SPACE = "tkizlApplyConfirm";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlApplyConfirm initBean(String operator) {
        TkizlApplyConfirm applyConfirm = new TkizlApplyConfirm();
        return applyConfirm;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlApplyConfirm> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlApplyConfirm> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param confirmId
     * @return
     */
    public TkizlApplyConfirm query(String confirmId) {
        if (StringUtils.isBlank(confirmId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("confirmId", confirmId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlApplyConfirm) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param applyConfirm
     * @return
     */
    public TkizlApplyConfirm insert(String operator, TkizlApplyConfirm applyConfirm) {
        applyConfirm.initAdd(operator);
        if (StringUtils.isEmpty(applyConfirm.getConfirmId())) {
            applyConfirm.setConfirmId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", applyConfirm);
        return applyConfirm;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param applyConfirm
     * @return
     */
    public TkizlApplyConfirm update(String operator, TkizlApplyConfirm applyConfirm) {
        applyConfirm.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", applyConfirm);
        return applyConfirm;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param confirmId
     * @return
     */
    public TkizlApplyConfirm deleteLogin(String operator, String confirmId) {
        TkizlApplyConfirm applyConfirm = new TkizlApplyConfirm();
        applyConfirm.setConfirmId(confirmId);
        applyConfirm.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", applyConfirm);
        return applyConfirm;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param confirmId
     * @return
     */
    public int delete(String operator, String confirmId) {
        return dao.delete(NAME_SPACE + ".delete", confirmId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param applyConfirm
     * @return
     */
    public TkizlApplyConfirm save(String operator, TkizlApplyConfirm applyConfirm) {
        if (StringUtils.isEmpty(applyConfirm.getConfirmId())) {
            return insert(operator, applyConfirm);
        } else {
            if (null == query(applyConfirm.getConfirmId())) {
                return insert(operator, applyConfirm);
            }
            return update(operator, applyConfirm);
        }
    }

}
