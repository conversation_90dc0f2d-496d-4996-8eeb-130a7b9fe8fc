package com.baosight.bsvkkj.ki.zl.business;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.flow.FlowKizlApply;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategyFactory;
import com.baosight.bsvkkj.mp.ad.utils.SRoleUtil;

/**
 * 境内专利_申请_基本信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLApplyBaseinfo extends BusinessBase {

    private static final Logger logger = LoggerFactory.getLogger(BusinessKIZLApplyBaseinfo.class);
    private static final String NAME_SPACE = "tkizlApplyBaseinfo";

    @Resource
    private BusinessKIZLApplyRyxx businessKIZLApplyRyxx;

    @Resource
    private BusinessKIZLApplySqr businessKIZLApplySqr;
    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;

    @Resource
    private WorkflowActivityStrategyFactory strategyFactory;

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlApplyBaseinfo initBean(String operator) {
        TkizlApplyBaseinfo applyBaseinfo = new TkizlApplyBaseinfo();
        return applyBaseinfo;
    }

    /**
     * 查询List
     *
     * @param param
     * @return
     */
    public List<TkizlApplyBaseinfo> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlApplyBaseinfo> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param applyId
     * @return
     */
    public TkizlApplyBaseinfo query(String applyId) {
        if (StringUtils.isBlank(applyId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("applyId", applyId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlApplyBaseinfo) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param applyBaseinfo
     * @return
     */
    public TkizlApplyBaseinfo insert(String operator, TkizlApplyBaseinfo applyBaseinfo) {
        applyBaseinfo.initAdd(operator);
        if (StringUtils.isEmpty(applyBaseinfo.getApplyId())) {
            applyBaseinfo.setApplyId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", applyBaseinfo);
        return applyBaseinfo;
    }

    /**
     * 修改
     *
     * @param operator
     * @param applyBaseinfo
     * @return
     */
    public TkizlApplyBaseinfo update(String operator, TkizlApplyBaseinfo applyBaseinfo) {
        applyBaseinfo.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", applyBaseinfo);
        return applyBaseinfo;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param applyId
     * @return
     */
    public TkizlApplyBaseinfo deleteLogin(String operator, String applyId) {
        TkizlApplyBaseinfo applyBaseinfo = new TkizlApplyBaseinfo();
        applyBaseinfo.setApplyId(applyId);
        applyBaseinfo.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", applyBaseinfo);
        return applyBaseinfo;
    }

    /**
     * 物理删除
     *
     * @param operator
     * @param applyId
     * @return
     */
    public int delete(String operator, String applyId) {
        return dao.delete(NAME_SPACE + ".delete", applyId);
    }

    /**
     * 保存
     *
     * @param operator
     * @param applyBaseinfo
     * @return
     */
    public TkizlApplyBaseinfo save(String operator, TkizlApplyBaseinfo applyBaseinfo) {
        if (StringUtils.isEmpty(applyBaseinfo.getApplyId())) {
            return insert(operator, applyBaseinfo);
        } else {
            if (null == query(applyBaseinfo.getApplyId())) {
                return insert(operator, applyBaseinfo);
            }
            return update(operator, applyBaseinfo);
        }
    }

    public EiInfo doSave(String operator, TkizlApplyBaseinfoEx beanEx) {
        EiInfo inInfo = new EiInfo();
        String applyId = beanEx.getApplyId();
        String firstDeptCode = beanEx.getFirstDeptCode();
        if (StrUtil.isBlank(applyId)) {
            applyId = BizIdUtil.INSTANCE.nextId();
            beanEx.setApplyId(applyId);
        }
        if (StrUtil.isNotBlank(beanEx.getStart()) && KIZLConstants.IS_STATR_PROCESS.equals(beanEx.getStart())) {
            TkizlApplyBaseinfo query = this.query(applyId);
            if (ObjectUtil.isNotEmpty(query) && StrUtil.isBlank(query.getSerialNum())) {
                beanEx.setSerialNum(generateSerialNumbers(firstDeptCode));
            } else if (ObjectUtil.isEmpty(query)) {
                beanEx.setSerialNum(generateSerialNumbers(firstDeptCode));
            }
        }
        this.save(operator, beanEx);
        // 保存发明人信息
        saveRyxxList(operator, applyId, beanEx.getRyxxList());

        // 保存申请人信息
        saveSqrList(operator, applyId, beanEx.getSqrList());

        WorkFlow workFlow = beanEx.getWorkFlow();
        if (ObjectUtil.isNotEmpty(workFlow)) {
            //暂存流程意见
            if (StrUtil.isNotBlank(workFlow.getComment()) && StrUtil.isNotBlank(workFlow.getTaskId())) {
                SWorkFlowUtil.updateComment(workFlow.getTaskId(), workFlow.getComment());
            }
        } else {
            workFlow = new WorkFlow();
        }
        //保存附件
        Map<String, String> attachmentS = beanEx.getAttachmentS();
        if (attachmentS != null) {
            for (String key : attachmentS.keySet()) {
                if (null != (attachmentS.get(key))) {
                    SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(applyId, attachmentS.get(key), "KIZL", key, null, null);
                }
            }
        }
        inInfo.setMsg("保存成功");
        inInfo.setCell("i", 0, "applyId", applyId);
        if (StrUtil.isNotBlank(beanEx.getStart()) && KIZLConstants.IS_STATR_PROCESS.equals(beanEx.getStart())) {
            FlowKizlApply.startProcess(operator, applyId, beanEx.getApplyName(), firstDeptCode, workFlow, inInfo);
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /**
     * 生成接收编号
     * 规则:年份后2位,部门代码第3-4位,每年清零的3位流水号
     * @param query 查询条件
     */
    public synchronized void generateJsbh(TkizlApplyBaseinfo query) {
        if (ObjectUtil.isEmpty(query)) {
            throw new BusinessException("申请信息不能为空，无法生成接收编号！");
        }
        
        String firstDeptCode = query.getFirstDeptCode();
        if (StrUtil.isBlank(firstDeptCode)) {
            throw new BusinessException("第一申报部门代码不能为空，无法生成接收编号！");
        }
        
        // 获取年份后2位
        String yearSuffix = String.valueOf(DateUtil.thisYear()).substring(2);
        
        // 获取部门代码第3-4位
        String deptCodePart;
        if (firstDeptCode.length() >= 4) {
            deptCodePart = firstDeptCode.substring(2, 4);
        } else if (firstDeptCode.length() >= 2) {
            deptCodePart = firstDeptCode.substring(2) + "0";
        } else {
            deptCodePart = "00";
        }
        
        // 构建前缀：年份后2位 + 部门代码第3-4位
        String jsbhPrefix = yearSuffix + deptCodePart;
        
        // 查询当年该部门的最大接收编号
        Map<String, Object> param = new HashMap<>();
        param.put("dynSql", "JSBH LIKE '" + jsbhPrefix + "%'");
        param.put("displayOrder", "JSBH DESC");
        List<TkizlApplyBaseinfo> list = this.queryList(param);
        
        String jsbh;
        try {
            if (ObjectUtil.isEmpty(list)) {
                // 如果没有记录，从001开始
                jsbh = jsbhPrefix + "001";
            } else {
                // 获取最大的接收编号，提取流水号部分并加1
                String lastJsbh = list.get(0).getJsbh();
                if (StrUtil.isNotBlank(lastJsbh) && lastJsbh.length() >= jsbhPrefix.length() + 3) {
                    String lastSequence = lastJsbh.substring(jsbhPrefix.length());
                    int nextNumber = Integer.parseInt(lastSequence) + 1;
                    jsbh = jsbhPrefix + String.format("%03d", nextNumber);
                } else {
                    // 如果格式不正确，从001开始
                    jsbh = jsbhPrefix + "001";
                }
            }
        } catch (NumberFormatException e) {
            throw new BusinessException("接收编号生成失败，数据格式错误！", e);
        }
        // 设置接收编号
        query.setJsbh(jsbh);
    }

    /*
        生成规则:10位阿拉伯数字
        1位:子公司类别(总部-0，高金-G，钛金-T，其它-Q)
        4位:年份
        5位:每年清零的流水号
   */
    public synchronized String generateSerialNumbers(String firstDeptCode) {
        ADOrg orgByOrgCode = SOrgUtil.getOrgByOrgCode(firstDeptCode);
        if (ObjectUtil.isEmpty(orgByOrgCode)) {
            throw new BusinessException("组织不存在,生成流水号异常,请联系系统管理员！");
        }
        
        // 确定子公司类别前缀（1位）
        String companyPrefix;
        String orgPathCode = orgByOrgCode.getOrgPathCode();
        if (orgPathCode.contains("BSVK00")) {
            companyPrefix = "0"; // 总部
        } else if (orgPathCode.contains("BSVK01")) {
            companyPrefix = "G"; // 高金
        } else if (orgPathCode.contains("BSVK02")) {
            companyPrefix = "T"; // 钛金
        } else {
            companyPrefix = "Q"; // 其它
        }
        // 获取4位年份
        String yearPart = String.valueOf(DateUtil.thisYear());
        // 构建前缀：1位公司类别 + 4位年份
        String serialNumberPrefix = companyPrefix + yearPart;
        // 查询当年该公司类别的最大流水号
        Map<String, Object> param = new HashMap<>();
        param.put("dynSql", "SERIAL_NUM LIKE '" + serialNumberPrefix + "%'");
        param.put("displayOrder", "SERIAL_NUM DESC");
        List<TkizlApplyBaseinfo> list = this.queryList(param);
        String serialNumber;
        try {
            if (ObjectUtil.isEmpty(list)) {
                // 如果没有记录，从00001开始
                serialNumber = serialNumberPrefix + "00001";
            } else {
                // 获取最大的流水号，提取流水号部分并加1
                String lastSerialNum = list.get(0).getSerialNum();
                if (StrUtil.isNotBlank(lastSerialNum) && lastSerialNum.length() >= serialNumberPrefix.length() + 5) {
                    String lastSequence = lastSerialNum.substring(serialNumberPrefix.length());
                    int nextNumber = Integer.parseInt(lastSequence) + 1;
                    serialNumber = serialNumberPrefix + String.format("%05d", nextNumber);
                } else {
                    // 如果格式不正确，从00001开始
                    serialNumber = serialNumberPrefix + "00001";
                }
            }
        } catch (NumberFormatException e) {
            throw new BusinessException("流水号生成失败，数据格式错误！", e);
        }

        return serialNumber;
    }

    /**
     * 保存发明人信息
     *
     * @param operator 操作人
     * @param applyId  申请ID
     * @param ryxxList 发明人列表
     */
    private void saveRyxxList(String operator, String applyId, List<TkizlApplyRyxx> ryxxList) {
        if (StrUtil.isBlank(applyId)) {
            return;
        }
        // 保存新的发明人信息
        if (ObjectUtil.isNotEmpty(ryxxList)) {
            this.businessKIZLApplyRyxx.deleteByC(applyId);
            for (TkizlApplyRyxx ryxx : ryxxList) {
                // 设置申请ID
                ryxx.setApplyId(applyId);
                // 设置序号
                if (ObjectUtil.isNotEmpty(ryxx.getRyxh()) && ryxx.getRyxh().intValue() == 0) {
                    ryxx.setRyxh(new BigDecimal(ryxxList.indexOf(ryxx) + 1));
                }
                // 设置类型（如果前端传递的是数字，需要转换为字符串）
                if (ObjectUtil.isNotEmpty(ryxx.getRylx())) {
                    String rylx = ryxx.getRylx();
                    if ("1".equals(rylx)) {
                        ryxx.setRylx(KIZLConstants.PersonType.BAOWU); // 宝武人员
                    } else if ("2".equals(rylx)) {
                        ryxx.setRylx(KIZLConstants.PersonType.NON_BAOWU); // 非宝武人员
                    }
                }
                // 保存发明人
                businessKIZLApplyRyxx.insert(operator, ryxx);
            }
        }
    }

    /**
     * 保存申请人信息
     *
     * @param operator 操作人
     * @param applyId  申请ID
     * @param sqrList  申请人列表
     */
    private void saveSqrList(String operator, String applyId, List<TkizlApplySqr> sqrList) {
        if (StrUtil.isBlank(applyId)) {
            return;
        }
        // 保存新的申请人信息
        if (ObjectUtil.isNotEmpty(sqrList)) {
            this.businessKIZLApplySqr.deleteByC(applyId);
            for (TkizlApplySqr sqr : sqrList) {
                // 设置申请ID
                sqr.setApplyId(applyId);

                // 设置序号
                if (ObjectUtil.isEmpty(sqr.getXh()) || sqr.getXh() == 0) {
                    sqr.setXh(sqrList.indexOf(sqr) + 1);
                }

                // 保存申请人
                businessKIZLApplySqr.insert(operator, sqr);
            }
        }
    }

    /**
     * 提交工作流
     * @param operator 操作人
     * @param beanEx 申请基本信息扩展对象
     * @return 工作流对象
     */
    public WorkFlow doSubmit(String operator, TkizlApplyBaseinfoEx beanEx) {
        WorkFlow workFlow = beanEx.getWorkFlow();
        String currentActivity = workFlow.getCurrentActivity();
        
        // 使用策略模式处理不同活动节点
        return strategyFactory.getStrategy(currentActivity).process(operator, beanEx);
    }

    /**
     * 获取同来源专利列表
     *
     * @param fromNo 来源编号
     * @return 专利列表
     */
    public List<Map<String, Object>> getRelatedPatents(String fromNo) {
        if (StringUtils.isBlank(fromNo)) {
            return new ArrayList<>();
        }
        
        Map<String, Object> param = new HashMap<>();
        param.put("fromNo", fromNo);
//        param.put("fromType", "kyxm"); // 只查询科研项目来源的专利

        // 查询同来源的专利申请基本信息
        List<Map<String, Object>> patentList = dao.query("KIZLPatenInfo.queryRelatedPatents", param);
        
        // 处理返回数据，确保字段名称正确
        for (Map<String, Object> patent : patentList) {
            // 如果需要转换字段名称或处理数据格式，在这里进行
            if (patent.get("jsbh") != null) {
                patent.put("receiveNo", patent.get("jsbh")); // 接收编号
            }
            if (patent.get("flowStatus") != null) {
                patent.put("latestStatus", getStatusDisplayName(patent.get("flowStatus").toString())); // 最新状态
            }
            if (patent.get("applyName") != null) {
                patent.put("patentName", patent.get("applyName")); // 专利名称
            }
            if (patent.get("qs") != null) {
                patent.put("ownership", patent.get("qs")); // 权属
            }
            if (patent.get("applyId") != null) {
                patent.put("id", patent.get("applyId")); // ID
            }
        }
        
        return patentList;
    }

    /**
     * 根据来源编号查询有效专利列表
     *
     * @param fromNo 来源编号
     * @return 有效专利列表
     */
    public List<Map<String, Object>> queryValidPatentsByFromNo(String fromNo) {
        if (StringUtils.isBlank(fromNo)) {
            return new ArrayList<>();
        }
        
        Map<String, Object> param = new HashMap<>();
        param.put("fromNo", fromNo);
        
        // 查询有效专利列表
        List<Map<String, Object>> validPatentList = dao.query("KIZLPatenInfo.queryValidPatentsByFromNo", param);
        
        // 处理返回数据，确保字段名称正确
        for (Map<String, Object> patent : validPatentList) {
            // 设置专利状态显示名称
            if (patent.get("patentStatus") != null) {
                patent.put("patentStatusName", getPatentStatusDisplayName(patent.get("patentStatus").toString()));
            }
            
            // 设置法律状态显示名称
            if (patent.get("flzt") != null) {
                patent.put("flztName", getLegalStatusDisplayName(patent.get("flzt").toString()));
            }
            
            // 设置专利类型显示名称
            if (patent.get("patentType") != null) {
                patent.put("patentTypeName", getPatentTypeDisplayName(patent.get("patentType").toString()));
            }
            
            // 设置有效性显示名称
            if (patent.get("isvalid") != null) {
                patent.put("isvalidName", "1".equals(patent.get("isvalid").toString()) ? "有效" : "无效");
            }
            
            // 设置实施状态显示名称
            if (patent.get("useMethod") != null) {
                patent.put("useMethodName", getUseMethodDisplayName(patent.get("useMethod").toString()));
            }
        }
        
        return validPatentList;
    }

    /**
     * 根据来源编号查询有效专利列表（带分页）
     *
     * @param param 查询参数
     * @return 分页数据
     */
    public TableDataInfo queryValidPatentsByFromNoPage(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }
        
        // 使用getPage方法进行分页查询
        return getPage("KIZLPatenInfo.queryValidPatentsByFromNo", param);
    }

    /**
     * 根据来源编号和其他条件查询有效专利列表
     *
     * @param param 查询参数，包含fromNo等条件
     * @return 有效专利列表
     */
    public List<Map<String, Object>> queryValidPatentsByConditions(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }
        
        // 查询有效专利列表
        List<Map<String, Object>> validPatentList = dao.query("KIZLPatenInfo.queryValidPatentsByFromNo", param);
        
        // 处理返回数据
        for (Map<String, Object> patent : validPatentList) {
            // 设置各种状态的显示名称
            enrichPatentDisplayInfo(patent);
        }
        
        return validPatentList;
    }

    /**
     * 丰富专利显示信息
     *
     * @param patent 专利信息Map
     */
    private void enrichPatentDisplayInfo(Map<String, Object> patent) {
        // 设置专利状态显示名称
        if (patent.get("patentStatus") != null) {
            patent.put("patentStatusName", getPatentStatusDisplayName(patent.get("patentStatus").toString()));
        }
        
        // 设置法律状态显示名称
        if (patent.get("flzt") != null) {
            patent.put("flztName", getLegalStatusDisplayName(patent.get("flzt").toString()));
        }
        
        // 设置专利类型显示名称
        if (patent.get("patentType") != null) {
            patent.put("patentTypeName", getPatentTypeDisplayName(patent.get("patentType").toString()));
        }
        
        // 设置有效性显示名称
        if (patent.get("isvalid") != null) {
            patent.put("isvalidName", "1".equals(patent.get("isvalid").toString()) ? "有效" : "无效");
        }
        
        // 设置实施状态显示名称
        if (patent.get("useMethod") != null) {
            patent.put("useMethodName", getUseMethodDisplayName(patent.get("useMethod").toString()));
        }
        
        // 设置来源类型显示名称
        if (patent.get("fromType") != null) {
            patent.put("fromTypeName", getFromTypeDisplayName(patent.get("fromType").toString()));
        }
    }

    /**
     * 获取专利状态显示名称
     *
     * @param statusCode 状态代码
     * @return 状态显示名称
     */
    private String getPatentStatusDisplayName(String statusCode) {
        switch (statusCode) {
            case "01":
                return "申请中";
            case "02":
                return "待交底";
            case "03":
                return "已交底";
            case "04":
                return "代理中";
            case "05":
                return "已受理";
            case "06":
                return "已授权";
            default:
                return statusCode;
        }
    }

    /**
     * 获取法律状态显示名称
     *
     * @param statusCode 状态代码
     * @return 状态显示名称
     */
    private String getLegalStatusDisplayName(String statusCode) {
        switch (statusCode) {
            case "01":
                return "申请中";
            case "02":
                return "已受理";
            case "03":
                return "主动放弃";
            case "04":
                return "视为撤回";
            case "05":
                return "主动撤回";
            case "06":
                return "驳回";
            case "07":
                return "到期";
            case "08":
                return "转让";
            case "09":
                return "终止";
            default:
                return statusCode;
        }
    }

    /**
     * 获取专利类型显示名称
     *
     * @param typeCode 类型代码
     * @return 类型显示名称
     */
    private String getPatentTypeDisplayName(String typeCode) {
        switch (typeCode) {
            case "FM":
                return "发明";
            case "SYXX":
                return "实用新型";
            case "WGSJ":
                return "外观设计";
            case "PCT_FM":
                return "PCT发明";
            case "PCT_SYXX":
                return "PCT实用新型";
            default:
                return typeCode;
        }
    }

    /**
     * 获取实施状态显示名称
     *
     * @param useMethodCode 实施状态代码
     * @return 实施状态显示名称
     */
    private String getUseMethodDisplayName(String useMethodCode) {
        switch (useMethodCode) {
            case "01":
                return "自用";
            case "02":
                return "许可";
            case "03":
                return "转让";
            case "04":
                return "未实施";
            default:
                return useMethodCode;
        }
    }

    /**
     * 获取来源类型显示名称
     *
     * @param fromTypeCode 来源类型代码
     * @return 来源类型显示名称
     */
    private String getFromTypeDisplayName(String fromTypeCode) {
        switch (fromTypeCode) {
            case "kyxm":
                return "科研项目";
            case "cpkf":
                return "产品开发";
            case "gxgc":
                return "工艺改进";
            case "qt":
                return "其他";
            default:
                return fromTypeCode;
        }
    }

    /**
     * 获取同来源技术秘密列表
     *
     * @param fromNo 来源编号
     * @return 技术秘密列表
     */
    public List<Map<String, Object>> getRelatedSecrets(String fromNo) {
        if (StringUtils.isBlank(fromNo)) {
            return new ArrayList<>();
        }
        
        Map<String, Object> param = new HashMap<>();
        param.put("fromNo", fromNo);
//        param.put("authSource", "kyxm"); // 只查询科研项目来源的技术秘密

        // 查询同来源的技术秘密信息
        List<Map<String, Object>> secretList = dao.query("KIZLPatenInfo.queryRelatedSecrets", param);
        
        // 处理返回数据，确保字段名称正确
        for (Map<String, Object> secret : secretList) {
            // 如果需要转换字段名称或处理数据格式，在这里进行
            if (secret.get("rdh") != null) {
                secret.put("certificationNo", secret.get("rdh")); // 认定号
            }
            if (secret.get("flowStatus") != null) {
                secret.put("latestStatus", getStatusDisplayName(secret.get("flowStatus").toString())); // 最新状态
            }
            if (secret.get("technologyName") != null) {
                secret.put("secretName", secret.get("technologyName")); // 技术秘密名称
            }
            if (secret.get("qs") != null) {
                secret.put("ownership", secret.get("qs")); // 权属
            }
            if (secret.get("technologyId") != null) {
                secret.put("id", secret.get("technologyId")); // ID
            }
        }
        
        return secretList;
    }

    /**
     * 获取同来源相关信息
     * @param fromNo 来源编号
     * @return 包含项目信息、产品信息、专利列表和技术秘密列表的Map
     */
    public Map<String, Object> getRelatedInfo(String fromNo) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取科研项目信息
            Map<String, Object> projectInfo = getProjectInfo(fromNo);
            result.put("projectInfo", projectInfo);
            
            // 获取相关产品信息
//            Map<String, Object> productInfo = getRelatedProductInfo(fromNo);
//            result.put("productInfo", productInfo);

            // 获取同来源专利列表
            List<Map<String, Object>> patentList = getRelatedPatents(fromNo);
            result.put("patentList", patentList);

            // 获取同来源技术秘密列表
            List<Map<String, Object>> secretList = getRelatedSecrets(fromNo);
            result.put("secretList", secretList);

        } catch (Exception e) {
            logger.error("获取同来源相关信息失败: fromNo=" + fromNo, e);
        }
        return result;
    }

    /**
     * 获取科研项目信息
     *
     * @param projectCode 项目编号
     * @return 项目信息
     */
    public Map<String, Object> getProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            return new HashMap<>();
        }
        
        Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        
        // 查询科研项目信息
        List<Map<String, Object>> projectList = dao.query("KIZLPatenInfo.queryProjectInfo", param);
        
        if (projectList != null && !projectList.isEmpty()) {
            Map<String, Object> project = projectList.get(0);
            // 处理返回数据，确保字段名称正确
            Map<String, Object> result = new HashMap<>();
            result.put("projectCode", project.get("projectCode"));
            result.put("projectName", project.get("projectName"));
            result.put("projectType", project.get("projectType"));
            result.put("startDate", project.get("startDate"));
            result.put("endDate", project.get("endDate"));
            result.put("status", project.get("status"));
            return result;
        }
        
        return new HashMap<>();
    }

    /**
     * 获取相关产品信息
     *
     * @param projectCode 项目编号
     * @return 产品信息
     */
    public Map<String, Object> getRelatedProductInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            return new HashMap<>();
        }
        
        Map<String, Object> param = new HashMap<>();
        param.put("projectCode", projectCode);
        
        // 查询相关产品信息
        List<Map<String, Object>> productList = dao.query("KIZLPatenInfo.queryRelatedProducts", param);
        
        if (productList != null && !productList.isEmpty()) {
            Map<String, Object> product = productList.get(0);
            // 处理返回数据，确保字段名称正确
            Map<String, Object> result = new HashMap<>();
            result.put("productName", product.get("productName"));
            result.put("productType", product.get("productType"));
            result.put("productDesc", product.get("productDesc"));
            return result;
        }
        
        return new HashMap<>();
    }

    /**
     * 获取状态显示名称
     *
     * @param statusCode 状态代码
     * @return 状态显示名称
     */
    private String getStatusDisplayName(String statusCode) {
        // 根据状态代码返回对应的显示名称
        // 这里可以根据实际的状态字典进行映射
        switch (statusCode) {
            case "draft":
                return "草稿";
            case "submit":
                return "已提交";
            case "approved":
                return "已审批";
            case "rejected":
                return "已拒绝";
            default:
                return statusCode;
        }
    }

    /**
     * 查询我的申请 - 本人作为发明人的专利申请
     * 连接流程信息表获取状态、处理时间和操作人信息
     *
     * @param param 查询参数
     * @return 分页数据
     */
    public TableDataInfo queryMyApplications(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }
        
        // 查询我的申请数据
        return getPage("KIZLPatenInfo.queryMyApplications", param);
    }


    public TableDataInfo queryMyProcessing(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }

        // 查询我的申请数据
        return getPage("KIZLPatenInfo.queryMyProcessing", param);
    }

    /**
     * 申请跟踪查询 - 按状态、单位、部门分类统计
     * 对主管部门专利管理员开放
     *
     * @param param 查询参数
     * @return 分页数据
     */
    public TableDataInfo queryApplyTracking(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }
        
        // 查询申请跟踪数据
        return getPage("KIZLPatenInfo.queryApplyTracking", param);
    }


    public TableDataInfo queryApplyTrackingDept(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }

        // 查询申请跟踪数据
        return getPage("KIZLPatenInfo.queryApplyTrackingDept", param);
    }

    public TableDataInfo queryApplyTrackingDept2(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }

        // 查询申请跟踪数据
        return getPage("KIZLPatenInfo.queryApplyTrackingDept2", param);
    }

    public TableDataInfo queryApplyTrackingProject(Map<String, Object> param) {
        if (param == null) {
            param = new HashMap<>();
        }

        // 查询申请跟踪数据
        return getPage("KIZLPatenInfo.queryApplyTrackingProject", param);
    }

    /**
     * 处理申请跟踪权限
     * 只有主管部门专利管理员才能查看所有数据
     *
     * @param queryData 查询参数
     * @param loginName 登录用户
     */
    public void processApplyTrackingPermissions(Map<String, Object> queryData, String loginName) {
        // 检查是否为管理员
        boolean isAdmin = SRoleUtil.isAdmin(loginName);
        if (isAdmin) {
            return; // 管理员可以查看所有数据
        }
        
        // 检查是否有全部读取权限
        boolean hasAllReadAccess = SRoleUtil.isRoleMember(KIZLConstants.KIZL_ALL_READALL, loginName);
        if (hasAllReadAccess) {
            return; // 有全部读取权限的用户可以查看所有数据
        }
        
        // 按优先级检查各种角色权限
        for (String role : KIZLConstants.roleHierarchy) {
            List<ADOrg> orgs = SRoleUtil.getOrgByUser(loginName, role);
            if (ObjectUtil.isNotEmpty(orgs)) {
                queryData.put("dynSql", buildOrgPermissionSql(orgs, "A.FIRST_DEPT_PATH"));
                return;
            }
        }
        
        // 如果没有匹配的角色权限，不允许查看申请跟踪数据
        queryData.put("dynSql", "1 = 0"); // 返回空结果
    }

    /**
     * 构建组织权限SQL
     */
    private String buildOrgPermissionSql(List<ADOrg> orgs, String fieldName) {
        StringBuffer dynSql = new StringBuffer("(");
        for (ADOrg adOrg : orgs) {
            if (dynSql.length() > 1) {
                dynSql.append(" OR ");
            }
            dynSql.append(" ").append(fieldName).append(" LIKE '%").append(adOrg.getOrgCode()).append("%'");
        }
        dynSql.append(")");
        return dynSql.toString();
    }

}
