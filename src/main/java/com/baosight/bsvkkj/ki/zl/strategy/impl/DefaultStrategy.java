package com.baosight.bsvkkj.ki.zl.strategy.impl;

import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 默认策略实现
 * 用于处理未明确实现的活动节点
 * 
 * <AUTHOR>
 */
@Component
public class DefaultStrategy implements WorkflowActivityStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(DefaultStrategy.class);
    
    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        WorkFlow workFlow = beanEx.getWorkFlow();
        String currentActivity = workFlow.getCurrentActivity();
        
        logger.warn("活动节点 {} 暂未实现具体业务逻辑，使用默认处理", currentActivity);
        
        // 默认处理：直接返回工作流，不做任何业务处理
        return workFlow;
    }
    
    @Override
    public String getSupportedActivity() {
        // 默认策略不注册到工厂中，由工厂在找不到策略时手动调用
        return "DEFAULT";
    }
} 