<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="KIZLPatenInfo">

    <!-- 专利综合查询 -->
    <select id="queryPatentAll" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        COALESCE(P.PATENT_ID, '') AS "patentId",
        A.APPLY_ID AS "applyId",
        COALESCE(P.SERIAL_NUM, A.SERIAL_NUM) AS "serialNum",
        COALESCE(P.JSBH, A.JSBH) AS "jsbh",
        P.EGBH AS "egbh",
        COALESCE(P.PATENT_NAME, A.APPLY_NAME) AS "patentName",
        P.PATENT_TYPE AS "patentType",
        P<PERSON>PATENT_NO AS "patentNo",
        P.SLRQ AS "slrq",
        <PERSON><PERSON>SQR<PERSON> AS "sqrq",
        P.PATENT_STATUS AS "patentStatus",
        P.FLZT AS "flzt",
        W.CURRENT_ACTIVITY_NAME as "currentActivityName",
        COALESCE(P.FIRST_DEPT_CODE, A.FIRST_DEPT_CODE) AS "firstDeptCode",
        COALESCE(P.FIRST_DEPT_NAME, A.FIRST_DEPT_NAME) AS "firstDeptName",
        COALESCE(P.GLDW_CODE, A.GLDW_CODE) AS "gldwCode",
        COALESCE(P.GLDW_NAME, A.GLDW_NAME) AS "gldwName",
        P.USE_METHOD AS "useMethod",
        P.USE_DEPT AS "useDept",
        P.USE_DEPT_DEPT AS "useDeptDept",
        P.USE_FIRSTDATE AS "useFirstdate",
        COALESCE(P.FROM_TYPE, A.FROM_TYPE) AS "fromType",
        COALESCE(P.FROM_NO, A.FROM_NO) AS "fromNo",
        COALESCE(P.FROM_NAME, A.FROM_NAME) AS "fromName",
        P.LABEL AS "label",
        P.SWS_GUID AS "swsGuid",
        P.SWSDLR AS "swsdlr",
        P.ZLH AS "zlh",
        P.QS AS "qs",
        COALESCE(P.LXR_NAME, A.LXR_NAME) AS "lxrName",
        COALESCE(P.UPDATE_DATE, A.UPDATE_DATE) AS "updateDate",
        SWS.SWS_NAME AS "swsName"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO P ON A.APPLY_ID = P.APPLY_ID AND P.DEL_STATUS = '0'
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO W ON A.APPLY_ID = W.BUSINESS_ID AND W.FLOW_CODE ='KIZLSB01'
        LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO SWS ON P.SWS_GUID = SWS.SWS_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX R ON A.APPLY_ID = R.APPLY_ID AND R.DEL_STATUS = '0'
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_SQR S ON A.APPLY_ID = S.APPLY_ID AND S.DEL_STATUS = '0'
        WHERE A.DEL_STATUS = '0'
        AND A.FLOW_STATUS NOT IN ('draft')
        <!-- 模糊查询：专利名称、流水号、鄂钢编号、接收编号、申请号、摘要 -->
        <isNotEmpty prepend=" AND " property="comprehensiveFuzzy">
            (COALESCE(P.PATENT_NAME, A.APPLY_NAME) LIKE '%$comprehensiveFuzzy$%'
            OR COALESCE(P.SERIAL_NUM, A.SERIAL_NUM) LIKE '%$comprehensiveFuzzy$%'
            OR P.EGBH LIKE '%$comprehensiveFuzzy$%'
            OR COALESCE(P.JSBH, A.JSBH) LIKE '%$comprehensiveFuzzy$%'
            OR P.PATENT_NO LIKE '%$comprehensiveFuzzy$%'
            OR A.SUMMARY LIKE '%$comprehensiveFuzzy$%')
        </isNotEmpty>
        <!-- 基本信息查询条件 -->
        <isNotEmpty prepend=" AND " property="serialNumLike">COALESCE(P.SERIAL_NUM, A.SERIAL_NUM) LIKE '%$serialNumLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNameLike">COALESCE(P.PATENT_NAME, A.APPLY_NAME) LIKE '%$patentNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentType">P.PATENT_TYPE = #patentType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">P.EGBH LIKE '%$egbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jsbhLike">COALESCE(P.JSBH, A.JSBH) LIKE '%$jsbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNoLike">P.PATENT_NO LIKE '%$patentNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="ownership">P.QS = #ownership#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstDeptCode">COALESCE(P.FIRST_DEPT_CODE, A.FIRST_DEPT_CODE) = #firstDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentStatus">P.PATENT_STATUS = #patentStatus#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flzt">P.FLZT = #flzt#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="useMethod">P.USE_METHOD = #useMethod#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromType">COALESCE(P.FROM_TYPE, A.FROM_TYPE) = #fromType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromNoLike">COALESCE(P.FROM_NO, A.FROM_NO) LIKE '%$fromNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromNameLike">COALESCE(P.FROM_NAME, A.FROM_NAME) LIKE '%$fromNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsGuid">P.SWS_GUID = #swsGuid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsdlr">P.SWSDLR = #swsdlr#</isNotEmpty>
        <!-- 日期范围查询 -->
        <isNotEmpty prepend=" AND " property="startDate">P.SLRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">P.SLRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
        <!-- 标签查询（支持多选） -->
        <isNotEmpty prepend=" AND " property="labelIn">P.LABEL IN ($labelIn$)</isNotEmpty>
        <!-- 发明人查询条件 -->
        <isNotEmpty prepend=" AND " property="inventorNameLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = A.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.EMP_NAME LIKE '%$inventorNameLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorJobNoLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = A.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.EMP_ID LIKE '%$inventorJobNoLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorPosition">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = A.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.POST_LEVEL = #inventorPosition#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorTitle">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = A.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.POST_TITLE = #inventorTitle#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorUnit">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = A.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.DEPT_CODE = #inventorUnit#)
        </isNotEmpty>

        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql2">$dynSql2$</isNotEmpty>
        <isNotEmpty prepend=" ORDER BY " property="displayOrder">$displayOrder$</isNotEmpty>
        <isEmpty prepend=" ORDER BY " property="displayOrder">COALESCE(P.UPDATE_DATE, A.UPDATE_DATE) DESC</isEmpty>
    </select>

    <!-- 查询同来源技术秘密列表 -->
    <select id="queryRelatedSecrets" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        t1.TECHNOLOGY_ID as "technologyId",
        t1.CONFIRM_NUM as "rdh",
        t1.TECHNOLOGY_NAME as "technologyName",
        t1.STATUS as "flowStatus",
        t1.OWNERSHIP_NUM as "qs"
        FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET t1
        WHERE t1.DEL_STATUS = '0'
        <isNotEmpty prepend=" AND " property="fromNo">
            t1.SOURCE_CODE = #fromNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="authSource">
            t1.AUTH_SOURCE = #authSource#
        </isNotEmpty>
        ORDER BY t1.CREATE_DATE DESC
    </select>

    <!-- 查询同来源专利列表 -->
    <select id="queryRelatedPatents" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        t1.APPLY_ID as "applyId",
        t1.JSBH as "jsbh",
        t1.APPLY_NAME as "applyName",
        t1.FLOW_STATUS as "flowStatus",
        t2.QS as "qs",
        t2.PATENT_ID as "patentId"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO t1
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO t2 ON t1.APPLY_ID = t2.APPLY_ID
        WHERE t1.DEL_STATUS = '0'
        <isNotEmpty prepend=" AND " property="fromNo">
            t1.FROM_NO = #fromNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromType">
            t1.FROM_TYPE = #fromType#
        </isNotEmpty>
        ORDER BY t1.CREATE_DATE DESC
    </select>

    <!-- 根据来源编号查询有效专利列表 -->
    <select id="queryValidPatentsByFromNo" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        t1.APPLY_ID as "applyId",
        t1.JSBH as "jsbh",
        t1.APPLY_NAME as "applyName",
        t1.FLOW_STATUS as "flowStatus",
        t1.FROM_NO as "fromNo",
        t1.FROM_NAME as "fromName",
        t1.FROM_TYPE as "fromType",
        t1.FIRST_DEPT_CODE as "firstDeptCode",
        t1.FIRST_DEPT_NAME as "firstDeptName",
        t1.GLDW_CODE as "gldwCode",
        t1.GLDW_NAME as "gldwName",
        t1.LXR_NAME as "lxrName",
        t1.LXR_CODE as "lxrCode",
        t1.CREATE_DATE as "createDate",
        t1.UPDATE_DATE as "updateDate",
        t2.PATENT_ID as "patentId",
        t2.PATENT_NAME as "patentName",
        t2.PATENT_TYPE as "patentType",
        t2.PATENT_NO as "patentNo",
        t2.PATENT_STATUS as "patentStatus",
        t2.FLZT as "flzt",
        t2.ISVALID as "isvalid",
        t2.QS as "qs",
        t2.EGBH as "egbh",
        t2.SLRQ as "slrq",
        t2.SQRQ as "sqrq",
        t2.ZLH as "zlh",
        t2.USE_METHOD as "useMethod",
        t2.USE_DEPT as "useDept",
        t2.USE_FIRSTDATE as "useFirstdate",
        t2.SWS_GUID as "swsGuid",
        t2.SWSDLR as "swsdlr"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO t1
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO t2 ON t1.APPLY_ID = t2.APPLY_ID AND t2.DEL_STATUS = '0'
        WHERE t1.DEL_STATUS = '0'
        AND t2.ISVALID = '1'
        <isNotEmpty prepend=" AND " property="fromNo">
            t1.FROM_NO = #fromNo#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromType">
            t1.FROM_TYPE = #fromType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentType">
            t2.PATENT_TYPE = #patentType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentStatus">
            t2.PATENT_STATUS = #patentStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="flzt">
            t2.FLZT = #flzt#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstDeptCode">
            t1.FIRST_DEPT_CODE = #firstDeptCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="gldwCode">
            t1.GLDW_CODE = #gldwCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNameLike">
            (t2.PATENT_NAME LIKE '%$patentNameLike$%' OR t1.APPLY_NAME LIKE '%$patentNameLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNoLike">
            t2.PATENT_NO LIKE '%$patentNoLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="jsbhLike">
            t1.JSBH LIKE '%$jsbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">
            t2.EGBH LIKE '%$egbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" ORDER BY " property="displayOrder">$displayOrder$</isNotEmpty>
        <isEmpty prepend=" ORDER BY " property="displayOrder">t1.UPDATE_DATE DESC</isEmpty>
    </select>

    <!-- 查询科研项目信息 -->
    <select id="queryProjectInfo" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        t1.PROJECT_CODE as "projectCode",
        t1.PROJECT_NAME as "projectName",
        t1.PROJECT_TYPE as "projectType",
        t1.START_DATE as "startDate",
        t1.END_DATE as "endDate",
        t1.STATUS as "status"
        FROM KJGL.T_KYXM_PROJECT t1
        WHERE t1.DEL_STATUS = '0'
        <isNotEmpty prepend=" AND " property="projectCode">
            t1.PROJECT_CODE = #projectCode#
        </isNotEmpty>
    </select>

    <!-- 查询相关产品信息 -->
    <select id="queryRelatedProducts" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        t1.PRODUCT_NAME as "productName",
        t1.PRODUCT_TYPE as "productType",
        t1.PRODUCT_DESC as "productDesc"
        FROM KJGL.T_KYXM_PROJECT_PRODUCT t1
        WHERE t1.DEL_STATUS = '0'
        <isNotEmpty prepend=" AND " property="projectCode">
            t1.PROJECT_CODE = #projectCode#
        </isNotEmpty>
        ORDER BY t1.CREATE_DATE DESC
    </select>

    <!-- 查询我的申请 - 本人作为发明人的专利申请 -->
    <select id="queryMyApplications" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        A.APPLY_ID as "applyId",
        A.APPLY_NAME as "applyName",
        A.APPLY_DATE as "applyDate",
        A.FIRST_DEPT_NAME as "firstDeptName",
        A.GLDW_NAME as "gldwName",
        A.LXR_NAME as "lxrName",
        A.FLOW_STATUS as "flowStatus",
        W.CURRENT_ACTIVITY_NAME as "currentActivityName",
        W.LAST_TIME as "lastTime",
        W.CURRENT_OPERATOR as "currentOperator"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        INNER JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX R ON A.APPLY_ID = R.APPLY_ID
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO W ON A.APPLY_ID = W.BUSINESS_ID
        WHERE 1=1
        AND A.DEL_STATUS = '0'
        AND A.FLOW_STATUS NOT IN ('draft')
        <isNotEmpty prepend=" AND " property="inventorEmpId">(R.EMP_ID = #inventorEmpId# OR A.LXR_CODE = #inventorEmpId#)</isNotEmpty>
        <isNotEmpty prepend=" AND " property="lxrCode">A.LXR_CODE = #lxrCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="applyNameLike">A.APPLY_NAME LIKE '%$applyNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstDeptCode">A.FIRST_DEPT_CODE = #firstDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowStatus">A.FLOW_STATUS = #flowStatus#</isNotEmpty>
        <isNotEmpty prepend=" ORDER BY " property="displayOrder">$displayOrder$</isNotEmpty>
    </select>


    <select id="queryMyProcessing" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        DISTINCT
        B.APPLY_ID AS "applyId",
        B.APPLY_NAME AS "applyName",
        B.LXR_NAME AS "lxrName",
        B.LXR_CODE AS "lxrCode",
        B.FIRST_DEPT_NAME AS "firstDeptName",
        B.FIRST_DEPT_CODE AS "firstDeptCode",
        A.FLOW_CODE AS "processCode" ,
        A.FLOW_ID AS "flowId" ,
        A.CURRENT_ACTIVITY AS "currentActivity" ,
        A.CURRENT_ACTIVITY_NAME AS "currentActivityName",
        A.BUSINESS_ID AS "businessId" ,
        A.BUSINESS_NAME AS "businessName" ,
        A.BUSINESS_TYPE AS "businessType" ,
        A.CURRENT_OPERATOR AS "currentOperator" ,
        A.PROCESS_INSTANCE_ID AS "processInstanceId",
        A.LAST_TIME AS "lastTime"
        FROM
        ${ggmkSchema}.V_MPWF_YB A
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO B ON A.BUSINESS_ID = B.APPLY_ID
        WHERE A.ASSIGNEE_ID = #userLabel#
        <isNotEmpty prepend=" AND " property="businessType">A.BUSINESS_TYPE = #businessType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowCode">A.FLOW_CODE IN
            <iterate property="flowCode" conjunction="," open="(" close=")">
                #flowCode[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">A.BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="applyNameLike">B.APPLY_NAME like '%$applyNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <!-- 申请跟踪查询 - 按状态、单位、部门分类统计 -->
    <select id="queryApplyTracking" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        COALESCE(B.CURRENT_ACTIVITY ,'yzz')   AS "currentActivity",
        COALESCE(B.CURRENT_ACTIVITY_NAME,'已结束')  AS "currentActivityName",
        count(COALESCE(B.CURRENT_ACTIVITY ,'yzz')) as "num"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO B
        ON A.APPLY_ID = B.BUSINESS_ID
        <dynamic prepend="WHERE">
            <isEmpty prepend=" AND " property="isDraft"> A.FLOW_STATUS NOT IN ('draft') </isEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        GROUP BY B.CURRENT_ACTIVITY, B.CURRENT_ACTIVITY_NAME
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <select id="queryApplyTrackingDept" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.GLDW_NAME as "gldwName",
        A.GLDW_CODE as "gldwCode",
        count (A.GLDW_CODE) as "num"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO B
        ON A.APPLY_ID = B.BUSINESS_ID
        <dynamic prepend="WHERE">
            <isEmpty prepend=" AND " property="isDraft"> A.FLOW_STATUS NOT IN ('draft') </isEmpty>
            <isNotEmpty prepend=" AND " property="currentActivity">B.CURRENT_ACTIVITY=#currentActivity#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="currentIsNull">B.CURRENT_ACTIVITY is null </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        GROUP BY A.GLDW_NAME,A.GLDW_CODE
    </select>
    <select id="queryApplyTrackingDept2" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.FIRST_DEPT_NAME as "firstDeptName",
        A.FIRST_DEPT_CODE as "firstDeptCode",
        count (A.FIRST_DEPT_CODE) as "num"
        FROM
        ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        left JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO B
        ON A.APPLY_ID = B.BUSINESS_ID
        <dynamic prepend="WHERE">
            <isEmpty prepend=" AND " property="isDraft"> A.FLOW_STATUS NOT IN ('draft') </isEmpty>
            <isNotEmpty prepend=" AND " property="currentActivity">B.CURRENT_ACTIVITY=#currentActivity#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="currentIsNull">B.CURRENT_ACTIVITY is null </isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">A.GLDW_CODE='$gldwCode$'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        GROUP BY A.FIRST_DEPT_NAME,A.FIRST_DEPT_CODE
    </select>

    <select id="queryApplyTrackingProject" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.APPLY_ID as "applyId",
        A.JSBH as "jsbh",
        A.APPLY_DATE as "applyDate",
        A.APPLY_NAME as "applyName",
        A.LXR_NAME as "lxrName"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        left JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO B
        ON A.APPLY_ID = B.BUSINESS_ID
        <dynamic prepend="WHERE">
            <isEmpty prepend=" AND " property="isDraft"> A.FLOW_STATUS NOT IN ('draft') </isEmpty>
            <isNotEmpty prepend=" AND " property="currentActivity">B.CURRENT_ACTIVITY=#currentActivity#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="currentIsNull">B.CURRENT_ACTIVITY is null </isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">A.GLDW_CODE='$gldwCode$'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstDeptCode">A.FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        GROUP BY A.APPLY_ID,A.JSBH,A.APPLY_DATE,A.APPLY_NAME,A.LXR_NAME
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <!-- 技术交底清单查询 -->
    <select id="queryjsjdqd" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
            A.APPLY_ID as "applyId",
            P.SWS_GUID as "swsGuid",
            SWS.SWS_NAME as "swsName",
            A.JSBH as "jsbh",
            A.APPLY_NAME as "applyName",
            A.LXR_NAME as "lxrName",
            A.LXR_PHONE as "lxrPhone",
            A.FLOW_STATUS as "flowStatus",
            A.ZGBM_NC as "zgbmNc",
            A.EXTRA2 as "extra2"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO P ON A.APPLY_ID = P.APPLY_ID AND P.DEL_STATUS = '0'
        LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO SWS ON P.SWS_GUID = SWS.SWS_ID
        WHERE A.DEL_STATUS = '0'
        AND P.PATENT_STATUS = '03'
        <isNotEmpty prepend=" AND " property="jsbhLike">
            A.JSBH LIKE '%$jsbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="applyNameLike">
            A.APPLY_NAME LIKE '%$applyNameLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="lxrNameLike">
            A.LXR_NAME LIKE '%$lxrNameLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <!-- 当年申请专利查询 -->
    <select id="queryApplyThisYear" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
            A.APPLY_ID as "applyId",
            A.JSBH as "jsbh",
            P.EGBH as "egbh",
            A.APPLY_NAME as "applyName",
            P.SLRQ as "slrq",
            A.FIRST_DEPT_NAME as "firstDeptName",
            A.GLDW_NAME as "gldwName",
            A.LXR_NAME as "lxrName",
            A.FLOW_STATUS as "flowStatus",
            P.PATENT_TYPE as "patentType",
            P.PATENT_STATUS as "patentStatus"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO P ON A.APPLY_ID = P.APPLY_ID AND P.DEL_STATUS = '0'
        WHERE A.DEL_STATUS = '0'
        AND EXTRACT(YEAR FROM P.SLRQ) = EXTRACT(YEAR FROM SYSDATE)
        <isNotEmpty prepend=" AND " property="jsbhLike">
            A.JSBH LIKE '%$jsbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="applyNameLike">
            A.APPLY_NAME LIKE '%$applyNameLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">
            P.EGBH LIKE '%$egbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentType">
            P.PATENT_TYPE = #patentType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentStatus">
            P.PATENT_STATUS = #patentStatus#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstDeptCode">
            A.FIRST_DEPT_CODE = #firstDeptCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="gldwCode">
            A.GLDW_CODE = #gldwCode#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">
            $dynSql$
        </isNotEmpty>
        <isNotEmpty prepend=" ORDER BY " property="displayOrder">$displayOrder$</isNotEmpty>
        <isEmpty prepend=" ORDER BY " property="displayOrder">P.EGBH DESC</isEmpty>
    </select>

    <!-- 专利交底发明人信息查询 -->
    <select id="queryPatentInventorInfo" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
            A.APPLY_ID as "applyId",
            A.ZGBM_NC as "zgbmNc",
            SWS.SWS_NAME as "swsName",
            A.JSBH as "jsbh",
            A.APPLY_NAME as "applyName",
            (SELECT LISTAGG(R2.EMP_NAME || '(' || R2.EMP_ID || ')', '；') WITHIN GROUP (ORDER BY R2.RYXH)
             FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX R2
             WHERE R2.APPLY_ID = A.APPLY_ID AND R2.DEL_STATUS = '0') as "inventorList",
            (SELECT COUNT(*)
             FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX R3
             WHERE R3.APPLY_ID = A.APPLY_ID AND R3.DEL_STATUS = '0') as "inventorCount",
            (SELECT R4.ID_CARD
             FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX R4
             WHERE R4.APPLY_ID = A.APPLY_ID AND R4.DEL_STATUS = '0' AND R4.RYXH = 1) as "firstInventorIdCard",
            (SELECT LISTAGG(SQ.LEGAL_NAME || '|' || COALESCE(SQ.MAIL_ADDRESS, '') || '|' || COALESCE(SQ.POST_OFFICE, ''), '；') WITHIN GROUP (ORDER BY SQ.XH)
             FROM ${zzzcSchema}.T_KIZL_APPLY_SQR SQ
             WHERE SQ.APPLY_ID = A.APPLY_ID AND SQ.DEL_STATUS = '0') as "applicantList"
        FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO P ON A.APPLY_ID = P.APPLY_ID AND P.DEL_STATUS = '0'
        LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO SWS ON P.SWS_GUID = SWS.SWS_ID
        WHERE A.DEL_STATUS = '0'
        AND P.PATENT_STATUS = '03'
        <isNotEmpty prepend=" AND " property="jsbhLike">
            A.JSBH LIKE '%$jsbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">
            P.EGBH LIKE '%$egbhLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNoLike">
            P.PATENT_NO LIKE '%$patentNoLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="applyNameLike">
            A.APPLY_NAME LIKE '%$applyNameLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorNameLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX R5
                    WHERE R5.APPLY_ID = A.APPLY_ID AND R5.DEL_STATUS = '0'
                    AND R5.EMP_NAME LIKE '%$inventorNameLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">
            $dynSql$
        </isNotEmpty>
        <isNotEmpty prepend=" ORDER BY " property="displayOrder">$displayOrder$</isNotEmpty>
        <isEmpty prepend=" ORDER BY " property="displayOrder">A.JSBH DESC</isEmpty>
    </select>

</sqlMap>