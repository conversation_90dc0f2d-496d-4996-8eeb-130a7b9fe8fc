package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyZjps;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyZjps;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 境内专利_申请_专家评审Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class ServiceKIZLApplyZjps extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessKIZLApplyZjps businessKIZLApplyZjps;

	/**
	*
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TkizlApplyZjps bean = businessKIZLApplyZjps.initBean(loginName);
            inInfo.set("applyZjps", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlApplyZjps> queryList = businessKIZLApplyZjps.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessKIZLApplyZjps.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkizlApplyZjps bean = BeanUtil.toBean(row, TkizlApplyZjps.class);
            businessKIZLApplyZjps.save(UserSession.getLoginName(), bean);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String ryxxId = (String) inInfo.get("ryxxId");
            TkizlApplyZjps query = businessKIZLApplyZjps.query(ryxxId);
            inInfo.set("applyZjps", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String ryxxId = (String) inInfo.get("ryxxId");
	        if (StrUtil.isNotBlank(ryxxId)) {
	            for (String id : ryxxId.split(",")) {
	                businessKIZLApplyZjps.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
}
