package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainFrom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_维护_来源信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLMaintainFrom extends BusinessBase {

    private static final String NAME_SPACE = "tkizlMaintainFrom";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlMaintainFrom initBean(String operator) {
        TkizlMaintainFrom maintainFrom = new TkizlMaintainFrom();
        return maintainFrom;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlMaintainFrom> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlMaintainFrom> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param fromId
     * @return
     */
    public TkizlMaintainFrom query(String fromId) {
        if (StringUtils.isBlank(fromId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("fromId", fromId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainFrom) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param maintainFrom
     * @return
     */
    public TkizlMaintainFrom insert(String operator, TkizlMaintainFrom maintainFrom) {
        maintainFrom.initAdd(operator);
        if (StringUtils.isEmpty(maintainFrom.getFromId())) {
            maintainFrom.setFromId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", maintainFrom);
        return maintainFrom;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param maintainFrom
     * @return
     */
    public TkizlMaintainFrom update(String operator, TkizlMaintainFrom maintainFrom) {
        maintainFrom.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", maintainFrom);
        return maintainFrom;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param fromId
     * @return
     */
    public TkizlMaintainFrom deleteLogin(String operator, String fromId) {
        TkizlMaintainFrom maintainFrom = new TkizlMaintainFrom();
        maintainFrom.setFromId(fromId);
        maintainFrom.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", maintainFrom);
        return maintainFrom;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param fromId
     * @return
     */
    public int delete(String operator, String fromId) {
        return dao.delete(NAME_SPACE + ".delete", fromId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param maintainFrom
     * @return
     */
    public TkizlMaintainFrom save(String operator, TkizlMaintainFrom maintainFrom) {
        if (StringUtils.isEmpty(maintainFrom.getFromId())) {
            return insert(operator, maintainFrom);
        } else {
            if (null == query(maintainFrom.getFromId())) {
                return insert(operator, maintainFrom);
            }
            return update(operator, maintainFrom);
        }
    }

}
