package com.baosight.bsvkkj.ki.zl.strategy.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLPatentInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.ki.zl.util.KIZLCommonUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Manual4活动节点策略实现
 * 主管部门接收
 * 
 * <AUTHOR>
 */
@Component
public class Manual4Strategy implements WorkflowActivityStrategy {
    
    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    
    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;
    
    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        WorkFlow workFlow = beanEx.getWorkFlow();
        TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(workFlow.getBusinessId());
        List<Map<String, Object>> kwzl_zlsq = KIZLCommonUtil.getStatusReview(workFlow.getBusinessId(), KIZLConstants.KIZL_ZLPS);
        if (ObjectUtil.isNotEmpty(kwzl_zlsq)) {
            if (!KIZLCommonUtil.isPsxx(kwzl_zlsq)) {
                throw new BusinessException("专家评审未结束!");
            }
        } else {
            //指定专家评审
            if ("2".equals(beanEx.getZgbmCz())) {
                throw new BusinessException("请先启动专家评审!");
            }
        }
        // 主管部门接收
        String zgbmCz = beanEx.getZgbmCz(); // 主管部门操作意见
        if (KIZLConstants.ZgbmOperation.AGREE.equals(zgbmCz)) {
            // 同意：保存patent表
            businessKIZLPatentInfo.savePatentInfo(operator, query);
        } else if (KIZLConstants.ZgbmOperation.DISAGREE.equals(zgbmCz)) {
            // 不同意：更新申请表的flowStatus为终止并且挂起流程
            query.setFlowStatus(KIZLConstants.FlowStatus.STOP);
        }
        businessKIZLApplyBaseinfo.update(operator, query);
        
        return workFlow;
    }
    
    @Override
    public String getSupportedActivity() {
        return KizlWorkflowActivityEnum.MANUAL4.getCode();
    }
} 