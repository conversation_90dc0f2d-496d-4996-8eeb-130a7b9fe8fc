package com.baosight.bsvkkj.ki.zl.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.domain.Ztree;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainTecharea;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Controller
@RequestMapping("/kizl/maintainTecharea")
public class ControllerKIZLMaintainTecharea extends BaseController{
    private String prefix = "/kizl/maintainTecharea";
    
    @GetMapping()
    public String maintainTecharea(){
        return prefix + "/maintainTechareaList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询【请填写功能名称】列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLMaintainTecharea", "page");
		return getDataTable(query);
	}

    /**
     * 新增【请填写功能名称】
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("maintainTecharea", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKIZLMaintainTecharea", "initLoad", "maintainTecharea"));
        return prefix + "/maintainTecharea";
    }

    /**
     * 修改【请填写功能名称】
     */
    @GetMapping("/edit/{areaId}")
    public String edit(@PathVariable("areaId") String areaId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("areaId", areaId);

        modelMap.put("maintainTecharea", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainTecharea", "load", "maintainTecharea"));
        return prefix + "/maintainTecharea";
    }

    /**
     * 保存【请填写功能名称】
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkizlMaintainTecharea maintainTecharea){
        EiInfo eiInfo = getEiInfo("i", maintainTecharea);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainTecharea", "save");
    }
    
    /**
     * 删除【请填写功能名称】
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("areaId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLMaintainTecharea", "remove");
	}

    /**
     * 导出【请填写功能名称】数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkizlMaintainTecharea> list = (List<TkizlMaintainTecharea>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainTecharea", "query", "list");
        ExcelUtil<TkizlMaintainTecharea> util = new ExcelUtil<>(TkizlMaintainTecharea.class);
        String title = "数据导出表";
		util.setSheet("【请填写功能名称】");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出【请填写功能名称】模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TkizlMaintainTecharea> listExcel = new ArrayList<>();
        ExcelUtil<TkizlMaintainTecharea> util = new ExcelUtil<>(TkizlMaintainTecharea.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }
}
