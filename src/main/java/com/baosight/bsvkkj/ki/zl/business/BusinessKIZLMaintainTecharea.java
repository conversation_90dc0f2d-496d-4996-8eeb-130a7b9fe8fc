package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.ki.domain.TkizlMaintainTecharea;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Component
public class BusinessKIZLMaintainTecharea extends BusinessBase {

    private static final String NAME_SPACE = "tkizlMaintainTecharea";

    /**
     * @param param
     * @return List
     */
    public List<TkizlMaintainTecharea> queryListPlus(Map<String, Object> param, int offset, int limit) {
        return dao.query(NAME_SPACE + ".query", param, offset, limit);
    }

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlMaintainTecharea initBean(String operator) {
        TkizlMaintainTecharea maintainTecharea = new TkizlMaintainTecharea();
        return maintainTecharea;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlMaintainTecharea> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlMaintainTecharea> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param areaId
     * @return
     */
    public TkizlMaintainTecharea query(String areaId) {
        if (StringUtils.isBlank(areaId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("areaId", areaId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlMaintainTecharea) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param maintainTecharea
     * @return
     */
    public TkizlMaintainTecharea insert(String operator, TkizlMaintainTecharea maintainTecharea) {
        maintainTecharea.initAdd(operator);
        if (StringUtils.isEmpty(maintainTecharea.getAreaId())) {
            maintainTecharea.setAreaId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", maintainTecharea);
        return maintainTecharea;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param maintainTecharea
     * @return
     */
    public TkizlMaintainTecharea update(String operator, TkizlMaintainTecharea maintainTecharea) {
        maintainTecharea.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", maintainTecharea);
        return maintainTecharea;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param areaId
     * @return
     */
    public TkizlMaintainTecharea deleteLogin(String operator, String areaId) {
        TkizlMaintainTecharea maintainTecharea = new TkizlMaintainTecharea();
        maintainTecharea.setAreaId(areaId);
        maintainTecharea.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", maintainTecharea);
        return maintainTecharea;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param areaId
     * @return
     */
    public int delete(String operator, String areaId) {
        return dao.delete(NAME_SPACE + ".delete", areaId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param maintainTecharea
     * @return
     */
    public TkizlMaintainTecharea save(String operator, TkizlMaintainTecharea maintainTecharea) {
        if (StringUtils.isEmpty(maintainTecharea.getAreaId())) {
            return insert(operator, maintainTecharea);
        } else {
            if (null == query(maintainTecharea.getAreaId())) {
                return insert(operator, maintainTecharea);
            }
            return update(operator, maintainTecharea);
        }
    }

}
