package com.baosight.bsvkkj.ki.zl.domain;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Data
public class TkizlApplyBaseinfoEx extends TkizlApplyBaseinfo {

    public static TkizlApplyBaseinfoEx initParent(TkizlApplyBaseinfo bean) {
        TkizlApplyBaseinfoEx beanEx = new TkizlApplyBaseinfoEx();
        BeanUtil.copyProperties(bean, beanEx, false);
        return beanEx;
    }

    /** 流程信息 */
    private WorkFlow workFlow;
    /** 附件信息 */
    private Map<String,String> attachmentS;

    /** 页面号 */
    private String pageNo;

    /**相关附件*/
    private String rhxgfj;

    private String gtsqxy;

    private String sqxgwj;

    private String jsjds;

    private String sqtzxgfj;

    private String sqxxxgfj;

    private String zlzsfj;

    private String ztwhfj;

    private String start;

    /**发明人列表*/
    private List<TkizlApplyRyxx> ryxxList;
    /**申请人列表*/
    private List<TkizlApplySqr> sqrList;

    /** 专利信息 */
    private @Valid TkizlPatentInfo patentInfo;

}
