package com.baosight.bsvkkj.ki.zl.controller;

import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlProcessCodeEnum;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.utils.ServiceUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@Controller
@RequestMapping("/kizl/workFlow")
public class ControllerKIZLWorkFlow extends BaseController{
    private String prefix = "/kizl/db";

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix + "/" + pageNo;
    }

    /**
     * 根据节点查询对应的待办数
     *
     * @param req
     * @param model
     * @return
     */
    @GetMapping("/KIZLDB00")
    public String queryBusinessTypeDbList(HttpServletRequest req, ModelMap model) {
        Map<String,String> query = paramMap();
        query.put("businessType", KIZLConstants.BUSINESS_TYPE);
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, query);
        EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLWorkFlow", "queryModuleDbList");
        outInfo.setAttr(paramMap());
        model.put("ei", outInfo);
        return prefix + "/KIZLDB00";
    }
    /**
     * 待办列表
     * @param map
     * @return
     */
    @PostMapping("/pageDB")
    @ResponseBody
    public TableDataInfo pageDB(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKIZLWorkFlow", "pageDB");
        return getDataTable(query);
    }


    /**
     * 待办详细
     * @param processCode
     * @param pageNo
     * @param businessId
     * @param taskId 可以为空
     * @param model
     * @return
     */
    @GetMapping(value={"/queryDBDetail/{processCode}/{pageNo}/{businessId}/{taskId}","/queryDBDetail/{processCode}/{pageNo}/{businessId}/"})
    public String queryDBDetail(@PathVariable("processCode") String processCode, @PathVariable("pageNo") String pageNo,
                                @PathVariable("businessId") String businessId,
                                @PathVariable(value="taskId",required = false) String taskId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("processCode", processCode);
        eiInfo.set("businessId", businessId);
        eiInfo.set("taskId", taskId);
        KizlProcessCodeEnum processCodeEnum = KizlProcessCodeEnum.getProcessCode(processCode);
        switch (processCodeEnum) {
            case KIZL_SB:
                TkizlApplyBaseinfoEx applyEx = (TkizlApplyBaseinfoEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLApplyBaseinfo", "queryDBDetail", "applyBaseinfo");
                model.put("applyBaseinfo", applyEx);
                if (StrUtil.isNotBlank(applyEx.getPageNo())) {
                    pageNo = applyEx.getPageNo();
                }
                break;
            default:
                throw new PlatException(pageNo+"找不到对应的服务!");
        }
        pageNo = processCodeEnum.getPathCode() +"/" + pageNo;


        model.put("businessId", businessId);
        model.put("taskId", taskId);
        return pageNo;
    }

    /**
     * 获取下一步提交相关操作
     */
    @PostMapping("/getNextSubmitWF")
    @ResponseBody
    public AjaxResult getNextSubmitWF(@Validated TkizlApplyBaseinfoEx beanEx) {
        EiInfo eiInfo = new EiInfo();
        WorkFlow workFlow = beanEx.getWorkFlow();
        eiInfo.set("workFlow", workFlow);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLWorkFlow", "getNextSubmitWF", "workFlow");
    }

    /**
     * 退回流程待办
     */
    @PostMapping(value = "/returnWF")
    @ResponseBody
    public AjaxResult returnWF(@Validated TkizlApplyBaseinfoEx beanEx) {
        EiInfo eiInfo = new EiInfo();
        WorkFlow workFlow = beanEx.getWorkFlow();
        eiInfo.set("workFlow", workFlow);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKIZLWorkFlow", "returnWF");
    }

}
