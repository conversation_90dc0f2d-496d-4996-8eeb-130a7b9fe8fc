package com.baosight.bsvkkj.ki.zl.business;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_申请_人员信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLApplyRyxx extends BusinessBase {

    private static final String NAME_SPACE = "tkizlApplyRyxx";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlApplyRyxx initBean(String operator) {
        TkizlApplyRyxx applyRyxx = new TkizlApplyRyxx();
        return applyRyxx;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlApplyRyxx> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlApplyRyxx> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param ryxxId
     * @return
     */
    public TkizlApplyRyxx query(String ryxxId) {
        if (StringUtils.isBlank(ryxxId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("ryxxId", ryxxId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlApplyRyxx) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param applyRyxx
     * @return
     */
    public TkizlApplyRyxx insert(String operator, TkizlApplyRyxx applyRyxx) {
        applyRyxx.initAdd(operator);
        if (StringUtils.isEmpty(applyRyxx.getRyxxId())) {
            applyRyxx.setRyxxId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", applyRyxx);
        return applyRyxx;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param applyRyxx
     * @return
     */
    public TkizlApplyRyxx update(String operator, TkizlApplyRyxx applyRyxx) {
        applyRyxx.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", applyRyxx);
        return applyRyxx;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param ryxxId
     * @return
     */
    public TkizlApplyRyxx deleteLogin(String operator, String ryxxId) {
        TkizlApplyRyxx applyRyxx = new TkizlApplyRyxx();
        applyRyxx.setRyxxId(ryxxId);
        applyRyxx.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", applyRyxx);
        return applyRyxx;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param ryxxId
     * @return
     */
    public int delete(String operator, String ryxxId) {
        return dao.delete(NAME_SPACE + ".delete", ryxxId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param applyRyxx
     * @return
     */
    public TkizlApplyRyxx save(String operator, TkizlApplyRyxx applyRyxx) {
        if (StringUtils.isEmpty(applyRyxx.getRyxxId())) {
            return insert(operator, applyRyxx);
        } else {
            if (null == query(applyRyxx.getRyxxId())) {
                return insert(operator, applyRyxx);
            }
            return update(operator, applyRyxx);
        }
    }

    public int deleteByC(String applyId) {
        Map<String, Object> param = new HashMap<>();
        param.put("applyId", applyId);
        List<TkizlApplyRyxx> list = this.queryList(param);
        if (ObjectUtil.isEmpty(list)) {
            return 0;
        }
        return dao.delete(NAME_SPACE + ".deleteByC", param);
    }

    public List<TkizlApplyRyxx> queryListByApplyId(String businessId) {
        Map<String, Object> param = new HashMap<>();
        param.put("applyId", businessId);
        param.put("displayOrder", "RYXH");
        List<TkizlApplyRyxx> list = this.queryList(param);
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        return list;
    }
}
