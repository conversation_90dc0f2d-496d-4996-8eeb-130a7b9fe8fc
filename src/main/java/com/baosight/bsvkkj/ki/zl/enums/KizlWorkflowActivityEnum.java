package com.baosight.bsvkkj.ki.zl.enums;

import java.util.Arrays;

/**
 * 工作流活动节点枚举
 */
public enum KizlWorkflowActivityEnum {
    MANUAL1("Manual1", "编辑申请单"),
    MANUAL2("Manual2", "初步审查"),
    MANUAL3("Manual3", "单位领导审批"),
    MANUAL4("Manual4", "主管部门管理员审核"),
    MANUAL5("Manual5", "主管部门领导审批")
    ,MANUAL6("Manual6", "待交底")
    ,MANUAL7("Manual7", "交底")
    ,MANUAL8("Manual8", "代理中")
    ,MANUAL9("Manual9", "授权通知录入")
    ,MANUAL10("Manual10", "授权信息录入")
    ;

    private final String code;
    private final String description;

    KizlWorkflowActivityEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据活动代码获取枚举
     *
     * @param code 活动代码
     * @return 活动枚举
     */
    public static KizlWorkflowActivityEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(activity -> activity.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为类型1显示的活动节点
     *
     * @param code 活动代码
     * @return true表示是类型1显示
     */
    public static boolean isType1Display(String code) {
        return MANUAL1.getCode().equals(code) || 
               MANUAL2.getCode().equals(code) || 
               MANUAL3.getCode().equals(code);
    }

    /**
     * 判断是否为类型2显示的活动节点
     *
     * @param code 活动代码
     * @return true表示是类型2显示
     */
    public static boolean isType2Display(String code) {
        return MANUAL4.getCode().equals(code) || 
               MANUAL5.getCode().equals(code);
    }
} 