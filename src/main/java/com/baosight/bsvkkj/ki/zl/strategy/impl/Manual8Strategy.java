package com.baosight.bsvkkj.ki.zl.strategy.impl;

import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLPatentInfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyRyxx;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplySqr;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlWorkflowActivityEnum;
import com.baosight.bsvkkj.ki.zl.strategy.WorkflowActivityStrategy;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manual8活动节点策略实现
 * 代理中阶段处理
 * 
 * <AUTHOR>
 */
@Component
public class Manual8Strategy implements WorkflowActivityStrategy {

    @Resource
    private BusinessKIZLPatentInfo businessKIZLPatentInfo;
    
    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    

    @Override
    public WorkFlow process(String operator, TkizlApplyBaseinfoEx beanEx) {
        WorkFlow workFlow = beanEx.getWorkFlow();
        String businessId = workFlow.getBusinessId();
        
        // 1. 保存申请基本信息
        this.businessKIZLApplyBaseinfo.doSave(operator, beanEx);
        // 2. 保存/更新专利信息
        TkizlPatentInfo patentInfo = beanEx.getPatentInfo();
        if (ObjectUtil.isNotEmpty(patentInfo)) {
            // 设置专利状态为已受理
            patentInfo.setPatentStatus(KIZLConstants.PatentStatus.ACCEPTED);
            patentInfo.setFlowStatus(KIZLConstants.FlowStatus.ACTIVE);

            // 如果申请号不为空，根据申请号自动计算专利类型
            if (StrUtil.isNotBlank(patentInfo.getPatentNo())) {
                String patentType = calculatePatentType(patentInfo.getPatentNo());
                if (StrUtil.isNotBlank(patentType)) {
                    patentInfo.setPatentType(patentType);
                }
            }
            // 自动生成鄂钢编号（如果还没有）
            if (StrUtil.isBlank(patentInfo.getEgbh()) && StrUtil.isNotBlank(patentInfo.getPatentNo())) {
                String egbh = generateEgbh(patentInfo.getPatentNo(), patentInfo.getPatentType());
                patentInfo.setEgbh(egbh);
            }
            // 保存专利信息
            businessKIZLPatentInfo.save(operator, patentInfo);
        }
        
        // 返回处理后的工作流对象
        return workFlow;
    }
    
    /**
     * 根据申请号计算专利类型
     * @param patentNo 申请号
     * @return 专利类型
     */
    private String calculatePatentType(String patentNo) {
        if (StrUtil.isBlank(patentNo) || patentNo.length() < 5) {
            return null;
        }
        
        char typeChar = patentNo.charAt(4); // 第5位
        switch (typeChar) {
            case '1':
                return "FM"; // 发明
            case '2':
                return "SYXX"; // 实用新型
            case '3':
                return "WGSJ"; // 外观设计
            case '8':
                return "PCT_FM"; // PCT发明
            case '9':
                return "PCT_SYXX"; // PCT实用新型
            default:
                return null;
        }
    }
    
    /**
     * 生成鄂钢编号
     * 规则:1位子公司类别(总部(BSVK00)-0，其它-Q) + 7位同一个子公司累计的流水号
     * @param patentNo 申请号
     * @param patentType 专利类型
     * @return 鄂钢编号
     */
    private String generateEgbh(String patentNo, String patentType) {
        if (StrUtil.isBlank(patentNo) || patentNo.length() < 4) {
            return null;
        }
        
        // 获取当前用户的组织信息，用于确定子公司类别
        String operator = UserSession.getLoginName();
        String firstDeptCode = getFirstDeptCodeByOperator(operator);
        
        // 确定子公司类别前缀（1位）
        String companyPrefix = getCompanyPrefix(firstDeptCode);
        
        // 生成7位流水号
        String sequence = generateSequenceNumber(companyPrefix);
        
        return companyPrefix + sequence;
    }
    
    /**
     * 根据操作员获取第一申报部门代码
     * @param operator 操作员
     * @return 第一申报部门代码
     */
    private String getFirstDeptCodeByOperator(String operator) {
        // 这里需要根据实际业务逻辑获取用户的第一申报部门
        // 可以通过用户组织关系或其他方式获取
        try {
            ADOrg userOrg = SOrgUtil.getMainOrgByUserCode(operator);
            return userOrg != null ? userOrg.getOrgCode() : null;
        } catch (Exception e) {
            // 如果获取失败，返回null
            return null;
        }
    }
    
    /**
     * 根据部门代码确定子公司类别前缀
     * @param firstDeptCode 第一申报部门代码
     * @return 子公司类别前缀
     */
    private String getCompanyPrefix(String firstDeptCode) {
        if (StrUtil.isBlank(firstDeptCode)) {
            return "Q"; // 默认为其它
        }
        
        try {
            ADOrg org = SOrgUtil.getOrgByOrgCode(firstDeptCode);
            if (org != null) {
                String orgPathCode = org.getOrgPathCode();
                if (StrUtil.isNotBlank(orgPathCode) && orgPathCode.contains("BSVK00")) {
                    return "0"; // 总部
                } else {
                    return "Q"; // 其它
                }
            }
        } catch (Exception e) {
            // 如果获取组织信息失败，默认为其它
        }
        
        return "Q"; // 默认为其它
    }
    
    /**
     * 生成7位流水号
     * @param companyPrefix 子公司类别前缀
     * @return 7位流水号
     */
    private String generateSequenceNumber(String companyPrefix) {
        // 查询当前子公司类别的最大鄂钢编号
        Map<String, Object> param = new HashMap<>();
        param.put("dynSql", "EGBH LIKE '" + companyPrefix + "%'");
        param.put("displayOrder", "EGBH DESC");
        
        try {
            List<TkizlPatentInfo> list = businessKIZLPatentInfo.queryList(param);
            
            if (ObjectUtil.isEmpty(list)) {
                // 如果没有记录，从0000001开始
                return "0000001";
            } else {
                // 获取最大的鄂钢编号，提取流水号部分并加1
                String lastEgbh = list.get(0).getEgbh();
                if (StrUtil.isNotBlank(lastEgbh) && lastEgbh.length() >= 8) {
                    String lastSequence = lastEgbh.substring(1); // 去掉第一位子公司类别
                    try {
                        int nextNumber = Integer.parseInt(lastSequence) + 1;
                        return String.format("%07d", nextNumber);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，从0000001开始
                        return "0000001";
                    }
                } else {
                    // 如果格式不正确，从0000001开始
                    return "0000001";
                }
            }
        } catch (Exception e) {
            // 如果查询失败，使用时间戳生成序号
            return String.format("%07d", System.currentTimeMillis() % 10000000);
        }
    }
    
    @Override
    public String getSupportedActivity() {
        return KizlWorkflowActivityEnum.MANUAL8.getCode();
    }
}
