package com.baosight.bsvkkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;
import com.baosight.bsvkkj.ki.zl.business.*;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlProcessCodeEnum;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SRoleUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import com.baosight.bsvkkj.mp.ty.dto.AttachmentMap;
import com.baosight.bsvkkj.mp.ty.utils.SAttachmentUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 境内专利_专利信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class ServiceKIZLPatentInfo extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessKIZLPatentInfo businessKIZLPatentInfo;

    @Resource
    private BusinessKIZLApplyRyxx businessKIZLApplyRyxx;
    @Resource
    private BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo;
    @Resource
    private BusinessKIZLApplySqr businessKIZLApplySqr;
    @Resource
    private BusinessKIZLWorkFlow businessKIZLWorkFlow;

	/**
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TkizlPatentInfo bean = businessKIZLPatentInfo.initBean(loginName);
            inInfo.set("patentInfo", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlPatentInfo> queryList = businessKIZLPatentInfo.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessKIZLPatentInfo.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkizlPatentInfo bean = BeanUtil.toBean(row, TkizlPatentInfo.class);
            businessKIZLPatentInfo.save(UserSession.getLoginName(), bean);
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String patentId = (String) inInfo.get("patentId");
            TkizlPatentInfo query = businessKIZLPatentInfo.query(patentId);
            inInfo.set("patentInfo", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String patentId = (String) inInfo.get("patentId");
            if (StrUtil.isNotBlank(patentId)) {
                for (String id : patentId.split(",")) {
                    businessKIZLPatentInfo.delete(loginName,id);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo queryDBDetail(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            String businessId = inInfo.getCellStr("i", 0, "businessId");
            TkizlApplyBaseinfo query = businessKIZLApplyBaseinfo.query(businessId);
            TkizlApplyBaseinfoEx beanEx = TkizlApplyBaseinfoEx.initParent(query);
            //发明人信息
            beanEx.setRyxxList(businessKIZLApplyRyxx.queryListByApplyId(businessId));
            //申请人信息
            beanEx.setSqrList(businessKIZLApplySqr.queryListByApplyId(businessId));
            //专利信息
            TkizlPatentInfo tkizlPatentInfo = businessKIZLPatentInfo.queryByApplyId(businessId);
            if (StrUtil.isBlank(tkizlPatentInfo.getExtra3())) {
                tkizlPatentInfo.setExtra3(UserSession.getLoginName());
            }
            beanEx.setPatentInfo(tkizlPatentInfo);
            inInfo.set("applyBaseinfo", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 保存专利状态维护信息
     */
    public EiInfo savePatentStatus(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map row = inInfo.getBlock("i").getRow(0);
            // 获取专利信息
            TkizlPatentInfo patentInfo = BeanUtil.toBean(row, TkizlPatentInfo.class);
            //设置无效
            patentInfo.setIsvalid(KIZLConstants.ValidFlag.INVALID);
            // 保存专利状态信息
            businessKIZLPatentInfo.save(operator, patentInfo);
            inInfo.setMsg("状态维护成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryDBPage(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            Map queryData = inInfo.getBlock("i").getRow(0);
            queryData.put("userLabel", UserSession.getLoginName());
            queryData.put("flowCode", KizlProcessCodeEnum.KIZL_SB.getProcessCode());
            TableDataInfo queryPage = businessKIZLWorkFlow.queryDBPage(queryData);

            // 为每条记录添加附件信息和评审意见
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rows = (List<Map<String, Object>>) queryPage.getRows();
            if (rows != null && !rows.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    String businessId = (String) row.get("businessId");
                    if (StrUtil.isNotBlank(businessId)) {
                        // 查询技术交底书附件
                        List<AttachmentMap> attachments = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(
                            businessId, "KIZL", "jsjds", null, null);
                        
                        if (attachments != null && !attachments.isEmpty()) {
                            row.put("attachmentCount", attachments.size());
                            row.put("attachmentList", attachments);
                            
                            // 构建附件名称列表用于前台显示
                            StringBuilder attachmentNames = new StringBuilder();
                            for (int i = 0; i < attachments.size(); i++) {
                                if (i > 0) {
                                    attachmentNames.append(", ");
                                }
                                attachmentNames.append(attachments.get(i).getAttachmentName());
                            }
                            row.put("attachmentNames", attachmentNames.toString());
                        } else {
                            row.put("attachmentCount", 0);
                            row.put("attachmentList", null);
                            row.put("attachmentNames", "");
                        }
                        
                        // 查询暂存的评审意见（从工作流任务中获取）
                        try {
                            row.put("reviewComment",SWorkFlowUtil.getComment(MapUtil.getStr(row,"taskId")));
                        } catch (Exception e) {
                            logger.warn("获取业务ID: " + businessId + " 的评审意见失败: " + e.getMessage());
                            row.put("reviewComment", "");
                        }
                    }
                }
            }

            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 批量提交到交底
     */
    @Transactional(rollbackFor = Exception.class)
    public EiInfo batchSubmitJD(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map row = inInfo.getBlock("i").getRow(0);
            
            String businessIds = (String) row.get("businessIds");
            String swsGuid = (String) row.get("swsGuid");
            String swsdlr = (String) row.get("swsdlr");
            String comment = (String) row.get("comment");

            if (StrUtil.isBlank(businessIds)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请选择要操作的数据");
                return inInfo;
            }
            
            if (StrUtil.isBlank(swsGuid)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请选择专利事务所");
                return inInfo;
            }
            
            if (StrUtil.isBlank(swsdlr)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请输入专利事务所联系人");
                return inInfo;
            }
            
            String[] businessIdArray = businessIds.split(",");
            
            // 使用单独的方法处理每个业务对象，确保事务一致性
            return processBatchSubmitJD(operator, businessIdArray, swsGuid, swsdlr,comment);
            
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("批量提交失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 处理批量提交到交底的核心逻辑
     * 采用逐个处理的方式，确保每个业务对象的事务完整性
     */
    private EiInfo processBatchSubmitJD(String operator, String[] businessIdArray, String swsGuid, String swsdlr,String comment) {
        EiInfo resultInfo = new EiInfo();
        int successCount = 0;
        int workflowSuccessCount = 0;
        List<String> errorMessages = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        
        for (String businessId : businessIdArray) {
            if (StrUtil.isNotBlank(businessId)) {
                try {
                    // 为每个业务对象单独处理，确保事务边界清晰
                    boolean success = processSingleSubmitJD(operator, businessId, swsGuid, swsdlr);
                    if (success) {
                        successCount++;
                        successIds.add(businessId);
                        
                        // 尝试提交工作流（工作流操作相对独立）
                        try {
                            boolean workflowSuccess = submitWorkflowForBusiness(operator, businessId, comment);
                            if (workflowSuccess) {
                                workflowSuccessCount++;
                            }
                        } catch (Exception workflowException) {
                            logger.warn("业务ID: " + businessId + " 数据更新成功但工作流提交失败: " + workflowException.getMessage());
                            // 工作流失败不影响数据更新的成功状态
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理业务ID: " + businessId + " 时发生错误", e);
                    errorMessages.add("业务ID: " + businessId + " 处理失败: " + e.getMessage());
                }
            }
        }
        
        // 构建返回消息
        StringBuilder resultMessage = new StringBuilder();
        resultMessage.append("成功提交 ").append(successCount).append(" 条数据到交底");
        if (workflowSuccessCount > 0) {
            resultMessage.append("，其中 ").append(workflowSuccessCount).append(" 条成功提交工作流");
        }
        if (!errorMessages.isEmpty()) {
            resultMessage.append("。失败 ").append(errorMessages.size()).append(" 条");
            if (errorMessages.size() <= 3) {
                resultMessage.append("：").append(String.join("; ", errorMessages));
            } else {
                resultMessage.append("，详细错误请查看日志");
            }
        }
        
        resultInfo.setMsg(resultMessage.toString());
        resultInfo.setStatus(EiConstant.STATUS_SUCCESS);
        resultInfo.set("successCount", successCount);
        resultInfo.set("workflowSuccessCount", workflowSuccessCount);
        resultInfo.set("errorCount", errorMessages.size());
        resultInfo.set("successIds", successIds);
        
        return resultInfo;
    }

    /**
     * 处理单个业务对象的数据更新
     * 使用独立事务确保数据一致性
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processSingleSubmitJD(String operator, String businessId, String swsGuid, String swsdlr) {
        try {
            // 查询申请基本信息
            TkizlApplyBaseinfo applyBaseinfo = businessKIZLApplyBaseinfo.query(businessId);
            if (applyBaseinfo == null) {
                throw new RuntimeException("未找到申请信息");
            }
            
            // 查询专利信息
            TkizlPatentInfo patentInfo = businessKIZLPatentInfo.queryByApplyId(businessId);
            if (patentInfo == null) {
                throw new RuntimeException("未找到专利信息");
            }
            
            // 更新专利信息 - 设置为交底状态
            patentInfo.setPatentStatus(KIZLConstants.PatentStatus.DISCLOSED); // 03-交底
            patentInfo.setSwsGuid(swsGuid);
            patentInfo.setSwsdlr(swsdlr);
            patentInfo.setJdPerson(operator);
            patentInfo.setJdDate(DateUtil.format(new Date(), "yyyy-MM-dd"));

            businessKIZLPatentInfo.save(operator, patentInfo);
            
            logger.info("业务ID: " + businessId + " 数据更新成功");
            return true;
            
        } catch (Exception e) {
            logger.error("业务ID: " + businessId + " 数据更新失败", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 为单个业务对象提交工作流
     * 工作流操作相对独立，失败不影响数据更新
     */
    private boolean submitWorkflowForBusiness(String operator, String businessId, String comment) {
        try {
            // 获取当前工作流信息
            WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessId);
            if (workFlow == null || StrUtil.isBlank(workFlow.getTaskId())) {
                logger.warn("业务ID: " + businessId + " 未找到有效的工作流任务");
                return false;
            }
            
            // 查询申请基本信息
            TkizlApplyBaseinfo applyBaseinfo = businessKIZLApplyBaseinfo.query(businessId);
            TkizlPatentInfo patentInfo = businessKIZLPatentInfo.queryByApplyId(businessId);
            
            // 构建申请基本信息扩展对象用于工作流处理
            TkizlApplyBaseinfoEx beanEx = TkizlApplyBaseinfoEx.initParent(applyBaseinfo);
            beanEx.setPatentInfo(patentInfo);
            beanEx.setWorkFlow(workFlow);
            
            // 设置工作流意见
            workFlow.setComment(comment);
            
            // 使用策略模式处理工作流
            WorkFlow processedWorkFlow = businessKIZLApplyBaseinfo.doSubmit(operator, beanEx);

            // 提交工作流
            String workflowResult = SWorkFlowUtil.submit(operator, processedWorkFlow);
            
            logger.info("业务ID: " + businessId + " 工作流提交成功: " + workflowResult);
            return true;
            
        } catch (Exception e) {
            logger.error("业务ID: " + businessId + " 工作流提交失败", e);
            return false;
        }
    }

    /**
     * 批量保存操作
     */
    public EiInfo batchSaveJDOperation(EiInfo inInfo) {
        logger.info("开始执行批量保存操作，接收到的参数：" + inInfo.toString());
        try {
            String operator = UserSession.getLoginName();
            Map row = inInfo.getBlock("i").getRow(0);
            logger.info("操作人：" + operator + "，参数：" + row.toString());
            
            String saveDataJson = (String) row.get("saveData");
            logger.info("保存数据JSON：" + saveDataJson);
            
            if (StrUtil.isBlank(saveDataJson)) {
                logger.warn("没有要保存的数据");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("没有要保存的数据");
                return inInfo;
            }

            // 解析JSON数据
            List<Map<String, Object>> saveDataList;
            try {
                List<Map> rawList = JSON.parseArray(saveDataJson, Map.class);
                saveDataList = new ArrayList<>();
                for (Map rawMap : rawList) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> typedMap = (Map<String, Object>) rawMap;
                    saveDataList.add(typedMap);
                }
                logger.info("解析后的数据条数：" + saveDataList.size());
            } catch (Exception e) {
                logger.error("JSON解析失败：" + e.getMessage(), e);
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("数据格式错误：" + e.getMessage());
                return inInfo;
            }
            
            int successCount = 0;
            
            for (Map<String, Object> itemData : saveDataList) {
                String businessId = (String) itemData.get("businessId");
                String operation = (String) itemData.get("operation");
                String swsGuid = (String) itemData.get("swsGuid");
                String swsdlr = (String) itemData.get("swsdlr");
                String comment = (String) itemData.get("comment");
                String taskId = (String) itemData.get("taskId");

                logger.info("处理业务ID：" + businessId + "，操作：" + operation);
                
                if (StrUtil.isNotBlank(businessId) && StrUtil.isNotBlank(operation)) {
                    try {
                        // 查询专利信息
                        TkizlPatentInfo patentInfo = businessKIZLPatentInfo.queryByApplyId(businessId);
                        if (patentInfo != null) {
                            logger.info("找到专利信息，当前状态：" + patentInfo.getPatentStatus());
                            
                            // 根据操作类型更新状态
                            if ("toDL".equals(operation)) {
                                // 提交到代理中 - 设置为代理中状态
                                patentInfo.setPatentStatus(KIZLConstants.PatentStatus.IN_AGENCY); // 04-代理中
                                if (StrUtil.isNotBlank(swsGuid)) {
                                    patentInfo.setSwsGuid(swsGuid);
                                }
                                if (StrUtil.isNotBlank(swsdlr)) {
                                    patentInfo.setSwsdlr(swsdlr);
                                }
                                patentInfo.setJdPerson(operator);
                                patentInfo.setJdDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
                                logger.info("设置为代理中状态");
                            } else if ("toWD".equals(operation)) {
                                // 提交到待定 - 在扩展字段中记录待定信息
                                patentInfo.setExtra8("pending"); // 标记为待定
                                patentInfo.setExtra9(DateUtil.format(new Date(), "yyyy-MM-dd")); // 待定日期
                                patentInfo.setExtra10(operator); // 待定操作人
                                logger.info("设置为待定状态");
                                patentInfo.setPatentStatus(KIZLConstants.PatentStatus.DISCLOSED);
                            }
                            businessKIZLPatentInfo.save(operator, patentInfo);
                            // 更新工作流评论
                            SWorkFlowUtil.updateComment(taskId, comment);

                            successCount++;
                            logger.info("保存成功，业务ID：" + businessId);
                        } else {
                            logger.warn("未找到专利信息，业务ID：" + businessId);
                        }
                    } catch (Exception e) {
                        logger.error("处理业务ID: " + businessId + " 时发生错误", e);
                    }
                } else {
                    logger.warn("业务ID或操作为空，跳过：businessId=" + businessId + ", operation=" + operation);
                }
            }
            
            logger.info("批量保存完成，成功保存 " + successCount + " 条数据");
            inInfo.setMsg("成功保存 " + successCount + " 条数据");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            logger.error("批量保存操作失败：" + e.getMessage(), e);
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("批量保存失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 批量提交到代理中
     */
    @Transactional(rollbackFor = Exception.class)
    public EiInfo batchSubmitToDL(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map row = inInfo.getBlock("i").getRow(0);
            
            String businessIds = (String) row.get("businessIds");
            String swsGuid = (String) row.get("swsGuid");
            String swsdlr = (String) row.get("swsdlr");
            String comment = (String) row.get("comment");

            if (StrUtil.isBlank(businessIds)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请选择要操作的数据");
                return inInfo;
            }
            
            if (StrUtil.isBlank(swsGuid)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请选择专利事务所");
                return inInfo;
            }
            
            if (StrUtil.isBlank(swsdlr)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请输入专利事务所联系人");
                return inInfo;
            }
            
            if (StrUtil.isBlank(comment)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请填写意见");
                return inInfo;
            }
            
            String[] businessIdArray = businessIds.split(",");
            
            // 使用单独的方法处理每个业务对象，确保事务一致性
            return processBatchSubmitToDL(operator, businessIdArray, swsGuid, swsdlr, comment);
            
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("批量提交到代理中失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 处理批量提交到代理中的核心逻辑
     */
    private EiInfo processBatchSubmitToDL(String operator, String[] businessIdArray, String swsGuid, String swsdlr, String comment) {
        EiInfo resultInfo = new EiInfo();
        int successCount = 0;
        int workflowSuccessCount = 0;
        List<String> errorMessages = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        
        for (String businessId : businessIdArray) {
            if (StrUtil.isNotBlank(businessId)) {
                try {
                    // 为每个业务对象单独处理，确保事务边界清晰
                    boolean success = processSingleSubmitToDL(operator, businessId, swsGuid, swsdlr);
                    if (success) {
                        successCount++;
                        successIds.add(businessId);
                        
                        // 尝试提交工作流（工作流操作相对独立）
                        try {
                            boolean workflowSuccess = submitWorkflowForBusiness(operator, businessId, comment);
                            if (workflowSuccess) {
                                workflowSuccessCount++;
                            }
                        } catch (Exception workflowException) {
                            logger.warn("业务ID: " + businessId + " 数据更新成功但工作流提交失败: " + workflowException.getMessage());
                            // 工作流失败不影响数据更新的成功状态
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理业务ID: " + businessId + " 时发生错误", e);
                    errorMessages.add("业务ID: " + businessId + " 处理失败: " + e.getMessage());
                }
            }
        }
        
        // 构建返回消息
        StringBuilder resultMessage = new StringBuilder();
        resultMessage.append("成功提交 ").append(successCount).append(" 条数据到代理中");
        if (workflowSuccessCount > 0) {
            resultMessage.append("，其中 ").append(workflowSuccessCount).append(" 条成功提交工作流");
        }
        if (!errorMessages.isEmpty()) {
            resultMessage.append("。失败 ").append(errorMessages.size()).append(" 条");
            if (errorMessages.size() <= 3) {
                resultMessage.append("：").append(String.join("; ", errorMessages));
            } else {
                resultMessage.append("，详细错误请查看日志");
            }
        }
        
        resultInfo.setMsg(resultMessage.toString());
        resultInfo.setStatus(EiConstant.STATUS_SUCCESS);
        resultInfo.set("successCount", successCount);
        resultInfo.set("workflowSuccessCount", workflowSuccessCount);
        resultInfo.set("errorCount", errorMessages.size());
        resultInfo.set("successIds", successIds);
        
        return resultInfo;
    }

    /**
     * 处理单个业务对象提交到代理中的数据更新
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processSingleSubmitToDL(String operator, String businessId, String swsGuid, String swsdlr) {
        try {
            // 查询申请基本信息
            TkizlApplyBaseinfo applyBaseinfo = businessKIZLApplyBaseinfo.query(businessId);
            if (applyBaseinfo == null) {
                throw new RuntimeException("未找到申请信息");
            }
            
            // 查询专利信息
            TkizlPatentInfo patentInfo = businessKIZLPatentInfo.queryByApplyId(businessId);
            if (patentInfo == null) {
                throw new RuntimeException("未找到专利信息");
            }
            
            // 更新专利信息 - 设置为代理中状态
            patentInfo.setPatentStatus(KIZLConstants.PatentStatus.IN_AGENCY); // 04-代理中
            patentInfo.setSwsGuid(swsGuid);
            patentInfo.setSwsdlr(swsdlr);
            patentInfo.setJdPerson(operator);
            patentInfo.setJdDate(DateUtil.format(new Date(), "yyyy-MM-dd"));

            businessKIZLPatentInfo.save(operator, patentInfo);
            
            logger.info("业务ID: " + businessId + " 数据更新成功（提交到代理中）");
            return true;
            
        } catch (Exception e) {
            logger.error("业务ID: " + businessId + " 数据更新失败（提交到代理中）", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 批量提交到待定
     */
    @Transactional(rollbackFor = Exception.class)
    public EiInfo batchSubmitToWD(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map row = inInfo.getBlock("i").getRow(0);
            
            String businessIds = (String) row.get("businessIds");
            String comment = (String) row.get("comment");

            if (StrUtil.isBlank(businessIds)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请选择要操作的数据");
                return inInfo;
            }
            
            if (StrUtil.isBlank(comment)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("请填写意见");
                return inInfo;
            }
            
            String[] businessIdArray = businessIds.split(",");
            
            // 使用单独的方法处理每个业务对象，确保事务一致性
            return processBatchSubmitToWD(operator, businessIdArray, comment);
            
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("批量提交到待定失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 处理批量提交到待定的核心逻辑
     */
    private EiInfo processBatchSubmitToWD(String operator, String[] businessIdArray, String comment) {
        EiInfo resultInfo = new EiInfo();
        int successCount = 0;
        int workflowSuccessCount = 0;
        List<String> errorMessages = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        
        for (String businessId : businessIdArray) {
            if (StrUtil.isNotBlank(businessId)) {
                try {
                    // 为每个业务对象单独处理，确保事务边界清晰
                    boolean success = processSingleSubmitToWD(operator, businessId);
                    if (success) {
                        successCount++;
                        successIds.add(businessId);
                        
                        // 尝试提交工作流（工作流操作相对独立）
                        try {
                            boolean workflowSuccess = submitWorkflowForBusiness(operator, businessId, comment);
                            if (workflowSuccess) {
                                workflowSuccessCount++;
                            }
                        } catch (Exception workflowException) {
                            logger.warn("业务ID: " + businessId + " 数据更新成功但工作流提交失败: " + workflowException.getMessage());
                            // 工作流失败不影响数据更新的成功状态
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理业务ID: " + businessId + " 时发生错误", e);
                    errorMessages.add("业务ID: " + businessId + " 处理失败: " + e.getMessage());
                }
            }
        }
        
        // 构建返回消息
        StringBuilder resultMessage = new StringBuilder();
        resultMessage.append("成功提交 ").append(successCount).append(" 条数据到待定");
        if (workflowSuccessCount > 0) {
            resultMessage.append("，其中 ").append(workflowSuccessCount).append(" 条成功提交工作流");
        }
        if (!errorMessages.isEmpty()) {
            resultMessage.append("。失败 ").append(errorMessages.size()).append(" 条");
            if (errorMessages.size() <= 3) {
                resultMessage.append("：").append(String.join("; ", errorMessages));
            } else {
                resultMessage.append("，详细错误请查看日志");
            }
        }
        
        resultInfo.setMsg(resultMessage.toString());
        resultInfo.setStatus(EiConstant.STATUS_SUCCESS);
        resultInfo.set("successCount", successCount);
        resultInfo.set("workflowSuccessCount", workflowSuccessCount);
        resultInfo.set("errorCount", errorMessages.size());
        resultInfo.set("successIds", successIds);
        
        return resultInfo;
    }

    /**
     * 处理单个业务对象提交到待定的数据更新
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processSingleSubmitToWD(String operator, String businessId) {
        try {
            // 查询申请基本信息
            TkizlApplyBaseinfo applyBaseinfo = businessKIZLApplyBaseinfo.query(businessId);
            if (applyBaseinfo == null) {
                throw new RuntimeException("未找到申请信息");
            }
            
            // 查询专利信息
            TkizlPatentInfo patentInfo = businessKIZLPatentInfo.queryByApplyId(businessId);
            if (patentInfo == null) {
                throw new RuntimeException("未找到专利信息");
            }
            
            // 更新专利信息 - 在扩展字段中记录待定信息
            patentInfo.setExtra8("pending"); // 标记为待定
            patentInfo.setExtra9(DateUtil.format(new Date(), "yyyy-MM-dd")); // 待定日期
            patentInfo.setExtra10(operator); // 待定操作人
            // 保持当前专利状态不变，只是标记为待定

            businessKIZLPatentInfo.save(operator, patentInfo);
            
            logger.info("业务ID: " + businessId + " 数据更新成功（提交到待定）");
            return true;
            
        } catch (Exception e) {
            logger.error("业务ID: " + businessId + " 数据更新失败（提交到待定）", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 综合查询专利信息 - 用于导出功能
     * 支持模糊查询、高级查询等多种查询条件，返回所有匹配的数据（不分页）
     */
    public EiInfo queryComprehensive(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);

            // 处理模糊查询参数
            String fuzzyQuery = (String) queryData.get("comprehensiveFuzzy");
            if (StrUtil.isNotBlank(fuzzyQuery)) {
                // 模糊查询：专利名称、流水号、鄂钢编号、接收编号、申请号、摘要
                queryData.put("comprehensiveFuzzy", fuzzyQuery);
            }

            // 处理高级查询参数
            processAdvancedQueryParams(queryData);

            // 处理用户权限
            processUserPermissions(queryData, UserSession.getLoginName());

            // 调用业务层进行综合查询（不分页，获取所有数据）
            List<Map<String, Object>> queryList = businessKIZLPatentInfo.queryComprehensiveList(queryData);

            if (queryList == null) {
                queryList = new ArrayList<>();
            }

            inInfo.set("list", queryList);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("查询失败：" + e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 专利综合查询 - 分页查询
     */
    public EiInfo queryComprehensivePage(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = inInfo.getRow("i", 0);

            // 处理高级查询参数
            processAdvancedQueryParams(queryData);

            // 处理用户权限
            processUserPermissions(queryData, UserSession.getLoginName());

            TableDataInfo queryPage = businessKIZLPatentInfo.queryComprehensivePage(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                // 发明人和申请人信息需要单独查询,如果前端需要
                for (Map<String, Object> row : (List<Map<String, Object>>) queryPage.getRows()) {
                    String applyId = (String) row.get("applyId");
                    // 发明人信息
                    List<TkizlApplyRyxx> inventorList = businessKIZLApplyRyxx.queryListByApplyId(applyId);
                    // 申请人信息
                    List<TkizlApplySqr> applicantList = businessKIZLApplySqr.queryListByApplyId(applyId);
                    //只取申请人姓名
                    row.put("applicantNames", applicantList.stream().map(TkizlApplySqr::getLegalName).collect(Collectors.joining(",")));
                    //只取发明人姓名
                    row.put("inventorNames", inventorList.stream().map(TkizlApplyRyxx::getEmpName).collect(Collectors.joining(",")));
                }
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {

                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 处理用户权限，构建动态SQL
     */
    private void processUserPermissions(Map<String, Object> queryData, String loginName) {
        boolean isAdmin = SRoleUtil.isAdmin(loginName);
        boolean hasAllReadAccess = SRoleUtil.isRoleMember(KIZLConstants.KIZL_ALL_READALL, loginName);
        if (isAdmin || hasAllReadAccess) {
            return; // 管理员或有全部读取权限的用户不需要额外处理
        }
        for (String role : KIZLConstants.roleHierarchy) {
            List<ADOrg> orgs = SRoleUtil.getOrgByUser(loginName, role);
            if (ObjectUtil.isNotEmpty(orgs)) {
                queryData.put("dynSql", buildOrgPermissionSql(orgs, "COALESCE(P.FIRST_DEPT_PATH, A.FIRST_DEPT_PATH)"));
                return;
            }
        }
        // 如果没有匹配的角色权限，只能查看自己相关的记录
        queryData.put("dynSql2", buildPersonalPermissionSql(loginName));
    }

    /**
     * 构建个人权限SQL
     */
    private String buildPersonalPermissionSql(String loginName) {
        return "(COALESCE(P.LXR_CODE, A.LXR_CODE) ='" + loginName + "' OR COALESCE(P.CREATE_USER_LABEL, A.CREATE_USER_LABEL) = '" + loginName + "' OR RY.EMP_ID = '" + loginName + "')";
    }

    /**
     * 构建组织权限SQL
     */
    private String buildOrgPermissionSql(List<ADOrg> orgs, String fieldName) {
        StringBuffer dynSql = new StringBuffer("(");
        for (ADOrg adOrg : orgs) {
            if (dynSql.length() > 1) {
                dynSql.append(" OR ");
            }
            dynSql.append(" ").append(fieldName).append(" LIKE '%").append(adOrg.getOrgCode()).append("%'");
        }
        dynSql.append(")");
        return dynSql.toString();
    }


    /**
     * 处理高级查询参数
     */
    private void processAdvancedQueryParams(Map<String, Object> queryData) {
        // 基本信息查询条件
        String serialNum = (String) queryData.get("serialNum");
        String patentName = (String) queryData.get("patentName");
        String patentType = (String) queryData.get("patentType");
        String egbh = (String) queryData.get("egbh");
        String jsbh = (String) queryData.get("jsbh");
        String patentNo = (String) queryData.get("patentNo");
        String ownership = (String) queryData.get("ownership");
        String unitType = (String) queryData.get("unitType");
        String applyUnit = (String) queryData.get("applyUnit");
        String latestStatus = (String) queryData.get("latestStatus");
        String statusDetail = (String) queryData.get("statusDetail");
        String legalStatus = (String) queryData.get("legalStatus");
        String legalStatusDetail = (String) queryData.get("legalStatusDetail");
        String startDate = (String) queryData.get("startDate");
        String endDate = (String) queryData.get("endDate");
        String implementStatus = (String) queryData.get("implementStatus");
        String implementDept = (String) queryData.get("implementDept");
        String sourceCategory = (String) queryData.get("sourceCategory");
        String sourceNo = (String) queryData.get("sourceNo");
        String sourceName = (String) queryData.get("sourceName");
        String reviewExpert = (String) queryData.get("reviewExpert");
        String labels = (String) queryData.get("labels");
        String agencyOffice = (String) queryData.get("agencyOffice");
        String agent = (String) queryData.get("agent");
        
        // 发明人查询条件
        String inventorName = (String) queryData.get("inventorName");
        String inventorJobNo = (String) queryData.get("inventorJobNo");
        String inventorPosition = (String) queryData.get("inventorPosition");
        String inventorTitle = (String) queryData.get("inventorTitle");
        String inventorUnit = (String) queryData.get("inventorUnit");
        
        // 处理模糊查询条件
        if (StrUtil.isNotBlank(serialNum)) {
            queryData.put("serialNumLike", serialNum);
        }
        if (StrUtil.isNotBlank(patentName)) {
            queryData.put("patentNameLike", patentName);
        }
        if (StrUtil.isNotBlank(egbh)) {
            queryData.put("egbhLike", egbh);
        }
        if (StrUtil.isNotBlank(jsbh)) {
            queryData.put("jsbhLike", jsbh);
        }
        if (StrUtil.isNotBlank(patentNo)) {
            queryData.put("patentNoLike", patentNo);
        }
        if (StrUtil.isNotBlank(sourceNo)) {
            queryData.put("fromNoLike", sourceNo);
        }
        if (StrUtil.isNotBlank(sourceName)) {
            queryData.put("fromNameLike", sourceName);
        }
        if (StrUtil.isNotBlank(inventorName)) {
            queryData.put("inventorNameLike", inventorName);
        }
        if (StrUtil.isNotBlank(inventorJobNo)) {
            queryData.put("inventorJobNoLike", inventorJobNo);
        }
        
        // 处理精确匹配条件
        if (StrUtil.isNotBlank(patentType)) {
            queryData.put("patentType", patentType);
        }
        if (StrUtil.isNotBlank(ownership)) {
            queryData.put("ownership", ownership);
        }
        if (StrUtil.isNotBlank(latestStatus)) {
            queryData.put("patentStatus", latestStatus);
        }
        if (StrUtil.isNotBlank(legalStatus)) {
            queryData.put("flzt", legalStatus);
        }
        if (StrUtil.isNotBlank(implementStatus)) {
            queryData.put("useMethod", implementStatus);
        }
        if (StrUtil.isNotBlank(sourceCategory)) {
            queryData.put("fromType", sourceCategory);
        }
        if (StrUtil.isNotBlank(agencyOffice)) {
            queryData.put("swsGuid", agencyOffice);
        }
        if (StrUtil.isNotBlank(agent)) {
            queryData.put("swsdlr", agent);
        }
        if (StrUtil.isNotBlank(inventorPosition)) {
            queryData.put("inventorPosition", inventorPosition);
        }
        if (StrUtil.isNotBlank(inventorTitle)) {
            queryData.put("inventorTitle", inventorTitle);
        }
        
        // 处理日期范围查询
        if (StrUtil.isNotBlank(startDate)) {
            queryData.put("startDate", startDate);
        }
        if (StrUtil.isNotBlank(endDate)) {
            queryData.put("endDate", endDate);
        }
        
        // 处理单位类型和申请单位
        if ("specified".equals(unitType) && StrUtil.isNotBlank(applyUnit)) {
            queryData.put("firstDeptCode", applyUnit);
        }
        
        // 处理标签（复选框）
        if (StrUtil.isNotBlank(labels)) {
            queryData.put("labelIn", labels);
        }
    }

}
