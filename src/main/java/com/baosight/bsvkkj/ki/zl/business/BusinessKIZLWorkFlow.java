package com.baosight.bsvkkj.ki.zl.business;

import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.ki.zl.constants.KIZLConstants;
import com.baosight.bsvkkj.ki.zl.enums.KizlProcessCodeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class BusinessKIZLWorkFlow extends BusinessBase {

    private static final String NAME_SPACE = "KIZLWorkFlow";


    /**
     * 查询用户模块待办列表
     *
     * @param businessType
     * @param userLable
     * @return
     */
    public List<Map<String, Object>> queryModuleDbList(String businessType, String userLable) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("businessType", "'"+businessType+"'");
        param.put("processCode", KizlProcessCodeEnum.KIZL_SB.getProcessCode());
        param.put("userLabel", userLable);
        param.put("state", "active");// 待办
        return dao.query(NAME_SPACE + ".queryModuleList", param);
    }

    /**
     * 查询List
     *
     * @param param
     * @return
     */
    public List queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".queryDB", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".queryDB", param);
    }


    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".queryDB", param);
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryBatchPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".queryBatchDB", param);
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo page(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".queryDB", param);
    }

    /**
     *
     * @param taskDefKey 流程节点
     * @param processInstanceId 流程实例Id
     * @return 当前流程节点待办处理人列表
     */
    public List<Map<String, Object>> queryCurrentActivityOperatorList(String taskDefKey, String processInstanceId) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("processInstanceId", processInstanceId);
        param.put("taskDefKey", taskDefKey);
        param.put("states", "'open','suspend'");//处于活跃或挂起状态
        return dao.query("HTSZFlowInfo.queryCurrentActivityOperatorList", param);
    }

    public TableDataInfo queryDBPage(Map param) {
        return getPage(NAME_SPACE + ".queryDBPage", param);
    }
}
