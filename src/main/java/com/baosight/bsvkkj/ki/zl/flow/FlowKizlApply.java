package com.baosight.bsvkkj.ki.zl.flow;

import com.baosight.bsvkkj.ki.zl.domain.TkizlApplyBaseinfoEx;
import com.baosight.bsvkkj.ki.zl.enums.KizlProcessCodeEnum;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class FlowKizlApply {

    /***
     * 启动流程
     * @param operator 操作人
     * @param applyId 申请ID
     * @param applyName 申请名称
     * @param firstDeptCode 第一部门代码
     * @param workFlow 流程对象
     * @param inInfo 输入信息
     */
    public static void startProcess(String operator, String applyId, String applyName, String firstDeptCode, WorkFlow workFlow, EiInfo inInfo) {
        KizlProcessCodeEnum processCode = KizlProcessCodeEnum.getProcessCode(KizlProcessCodeEnum.KIZL_SB.getProcessCode());
        workFlow.setComment("启动专利申请");
        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("orgParamter", firstDeptCode);
        workFlow.setBusinessName(applyName);
        workFlow.setBusinessLabel(processCode.getProcessCode());
        workFlow.setVariable(variableMap);
        workFlow.setBusinessId(applyId);
        workFlow.setBusinessType("KIZL");
        workFlow.setProcessCode("KIZLSB01");
        inInfo.setMsg(SWorkFlowUtil.startProcessAndSubmit(operator, workFlow));
    }
}
