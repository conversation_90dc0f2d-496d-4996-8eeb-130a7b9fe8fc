<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="KIZLWorkFlow">
    <!-- 模块代码列表 -->
    <select id="queryModuleList" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.FLOW_CODE as "processCode" ,
        C.PROCESS_NAME AS "processName" ,
        A.CURRENT_ACTIVITY as "currentActivity" ,
        A.CURRENT_ACTIVITY_NAME as "currentActivityName" ,
        A.BUSINESS_LABEL as "businessLabel" ,
        count(A.CURRENT_ACTIVITY) as "todoNum"
        FROM
        GGMK.T_MPWF_FLOW_INFO A
        JOIN "iplat4j".TEWPT00 B ON B.PROCESS_INSTANCE_ID = A.FLOW_ID
        JOIN "iplat4j".TEWPD01 C ON C.ACT_PROC_DEF_ID = B.ACT_PROCESS_DEF_ID
        WHERE
        B.STATE = 'open'
        <isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE = #processCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCodes">A.FLOW_CODE in($processCodes$)</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessType">business_type in($businessType$)</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessName">business_name = #businessName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">business_name like '%$businessNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivityName">A.CURRENT_ACTIVITY_NAME = #currentActivityName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivityNameLike">A.CURRENT_ACTIVITY_NAME like
            '%$currentActivityNameLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="state">A.FLOW_STATE = #state#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
        GROUP BY A.FLOW_CODE, C.PROCESS_NAME, A.CURRENT_ACTIVITY, A.CURRENT_ACTIVITY_NAME, A.BUSINESS_LABEL
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>


    <!-- 取当前登陆用户的待办 -->
    <select id="queryDB" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.FLOW_CODE AS "processCode" ,
        B.TASK_DEF_KEY AS "currentActivity" ,
        B.TASK_NAME AS "currentActivityName" ,
        A.FLOW_ID AS "flowId" ,
        A.BUSINESS_ID AS "businessId" ,
        A.BUSINESS_TYPE AS "businessType" ,
        A.BUSINESS_LABEL AS "businessLabel" ,
        A.BUSINESS_NAME AS "businessName" ,
        A.LAST_OPERATOR AS "lastOperator" ,
        A.LAST_OPERATOR_NAME AS "lastOperatorName" ,
        A.LAST_TIME AS "lastTime" ,
        A.PARENT_FLOW_ID AS "parentFlowId" ,
        A.DEPARTMENT_NO AS "departmentNo" ,
        B.FORM AS "pageNo" ,
        B.PROCESS_INSTANCE_ID AS "processInstanceId" ,
        B.TASK_NAME AS "taskName" ,
        B.TASK_ID AS "taskId" ,
        B.TASK_TYPE AS "taskType" ,
        C.FIRST_DEPT_NAME AS "firstDeptName" ,
        C.APPLY_NAME AS "applyName" ,
        C.LXR_NAME AS "lxrName" ,
        C.JSBH AS "jsbh" ,
        C.GLDW_NAME AS "gldwName" ,
        C.FLOW_STATUS AS "flowStatus"
        FROM
        GGMK.T_MPWF_FLOW_INFO A
        JOIN ${platSchema}.TEWPT00 B ON A.FLOW_ID = B.PROCESS_INSTANCE_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO C ON A.BUSINESS_ID = C.APPLY_ID
        WHERE
        1=1
        <isNotEmpty prepend=" AND " property="businessType">A.BUSINESS_TYPE in($businessType$)</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessLabel">B.BUSINESS_LABEL = #businessLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="assigneeId">B.ASSIGNEE_ID = #assigneeId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="state">B.STATE = #state#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowCode">A.FLOW_CODE = #flowCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">A.current_activity = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">A.BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryDBPage"  parameterClass="hashmap" resultClass="hashmap">
        SELECT
        tmp.processCode AS "processCode",
        tmp.currentActivity AS "currentActivity" ,
        tmp.currentActivityName AS "currentActivityName",
        tmp.flowId AS "flowId" ,
        tmp.parentFlowId AS "parentFlowId" ,
        T.APPLY_ID AS "businessId" ,
        T.APPLY_NAME AS "businessName" ,
        tmp.lastOperatorName AS "lastOperatorName" ,
        tmp.lastTime AS "lastTime" ,
        tmp.departmentNo AS "departmentNo" ,
        tmp.businessType AS "businessType" ,
        tmp.pageNo AS "pageNo" ,
        tmp.processInstanceId AS "processInstanceId" ,
        tmp.taskName AS "taskName" ,
        tmp.taskId AS "taskId" ,
        T.JSBH AS "jsbh" ,
        TP.PATENT_NAME AS "patentName" ,
        T.APPLY_NAME AS "applyName" ,
        T.LXR_NAME AS "lxrName" ,
        T.LXR_MOBILE AS "lxrMobile" ,
        T.OWNERSHIP AS "ownership" ,
        T.FIRST_DEPT_PATH AS "firstDeptPath",
        T.FIRST_DEPT_CODE AS "firstDeptCode",
        T.FIRST_DEPT_NAME AS "firstDeptName",
        T.GLDW_CODE AS "gldwCode",
        TP.PATENT_ID AS "patentId",
        TP.JD_DATE AS "jdDate",
        TP.DJD_DATE AS "djdDate",
        TP.DJD_PERSON AS "djdPerson",
        TP.PATENT_STATUS AS "patentStatus",
        TP.EXTRA6 AS "extra6",
        TP.EXTRA7 AS "extra7",
        TP.EXTRA8 AS "extra8",
        TP.EXTRA9 AS "extra9",
        TP.EXTRA10 AS "extra10",
        DLS.SWS_ID AS "swsId",
        DLS.SWS_NAME AS "swsName",
        TP.SWSDLR AS "swsdlr",
        TP.SWSDLR_PHONE AS "swsdlrPhone",
        TP.PATENT_NO AS "patentNo",
        T.APPLY_DATE AS "applyDate",
        TP.EGBH AS "egbh",
        TP.SQRQ AS "sqrq",
        TP.SLRQ AS "slrq"
        FROM
        ( SELECT
        A.FLOW_CODE AS processCode ,
        A.CURRENT_ACTIVITY AS currentActivity ,
        A.CURRENT_ACTIVITY_NAME AS currentActivityName,
        A.FLOW_ID AS flowId ,
        A.PARENT_FLOW_ID AS parentFlowId ,
        A.BUSINESS_ID AS businessId ,
        A.BUSINESS_NAME AS businessName ,
        A.LAST_OPERATOR_NAME AS lastOperatorName ,
        A.LAST_TIME AS lastTime ,
        A.DEPARTMENT_NO AS departmentNo ,
        A.BUSINESS_TYPE AS businessType ,
        B.FORM AS pageNo ,
        B.PROCESS_INSTANCE_ID AS processInstanceId ,
        B.TASK_NAME AS taskName ,
        B.TASK_ID AS taskId
        FROM
        ${ggmkSchema}.T_MPWF_FLOW_INFO A,
        ${platSchema}.TEWPT00 B
        WHERE
        A.FLOW_ID = B.PROCESS_INSTANCE_ID
        AND B.STATE = 'open'
        <isNotEmpty prepend=" AND " property="businessType">A.BUSINESS_TYPE =  '$businessType$'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID =  '$userLabel$'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowCode">A.FLOW_CODE =  '$flowCode$'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">B.TASK_DEF_KEY =  '$currentActivity$'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">A.BUSINESS_NAME LIKE '%$businessNameLike$%'</isNotEmpty> ) tmp
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON T.APPLY_ID = tmp.businessId
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO DLS ON TP.SWS_GUID = DLS.SWS_ID
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="QS">(TP.QS &lt;='1' OR (TP.QS IS NULL AND T.OWNERSHIP &lt;='1'))</isNotEmpty>
        <isNotEmpty prepend=" AND " property="delStatus">T.DEL_STATUS = #delStatus#</isNotEmpty>
        <isEmpty prepend=" AND " property="delStatus">T.DEL_STATUS = '0'</isEmpty>
        <isNotEmpty prepend=" AND " property="anythingLike">(TP.JSBH like '%$anythingLike$%'
            OR TP.EGBH like '%$anythingLike$%'
            OR TP.PATENT_NO like '%$anythingLike$%'
            OR T.APPLY_NAME like '%$anythingLike$%'
            OR TP.PATENT_NAME like '%$anythingLike$%'
            OR TP.GLDW_NAME like '%$anythingLike$%'
            OR DLS.SWS_NAME like '%$anythingLike$%')</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jsbhLike">TP.JSBH like '%$jsbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">TP.EGBH like '%$egbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNoLike">TP.PATENT_NO like '%$patentNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="gldwCode">TP.gldw_code =  '$gldwCode$'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsId">DLS.SWS_NO =#swsId#</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <!-- 专利综合查询 -->
    <select id="queryPatentAll" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        P.PATENT_ID AS "patentId",
        P.APPLY_ID AS "applyId",
        P.SERIAL_NUM AS "serialNum",
        P.JSBH AS "jsbh",
        P.EGBH AS "egbh",
        P.PATENT_NAME AS "patentName",
        P.PATENT_TYPE AS "patentType",
        P.PATENT_NO AS "patentNo",
        P.SLRQ AS "slrq",
        P.SQRQ AS "sqrq",
        P.PATENT_STATUS AS "patentStatus",
        P.FLZT AS "flzt",
        P.FIRST_DEPT_CODE AS "firstDeptCode",
        P.FIRST_DEPT_NAME AS "firstDeptName",
        P.GLDW_CODE AS "gldwCode",
        P.GLDW_NAME AS "gldwName",
        P.USE_METHOD AS "useMethod",
        P.USE_DEPT AS "useDept",
        P.USE_DEPT_DEPT AS "useDeptDept",
        P.USE_FIRSTDATE AS "useFirstdate",
        P.FROM_TYPE AS "fromType",
        P.FROM_NO AS "fromNo",
        P.FROM_NAME AS "fromName",
        P.LABEL AS "label",
        P.SWS_GUID AS "swsGuid",
        P.SWSDLR AS "swsdlr",
        P.ZLH AS "zlh",
        P.QS AS "qs",
        P.LXR_NAME AS "lxrName",
        P.UPDATE_DATE AS "updateDate",
        SWS.SWS_NAME AS "swsName",
        (SELECT LISTAGG(RY.EMP_NAME, ',') WITHIN GROUP (ORDER BY RY.RYXH)
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
        WHERE RY.APPLY_ID = P.APPLY_ID AND RY.DEL_STATUS = '0' AND RY.EMP_NAME IS NOT NULL) AS "inventorNames",
        (SELECT LISTAGG(TO_CHAR(RY.GXXS), ',') WITHIN GROUP (ORDER BY RY.RYXH)
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
        WHERE RY.APPLY_ID = P.APPLY_ID AND RY.DEL_STATUS = '0' AND RY.GXXS IS NOT NULL) AS "inventorCoefficients",
        (SELECT LISTAGG(SQ.LEGAL_NAME, ',') WITHIN GROUP (ORDER BY SQ.XH)
        FROM ${zzzcSchema}.T_KIZL_APPLY_SQR SQ
        WHERE SQ.APPLY_ID = P.APPLY_ID AND SQ.DEL_STATUS = '0' AND SQ.LEGAL_NAME IS NOT NULL) AS "applicantNames"
        FROM ${zzzcSchema}.T_KIZL_PATENT_INFO P
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A ON P.APPLY_ID = A.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO SWS ON P.SWS_GUID = SWS.SWS_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX R ON P.APPLY_ID = R.APPLY_ID AND R.DEL_STATUS = '0'
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_SQR S ON P.APPLY_ID = S.APPLY_ID AND S.DEL_STATUS = '0'
        WHERE P.DEL_STATUS = '0'
        <!-- 模糊查询：专利名称、流水号、鄂钢编号、接收编号、申请号、摘要 -->
        <isNotEmpty prepend=" AND " property="comprehensiveFuzzy">
            (P.PATENT_NAME LIKE '%$comprehensiveFuzzy$%'
            OR P.SERIAL_NUM LIKE '%$comprehensiveFuzzy$%'
            OR P.EGBH LIKE '%$comprehensiveFuzzy$%'
            OR P.JSBH LIKE '%$comprehensiveFuzzy$%'
            OR P.PATENT_NO LIKE '%$comprehensiveFuzzy$%'
            OR A.ABSTRACT LIKE '%$comprehensiveFuzzy$%')
        </isNotEmpty>
        <!-- 基本信息查询条件 -->
        <isNotEmpty prepend=" AND " property="serialNumLike">P.SERIAL_NUM LIKE '%$serialNumLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNameLike">P.PATENT_NAME LIKE '%$patentNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentType">P.PATENT_TYPE = #patentType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">P.EGBH LIKE '%$egbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jsbhLike">P.JSBH LIKE '%$jsbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNoLike">P.PATENT_NO LIKE '%$patentNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="ownership">P.QS = #ownership#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstDeptCode">P.FIRST_DEPT_CODE = #firstDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentStatus">P.PATENT_STATUS = #patentStatus#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flzt">P.FLZT = #flzt#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="useMethod">P.USE_METHOD = #useMethod#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromType">P.FROM_TYPE = #fromType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromNoLike">P.FROM_NO LIKE '%$fromNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromNameLike">P.FROM_NAME LIKE '%$fromNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsGuid">P.SWS_GUID = #swsGuid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsdlr">P.SWSDLR = #swsdlr#</isNotEmpty>
        <!-- 日期范围查询 -->
        <isNotEmpty prepend=" AND " property="startDate">P.SLRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">P.SLRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
        <!-- 标签查询（支持多选） -->
        <isNotEmpty prepend=" AND " property="labelIn">P.LABEL IN ($labelIn$)</isNotEmpty>
        <!-- 发明人查询条件 -->
        <isNotEmpty prepend=" AND " property="inventorNameLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.EMP_NAME LIKE '%$inventorNameLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorJobNoLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.EMP_ID LIKE '%$inventorJobNoLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorPosition">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.POST_LEVEL = #inventorPosition#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorTitle">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.POST_TITLE = #inventorTitle#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorUnit">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.DEPT_CODE = #inventorUnit#)
        </isNotEmpty>
        <isNotEmpty prepend=" ORDER BY " property="displayOrder">$displayOrder$</isNotEmpty>
        <isEmpty prepend=" ORDER BY " property="displayOrder">P.UPDATE_DATE DESC</isEmpty>
    </select>

    <!-- 专利综合查询 - 计数查询 -->
    <select id="count" parameterClass="hashmap" resultClass="int">
        SELECT COUNT(DISTINCT P.PATENT_ID)
        FROM ${zzzcSchema}.T_KIZL_PATENT_INFO P
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A ON P.APPLY_ID = A.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO SWS ON P.SWS_GUID = SWS.SWS_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX R ON P.APPLY_ID = R.APPLY_ID AND R.DEL_STATUS = '0'
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_SQR S ON P.APPLY_ID = S.APPLY_ID AND S.DEL_STATUS = '0'
        WHERE P.DEL_STATUS = '0'
        <!-- 模糊查询：专利名称、流水号、鄂钢编号、接收编号、申请号、摘要 -->
        <isNotEmpty prepend=" AND " property="comprehensiveFuzzy">
            (P.PATENT_NAME LIKE '%$comprehensiveFuzzy$%'
            OR P.SERIAL_NUM LIKE '%$comprehensiveFuzzy$%'
            OR P.EGBH LIKE '%$comprehensiveFuzzy$%'
            OR P.JSBH LIKE '%$comprehensiveFuzzy$%'
            OR P.PATENT_NO LIKE '%$comprehensiveFuzzy$%'
            OR A.ABSTRACT LIKE '%$comprehensiveFuzzy$%')
        </isNotEmpty>
        <!-- 基本信息查询条件 -->
        <isNotEmpty prepend=" AND " property="serialNumLike">P.SERIAL_NUM LIKE '%$serialNumLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNameLike">P.PATENT_NAME LIKE '%$patentNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentType">P.PATENT_TYPE = #patentType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="egbhLike">P.EGBH LIKE '%$egbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jsbhLike">P.JSBH LIKE '%$jsbhLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentNoLike">P.PATENT_NO LIKE '%$patentNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="ownership">P.QS = #ownership#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="firstDeptCode">P.FIRST_DEPT_CODE = #firstDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="patentStatus">P.PATENT_STATUS = #patentStatus#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flzt">P.FLZT = #flzt#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="useMethod">P.USE_METHOD = #useMethod#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromType">P.FROM_TYPE = #fromType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromNoLike">P.FROM_NO LIKE '%$fromNoLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="fromNameLike">P.FROM_NAME LIKE '%$fromNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsGuid">P.SWS_GUID = #swsGuid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="swsdlr">P.SWSDLR = #swsdlr#</isNotEmpty>
        <!-- 日期范围查询 -->
        <isNotEmpty prepend=" AND " property="startDate">P.SLRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">P.SLRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
        <!-- 标签查询（支持多选） -->
        <isNotEmpty prepend=" AND " property="labelIn">P.LABEL IN ($labelIn$)</isNotEmpty>
        <!-- 发明人查询条件 -->
        <isNotEmpty prepend=" AND " property="inventorNameLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.EMP_NAME LIKE '%$inventorNameLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorJobNoLike">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.EMP_ID LIKE '%$inventorJobNoLike$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorPosition">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.POST_LEVEL = #inventorPosition#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorTitle">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.POST_TITLE = #inventorTitle#)
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="inventorUnit">
            EXISTS (SELECT 1 FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX RY
            WHERE RY.APPLY_ID = P.APPLY_ID
            AND RY.DEL_STATUS = '0'
            AND RY.DEPT_CODE = #inventorUnit#)
        </isNotEmpty>
    </select>
</sqlMap>