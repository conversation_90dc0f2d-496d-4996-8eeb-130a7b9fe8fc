package com.baosight.bsvkkj.ki.zl.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.ki.zl.business.BusinessKIZLApplyBaseinfo;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XServiceManager;

import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class KIZLCommonUtil {

    private static BusinessKIZLApplyBaseinfo businessKIZLApplyBaseinfo = SpringUtil.getBean(BusinessKIZLApplyBaseinfo.class);

    /* 判断人员是否配置组织 */
    public static void isOrg(String operator) {
        ADOrg adOrg = SOrgUtil.getMainOrgByUserCode(operator);
        if (ObjectUtil.isEmpty(adOrg)) {
            throw new BusinessException("该人员未配置组织！");
        }
    }


    /**
     * 获取申请人数
     *
     * @param
     * @return
     */
    public static List<Map> getApplyNum() {
        //申请人数量
        List<Map> stationList = new ArrayList<Map>();
        Map map = new HashMap();
        map.put("id", "0");
        map.put("name", "请选择");
        stationList.add(map);
        for (int i = 1; i < 11; i++) {
            Map row = new HashMap();
            row.put("id", i);
            row.put("name", i);
            stationList.add(row);
        }
        return stationList;
    }

    /**
     * 获取专利评审状态
     *
     * @param bizGuid
     * @param moduleCode
     * @return
     */
    public static List<Map<String, Object>> getStatusReview(String bizGuid, String moduleCode) {
        EiInfo einfo = new EiInfo();
        einfo.set("bizId", bizGuid);
        einfo.set("moduleCode", moduleCode);
        einfo.set(EiConstant.serviceId, "S_MP_PS_02");
        EiInfo outInfo = XServiceManager.call(einfo);
        List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.get("list");
        return list;
    }

    /**
     * 判断专利评审是否结束
     *
     * @param list
     * @return true:结束; false:未结束
     */
    public static boolean isPsxx(List<Map<String, Object>> list) {
        Map<String, Object> psMap = list.get(0);
        Object isEnd = psMap.get("isEnd");
        return "1".equals(isEnd);
    }
}
