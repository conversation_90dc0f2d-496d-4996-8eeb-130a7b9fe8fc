package com.baosight.bsvkkj.ki.zl.business;

import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 境内专利_申请_申请人信息
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class BusinessKIZLApplySqr extends BusinessBase {

    private static final String NAME_SPACE = "tkizlApplySqr";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TkizlApplySqr initBean(String operator) {
        TkizlApplySqr applySqr = new TkizlApplySqr();
        return applySqr;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TkizlApplySqr> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TkizlApplySqr> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param sqrId
     * @return
     */
    public TkizlApplySqr query(String sqrId) {
        if (StringUtils.isBlank(sqrId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("sqrId", sqrId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TkizlApplySqr) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param applySqr
     * @return
     */
    public TkizlApplySqr insert(String operator, TkizlApplySqr applySqr) {
        applySqr.initAdd(operator);
        if (StringUtils.isEmpty(applySqr.getSqrId())) {
            applySqr.setSqrId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", applySqr);
        return applySqr;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param applySqr
     * @return
     */
    public TkizlApplySqr update(String operator, TkizlApplySqr applySqr) {
        applySqr.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", applySqr);
        return applySqr;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param sqrId
     * @return
     */
    public TkizlApplySqr deleteLogin(String operator, String sqrId) {
        TkizlApplySqr applySqr = new TkizlApplySqr();
        applySqr.setSqrId(sqrId);
        applySqr.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", applySqr);
        return applySqr;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param sqrId
     * @return
     */
    public int delete(String operator, String sqrId) {
        return dao.delete(NAME_SPACE + ".delete", sqrId);
    }

    /**
     * 保存
     *
	 * @param operator
     * @param applySqr
     * @return
     */
    public TkizlApplySqr save(String operator, TkizlApplySqr applySqr) {
        if (StringUtils.isEmpty(applySqr.getSqrId())) {
            return insert(operator, applySqr);
        } else {
            if (null == query(applySqr.getSqrId())) {
                return insert(operator, applySqr);
            }
            return update(operator, applySqr);
        }
    }

    public int deleteByC(String applyId) {
        Map<String, Object> param = new HashMap<>();
        param.put("applyId", applyId);
        List<TkizlApplySqr> list = this.queryList(param);
        if (ObjectUtil.isEmpty(list)) {
            return 0;
        }
        return dao.delete(NAME_SPACE + ".deleteByC", param);
    }

    public List<TkizlApplySqr> queryListByApplyId(String businessId) {
        Map<String, Object> param = new HashMap<>();
        param.put("applyId", businessId);
        param.put("displayOrder", "XH");
        List<TkizlApplySqr> list = this.queryList(param);
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        return list;
    }
}
