package com.baosight.bsvkkj.common.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

import java.util.HashMap;
import java.util.Map;

public class PageService extends ServiceBase {
    /***分页常量 */
    private final String PAGE_CONSTANT = "pageData";

    /**
     * 获取查询条件
     *
     * @param inInfo
     * @return
     */
    public Map<String, Object> getQueryData(EiInfo inInfo) {

        EiBlock block = inInfo.getBlock(EiConstant.queryBlock);
        Map<String, Object> map = null;
        if (block != null) {
            map = block.getRow(0);
        } else {
            map = new HashMap<>();
        }
        return map;
    }

    /**
     * 设置分页数据
     *
     * @param inInfo
     * @param tableDataInfo
     * @return
     */
    public EiInfo setPage(EiInfo inInfo, TableDataInfo tableDataInfo) {
        inInfo.addBlock(EiConstant.resultBlock).set(PAGE_CONSTANT, tableDataInfo);
        return inInfo;
    }

    public EiInfo callLocal(String serviceName, String methodName, Map<String, Object> param) {
        EiInfo inInfo = new EiInfo();
        inInfo.addRow(EiConstant.queryBlock, param);
        inInfo.set(EiConstant.serviceName, serviceName); //设置服务名
        inInfo.set(EiConstant.methodName, methodName);//设置方法名
        return XLocalManager.call(inInfo);
    }

}
