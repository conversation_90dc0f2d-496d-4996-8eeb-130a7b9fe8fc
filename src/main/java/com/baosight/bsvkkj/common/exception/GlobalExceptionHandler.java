package com.baosight.bsvkkj.common.exception;

import java.sql.SQLException;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.utils.ExceptionUtil;
import com.baosight.bsvkkj.utils.ServletUtils;
import com.baosight.iplat4j.core.exception.PlatException;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
	private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

	/**
	 * 请求方式不支持
	 */
	@ExceptionHandler({ HttpRequestMethodNotSupportedException.class })
	public AjaxResult handleException(HttpRequestMethodNotSupportedException e) {
		log.error(e.getMessage(), e);
		return AjaxResult.error("不支持' " + e.getMethod() + "'请求");
	}

	/**
	 * 拦截未知的运行时异常
	 */
	@ExceptionHandler(RuntimeException.class)
	public AjaxResult notFount(RuntimeException e) {
		log.error("运行时异常:", e);
		if (e instanceof NullPointerException) {
			return AjaxResult.error(ExceptionUtil.getMsg((NullPointerException)e));		
		}
		return AjaxResult.error("运行时异常:" + e.getMessage());
	}
	
	/**
	 * SQL异常
	 */
	@ExceptionHandler(SQLException.class)
	public AjaxResult handleException(SQLException e) {
		log.error(e.getMessage(), e);
		return AjaxResult.error(ExceptionUtil.getMsg(e));
	}

	/**
	 * 系统异常
	 */
	@ExceptionHandler(Exception.class)
	public AjaxResult handleException(Exception e) {
		log.error(e.getMessage(), e);
		if (StringUtils.isEmpty(e.getMessage())) {
			return AjaxResult.error("服务器错误，请联系管理员");
		} else {
			return AjaxResult.error(e.getMessage());
		}
	}

	/**
	 * 业务异常
	 */
	@ExceptionHandler(BusinessException.class)
	public Object businessException(HttpServletRequest request, BusinessException e) {
		log.error(e.getMessage(), e);
		if (ServletUtils.isAjaxRequest(request)) {
			if(e.getMessage().indexOf("SQLCODE=-302")>0) {
				return AjaxResult.error(ExceptionUtil.getMsg(e));
			}
			return AjaxResult.error(e.getMessage());
		} else {
			ModelAndView modelAndView = new ModelAndView();
			if(e.getMessage().indexOf("SQLCODE=-302")>0) {
				modelAndView.addObject("errorMessage", ExceptionUtil.getMsg(e));
			}else {
				modelAndView.addObject("errorMessage", e.getMessage());
			}
			modelAndView.setViewName("error/business");
			return modelAndView;
		}
	}

	/**
	 * 平台异常
	 */
	@ExceptionHandler(PlatException.class)
	public Object platException(HttpServletRequest request, PlatException e) {
		log.error(e.getMessage(), e);
		if (ServletUtils.isAjaxRequest(request)) {
			if(e.getMessage().indexOf("SQLCODE=-302")>0) {
				return AjaxResult.error(ExceptionUtil.getMsg(e));
			}
			return AjaxResult.error(e.getMessage());
		} else {
			ModelAndView modelAndView = new ModelAndView();
			if(e.getMessage().indexOf("SQLCODE=-302")>0) {
				modelAndView.addObject("errorMessage", ExceptionUtil.getMsg(e));
			}else {
				modelAndView.addObject("errorMessage", e.getMessage());
			}
			modelAndView.setViewName("error/business");
			return modelAndView;
		}
	}

	/**
	 * 自定义验证异常
	 */
	@ExceptionHandler(BindException.class)
	public AjaxResult validatedBindException(BindException e) {
		log.error(e.getMessage(), e);
		String message = e.getAllErrors().get(0).getDefaultMessage();
		return AjaxResult.error(message);
	}

	@ExceptionHandler(MethodArgumentNotValidException.class)
	public AjaxResult methodArgumentException(MethodArgumentNotValidException e) {
		String message = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
		return AjaxResult.error(message);
	}
}
