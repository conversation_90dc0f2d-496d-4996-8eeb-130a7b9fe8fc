package com.baosight.bsvkkj.common.domain;

import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.data.DaoEPBase;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;

public class AbstractDomain extends DaoEPBase {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    @JsonIgnore
    private Date createDate;
    @JsonIgnore
    private Date updateDate;
    @JsonIgnore
    private Date deleteDate;
    @JsonIgnore
    private String createUserLabel;
    @JsonIgnore
    private String updateUserLabel;
    @JsonIgnore
    private String deleteUserLabel;
    @JsonIgnore
    private Integer recordVersion;
    @JsonIgnore
    private String delStatus;//记录状态(0:有效/正常/未删除,1:无效/失效/已删除)
    @JsonIgnore
    private String tenantId;//租户ID

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getDeleteDate() {
        return deleteDate;
    }

    public void setDeleteDate(Date deleteDate) {
        this.deleteDate = deleteDate;
    }

    public String getCreateUserLabel() {
        return createUserLabel;
    }

    public void setCreateUserLabel(String createUserLabel) {
        this.createUserLabel = createUserLabel;
    }

    public String getUpdateUserLabel() {
        return updateUserLabel;
    }

    public void setUpdateUserLabel(String updateUserLabel) {
        this.updateUserLabel = updateUserLabel;
    }

    public String getDeleteUserLabel() {
        return deleteUserLabel;
    }

    public void setDeleteUserLabel(String deleteUserLabel) {
        this.deleteUserLabel = deleteUserLabel;
    }

    public Integer getRecordVersion() {
        return recordVersion;
    }

    public void setRecordVersion(Integer recordVersion) {
        this.recordVersion = recordVersion;
    }

    public String getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(String delStatus) {
        this.delStatus = delStatus;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public void initial() {
        setCreateUserLabel(null);
        setUpdateUserLabel(null);
        setDeleteUserLabel(null);
        setCreateDate(null);
        setUpdateDate(null);
        setDeleteDate(null);
        setTenantId(null);
    }

    public void initAdd(String operator) {
        if (StrUtil.isBlank(this.createUserLabel)) {
            setCreateUserLabel(operator);
        }
        setCreateDate(new Date());
        setUpdateUserLabel(operator);
        setUpdateDate(new Date());
        setRecordVersion(1);
        setDelStatus("0");
    }

    public void initUpdate(String operator) {
        setUpdateUserLabel(operator);
        setUpdateDate(new Date());
        if (getRecordVersion() != null)
            setRecordVersion(getRecordVersion() + 1);
    }

    public void initLogicDel(String operator) {
        setDeleteUserLabel(operator);
        setDeleteDate(new Date());
        setDelStatus("1");
    }
}
