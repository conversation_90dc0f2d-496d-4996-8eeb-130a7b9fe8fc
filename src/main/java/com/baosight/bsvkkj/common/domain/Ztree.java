package com.baosight.bsvkkj.common.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * ztree树结构实体类
 *
 * <AUTHOR>
 */

public class Ztree implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点父ID
     */
    private String pId;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点标题
     */
    private String title;

    /**
     * 是否勾选
     */
    private boolean checked = false;

    /**
     * 是否展开
     */
    private boolean open = false;

    /**
     * 不可缺少，否则不会加载展开按钮
     */
    private boolean isParent;

    /**
     * 跳转路径
     */
    private String tegarUrl;
    /**
     * 是否能勾选
     */
    private boolean nocheck = false;
    private String code;
    private List<Ztree> childNode;

    @Getter
    @Setter
    private String orgPathName;

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    public String getTegarUrl() {
        return tegarUrl;
    }

    public void setTegarUrl(String tegarUrl) {
        this.tegarUrl = tegarUrl;
    }

    public List<Ztree> getChildNode() {
        return childNode;
    }

    public void setChildNode(List<Ztree> childNode) {
        this.childNode = childNode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public boolean isNocheck() {
        return nocheck;
    }

    public void setNocheck(boolean nocheck) {
        this.nocheck = nocheck;
    }

    public boolean getIsParent() {
        return isParent;
    }

    public void setIsParent(boolean parent) {
        isParent = parent;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "Ztree [id=" + id + ", pId=" + pId + ", name=" + name + ", title=" + title + ", checked=" + checked
                + ", open=" + open + ", isParent=" + isParent + ", tegarUrl=" + tegarUrl + ", nocheck=" + nocheck
                + ", childNode=" + childNode + "]";
    }
}
