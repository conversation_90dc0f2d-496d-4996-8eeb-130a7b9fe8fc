<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlPatentInfo">
	<typeAlias alias="tkizlPatentInfoResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlPatentInfo"/>
	<select id="load" parameterClass="string" resultClass="tkizlPatentInfoResult">
		SELECT
			PATENT_ID as "patentId" , <!-- 主键 -->
			APPLY_ID as "applyId" , <!-- 申请主键 -->
			SERIAL_NUM as "serialNum" , <!-- 流水号 -->
			JSBH as "jsbh" , <!-- 接收编号 -->
			EGBH as "egbh" , <!-- 鄂钢编号 -->
			PATENT_NAME as "patentName" , <!-- 申报名称 -->
			FIRST_DEPT_CODE as "firstDeptCode" , <!-- 第一申报部门 -->
			FIRST_DEPT_NAME as "firstDeptName" , <!-- 第一申报部门名称 -->
			GLDW_CODE as "gldwCode" , <!-- 管理单位 -->
			GLDW_NAME as "gldwName" , <!-- 管理单位名称 -->
			LABEL as "label" , <!-- 标签 -->
			TECH_AREA as "techArea" , <!-- 技术领域 -->
			USE_PROPOSE as "usePropose" , <!-- 用途 -->
			KNOWLEDGE_CLASS as "knowledgeClass" , <!-- 知识分类 -->
			LXR as "lxr" , <!-- 联系人 -->
			LXR_NAME as "lxrName" , <!-- 联系人姓名 -->
			LXRPHONE as "lxrphone" , <!-- 联系人电话 -->
			LXR_EMAIL as "lxrEmail" , <!-- 联系人邮箱 -->
			LXR_MOBILE as "lxrMobile" , <!-- 联系人手机 -->
			FROM_TYPE as "fromType" , <!-- 来源类型 -->
			FROM_NO as "fromNo" , <!-- 来源编号 -->
			FROM_NAME as "fromName" , <!-- 来源名称 -->
			FROM_CONTENT as "fromContent" , <!-- 其他来源情况说明 -->
			USE_METHOD as "useMethod" , <!-- 应用方式 -->
			USE_DEPT as "useDept" , <!-- 应用部门 -->
			USE_DEPT_DEPT as "useDeptDept" , <!-- 应用部门名称 -->
			USE_EXPECTED as "useExpected" , <!-- 预计应用部门 -->
			USE_EXPECTED_NAME as "useExpectedName" , <!-- 预计应用部门名称 -->
			USE_FIRSTDATE as "useFirstdate" , <!-- 初始应用日期 -->
			REASON_NOUSE as "reasonNouse" , <!-- 未应用原因 -->
			CONTENT_NOUSE as "contentNouse" , <!-- 未应用原因备注 -->
			FIRST_DEPT_PATH as "firstDeptPath" , <!-- 缴费与资助责任单位 -->
			FLOW_STATUS as "flowStatus" , <!-- 流程状态 draft-草稿 active-流程中 end-结束 -->
			PATENT_STATUS as "patentStatus" , <!-- 专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权 -->
			DJD_PERSON as "djdPerson" , <!-- 待交底人员 -->
			DJD_DATE as "djdDate" , <!-- 待交底日期 -->
			JD_PERSON as "jdPerson" , <!-- 交底人员 -->
			JD_DATE as "jdDate" , <!-- 交底日期 -->
			QS as "qs" , <!-- 受理_权属 -->
			SLRQ as "slrq" , <!-- 受理_受理日期 -->
			PATENT_NO as "patentNo" , <!-- 受理_申请号 -->
			PATENT_TYPE as "patentType" , <!-- 受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计 -->
			YXQR as "yxqr" , <!-- 受理_优先权日 -->
			QLYQSL as "qlyqsl" , <!-- 受理_权利要求数量 -->
			SMSYS as "smsys" , <!-- 受理_说明书页数 -->
			SWS_GUID as "swsGuid" , <!-- 代理事务所 -->
			SWSDLR as "swsdlr" , <!-- 代理人 -->
			SWSDLR_PHONE as "swsdlrPhone" , <!-- 代理人电话 -->
			SWSDLR_EMAIL as "swsdlrEmail" , <!-- 代理人邮箱 -->
			MONEY_DLF as "moneyDlf" , <!-- 受理_代理费 -->
			MONEY_QF as "moneyQf" , <!-- 受理_权附 -->
			MONEY_SQF as "moneySqf" , <!-- 受理_申请费 -->
			MONEY_GBYSF as "moneyGbysf" , <!-- 受理_公布印刷费 -->
			MONEY_SMSFJF as "moneySmsfjf" , <!-- 受理_说明书附加费 -->
			MONEY_YHS as "moneyYhs" , <!-- 授权通知_印花税 -->
			SQTZ_FWDATE as "sqtzFwdate" , <!-- 授权通知_发文日 -->
			MONEY_FIRST as "moneyFirst" , <!-- 授权通知_第一次缴费 -->
			ZLH as "zlh" , <!-- 专利号 -->
			SQRQ as "sqrq" , <!-- 授权日期 -->
			NBCKYJ as "nbckyj" , <!-- 内部参考意见 -->
			FLZT as "flzt" , <!-- 法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止 -->
			ISVALID as "isvalid" , <!-- 是否有效 1-有效 0-无效 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			EXTRA6 as "extra6" , <!-- 扩展字段6 -->
			EXTRA7 as "extra7" , <!-- 扩展字段7 -->
			EXTRA8 as "extra8" , <!-- 扩展字段8 -->
			EXTRA9 as "extra9" , <!-- 扩展字段9 -->
			EXTRA10 as "extra10" , <!-- 扩展字段10 -->
			EXTRA11 as "extra11" , <!-- 扩展字段11 -->
			EXTRA12 as "extra12" , <!-- 扩展字段12 -->
			EXTRA13 as "extra13" , <!-- 扩展字段13 -->
			EXTRA14 as "extra14" , <!-- 扩展字段14 -->
			EXTRA15 as "extra15" , <!-- 扩展字段15 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
		WHERE
			PATENT_ID = #patentId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlPatentInfoResult">
		SELECT
			PATENT_ID as "patentId" ,
			APPLY_ID as "applyId" ,
			SERIAL_NUM as "serialNum" ,
			JSBH as "jsbh" ,
			EGBH as "egbh" ,
			PATENT_NAME as "patentName" ,
			FIRST_DEPT_CODE as "firstDeptCode" ,
			FIRST_DEPT_NAME as "firstDeptName" ,
			GLDW_CODE as "gldwCode" ,
			GLDW_NAME as "gldwName" ,
			LABEL as "label" ,
			TECH_AREA as "techArea" ,
			USE_PROPOSE as "usePropose" ,
			KNOWLEDGE_CLASS as "knowledgeClass" ,
			LXR as "lxr" ,
			LXR_NAME as "lxrName" ,
			LXRPHONE as "lxrphone" ,
			LXR_EMAIL as "lxrEmail" ,
			LXR_MOBILE as "lxrMobile" ,
			FROM_TYPE as "fromType" ,
			FROM_NO as "fromNo" ,
			FROM_NAME as "fromName" ,
			FROM_CONTENT as "fromContent" ,
			USE_METHOD as "useMethod" ,
			USE_DEPT as "useDept" ,
			USE_DEPT_DEPT as "useDeptDept" ,
			USE_EXPECTED as "useExpected" ,
			USE_EXPECTED_NAME as "useExpectedName" ,
			USE_FIRSTDATE as "useFirstdate" ,
			REASON_NOUSE as "reasonNouse" ,
			CONTENT_NOUSE as "contentNouse" ,
			FIRST_DEPT_PATH as "firstDeptPath" ,
			FLOW_STATUS as "flowStatus" ,
			PATENT_STATUS as "patentStatus" ,
			DJD_PERSON as "djdPerson" ,
			DJD_DATE as "djdDate" ,
			JD_PERSON as "jdPerson" ,
			JD_DATE as "jdDate" ,
			QS as "qs" ,
			SLRQ as "slrq" ,
			PATENT_NO as "patentNo" ,
			PATENT_TYPE as "patentType" ,
			YXQR as "yxqr" ,
			QLYQSL as "qlyqsl" ,
			SMSYS as "smsys" ,
			SWS_GUID as "swsGuid" ,
			SWSDLR as "swsdlr" ,
			SWSDLR_PHONE as "swsdlrPhone" ,
			SWSDLR_EMAIL as "swsdlrEmail" ,
			MONEY_DLF as "moneyDlf" ,
			MONEY_QF as "moneyQf" ,
			MONEY_SQF as "moneySqf" ,
			MONEY_GBYSF as "moneyGbysf" ,
			MONEY_SMSFJF as "moneySmsfjf" ,
			MONEY_YHS as "moneyYhs" ,
			SQTZ_FWDATE as "sqtzFwdate" ,
			MONEY_FIRST as "moneyFirst" ,
			ZLH as "zlh" ,
			SQRQ as "sqrq" ,
			NBCKYJ as "nbckyj" ,
			FLZT as "flzt" ,
			ISVALID as "isvalid" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			EXTRA6 as "extra6" ,
			EXTRA7 as "extra7" ,
			EXTRA8 as "extra8" ,
			EXTRA9 as "extra9" ,
			EXTRA10 as "extra10" ,
			EXTRA11 as "extra11" ,
			EXTRA12 as "extra12" ,
			EXTRA13 as "extra13" ,
			EXTRA14 as "extra14" ,
			EXTRA15 as "extra15" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="egbh">EGBH =  #egbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentName">PATENT_NAME =  #patentName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">LXR =  #lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrphone">LXRPHONE =  #lxrphone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL =  #lxrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDeptDept">USE_DEPT_DEPT =  #useDeptDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON =  #djdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE =  #djdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON =  #jdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdDate">JD_DATE =  #jdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qs">QS =  #qs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="smsys">SMSYS =  #smsys#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR =  #swsdlr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF =  #moneyDlf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF =  #moneyQf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF =  #moneySqf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS =  #moneyYhs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST =  #moneyFirst#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zlh">ZLH =  #zlh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nbckyj">NBCKYJ =  #nbckyj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">EXTRA7 =  #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">EXTRA8 =  #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">EXTRA9 =  #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">EXTRA10 =  #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">EXTRA11 =  #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">EXTRA12 =  #extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">EXTRA13 =  #extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">EXTRA14 =  #extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">EXTRA15 =  #extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlPatentInfoResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_PATENT_INFO ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="patentId">PATENT_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNum">SERIAL_NUM</isNotEmpty>
			<isNotEmpty prepend=" , " property="jsbh">JSBH</isNotEmpty>
			<isNotEmpty prepend=" , " property="egbh">EGBH</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentName">PATENT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">FIRST_DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">FIRST_DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="label">LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="techArea">TECH_AREA</isNotEmpty>
			<isNotEmpty prepend=" , " property="usePropose">USE_PROPOSE</isNotEmpty>
			<isNotEmpty prepend=" , " property="knowledgeClass">KNOWLEDGE_CLASS</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">LXR</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">LXR_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrphone">LXRPHONE</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrEmail">LXR_EMAIL</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMobile">LXR_MOBILE</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">FROM_TYPE</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromNo">FROM_NO</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">FROM_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromContent">FROM_CONTENT</isNotEmpty>
			<isNotEmpty prepend=" , " property="useMethod">USE_METHOD</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDept">USE_DEPT</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDeptDept">USE_DEPT_DEPT</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpected">USE_EXPECTED</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpectedName">USE_EXPECTED_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="useFirstdate">USE_FIRSTDATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="reasonNouse">REASON_NOUSE</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentNouse">CONTENT_NOUSE</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptPath">FIRST_DEPT_PATH</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">FLOW_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentStatus">PATENT_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="djdPerson">DJD_PERSON</isNotEmpty>
			<isNotEmpty prepend=" , " property="djdDate">DJD_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="jdPerson">JD_PERSON</isNotEmpty>
			<isNotEmpty prepend=" , " property="jdDate">JD_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="qs">QS</isNotEmpty>
			<isNotEmpty prepend=" , " property="slrq">SLRQ</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentNo">PATENT_NO</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentType">PATENT_TYPE</isNotEmpty>
			<isNotEmpty prepend=" , " property="yxqr">YXQR</isNotEmpty>
			<isNotEmpty prepend=" , " property="qlyqsl">QLYQSL</isNotEmpty>
			<isNotEmpty prepend=" , " property="smsys">SMSYS</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsGuid">SWS_GUID</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlr">SWSDLR</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlrPhone">SWSDLR_PHONE</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlrEmail">SWSDLR_EMAIL</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyDlf">MONEY_DLF</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyQf">MONEY_QF</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneySqf">MONEY_SQF</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyGbysf">MONEY_GBYSF</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneySmsfjf">MONEY_SMSFJF</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyYhs">MONEY_YHS</isNotEmpty>
			<isNotEmpty prepend=" , " property="sqtzFwdate">SQTZ_FWDATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyFirst">MONEY_FIRST</isNotEmpty>
			<isNotEmpty prepend=" , " property="zlh">ZLH</isNotEmpty>
			<isNotEmpty prepend=" , " property="sqrq">SQRQ</isNotEmpty>
			<isNotEmpty prepend=" , " property="nbckyj">NBCKYJ</isNotEmpty>
			<isNotEmpty prepend=" , " property="flzt">FLZT</isNotEmpty>
			<isNotEmpty prepend=" , " property="isvalid">ISVALID</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">EXTRA6</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">EXTRA7</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">EXTRA8</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">EXTRA9</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">EXTRA10</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">EXTRA11</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">EXTRA12</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">EXTRA13</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">EXTRA14</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">EXTRA15</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="patentId">#patentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNum">#serialNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jsbh">#jsbh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="egbh">#egbh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentName">#patentName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="label">#label#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techArea">#techArea#</isNotEmpty>
			<isNotEmpty prepend=" , " property="usePropose">#usePropose#</isNotEmpty>
			<isNotEmpty prepend=" , " property="knowledgeClass">#knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">#lxr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrphone">#lxrphone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrEmail">#lxrEmail#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMobile">#lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">#fromType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromNo">#fromNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">#fromName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromContent">#fromContent#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useMethod">#useMethod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDept">#useDept#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDeptDept">#useDeptDept#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpected">#useExpected#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpectedName">#useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useFirstdate">#useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="reasonNouse">#reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentNouse">#contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptPath">#firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentStatus">#patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="djdPerson">#djdPerson#</isNotEmpty>
			<isNotEmpty prepend=" , " property="djdDate">#djdDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jdPerson">#jdPerson#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jdDate">#jdDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="qs">#qs#</isNotEmpty>
			<isNotEmpty prepend=" , " property="slrq">#slrq#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentNo">#patentNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentType">#patentType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="yxqr">#yxqr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="qlyqsl">#qlyqsl#</isNotEmpty>
			<isNotEmpty prepend=" , " property="smsys">#smsys#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsGuid">#swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlr">#swsdlr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlrPhone">#swsdlrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlrEmail">#swsdlrEmail#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyDlf">#moneyDlf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyQf">#moneyQf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneySqf">#moneySqf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyGbysf">#moneyGbysf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneySmsfjf">#moneySmsfjf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyYhs">#moneyYhs#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sqtzFwdate">#sqtzFwdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyFirst">#moneyFirst#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zlh">#zlh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sqrq">#sqrq#</isNotEmpty>
			<isNotEmpty prepend=" , " property="nbckyj">#nbckyj#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flzt">#flzt#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isvalid">#isvalid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">#extra6#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">#extra7#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">#extra8#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">#extra9#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">#extra10#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">#extra11#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">#extra12#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">#extra13#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">#extra14#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">#extra15#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_PATENT_INFO
		WHERE 
		    PATENT_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_PATENT_INFO
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID=#patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM=#serialNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH=#jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="egbh">EGBH=#egbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentName">PATENT_NAME=#patentName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME=#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="label">LABEL=#label#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techArea">TECH_AREA=#techArea#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE=#usePropose#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS=#knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">LXR=#lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME=#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrphone">LXRPHONE=#lxrphone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL=#lxrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE=#lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE=#fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromNo">FROM_NO=#fromNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME=#fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT=#fromContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD=#useMethod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDept">USE_DEPT=#useDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDeptDept">USE_DEPT_DEPT=#useDeptDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED=#useExpected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME=#useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE=#useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE=#reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE=#contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH=#firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS=#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS=#patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON=#djdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE=#djdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON=#jdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdDate">JD_DATE=#jdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qs">QS=#qs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slrq">SLRQ=#slrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO=#patentNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE=#patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yxqr">YXQR=#yxqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL=#qlyqsl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="smsys">SMSYS=#smsys#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID=#swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR=#swsdlr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE=#swsdlrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL=#swsdlrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF=#moneyDlf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF=#moneyQf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF=#moneySqf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF=#moneyGbysf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF=#moneySmsfjf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS=#moneyYhs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE=#sqtzFwdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST=#moneyFirst#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zlh">ZLH=#zlh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrq">SQRQ=#sqrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nbckyj">NBCKYJ=#nbckyj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flzt">FLZT=#flzt#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isvalid">ISVALID=#isvalid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6=#extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">EXTRA7=#extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">EXTRA8=#extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">EXTRA9=#extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">EXTRA10=#extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">EXTRA11=#extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">EXTRA12=#extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">EXTRA13=#extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">EXTRA14=#extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">EXTRA15=#extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlPatentInfoResult">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_INFO	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="patentId">PATENT_ID=#patentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNum">SERIAL_NUM=#serialNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jsbh">JSBH=#jsbh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="egbh">EGBH=#egbh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentName">PATENT_NAME=#patentName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">FIRST_DEPT_NAME=#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="label">LABEL=#label#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techArea">TECH_AREA=#techArea#</isNotEmpty>
			<isNotEmpty prepend=" , " property="usePropose">USE_PROPOSE=#usePropose#</isNotEmpty>
			<isNotEmpty prepend=" , " property="knowledgeClass">KNOWLEDGE_CLASS=#knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">LXR=#lxr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">LXR_NAME=#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrphone">LXRPHONE=#lxrphone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrEmail">LXR_EMAIL=#lxrEmail#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMobile">LXR_MOBILE=#lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">FROM_TYPE=#fromType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromNo">FROM_NO=#fromNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">FROM_NAME=#fromName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromContent">FROM_CONTENT=#fromContent#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useMethod">USE_METHOD=#useMethod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDept">USE_DEPT=#useDept#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDeptDept">USE_DEPT_DEPT=#useDeptDept#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpected">USE_EXPECTED=#useExpected#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpectedName">USE_EXPECTED_NAME=#useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useFirstdate">USE_FIRSTDATE=#useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="reasonNouse">REASON_NOUSE=#reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentNouse">CONTENT_NOUSE=#contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptPath">FIRST_DEPT_PATH=#firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">FLOW_STATUS=#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentStatus">PATENT_STATUS=#patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="djdPerson">DJD_PERSON=#djdPerson#</isNotEmpty>
			<isNotEmpty prepend=" , " property="djdDate">DJD_DATE=#djdDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jdPerson">JD_PERSON=#jdPerson#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jdDate">JD_DATE=#jdDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="qs">QS=#qs#</isNotEmpty>
			<isNotEmpty prepend=" , " property="slrq">SLRQ=#slrq#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentNo">PATENT_NO=#patentNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentType">PATENT_TYPE=#patentType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="yxqr">YXQR=#yxqr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="qlyqsl">QLYQSL=#qlyqsl#</isNotEmpty>
			<isNotEmpty prepend=" , " property="smsys">SMSYS=#smsys#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsGuid">SWS_GUID=#swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlr">SWSDLR=#swsdlr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlrPhone">SWSDLR_PHONE=#swsdlrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsdlrEmail">SWSDLR_EMAIL=#swsdlrEmail#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyDlf">MONEY_DLF=#moneyDlf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyQf">MONEY_QF=#moneyQf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneySqf">MONEY_SQF=#moneySqf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyGbysf">MONEY_GBYSF=#moneyGbysf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneySmsfjf">MONEY_SMSFJF=#moneySmsfjf#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyYhs">MONEY_YHS=#moneyYhs#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sqtzFwdate">SQTZ_FWDATE=#sqtzFwdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="moneyFirst">MONEY_FIRST=#moneyFirst#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zlh">ZLH=#zlh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sqrq">SQRQ=#sqrq#</isNotEmpty>
			<isNotEmpty prepend=" , " property="nbckyj">NBCKYJ=#nbckyj#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flzt">FLZT=#flzt#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isvalid">ISVALID=#isvalid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">EXTRA6=#extra6#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">EXTRA7=#extra7#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">EXTRA8=#extra8#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">EXTRA9=#extra9#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">EXTRA10=#extra10#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">EXTRA11=#extra11#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">EXTRA12=#extra12#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">EXTRA13=#extra13#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">EXTRA14=#extra14#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">EXTRA15=#extra15#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			PATENT_ID =#patentId#
	</update>

</sqlMap>