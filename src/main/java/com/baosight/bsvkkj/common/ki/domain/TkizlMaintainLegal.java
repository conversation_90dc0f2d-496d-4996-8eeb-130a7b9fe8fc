package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_维护_申请人信息对象 t_kizl_maintain_legal
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlMaintainLegal extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * LEGAL_ID
     */
    private String legalId;

    /**
     * 管理单位
     * MANAGE_DEPT
     */
    @Excel(name = "管理单位" , width = 30)
    private String manageDept;

    /**
     * 一级单位
     * DEPT_FIRST
     */
    @Excel(name = "一级单位" , width = 30)
    private String deptFirst;

    /**
     * 二级单位
     * DEPT_SECOND
     */
    @Excel(name = "二级单位" , width = 30)
    private String deptSecond;

    /**
     * 通信地址
     * MAIL_ADDRESS
     */
    @Excel(name = "通信地址" , width = 30)
    private String mailAddress;

    /**
     * 邮政编码
     * POST_OFFICE
     */
    @Excel(name = "邮政编码" , width = 30)
    private String postOffice;

    /**
     * 付费联系人
     * LXR_MONEY
     */
    @Excel(name = "付费联系人" , width = 30)
    private String lxrMoney;

    /**
     * 联系电话
     * LXR_PHONE
     */
    @Excel(name = "联系电话" , width = 30)
    private String lxrPhone;

    /**
     * 邮箱
     * EMAIL
     */
    @Excel(name = "邮箱" , width = 30)
    private String email;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    private String extra5;


    //管理单位名称 : MANAGE_DEPT_NAME
    private String manageDeptName;

    //第一单位名称 : DEPT_FIRST_NAME
    private String deptFirstName;

    //第二单位名称 : DEPT_SECOND_NAME
    private String deptSecondName;

    //第三单位名称 : DEPT_THIRD_NAME
    private String deptThirdName;

    private String deptThird;
}
