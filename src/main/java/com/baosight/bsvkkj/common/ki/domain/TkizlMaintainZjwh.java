package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_维护_专家维护对象 t_kizl_maintain_zjwh
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlMaintainZjwh extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * MONEY_ID
     */
    private String moneyId;

    /**
     * 组织代码
     * DEPT_CODE
     */
    @Excel(name = "组织代码" , width = 30)
    @Size(max = 20, message = "组织代码最大为20位字符")
    private String deptCode;

    /**
     * 组织名称
     * DEPT_NAME
     */
    @Excel(name = "组织名称" , width = 30)
    @Size(max = 100, message = "组织名称最大为100位字符")
    private String deptName;

    /**
     * 专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计
     * EXPERT_GH
     */
    @Excel(name = "专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计" , width = 30)
    @Size(max = 10, message = "专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计最大为10位字符")
    private String expertGh;

    /**
     * 费用名称
     * EXPERT_NAME
     */
    @Excel(name = "费用名称" , width = 30)
    @Size(max = 50, message = "费用名称最大为50位字符")
    private String expertName;

    /**
     * 费用序号
     * AREA
     */
    @Excel(name = "费用序号" , width = 30)
    @Size(max = 2, message = "费用序号最大为2位字符")
    private String area;

    /**
     * 金额
     * MAJOR
     */
    @Excel(name = "金额" , width = 30)
    @Size(max = 50, message = "金额最大为50位字符")
    private String major;

    /**
     * $column.columnComment
     * EMPLOYED_OR_NOT
     */
    @Excel(name = "金额" , width = 30)
    @Size(max = 1, message = "金额最大为1位字符")
    private String employedOrNot;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
