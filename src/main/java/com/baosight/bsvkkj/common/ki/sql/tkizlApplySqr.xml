<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplySqr">
	<typeAlias alias="tkizlApplySqrResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlApplySqr"/>
	<select id="load" parameterClass="string" resultClass="tkizlApplySqrResult">
		SELECT
			SQR_ID as "sqrId" , <!-- 主键 -->
			APPLY_ID as "applyId" , <!-- 申请主键 -->
			XH as "xh" , <!-- 序号 -->
			LEGAL_ID as "legalId" , <!-- 申请人主键 -->
			LEGAL_NAME as "legalName" , <!-- 申请人名称 -->
			MAIL_ADDRESS as "mailAddress" , <!-- 通信地址 -->
			POST_OFFICE as "postOffice" , <!-- 邮政编码 -->
			LXR_MONEY as "lxrMoney" , <!-- 付费联系人 -->
			LXR_PHONE as "lxrPhone" , <!-- 联系电话 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_APPLY_SQR
		WHERE
			SQR_ID = #sqrId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlApplySqrResult">
		SELECT
			SQR_ID as "sqrId" ,
			APPLY_ID as "applyId" ,
			XH as "xh" ,
			LEGAL_ID as "legalId" ,
			LEGAL_NAME as "legalName" ,
			MAIL_ADDRESS as "mailAddress" ,
			POST_OFFICE as "postOffice" ,
			LXR_MONEY as "lxrMoney" ,
			LXR_PHONE as "lxrPhone" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_APPLY_SQR
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="sqrId">SQR_ID =  #sqrId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xh">XH =  #xh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalId">LEGAL_ID =  #legalId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalName">LEGAL_NAME =  #legalName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mailAddress">MAIL_ADDRESS =  #mailAddress#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postOffice">POST_OFFICE =  #postOffice#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMoney">LXR_MONEY =  #lxrMoney#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlApplySqrResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_SQR ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="sqrId">SQR_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="xh">XH</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalId">LEGAL_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalName">LEGAL_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="mailAddress">MAIL_ADDRESS</isNotEmpty>
			<isNotEmpty prepend=" , " property="postOffice">POST_OFFICE</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMoney">LXR_MONEY</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">LXR_PHONE</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="sqrId">#sqrId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="xh">#xh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalId">#legalId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalName">#legalName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="mailAddress">#mailAddress#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postOffice">#postOffice#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMoney">#lxrMoney#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_SQR
		WHERE 
		    SQR_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_SQR
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="sqrId">SQR_ID=#sqrId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xh">XH=#xh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalId">LEGAL_ID=#legalId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalName">LEGAL_NAME=#legalName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mailAddress">MAIL_ADDRESS=#mailAddress#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postOffice">POST_OFFICE=#postOffice#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMoney">LXR_MONEY=#lxrMoney#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE=#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlApplySqrResult">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_SQR	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="sqrId">SQR_ID=#sqrId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="xh">XH=#xh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalId">LEGAL_ID=#legalId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalName">LEGAL_NAME=#legalName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="mailAddress">MAIL_ADDRESS=#mailAddress#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postOffice">POST_OFFICE=#postOffice#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMoney">LXR_MONEY=#lxrMoney#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">LXR_PHONE=#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			SQR_ID =#sqrId#
	</update>

</sqlMap>