<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplyRyxx">
	<typeAlias alias="tkizlApplyRyxxResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlApplyRyxx"/>
	<select id="load" parameterClass="string" resultClass="tkizlApplyRyxxResult">
		SELECT
			RYXX_ID as "ryxxId" , <!-- 主键 -->
			APPLY_ID as "applyId" , <!-- 申请主键 -->
			PATENT_ID as "patentId" , <!-- 专利主键 -->
			RYXH as "ryxh" , <!-- 序号 -->
			RYLX as "rylx" , <!-- 类型 01-宝武人员 02-非宝武人员 -->
			LEGAL_ID as "legalId" , <!-- 所属法人 -->
			EMP_ID as "empId" , <!-- 工号 -->
			EMP_NAME as "empName" , <!-- 姓名 -->
			DEPT_CODE as "deptCode" , <!-- 人员组织 -->
			DEPT_NAME as "deptName" , <!-- 人员组织名称 -->
			POST_TITLE as "postTitle" , <!-- 职称 -->
			POST_LEVEL as "postLevel" , <!-- 岗位 -->
			POST_NAME as "postName" , <!-- 岗位名称 -->
			GXXS as "gxxs" , <!-- 贡献系数 -->
			ID_CARD as "idCard" , <!-- 身份证号 -->
			CONTENT_MEMO as "contentMemo" , <!-- 备注 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX
		WHERE
			RYXX_ID = #ryxxId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlApplyRyxxResult">
		SELECT
			RYXX_ID as "ryxxId" ,
			APPLY_ID as "applyId" ,
			PATENT_ID as "patentId" ,
			RYXH as "ryxh" ,
			RYLX as "rylx" ,
			LEGAL_ID as "legalId" ,
			EMP_ID as "empId" ,
			EMP_NAME as "empName" ,
			DEPT_CODE as "deptCode" ,
			DEPT_NAME as "deptName" ,
			POST_TITLE as "postTitle" ,
			POST_LEVEL as "postLevel" ,
			POST_NAME as "postName" ,
			GXXS as "gxxs" ,
			ID_CARD as "idCard" ,
			CONTENT_MEMO as "contentMemo" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID =  #ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ryxh">RYXH =  #ryxh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="rylx">RYLX =  #rylx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalId">LEGAL_ID =  #legalId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="empId">EMP_ID =  #empId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="empName">EMP_NAME =  #empName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postTitle">POST_TITLE =  #postTitle#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postLevel">POST_LEVEL =  #postLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postName">POST_NAME =  #postName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gxxs">GXXS =  #gxxs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="idCard">ID_CARD =  #idCard#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentMemo">CONTENT_MEMO =  #contentMemo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlApplyRyxxResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_RYXX ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="ryxxId">RYXX_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentId">PATENT_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="ryxh">RYXH</isNotEmpty>
			<isNotEmpty prepend=" , " property="rylx">RYLX</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalId">LEGAL_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="empId">EMP_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="empName">EMP_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptCode">DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptName">DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="postTitle">POST_TITLE</isNotEmpty>
			<isNotEmpty prepend=" , " property="postLevel">POST_LEVEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="postName">POST_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="gxxs">GXXS</isNotEmpty>
			<isNotEmpty prepend=" , " property="idCard">ID_CARD</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentMemo">CONTENT_MEMO</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="ryxxId">#ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentId">#patentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ryxh">#ryxh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="rylx">#rylx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalId">#legalId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="empId">#empId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="empName">#empName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptCode">#deptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptName">#deptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postTitle">#postTitle#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postLevel">#postLevel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postName">#postName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gxxs">#gxxs#</isNotEmpty>
			<isNotEmpty prepend=" , " property="idCard">#idCard#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentMemo">#contentMemo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_RYXX
		WHERE 
		    RYXX_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_RYXX
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID=#ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID=#patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ryxh">RYXH=#ryxh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="rylx">RYLX=#rylx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalId">LEGAL_ID=#legalId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="empId">EMP_ID=#empId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="empName">EMP_NAME=#empName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE=#deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME=#deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postTitle">POST_TITLE=#postTitle#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postLevel">POST_LEVEL=#postLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postName">POST_NAME=#postName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gxxs">GXXS=#gxxs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="idCard">ID_CARD=#idCard#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentMemo">CONTENT_MEMO=#contentMemo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlApplyRyxxResult">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_RYXX	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="ryxxId">RYXX_ID=#ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentId">PATENT_ID=#patentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ryxh">RYXH=#ryxh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="rylx">RYLX=#rylx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="legalId">LEGAL_ID=#legalId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="empId">EMP_ID=#empId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="empName">EMP_NAME=#empName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptCode">DEPT_CODE=#deptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptName">DEPT_NAME=#deptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postTitle">POST_TITLE=#postTitle#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postLevel">POST_LEVEL=#postLevel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postName">POST_NAME=#postName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gxxs">GXXS=#gxxs#</isNotEmpty>
			<isNotEmpty prepend=" , " property="idCard">ID_CARD=#idCard#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentMemo">CONTENT_MEMO=#contentMemo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			RYXX_ID =#ryxxId#
	</update>

</sqlMap>