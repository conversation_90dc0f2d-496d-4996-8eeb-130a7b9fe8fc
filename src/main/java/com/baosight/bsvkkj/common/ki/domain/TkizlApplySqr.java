package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_申请_申请人信息对象 t_kizl_apply_sqr
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlApplySqr extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * SQR_ID
     */
    private String sqrId;

    /**
     * 申请主键
     * APPLY_ID
     */
    @Excel(name = "申请主键" , width = 30)
    @Size(max = 50, message = "申请主键最大为36位字符")
    private String applyId;

    /**
     * 序号
     * XH
     */
    @Excel(name = "序号" , width = 30)
    private Integer xh;

    /**
     * 申请人主键
     * LEGAL_ID
     */
    @Excel(name = "申请人主键" , width = 30)
    @Size(max = 50, message = "申请人主键最大为36位字符")
    private String legalId;

    /**
     * 申请人名称
     * LEGAL_NAME
     */
    @Excel(name = "申请人名称" , width = 30)
    @Size(max = 200, message = "申请人名称最大为100位字符")
    private String legalName;

    /**
     * 通信地址
     * MAIL_ADDRESS
     */
    @Excel(name = "通信地址" , width = 30)
    @Size(max = 500, message = "通信地址最大为200位字符")
    private String mailAddress;

    /**
     * 邮政编码
     * POST_OFFICE
     */
    @Excel(name = "邮政编码" , width = 30)
    @Size(max = 20, message = "邮政编码最大为20位字符")
    private String postOffice;

    /**
     * 付费联系人
     * LXR_MONEY
     */
    @Excel(name = "付费联系人" , width = 30)
    @Size(max = 100, message = "付费联系人最大为20位字符")
    private String lxrMoney;

    /**
     * 联系电话
     * LXR_PHONE
     */
    @Excel(name = "联系电话" , width = 30)
    @Size(max = 30, message = "联系电话最大为30位字符")
    private String lxrPhone;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
