<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainTecharea">
	<typeAlias alias="tkizlMaintainTechareaResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlMaintainTecharea"/>
	<select id="load" parameterClass="string" resultClass="tkizlMaintainTechareaResult">
		SELECT
			AREA_ID as "areaId" , <!-- ${column.columnComment} -->
			AREA_NAME as "areaName" , <!-- ${column.columnComment} -->
			PARENT_NAME as "parentName" , <!-- ${column.columnComment} -->
			PARENT_ID as "parentId" , <!-- ${column.columnComment} -->
			FULL_ID as "fullId" , <!-- ${column.columnComment} -->
			FULL_NAME as "fullName" , <!-- ${column.columnComment} -->
			SORTNO as "sortno" , <!-- ${column.columnComment} -->
			EXTRA1 as "extra1" , <!-- ${column.columnComment} -->
			EXTRA2 as "extra2" , <!-- ${column.columnComment} -->
			EXTRA3 as "extra3" , <!-- ${column.columnComment} -->
			EXTRA4 as "extra4" , <!-- ${column.columnComment} -->
			EXTRA5 as "extra5" , <!-- ${column.columnComment} -->
			DEL_STATUS as "delStatus" , <!-- ${column.columnComment} -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- ${column.columnComment} -->
			CREATE_DATE as "createDate" , <!-- ${column.columnComment} -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- ${column.columnComment} -->
			UPDATE_DATE as "updateDate" , <!-- ${column.columnComment} -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- ${column.columnComment} -->
			DELETE_DATE as "deleteDate" , <!-- ${column.columnComment} -->
			RECORD_VERSION as "recordVersion"  <!-- ${column.columnComment} -->
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_TECHAREA
		WHERE
			AREA_ID = #areaId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlMaintainTechareaResult">
		SELECT
			AREA_ID as "areaId" ,
			AREA_NAME as "areaName" ,
			PARENT_NAME as "parentName" ,
			PARENT_ID as "parentId" ,
			FULL_ID as "fullId" ,
			FULL_NAME as "fullName" ,
			SORTNO as "sortno" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_TECHAREA
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="areaId">AREA_ID =  #areaId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="areaName">AREA_NAME =  #areaName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="parentName">PARENT_NAME =  #parentName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="parentId">PARENT_ID =  #parentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fullId">FULL_ID =  #fullId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fullName">FULL_NAME =  #fullName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sortno">SORTNO =  #sortno#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlMaintainTechareaResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_TECHAREA ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="areaId">AREA_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="areaName">AREA_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="parentName">PARENT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="parentId">PARENT_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="fullId">FULL_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="fullName">FULL_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="sortno">SORTNO</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="areaId">#areaId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="areaName">#areaName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="parentName">#parentName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="parentId">#parentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fullId">#fullId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fullName">#fullName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sortno">#sortno#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_TECHAREA
		WHERE 
		    AREA_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_TECHAREA
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="areaId">AREA_ID=#areaId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="areaName">AREA_NAME=#areaName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="parentName">PARENT_NAME=#parentName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="parentId">PARENT_ID=#parentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fullId">FULL_ID=#fullId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fullName">FULL_NAME=#fullName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sortno">SORTNO=#sortno#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlMaintainTechareaResult">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_TECHAREA	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="areaId">AREA_ID=#areaId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="areaName">AREA_NAME=#areaName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="parentName">PARENT_NAME=#parentName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="parentId">PARENT_ID=#parentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fullId">FULL_ID=#fullId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fullName">FULL_NAME=#fullName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sortno">SORTNO=#sortno#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			AREA_ID =#areaId#
	</update>

</sqlMap>