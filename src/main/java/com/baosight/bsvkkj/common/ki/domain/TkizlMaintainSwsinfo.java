package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_维护_事务所对象 t_kizl_maintain_swsinfo
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlMaintainSwsinfo extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * SWS_ID
     */
    private String swsId;

    /**
     * 管理组织代码
     * GLDW_CODE
     */
    @Excel(name = "管理组织代码" , width = 30)
    @Size(max = 20, message = "管理组织代码最大为20位字符")
    private String gldwCode;

    /**
     * 管理组织名称
     * GLDW_NAME
     */
    @Excel(name = "管理组织名称" , width = 30)
    @Size(max = 100, message = "管理组织名称最大为100位字符")
    private String gldwName;

    /**
     * 事务所名称
     * PATENT_TYPE
     */
    @Excel(name = "事务所名称" , width = 30)
    @Size(max = 50, message = "事务所名称最大为50位字符")
    private String swsName;

    /**
     * 联系人
     * LXR
     */
    @Excel(name = "联系人" , width = 30)
    @Size(max = 30, message = "联系人最大为30位字符")
    private String lxr;

    /**
     * 地址
     * ADDRESS
     */
    @Excel(name = "地址" , width = 30)
    @Size(max = 80, message = "地址最大为80位字符")
    private String address;

    /**
     * 邮箱
     * EMAIL
     */
    @Excel(name = "邮箱" , width = 30)
    @Size(max = 30, message = "邮箱最大为30位字符")
    private String email;

    /**
     * 联系电话
     * LXR_PHONE
     */
    @Excel(name = "联系电话" , width = 30)
    @Size(max = 20, message = "联系电话最大为20位字符")
    private String lxrPhone;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
