<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplyConfirm">
	<typeAlias alias="tkizlApplyConfirmResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlApplyConfirm"/>
	<select id="load" parameterClass="string" resultClass="tkizlApplyConfirmResult">
		SELECT
			CONFIRM_ID as "confirmId" , <!-- 主键 -->
			APPLY_ID as "applyId" , <!-- 申请主键 -->
			PATENT_ID as "patentId" , <!-- 专利主键 -->
			FLOW_STATUS as "flowStatus" , <!-- draft-草稿 active-审批中 end-结束 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_APPLY_CONFIRM
		WHERE
			CONFIRM_ID = #confirmId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlApplyConfirmResult">
		SELECT
			CONFIRM_ID as "confirmId" ,
			APPLY_ID as "applyId" ,
			PATENT_ID as "patentId" ,
			FLOW_STATUS as "flowStatus" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_APPLY_CONFIRM
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="confirmId">CONFIRM_ID =  #confirmId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlApplyConfirmResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_CONFIRM ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="confirmId">CONFIRM_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentId">PATENT_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">FLOW_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="confirmId">#confirmId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentId">#patentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_CONFIRM
		WHERE 
		    CONFIRM_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_CONFIRM
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="confirmId">CONFIRM_ID=#confirmId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID=#patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS=#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlApplyConfirmResult">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_CONFIRM	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="confirmId">CONFIRM_ID=#confirmId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="patentId">PATENT_ID=#patentId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">FLOW_STATUS=#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			CONFIRM_ID =#confirmId#
	</update>

</sqlMap>