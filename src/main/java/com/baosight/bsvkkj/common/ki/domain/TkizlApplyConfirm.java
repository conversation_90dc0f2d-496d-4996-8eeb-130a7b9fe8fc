package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_申请_确认信息对象 t_kizl_apply_confirm
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlApplyConfirm extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * CONFIRM_ID
     */
    private String confirmId;

    /**
     * 申请主键
     * APPLY_ID
     */
    @Excel(name = "申请主键" , width = 30)
    @Size(max = 36, message = "申请主键最大为36位字符")
    private String applyId;

    /**
     * 专利主键
     * PATENT_ID
     */
    @Excel(name = "专利主键" , width = 30)
    @Size(max = 36, message = "专利主键最大为36位字符")
    private String patentId;

    /**
     * draft-草稿 active-审批中 end-结束
     * FLOW_STATUS
     */
    @Excel(name = "draft-草稿 active-审批中 end-结束" , width = 30)
    @Size(max = 20, message = "draft-草稿 active-审批中 end-结束最大为20位字符")
    private String flowStatus;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
