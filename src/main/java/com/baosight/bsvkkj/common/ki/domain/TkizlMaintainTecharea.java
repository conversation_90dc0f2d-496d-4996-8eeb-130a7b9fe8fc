package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 【请填写功能名称】对象 t_kizl_maintain_techarea
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Getter
@Setter
@ToString
public class TkizlMaintainTecharea extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     * AREA_ID
     */
    @Excel(name = "${comment}" , width = 30)
    private String areaId;

    /**
     * $column.columnComment
     * AREA_NAME
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 60, message = "${comment}最大为60位字符")
    private String areaName;

    /**
     * $column.columnComment
     * PARENT_NAME
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 60, message = "${comment}最大为60位字符")
    private String parentName;

    /**
     * $column.columnComment
     * PARENT_ID
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 36, message = "${comment}最大为36位字符")
    private String parentId;

    /**
     * $column.columnComment
     * FULL_ID
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 400, message = "${comment}最大为400位字符")
    private String fullId;

    /**
     * $column.columnComment
     * FULL_NAME
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 200, message = "${comment}最大为200位字符")
    private String fullName;

    /**
     * $column.columnComment
     * SORTNO
     */
    @Excel(name = "${comment}" , width = 30)
    private Integer sortno;

    /**
     * $column.columnComment
     * EXTRA1
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String extra1;

    /**
     * $column.columnComment
     * EXTRA2
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String extra2;

    /**
     * $column.columnComment
     * EXTRA3
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String extra3;

    /**
     * $column.columnComment
     * EXTRA4
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String extra4;

    /**
     * $column.columnComment
     * EXTRA5
     */
    @Excel(name = "${comment}" , width = 30)
    @Size(max = 30, message = "${comment}最大为30位字符")
    private String extra5;

}
