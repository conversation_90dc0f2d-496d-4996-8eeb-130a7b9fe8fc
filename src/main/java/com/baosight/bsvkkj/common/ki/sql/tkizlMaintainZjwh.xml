<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainZjwh">
	<typeAlias alias="tkizlMaintainZjwhResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlMaintainZjwh"/>
	<select id="load" parameterClass="string" resultClass="tkizlMaintainZjwhResult">
		SELECT
			MONEY_ID as "moneyId" , <!-- 主键 -->
			DEPT_CODE as "deptCode" , <!-- 组织代码 -->
			DEPT_NAME as "deptName" , <!-- 组织名称 -->
			EXPERT_GH as "expertGh" , <!-- 专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计 -->
			EXPERT_NAME as "expertName" , <!-- 费用名称 -->
			AREA as "area" , <!-- 费用序号 -->
			MAJOR as "major" , <!-- 金额 -->
			EMPLOYED_OR_NOT as "employedOrNot" , <!-- ${column.columnComment} -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_ZJWH
		WHERE
			MONEY_ID = #moneyId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlMaintainZjwhResult">
		SELECT
			MONEY_ID as "moneyId" ,
			DEPT_CODE as "deptCode" ,
			DEPT_NAME as "deptName" ,
			EXPERT_GH as "expertGh" ,
			EXPERT_NAME as "expertName" ,
			AREA as "area" ,
			MAJOR as "major" ,
			EMPLOYED_OR_NOT as "employedOrNot" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_ZJWH
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="moneyId">MONEY_ID =  #moneyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="expertGh">EXPERT_GH =  #expertGh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="expertName">EXPERT_NAME =  #expertName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="area">AREA =  #area#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="major">MAJOR =  #major#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="employedOrNot">EMPLOYED_OR_NOT =  #employedOrNot#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlMaintainZjwhResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_ZJWH ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="moneyId">MONEY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptCode">DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptName">DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="expertGh">EXPERT_GH</isNotEmpty>
			<isNotEmpty prepend=" , " property="expertName">EXPERT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="area">AREA</isNotEmpty>
			<isNotEmpty prepend=" , " property="major">MAJOR</isNotEmpty>
			<isNotEmpty prepend=" , " property="employedOrNot">EMPLOYED_OR_NOT</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="moneyId">#moneyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptCode">#deptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptName">#deptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="expertGh">#expertGh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="expertName">#expertName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="area">#area#</isNotEmpty>
			<isNotEmpty prepend=" , " property="major">#major#</isNotEmpty>
			<isNotEmpty prepend=" , " property="employedOrNot">#employedOrNot#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_ZJWH
		WHERE 
		    MONEY_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_ZJWH
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="moneyId">MONEY_ID=#moneyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE=#deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME=#deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="expertGh">EXPERT_GH=#expertGh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="expertName">EXPERT_NAME=#expertName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="area">AREA=#area#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="major">MAJOR=#major#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="employedOrNot">EMPLOYED_OR_NOT=#employedOrNot#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlMaintainZjwhResult">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_ZJWH	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="moneyId">MONEY_ID=#moneyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptCode">DEPT_CODE=#deptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptName">DEPT_NAME=#deptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="expertGh">EXPERT_GH=#expertGh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="expertName">EXPERT_NAME=#expertName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="area">AREA=#area#</isNotEmpty>
			<isNotEmpty prepend=" , " property="major">MAJOR=#major#</isNotEmpty>
			<isNotEmpty prepend=" , " property="employedOrNot">EMPLOYED_OR_NOT=#employedOrNot#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			MONEY_ID =#moneyId#
	</update>

</sqlMap>