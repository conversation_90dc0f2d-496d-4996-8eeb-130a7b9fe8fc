package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;
import java.math.BigDecimal;


/**
 * 境内专利_专利信息对象 t_kizl_patent_info
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlPatentInfo extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * PATENT_ID
     */
    private String patentId;

    /**
     * 申请主键
     * APPLY_ID
     */
    @Excel(name = "申请主键" , width = 30)
    @Size(max = 36, message = "申请主键最大为36位字符")
    private String applyId;

    /**
     * 流水号
     * SERIAL_NUM
     */
    @Excel(name = "流水号" , width = 30)
    @Size(max = 20, message = "流水号最大为20位字符")
    private String serialNum;

    /**
     * 接收编号
     * JSBH
     */
    @Excel(name = "接收编号" , width = 30)
    @Size(max = 20, message = "接收编号最大为20位字符")
    private String jsbh;

    /**
     * 鄂钢编号
     * EGBH
     */
    @Excel(name = "鄂钢编号" , width = 30)
    @Size(max = 20, message = "鄂钢编号最大为20位字符")
    private String egbh;

    /**
     * 申报名称
     * PATENT_NAME
     */
    @Excel(name = "专利名称" , width = 30)
    @Size(max = 200, message = "申报名称最大为200位字符")
    private String patentName;

    /**
     * 第一申报部门
     * FIRST_DEPT_CODE
     */
    @Excel(name = "第一申报部门" , width = 30)
    @Size(max = 20, message = "第一申报部门最大为20位字符")
    private String firstDeptCode;

    /**
     * 第一申报部门名称
     * FIRST_DEPT_NAME
     */
    @Excel(name = "第一申报部门名称" , width = 30)
    @Size(max = 20, message = "第一申报部门名称最大为20位字符")
    private String firstDeptName;

    /**
     * 管理单位
     * GLDW_CODE
     */
    @Excel(name = "管理单位" , width = 30)
    @Size(max = 20, message = "管理单位最大为20位字符")
    private String gldwCode;

    /**
     * 管理单位名称
     * GLDW_NAME
     */
    @Excel(name = "管理单位名称" , width = 30)
    @Size(max = 20, message = "管理单位名称最大为20位字符")
    private String gldwName;

    /**
     * 标签
     * LABEL
     */
    @Excel(name = "标签" , width = 30)
    private String label;

    /**
     * 技术领域
     * TECH_AREA
     */
    @Excel(name = "技术领域" , width = 30)
    private String techArea;

    /**
     * 用途
     * USE_PROPOSE
     */
    @Excel(name = "用途" , width = 30)
    private String usePropose;

    /**
     * 知识分类
     * KNOWLEDGE_CLASS
     */
    @Excel(name = "知识分类" , width = 30)
    private String knowledgeClass;

    /**
     * 联系人
     * LXR
     */
    @Excel(name = "联系人" , width = 30)
    @Size(max = 10, message = "联系人最大为10位字符")
    private String lxr;

    /**
     * 联系人姓名
     * LXR_NAME
     */
    @Excel(name = "联系人姓名" , width = 30)
    @Size(max = 10, message = "联系人姓名最大为10位字符")
    private String lxrName;

    /**
     * 联系人电话
     * LXRPHONE
     */
    @Excel(name = "联系人电话" , width = 30)
    @Size(max = 20, message = "联系人电话最大为20位字符")
    private String lxrphone;

    /**
     * 联系人邮箱
     * LXR_EMAIL
     */
    @Excel(name = "联系人邮箱" , width = 30)
    @Size(max = 30, message = "联系人邮箱最大为30位字符")
    private String lxrEmail;

    /**
     * 联系人手机
     * LXR_MOBILE
     */
    @Excel(name = "联系人手机" , width = 30)
    @Size(max = 20, message = "联系人手机最大为20位字符")
    private String lxrMobile;

    /**
     * 来源类型
     * FROM_TYPE
     */
    @Excel(name = "来源类型" , width = 30)
    private String fromType;

    /**
     * 来源编号
     * FROM_NO
     */
    @Excel(name = "来源编号" , width = 30)
    @Size(max = 20, message = "来源编号最大为20位字符")
    private String fromNo;

    /**
     * 来源名称
     * FROM_NAME
     */
    @Excel(name = "来源名称" , width = 30)
    @Size(max = 200, message = "来源名称最大为200位字符")
    private String fromName;

    /**
     * 其他来源情况说明
     * FROM_CONTENT
     */
    @Excel(name = "其他来源情况说明" , width = 30)
    @Size(max = 200, message = "其他来源情况说明最大为200位字符")
    private String fromContent;

    /**
     * 应用方式
     * USE_METHOD
     */
    @Excel(name = "应用方式" , width = 30)
    private String useMethod;

    /**
     * 应用部门
     * USE_DEPT
     */
    @Excel(name = "应用部门" , width = 30)
    @Size(max = 200, message = "应用部门最大为200位字符")
    private String useDept;

    /**
     * 应用部门名称
     * USE_DEPT_DEPT
     */
    @Excel(name = "应用部门名称" , width = 30)
    @Size(max = 500, message = "应用部门名称最大为500位字符")
    private String useDeptDept;

    /**
     * 预计应用部门
     * USE_EXPECTED
     */
    @Excel(name = "预计应用部门" , width = 30)
    @Size(max = 200, message = "预计应用部门最大为200位字符")
    private String useExpected;

    /**
     * 预计应用部门名称
     * USE_EXPECTED_NAME
     */
    @Excel(name = "预计应用部门名称" , width = 30)
    @Size(max = 500, message = "预计应用部门名称最大为500位字符")
    private String useExpectedName;

    /**
     * 初始应用日期
     * USE_FIRSTDATE
     */
    @Excel(name = "初始应用日期" , width = 30)
    @Size(max = 10, message = "初始应用日期最大为10位字符")
    private String useFirstdate;

    /**
     * 未应用原因
     * REASON_NOUSE
     */
    @Excel(name = "未应用原因" , width = 30)
    @Size(max = 50, message = "未应用原因最大为50位字符")
    private String reasonNouse;

    /**
     * 未应用原因备注
     * CONTENT_NOUSE
     */
    @Excel(name = "未应用原因备注" , width = 30)
    @Size(max = 200, message = "未应用原因备注最大为200位字符")
    private String contentNouse;

    private String firstDeptPath;

    /**
     * 流程状态 draft-草稿 active-流程中 end-结束
     * FLOW_STATUS
     */
    @Excel(name = "流程状态 draft-草稿 active-流程中 end-结束" , width = 30)
    @Size(max = 2, message = "流程状态 draft-草稿 active-流程中 end-结束最大为2位字符")
    private String flowStatus;

    /**
     * 专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权
     * PATENT_STATUS
     */
    @Excel(name = "专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权" , width = 30)
    @Size(max = 2, message = "专利状态 01-申请中 02-待交底 03-交底 04-代理中 05-已受理 06-已授权最大为2位字符")
    private String patentStatus;

    /**
     * 待交底人员
     * DJD_PERSON
     */
    @Excel(name = "待交底人员" , width = 30)
    @Size(max = 10, message = "待交底人员最大为10位字符")
    private String djdPerson;

    /**
     * 待交底日期
     * DJD_DATE
     */
    @Excel(name = "待交底日期" , width = 30)
    @Size(max = 10, message = "待交底日期最大为10位字符")
    private String djdDate;

    /**
     * 交底人员
     * JD_PERSON
     */
    @Excel(name = "交底人员" , width = 30)
    @Size(max = 10, message = "交底人员最大为10位字符")
    private String jdPerson;

    /**
     * 交底日期
     * JD_DATE
     */
    @Excel(name = "交底日期" , width = 30)
    @Size(max = 10, message = "交底日期最大为10位字符")
    private String jdDate;

    /**
     * 受理_权属
     * QS
     */
    @Excel(name = "受理_权属" , width = 30)
    private Integer qs;

    /**
     * 受理_受理日期
     * SLRQ
     */
    @Excel(name = "受理_受理日期" , width = 30)
    @Size(max = 10, message = "受理_受理日期最大为10位字符")
    private String slrq;

    /**
     * 受理_申请号
     * PATENT_NO
     */
    @Excel(name = "受理_申请号" , width = 30)
    @Size(max = 20, message = "受理_申请号最大为20位字符")
    private String patentNo;

    /**
     * 受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计
     * PATENT_TYPE
     */
    @Excel(name = "受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计" , width = 30)
    @Size(max = 4, message = "受理_专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计最大为4位字符")
    private String patentType;

    /**
     * 受理_优先权日
     * YXQR
     */
    @Excel(name = "受理_优先权日" , width = 30)
    @Size(max = 10, message = "受理_优先权日最大为10位字符")
    private String yxqr;

    /**
     * 受理_权利要求数量
     * QLYQSL
     */
    @Excel(name = "受理_权利要求数量" , width = 30)
    private Integer qlyqsl;

    /**
     * 受理_说明书页数
     * SMSYS
     */
    @Excel(name = "受理_说明书页数" , width = 30)
    private Integer smsys;

    /**
     * 代理事务所
     * SWS_GUID
     */
    @Excel(name = "代理事务所" , width = 30)
    @Size(max = 36, message = "代理事务所最大为36位字符")
    private String swsGuid;

    /**
     * 代理人
     * SWSDLR
     */
    @Excel(name = "代理人" , width = 30)
    @Size(max = 20, message = "代理人最大为20位字符")
    private String swsdlr;

    /**
     * 代理人电话
     * SWSDLR_PHONE
     */
    @Excel(name = "代理人电话" , width = 30)
    @Size(max = 20, message = "代理人电话最大为20位字符")
    private String swsdlrPhone;

    /**
     * 代理人邮箱
     * SWSDLR_EMAIL
     */
    @Excel(name = "代理人邮箱" , width = 30)
    @Size(max = 30, message = "代理人邮箱最大为30位字符")
    private String swsdlrEmail;

    /**
     * 受理_代理费
     * MONEY_DLF
     */
    @Excel(name = "受理_代理费" , width = 30)
    private BigDecimal moneyDlf;

    /**
     * 受理_权附
     * MONEY_QF
     */
    @Excel(name = "受理_权附" , width = 30)
    private BigDecimal moneyQf;

    /**
     * 受理_申请费
     * MONEY_SQF
     */
    @Excel(name = "受理_申请费" , width = 30)
    private BigDecimal moneySqf;

    /**
     * 受理_公布印刷费
     * MONEY_GBYSF
     */
    @Excel(name = "受理_公布印刷费" , width = 30)
    private BigDecimal moneyGbysf;

    /**
     * 受理_说明书附加费
     * MONEY_SMSFJF
     */
    @Excel(name = "受理_说明书附加费" , width = 30)
    private BigDecimal moneySmsfjf;

    /**
     * 授权通知_印花税
     * MONEY_YHS
     */
    @Excel(name = "授权通知_印花税" , width = 30)
    private BigDecimal moneyYhs;

    /**
     * 授权通知_发文日
     * SQTZ_FWDATE
     */
    @Excel(name = "授权通知_发文日" , width = 30)
    @Size(max = 10, message = "授权通知_发文日最大为10位字符")
    private String sqtzFwdate;

    /**
     * 授权通知_第一次缴费
     * MONEY_FIRST
     */
    @Excel(name = "授权通知_第一次缴费" , width = 30)
    @Size(max = 20, message = "授权通知_第一次缴费最大为20位字符")
    private String moneyFirst;

    /**
     * 专利号
     * ZLH
     */
    @Excel(name = "专利号" , width = 30)
    @Size(max = 20, message = "专利号最大为20位字符")
    private String zlh;

    /**
     * 授权日期
     * SQRQ
     */
    @Excel(name = "授权日期" , width = 30)
    @Size(max = 10, message = "授权日期最大为10位字符")
    private String sqrq;

    @Excel(name = "内部参考意见" , width = 30)
    private String nbckyj;

    /**
     * 法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止
     * FLZT
     */
    @Excel(name = "法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止" , width = 30)
    @Size(max = 2, message = "法律状态 01-申请中 02-已受理 03-主动放弃 04-视为撤回 05-主动撤回 06-驳回 07-到期 08-转让 09-终止最大为2位字符")
    private String flzt;

    /**
     * 是否有效 1-有效 0-无效
     * ISVALID
     */
    @Excel(name = "是否有效 1-有效 0-无效" , width = 30)
    @Size(max = 1, message = "是否有效 1-有效 0-无效最大为1位字符")
    private String isvalid;

    /**
     * IPC主分类号
     * EXTRA1
     */
    @Excel(name = "IPC主分类号" , width = 30)
    @Size(max = 30, message = "IPC主分类号最大为30位字符")
    private String extra1;

    /**
     * 终止日期
     * EXTRA2
     */
    @Excel(name = "终止日期" , width = 30)
    @Size(max = 30, message = "终止日期最大为30位字符")
    private String extra2;

    /**
     * 维护填写人
     * EXTRA3
     */
    @Excel(name = "填写人" , width = 30)
    private String extra3;

    /**
     * 维护填写日期
     * EXTRA4
     */
    @Excel(name = "填写日期" , width = 30)
    private String extra4;

    /**
     * 维护备注
     * EXTRA5
     */
    @Excel(name = "维护备注" , width = 30)
    @Size(max = 30, message = "维护备注最大为30位字符")
    private String extra5;
    /**是否暂缓交底*/
    private String extra6;
    /**是否暂缓交底日期*/
    private String extra7;
    private String extra8;
    private String extra9;
    private String extra10;
    private String extra11;
    private String extra12;
    private String extra13;
    private String extra14;
    private String extra15;

}
