package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * 境内专利_申请_人员信息对象 t_kizl_apply_ryxx
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlApplyRyxx extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * RYXX_ID
     */
    private String ryxxId;

    /**
     * 申请主键
     * APPLY_ID
     */
    @Excel(name = "申请主键" , width = 30)
    @Size(max = 50, message = "申请主键最大为36位字符")
    private String applyId;

    /**
     * 专利主键
     * PATENT_ID
     */
    @Excel(name = "专利主键" , width = 30)
    @Size(max = 50, message = "专利主键最大为36位字符")
    private String patentId;

    /**
     * 序号
     * RYXH
     */
    @Excel(name = "序号" , width = 30)
    private BigDecimal ryxh;

    /**
     * 类型 01-宝武人员 02-非宝武人员
     * RYLX
     */
    @Excel(name = "类型 01-宝武人员 02-非宝武人员" , width = 30)
    @Size(max = 2, message = "类型 01-宝武人员 02-非宝武人员最大为2位字符")
    private String rylx;

    /**
     * 所属法人
     * LEGAL_ID
     */
    @Excel(name = "所属法人" , width = 30)
    @Size(max = 100, message = "所属法人最大为36位字符")
    private String legalId;

    /**
     * 工号
     * EMP_ID
     */
    @Excel(name = "工号" , width = 30)
    @Size(max = 10, message = "工号最大为10位字符")
    private String empId;

    /**
     * 姓名
     * EMP_NAME
     */
    @Excel(name = "姓名" , width = 30)
    @Size(max = 50, message = "姓名最大为20位字符")
    private String empName;

    /**
     * 人员组织
     * DEPT_CODE
     */
    @Excel(name = "人员组织" , width = 30)
    @Size(max = 20, message = "人员组织最大为20位字符")
    private String deptCode;

    /**
     * 人员组织名称
     * DEPT_NAME
     */
    @Excel(name = "人员组织名称" , width = 30)
    @Size(max = 500, message = "人员组织名称最大为100位字符")
    private String deptName;

    /**
     * 职称
     * POST_TITLE
     */
    @Excel(name = "职称" , width = 30)
    @Size(max = 50, message = "职称最大为2位字符")
    private String postTitle;

    /**
     * 岗位
     * POST_LEVEL
     */
    @Excel(name = "岗位" , width = 30)
    @Size(max = 2, message = "岗位最大为2位字符")
    private String postLevel;
    
    /**
     * 岗位名称
     * POST_NAME
     */
    @Excel(name = "岗位名称" , width = 30)
    @Size(max = 200, message = "岗位名称最大为20位字符")
    private String postName;

    /**
     * 贡献系数
     * GXXS
     */
    @Excel(name = "贡献系数" , width = 30)
    private BigDecimal gxxs;

    /**
     * 身份证号
     * ID_CARD
     */
    @Excel(name = "身份证号" , width = 30)
    @Size(max = 20, message = "身份证号最大为20位字符")
    private String idCard;

    /**
     * 备注
     * CONTENT_MEMO
     */
    @Excel(name = "备注" , width = 30)
    @Size(max = 500, message = "备注最大为500位字符")
    private String contentMemo;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
