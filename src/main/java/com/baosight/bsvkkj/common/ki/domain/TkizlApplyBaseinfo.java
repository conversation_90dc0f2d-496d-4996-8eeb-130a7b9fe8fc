package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_申请_基本信息对象 t_kizl_apply_baseinfo
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlApplyBaseinfo extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * APPLY_ID
     */
    private String applyId;

    /**
     * 流水号
     * SERIAL_NUM
     */
    @Excel(name = "流水号" , width = 30)
    @Size(max = 30, message = "流水号最大为20位字符")
    private String serialNum;

    /**
     * 接收编号
     * JSBH
     */
    @Excel(name = "接收编号" , width = 30)
    @Size(max = 20, message = "接收编号最大为20位字符")
    private String jsbh;

    /**
     * 申请日期
     * APPLY_DATE
     */
    @Excel(name = "申请日期" , width = 30)
    @Size(max = 10, message = "申请日期最大为10位字符")
    private String applyDate;

    /**
     * 申报名称
     * APPLY_NAME
     */
    @Excel(name = "申报名称" , width = 30)
    @Size(max = 500, message = "申报名称最大为200位字符")
    private String applyName;

    /**
     * 第一申报部门
     * FIRST_DEPT_CODE
     */
    @Excel(name = "第一申报部门" , width = 30)
    @Size(max = 20, message = "第一申报部门最大为20位字符")
    private String firstDeptCode;

    /**
     * 第一申报部门名称
     * FIRST_DEPT_NAME
     */
    @Excel(name = "第一申报部门名称" , width = 30)
    @Size(max = 500, message = "第一申报部门名称最大为100位字符")
    private String firstDeptName;

    /**
     * 管理单位
     * GLDW_CODE
     */
    @Excel(name = "管理单位" , width = 30)
    @Size(max = 20, message = "管理单位最大为20位字符")
    private String gldwCode;

    /**
     * 管理单位名称
     * GLDW_NAME
     */
    @Excel(name = "管理单位名称" , width = 30)
    @Size(max = 500, message = "管理单位名称最大为100位字符")
    private String gldwName;

    /**
     * 权属
     * OWNERSHIP
     */
    @Excel(name = "权属" , width = 30)
    private Integer ownership;

    /**
     * 标签
     * LABEL
     */
    @Excel(name = "标签" , width = 30)
    @Size(max = 100, message = "标签最大为100位字符")
    private String label;

    /**
     * 技术领域
     * TECH_AREA
     */
    @Excel(name = "技术领域" , width = 30)
    @Size(max = 100, message = "技术领域最大为100位字符")
    private String techArea;

    /**
     * 用途
     * USE_PROPOSE
     */
    @Excel(name = "用途" , width = 30)
    @Size(max = 100, message = "用途最大为100位字符")
    private String usePropose;

    /**
     * 是否产品专利 1-是 0-否
     * KNOWLEDGE_CLASS
     */
    @Excel(name = "是否产品专利 1-是 0-否" , width = 30)
    @Size(max = 1, message = "是否产品专利 1-是 0-否最大为1位字符")
    private String knowledgeClass;

    /**
     * 产品领域
     * ISHAVEPH
     */
    @Size(max = 2, message = "产品领域  最大为2位字符")
    private String ishaveph;

    /**
     * 联系人
     * LXR_CODE
     */
    @Excel(name = "联系人" , width = 30)
    @Size(max = 10, message = "联系人最大为10位字符")
    private String lxrCode;

    /**
     * <div th:include="/component/select
     * LXR_NAME
     */
    @Excel(name = "联系人" , width = 30)
    @Size(max = 50, message = "联系人最大为20位字符")
    private String lxrName;

    /**
     * 联系人电话
     * LXR_PHONE
     */
    @Excel(name = "联系人电话" , width = 30)
    @Size(max = 20, message = "联系人电话最大为20位字符")
    private String lxrPhone;

    /**
     * 联系人手机
     * LXR_MOBILE
     */
    @Excel(name = "联系人手机" , width = 30)
    @Size(max = 20, message = "联系人手机最大为20位字符")
    private String lxrMobile;

    /**
     * 专利检索情况
     * SEARCH_TYPE
     */
    @Excel(name = "专利检索情况" , width = 30)
    @Size(max = 2000, message = "专利检索情况最大为2000位字符")
    private String searchType;

    /**
     * 来源类型
     * FROM_TYPE
     */
    @Excel(name = "来源类型" , width = 30)
    @Size(max = 10, message = "来源类型最大为2位字符")
    private String fromType;

    /**
     * 来源编号
     * FROM_NO
     */
    @Excel(name = "来源编号" , width = 30)
    @Size(max = 30, message = "来源编号最大为20位字符")
    private String fromNo;

    /**
     * 来源名称
     * FROM_NAME
     */
    @Excel(name = "来源名称" , width = 30)
    @Size(max = 500, message = "来源名称最大为200位字符")
    private String fromName;

    /**
     * 其他来源情况说明
     * FROM_CONTENT
     */
    @Excel(name = "其他来源情况说明" , width = 30)
    @Size(max = 500, message = "其他来源情况说明最大为200位字符")
    private String fromContent;

    /**
     * 应用方式
     * USE_METHOD
     */
    @Excel(name = "应用方式" , width = 30)
    @Size(max = 10, message = "应用方式最大为10位字符")
    private String useMethod;

    /**
     * 应用部门
     * USE_DEPT
     */
    @Excel(name = "应用部门" , width = 30)
    @Size(max = 200, message = "应用部门最大为200位字符")
    private String useDept;

    /**
     * 应用部门名称
     * USE_DEPT_NAME
     */
    @Excel(name = "应用部门名称" , width = 30)
    @Size(max = 500, message = "应用部门名称最大为500位字符")
    private String useDeptName;

    /**
     * 预计应用部门
     * USE_EXPECTED
     */
    @Excel(name = "预计应用部门" , width = 30)
    @Size(max = 200, message = "预计应用部门最大为200位字符")
    private String useExpected;

    /**
     * 预计应用部门名称
     * USE_EXPECTED_NAME
     */
    @Excel(name = "预计应用部门名称" , width = 30)
    @Size(max = 500, message = "预计应用部门名称最大为500位字符")
    private String useExpectedName;

    /**
     * 初始应用日期
     * USE_FIRSTDATE
     */
    @Excel(name = "初始应用日期" , width = 30)
    @Size(max = 10, message = "初始应用日期最大为10位字符")
    private String useFirstdate;

    /**
     * 未应用原因
     * REASON_NOUSE
     */
    @Excel(name = "未应用原因" , width = 30)
    @Size(max = 500, message = "未应用原因最大为50位字符")
    private String reasonNouse;

    /**
     * 未应用原因备注
     * CONTENT_NOUSE
     */
    @Excel(name = "未应用原因备注" , width = 30)
    @Size(max = 500, message = "未应用原因备注最大为200位字符")
    private String contentNouse;

    /**
     * 预计效果
     * RESULT_EXPECT
     */
    @Excel(name = "预计效果" , width = 30)
    @Size(max = 100, message = "预计效果最大为100位字符")
    private String resultExpect;

    /**
     * 摘要
     * SUMMARY
     */
    @Excel(name = "摘要" , width = 30)
    @Size(max = 500, message = "摘要最大为500位字符")
    private String summary;

    /**
     * 是否需要保密确认 1-是 0-否
     * ZGBM_ISBMQR
     */
    @Excel(name = "是否需要保密确认 1-是 0-否" , width = 30)
    @Size(max = 1, message = "是否需要保密确认 1-是 0-否最大为1位字符")
    private String zgbmIsbmqr;

    /**
     * 主管部门内参意见
     * ZGBM_NC
     */
    @Excel(name = "主管部门内参意见" , width = 30)
    @Size(max = 500, message = "主管部门内参意见最大为500位字符")
    private String zgbmNc;

    /**
     * 主管部门操作意见
     * ZGBM_CZ
     */
    @Excel(name = "主管部门操作意见" , width = 30)
    @Size(max = 500, message = "主管部门操作意见最大为50位字符")
    private String zgbmCz;

    /**
     * draft-草稿 active-审批中 end-结束
     * FLOW_STATUS
     */
    @Excel(name = "draft-草稿 active-审批中 end-结束" , width = 30)
    @Size(max = 20, message = "draft-草稿 active-审批中 end-结束最大为20位字符")
    private String flowStatus;

    /**
     * 1-是 0-否
     * FLAG_OLD
     */
    @Excel(name = "1-是 0-否" , width = 30)
    @Size(max = 1, message = "1-是 0-否最大为1位字符")
    private String flagOld;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 300, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 300, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 300, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 300, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 300, message = "扩展字段5最大为30位字符")
    private String extra5;

    /**
     * 扩展字段5
     * EXTRA6
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 300, message = "扩展字段5最大为30位字符")
    private String extra6;

    /**
     * 扩展字段5
     * EXTRA7
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 300, message = "扩展字段5最大为30位字符")
    private String extra7;

    /**
     * 扩展字段5
     * EXTRA8
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 300, message = "扩展字段5最大为30位字符")
    private String extra8;

    /**
     * 扩展字段5
     * EXTRA9
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 300, message = "扩展字段5最大为30位字符")
    private String extra9;

    /**
     * 扩展字段5
     * EXTRA10
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra10;

    /**
     * <div th:include="/component/select
     * EXTRA11
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra11;

    /**
     * <div th:include="/component/select
     * EXTRA12
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra12;

    /**
     * <div th:include="/component/select
     * EXTRA13
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra13;

    /**
     * <div th:include="/component/select
     * EXTRA14
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra14;

    /**
     * <div th:include="/component/select
     * EXTRA15
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra15;

    /**
     * <div th:include="/component/select
     * EXTRA16
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra16;

    /**
     * <div th:include="/component/select
     * EXTRA17
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra17;

    /**
     * <div th:include="/component/select
     * EXTRA18
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra18;

    /**
     * <div th:include="/component/select
     * EXTRA19
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra19;

    private String firstDeptPath;

}


