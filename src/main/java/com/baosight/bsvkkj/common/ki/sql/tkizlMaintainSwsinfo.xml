<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainSwsinfo">
	<typeAlias alias="tkizlMaintainSwsinfoResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlMaintainSwsinfo"/>
	<select id="load" parameterClass="string" resultClass="tkizlMaintainSwsinfoResult">
		SELECT
			SWS_ID as "swsId" , <!-- 主键 -->
			GLDW_CODE as "gldwCode" , <!-- 管理组织代码 -->
			GLDW_NAME as "gldwName" , <!-- 管理组织名称 -->
			SWS_NAME as "swsName" , <!-- 事务所名称 -->
			L<PERSON><PERSON> as "lxr" , <!-- 联系人 -->
			ADDRESS as "address" , <!-- 地址 -->
			EMAIL as "email" , <!-- 邮箱 -->
			LXR_PHONE as "lxrPhone" , <!-- 联系电话 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO
		WHERE
			SWS_ID = #swsId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlMaintainSwsinfoResult">
		SELECT
			SWS_ID as "swsId" ,
			GLDW_CODE as "gldwCode" ,
			GLDW_NAME as "gldwName" ,
			SWS_NAME as "swsName" ,
			LXR as "lxr" ,
			ADDRESS as "address" ,
			EMAIL as "email" ,
			LXR_PHONE as "lxrPhone" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="swsId">SWS_ID =  #swsId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsName">SWS_NAME =  #swsName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">LXR =  #lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="address">ADDRESS =  #address#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="email">EMAIL =  #email#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlMaintainSwsinfoResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="swsId">SWS_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsName">SWS_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">LXR</isNotEmpty>
			<isNotEmpty prepend=" , " property="address">ADDRESS</isNotEmpty>
			<isNotEmpty prepend=" , " property="email">EMAIL</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">LXR_PHONE</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="swsId">#swsId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsName">#swsName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">#lxr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="address">#address#</isNotEmpty>
			<isNotEmpty prepend=" , " property="email">#email#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO
		WHERE 
		    SWS_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="swsId">SWS_ID=#swsId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsName">SWS_NAME=#swsName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">LXR=#lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="address">ADDRESS=#address#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="email">EMAIL=#email#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE=#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlMaintainSwsinfoResult">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="swsId">SWS_ID=#swsId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="swsName">SWS_NAME=#swsName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">LXR=#lxr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="address">ADDRESS=#address#</isNotEmpty>
			<isNotEmpty prepend=" , " property="email">EMAIL=#email#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">LXR_PHONE=#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			SWS_ID =#swsId#
	</update>

</sqlMap>