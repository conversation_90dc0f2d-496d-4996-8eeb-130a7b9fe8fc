<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplyBaseinfo">
	<typeAlias alias="tkizlApplyBaseinfoResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlApplyBaseinfo"/>
	<select id="load" parameterClass="string" resultClass="tkizlApplyBaseinfoResult">
		SELECT
			APPLY_ID as "applyId" , <!-- 主键 -->
			SERIAL_NUM as "serialNum" , <!-- 流水号 -->
			JSBH as "jsbh" , <!-- 接收编号 -->
			APPLY_DATE as "applyDate" , <!-- 申请日期 -->
			APPLY_NAME as "applyName" , <!-- 申报名称 -->
			FIRST_DEPT_CODE as "firstDeptCode" , <!-- 第一申报部门 -->
			FIRST_DEPT_NAME as "firstDeptName" , <!-- 第一申报部门名称 -->
			GLDW_CODE as "gldwCode" , <!-- 管理单位 -->
			GLDW_NAME as "gldwName" , <!-- 管理单位名称 -->
			OWNERSHIP as "ownership" , <!-- 权属 -->
			LABEL as "label" , <!-- 标签 -->
			TECH_AREA as "techArea" , <!-- 技术领域 -->
			USE_PROPOSE as "usePropose" , <!-- 用途 -->
			KNOWLEDGE_CLASS as "knowledgeClass" , <!-- 是否产品专利 1-是 0-否 -->
			ISHAVEPH as "ishaveph" , <!-- 产品领域  1-是 0-否 -->
			LXR_CODE as "lxrCode" , <!-- 联系人 -->
			LXR_NAME as "lxrName" , <!-- ${column.columnComment} -->
			LXR_PHONE as "lxrPhone" , <!-- 联系人电话 -->
			LXR_MOBILE as "lxrMobile" , <!-- 联系人手机 -->
			SEARCH_TYPE as "searchType" , <!-- 专利检索情况 -->
			FROM_TYPE as "fromType" , <!-- 来源类型 -->
			FROM_NO as "fromNo" , <!-- 来源编号 -->
			FROM_NAME as "fromName" , <!-- 来源名称 -->
			FROM_CONTENT as "fromContent" , <!-- 其他来源情况说明 -->
			USE_METHOD as "useMethod" , <!-- 应用方式 -->
			USE_DEPT as "useDept" , <!-- 应用部门 -->
			USE_DEPT_NAME as "useDeptName" , <!-- 应用部门名称 -->
			USE_EXPECTED as "useExpected" , <!-- 预计应用部门 -->
			USE_EXPECTED_NAME as "useExpectedName" , <!-- 预计应用部门名称 -->
			USE_FIRSTDATE as "useFirstdate" , <!-- 初始应用日期 -->
			REASON_NOUSE as "reasonNouse" , <!-- 未应用原因 -->
			CONTENT_NOUSE as "contentNouse" , <!-- 未应用原因备注 -->
			RESULT_EXPECT as "resultExpect" , <!-- 预计效果 -->
			SUMMARY as "summary" , <!-- 摘要 -->
			ZGBM_ISBMQR as "zgbmIsbmqr" , <!-- 是否需要保密确认 1-是 0-否 -->
			ZGBM_NC as "zgbmNc" , <!-- 主管部门内参意见 -->
			ZGBM_CZ as "zgbmCz" , <!-- 主管部门操作意见 -->
			FLOW_STATUS as "flowStatus" , <!-- draft-草稿 active-审批中 end-结束 -->
			FLAG_OLD as "flagOld" , <!-- 1-是 0-否 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			EXTRA6 as "extra6" , <!-- 扩展字段5 -->
			EXTRA7 as "extra7" , <!-- 扩展字段5 -->
			EXTRA8 as "extra8" , <!-- 扩展字段5 -->
			EXTRA9 as "extra9" , <!-- 扩展字段5 -->
			EXTRA10 as "extra10" , <!-- 扩展字段5 -->
			EXTRA11 as "extra11" , <!-- ${column.columnComment} -->
			EXTRA12 as "extra12" , <!-- ${column.columnComment} -->
			EXTRA13 as "extra13" , <!-- ${column.columnComment} -->
			EXTRA14 as "extra14" , <!-- ${column.columnComment} -->
			EXTRA15 as "extra15" , <!-- ${column.columnComment} -->
			EXTRA16 as "extra16" , <!-- ${column.columnComment} -->
			EXTRA17 as "extra17" , <!-- ${column.columnComment} -->
			EXTRA18 as "extra18" , <!-- ${column.columnComment} -->
			EXTRA19 as "extra19" , <!-- ${column.columnComment} -->
			FIRST_DEPT_PATH as "firstDeptPath" , <!-- ${column.columnComment} -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO
		WHERE
			APPLY_ID = #applyId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlApplyBaseinfoResult">
		SELECT
			APPLY_ID as "applyId" ,
			SERIAL_NUM as "serialNum" ,
			JSBH as "jsbh" ,
			APPLY_DATE as "applyDate" ,
			APPLY_NAME as "applyName" ,
			FIRST_DEPT_CODE as "firstDeptCode" ,
			FIRST_DEPT_NAME as "firstDeptName" ,
			GLDW_CODE as "gldwCode" ,
			GLDW_NAME as "gldwName" ,
			OWNERSHIP as "ownership" ,
			LABEL as "label" ,
			TECH_AREA as "techArea" ,
			USE_PROPOSE as "usePropose" ,
			KNOWLEDGE_CLASS as "knowledgeClass" ,
			ISHAVEPH as "ishaveph" ,
			LXR_CODE as "lxrCode" ,
			LXR_NAME as "lxrName" ,
			LXR_PHONE as "lxrPhone" ,
			LXR_MOBILE as "lxrMobile" ,
			SEARCH_TYPE as "searchType" ,
			FROM_TYPE as "fromType" ,
			FROM_NO as "fromNo" ,
			FROM_NAME as "fromName" ,
			FROM_CONTENT as "fromContent" ,
			USE_METHOD as "useMethod" ,
			USE_DEPT as "useDept" ,
			USE_DEPT_NAME as "useDeptName" ,
			USE_EXPECTED as "useExpected" ,
			USE_EXPECTED_NAME as "useExpectedName" ,
			USE_FIRSTDATE as "useFirstdate" ,
			REASON_NOUSE as "reasonNouse" ,
			CONTENT_NOUSE as "contentNouse" ,
			RESULT_EXPECT as "resultExpect" ,
			SUMMARY as "summary" ,
			ZGBM_ISBMQR as "zgbmIsbmqr" ,
			ZGBM_NC as "zgbmNc" ,
			ZGBM_CZ as "zgbmCz" ,
			FLOW_STATUS as "flowStatus" ,
			FLAG_OLD as "flagOld" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			EXTRA6 as "extra6" ,
			EXTRA7 as "extra7" ,
			EXTRA8 as "extra8" ,
			EXTRA9 as "extra9" ,
			EXTRA10 as "extra10" ,
			EXTRA11 as "extra11" ,
			EXTRA12 as "extra12" ,
			EXTRA13 as "extra13" ,
			EXTRA14 as "extra14" ,
			EXTRA15 as "extra15" ,
			EXTRA16 as "extra16" ,
			EXTRA17 as "extra17" ,
			EXTRA18 as "extra18" ,
			EXTRA19 as "extra19" ,
			FIRST_DEPT_PATH as "firstDeptPath" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyDate">APPLY_DATE =  #applyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ownership">OWNERSHIP =  #ownership#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ishaveph">ISHAVEPH =  #ishaveph#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE =  #lxrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="searchType">SEARCH_TYPE =  #searchType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="resultExpect">RESULT_EXPECT =  #resultExpect#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="summary">SUMMARY =  #summary#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zgbmIsbmqr">ZGBM_ISBMQR =  #zgbmIsbmqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zgbmNc">ZGBM_NC =  #zgbmNc#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zgbmCz">ZGBM_CZ =  #zgbmCz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flagOld">FLAG_OLD =  #flagOld#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">EXTRA7 =  #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">EXTRA8 =  #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">EXTRA9 =  #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">EXTRA10 =  #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">EXTRA11 =  #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">EXTRA12 =  #extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">EXTRA13 =  #extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">EXTRA14 =  #extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">EXTRA15 =  #extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">EXTRA16 =  #extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">EXTRA17 =  #extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">EXTRA18 =  #extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">EXTRA19 =  #extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlApplyBaseinfoResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_BASEINFO ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNum">SERIAL_NUM</isNotEmpty>
			<isNotEmpty prepend=" , " property="jsbh">JSBH</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyDate">APPLY_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyName">APPLY_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">FIRST_DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">FIRST_DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="ownership">OWNERSHIP</isNotEmpty>
			<isNotEmpty prepend=" , " property="label">LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="techArea">TECH_AREA</isNotEmpty>
			<isNotEmpty prepend=" , " property="usePropose">USE_PROPOSE</isNotEmpty>
			<isNotEmpty prepend=" , " property="knowledgeClass">KNOWLEDGE_CLASS</isNotEmpty>
			<isNotEmpty prepend=" , " property="ishaveph">ISHAVEPH</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrCode">LXR_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">LXR_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">LXR_PHONE</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMobile">LXR_MOBILE</isNotEmpty>
			<isNotEmpty prepend=" , " property="searchType">SEARCH_TYPE</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">FROM_TYPE</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromNo">FROM_NO</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">FROM_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromContent">FROM_CONTENT</isNotEmpty>
			<isNotEmpty prepend=" , " property="useMethod">USE_METHOD</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDept">USE_DEPT</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDeptName">USE_DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpected">USE_EXPECTED</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpectedName">USE_EXPECTED_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="useFirstdate">USE_FIRSTDATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="reasonNouse">REASON_NOUSE</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentNouse">CONTENT_NOUSE</isNotEmpty>
			<isNotEmpty prepend=" , " property="resultExpect">RESULT_EXPECT</isNotEmpty>
			<isNotEmpty prepend=" , " property="summary">SUMMARY</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmIsbmqr">ZGBM_ISBMQR</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmNc">ZGBM_NC</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmCz">ZGBM_CZ</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">FLOW_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="flagOld">FLAG_OLD</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">EXTRA6</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">EXTRA7</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">EXTRA8</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">EXTRA9</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">EXTRA10</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">EXTRA11</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">EXTRA12</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">EXTRA13</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">EXTRA14</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">EXTRA15</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra16">EXTRA16</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra17">EXTRA17</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra18">EXTRA18</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra19">EXTRA19</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptPath">FIRST_DEPT_PATH</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="applyId">#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNum">#serialNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jsbh">#jsbh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyDate">#applyDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyName">#applyName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ownership">#ownership#</isNotEmpty>
			<isNotEmpty prepend=" , " property="label">#label#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techArea">#techArea#</isNotEmpty>
			<isNotEmpty prepend=" , " property="usePropose">#usePropose#</isNotEmpty>
			<isNotEmpty prepend=" , " property="knowledgeClass">#knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ishaveph">#ishaveph#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrCode">#lxrCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMobile">#lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" , " property="searchType">#searchType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">#fromType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromNo">#fromNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">#fromName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromContent">#fromContent#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useMethod">#useMethod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDept">#useDept#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDeptName">#useDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpected">#useExpected#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpectedName">#useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useFirstdate">#useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="reasonNouse">#reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentNouse">#contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="resultExpect">#resultExpect#</isNotEmpty>
			<isNotEmpty prepend=" , " property="summary">#summary#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmIsbmqr">#zgbmIsbmqr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmNc">#zgbmNc#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmCz">#zgbmCz#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flagOld">#flagOld#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">#extra6#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">#extra7#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">#extra8#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">#extra9#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">#extra10#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">#extra11#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">#extra12#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">#extra13#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">#extra14#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">#extra15#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra16">#extra16#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra17">#extra17#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra18">#extra18#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra19">#extra19#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptPath">#firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_BASEINFO
		WHERE 
		    APPLY_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_BASEINFO
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM=#serialNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH=#jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyDate">APPLY_DATE=#applyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME=#applyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME=#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ownership">OWNERSHIP=#ownership#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="label">LABEL=#label#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techArea">TECH_AREA=#techArea#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE=#usePropose#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS=#knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ishaveph">ISHAVEPH=#ishaveph#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE=#lxrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME=#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE=#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE=#lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="searchType">SEARCH_TYPE=#searchType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE=#fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromNo">FROM_NO=#fromNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME=#fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT=#fromContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD=#useMethod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDept">USE_DEPT=#useDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME=#useDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED=#useExpected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME=#useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE=#useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE=#reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE=#contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="resultExpect">RESULT_EXPECT=#resultExpect#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="summary">SUMMARY=#summary#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zgbmIsbmqr">ZGBM_ISBMQR=#zgbmIsbmqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zgbmNc">ZGBM_NC=#zgbmNc#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zgbmCz">ZGBM_CZ=#zgbmCz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS=#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flagOld">FLAG_OLD=#flagOld#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6=#extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">EXTRA7=#extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">EXTRA8=#extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">EXTRA9=#extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">EXTRA10=#extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">EXTRA11=#extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">EXTRA12=#extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">EXTRA13=#extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">EXTRA14=#extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">EXTRA15=#extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">EXTRA16=#extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">EXTRA17=#extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">EXTRA18=#extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">EXTRA19=#extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH=#firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlApplyBaseinfoResult">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_BASEINFO	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNum">SERIAL_NUM=#serialNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jsbh">JSBH=#jsbh#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyDate">APPLY_DATE=#applyDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyName">APPLY_NAME=#applyName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">FIRST_DEPT_NAME=#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ownership">OWNERSHIP=#ownership#</isNotEmpty>
			<isNotEmpty prepend=" , " property="label">LABEL=#label#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techArea">TECH_AREA=#techArea#</isNotEmpty>
			<isNotEmpty prepend=" , " property="usePropose">USE_PROPOSE=#usePropose#</isNotEmpty>
			<isNotEmpty prepend=" , " property="knowledgeClass">KNOWLEDGE_CLASS=#knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ishaveph">ISHAVEPH=#ishaveph#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrCode">LXR_CODE=#lxrCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">LXR_NAME=#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrPhone">LXR_PHONE=#lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrMobile">LXR_MOBILE=#lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" , " property="searchType">SEARCH_TYPE=#searchType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">FROM_TYPE=#fromType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromNo">FROM_NO=#fromNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">FROM_NAME=#fromName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromContent">FROM_CONTENT=#fromContent#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useMethod">USE_METHOD=#useMethod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDept">USE_DEPT=#useDept#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDeptName">USE_DEPT_NAME=#useDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpected">USE_EXPECTED=#useExpected#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useExpectedName">USE_EXPECTED_NAME=#useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useFirstdate">USE_FIRSTDATE=#useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="reasonNouse">REASON_NOUSE=#reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentNouse">CONTENT_NOUSE=#contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" , " property="resultExpect">RESULT_EXPECT=#resultExpect#</isNotEmpty>
			<isNotEmpty prepend=" , " property="summary">SUMMARY=#summary#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmIsbmqr">ZGBM_ISBMQR=#zgbmIsbmqr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmNc">ZGBM_NC=#zgbmNc#</isNotEmpty>
			<isNotEmpty prepend=" , " property="zgbmCz">ZGBM_CZ=#zgbmCz#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flowStatus">FLOW_STATUS=#flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="flagOld">FLAG_OLD=#flagOld#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">EXTRA6=#extra6#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">EXTRA7=#extra7#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">EXTRA8=#extra8#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">EXTRA9=#extra9#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">EXTRA10=#extra10#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">EXTRA11=#extra11#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">EXTRA12=#extra12#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">EXTRA13=#extra13#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">EXTRA14=#extra14#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">EXTRA15=#extra15#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra16">EXTRA16=#extra16#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra17">EXTRA17=#extra17#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra18">EXTRA18=#extra18#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra19">EXTRA19=#extra19#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptPath">FIRST_DEPT_PATH=#firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			APPLY_ID =#applyId#
	</update>

</sqlMap>