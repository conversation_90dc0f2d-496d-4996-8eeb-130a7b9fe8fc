package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_申请_专家评审对象 t_kizl_apply_zjps
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlApplyZjps extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * RYXX_ID
     */
    private String ryxxId;

    /**
     * 申请主键
     * APPLY_ID
     */
    @Excel(name = "申请主键" , width = 30)
    @Size(max = 36, message = "申请主键最大为36位字符")
    private String applyId;

    /**
     * 专利主键
     * NOVELTY
     */
    @Excel(name = "专利主键" , width = 30)
    @Size(max = 1, message = "专利主键最大为1位字符")
    private String novelty;

    /**
     * 序号
     * NOVELTY_LY
     */
    @Excel(name = "序号" , width = 30)
    @Size(max = 2000, message = "序号最大为2000位字符")
    private String noveltyLy;

    /**
     * 类型 01-宝武人员 02-非宝武人员
     * CREATIVITY
     */
    @Excel(name = "类型 01-宝武人员 02-非宝武人员" , width = 30)
    @Size(max = 1, message = "类型 01-宝武人员 02-非宝武人员最大为1位字符")
    private String creativity;

    /**
     * 所属法人
     * CREATIVITY_LY
     */
    @Excel(name = "所属法人" , width = 30)
    @Size(max = 2000, message = "所属法人最大为2000位字符")
    private String creativityLy;

    /**
     * 工号
     * PRACTICALITY
     */
    @Excel(name = "工号" , width = 30)
    @Size(max = 1, message = "工号最大为1位字符")
    private String practicality;

    /**
     * 姓名
     * PRACTICALITY_LY
     */
    @Excel(name = "姓名" , width = 30)
    @Size(max = 2000, message = "姓名最大为2000位字符")
    private String practicalityLy;

    /**
     * 人员组织
     * IS_TECHNOLOGY
     */
    @Excel(name = "人员组织" , width = 30)
    @Size(max = 1, message = "人员组织最大为1位字符")
    private String isTechnology;

    /**
     * 人员组织名称
     * TECHNOLOGY_LY
     */
    @Excel(name = "人员组织名称" , width = 30)
    @Size(max = 2000, message = "人员组织名称最大为2000位字符")
    private String technologyLy;

    private String protectedName;

    /**
     * 岗位
     * TAG
     */
    @Excel(name = "岗位" , width = 30)
    @Size(max = 1, message = "岗位最大为1位字符")
    private String tag;

    /**
     * 岗位名称
     * CONCLUSION
     */
    @Excel(name = "岗位名称" , width = 30)
    @Size(max = 2000, message = "岗位名称最大为2000位字符")
    private String conclusion;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
