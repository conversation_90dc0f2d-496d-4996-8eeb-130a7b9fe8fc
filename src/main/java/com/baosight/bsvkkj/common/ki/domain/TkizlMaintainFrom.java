package com.baosight.bsvkkj.common.ki.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 境内专利_维护_来源信息对象 t_kizl_maintain_from
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@Setter
@ToString
public class TkizlMaintainFrom extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * FROM_ID
     */
    private String fromId;

    /**
     * 管理单位
     * GLDW_CODE
     */
    @Excel(name = "管理单位" , width = 30)
    @Size(max = 20, message = "管理单位最大为20位字符")
    private String gldwCode;

    /**
     * 管理单位名称
     * GLDW_NAME
     */
    @Excel(name = "管理单位名称" , width = 30)
    @Size(max = 100, message = "管理单位名称最大为100位字符")
    private String gldwName;

    /**
     * 来源类型代码
     * FROM_TYPE
     */
    @Excel(name = "来源类型代码" , width = 30)
    @Size(max = 10, message = "来源类型代码最大为10位字符")
    private String fromType;

    /**
     * 来源类型名称
     * FROM_NAME
     */
    @Excel(name = "来源类型名称" , width = 30)
    @Size(max = 30, message = "来源类型名称最大为30位字符")
    private String fromName;

    /**
     * 来源信息获取地址
     * FROM_URL
     */
    @Excel(name = "来源信息获取地址" , width = 30)
    private Integer fromUrl;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
