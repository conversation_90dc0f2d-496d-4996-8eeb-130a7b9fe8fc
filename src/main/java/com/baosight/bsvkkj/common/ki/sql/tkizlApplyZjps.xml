<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplyZjps">
	<typeAlias alias="tkizlApplyZjpsResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlApplyZjps"/>
	<select id="load" parameterClass="string" resultClass="tkizlApplyZjpsResult">
		SELECT
			RYXX_ID as "ryxxId" , <!-- 主键 -->
			APPLY_ID as "applyId" , <!-- 申请主键 -->
			NOVELTY as "novelty" , <!-- 专利主键 -->
			NOVELTY_LY as "noveltyLy" , <!-- 序号 -->
			CREATIVITY as "creativity" , <!-- 类型 01-宝武人员 02-非宝武人员 -->
			CREATIVITY_LY as "creativityLy" , <!-- 所属法人 -->
			PRACTICALITY as "practicality" , <!-- 工号 -->
			PRACTICALITY_LY as "practicalityLy" , <!-- 姓名 -->
			IS_TECHNOLOGY as "isTechnology" , <!-- 人员组织 -->
			TECHNOLOGY_LY as "technologyLy" , <!-- 人员组织名称 -->
			PROTECTED as "protected" , <!-- ${column.columnComment} -->
			TAG as "tag" , <!-- 岗位 -->
			CONCLUSION as "conclusion" , <!-- 岗位名称 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_APPLY_ZJPS
		WHERE
			RYXX_ID = #ryxxId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlApplyZjpsResult">
		SELECT
			RYXX_ID as "ryxxId" ,
			APPLY_ID as "applyId" ,
			NOVELTY as "novelty" ,
			NOVELTY_LY as "noveltyLy" ,
			CREATIVITY as "creativity" ,
			CREATIVITY_LY as "creativityLy" ,
			PRACTICALITY as "practicality" ,
			PRACTICALITY_LY as "practicalityLy" ,
			IS_TECHNOLOGY as "isTechnology" ,
			TECHNOLOGY_LY as "technologyLy" ,
			PROTECTED as "protected" ,
			TAG as "tag" ,
			CONCLUSION as "conclusion" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_APPLY_ZJPS
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID =  #ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="novelty">NOVELTY =  #novelty#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="noveltyLy">NOVELTY_LY =  #noveltyLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="creativity">CREATIVITY =  #creativity#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="creativityLy">CREATIVITY_LY =  #creativityLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="practicality">PRACTICALITY =  #practicality#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="practicalityLy">PRACTICALITY_LY =  #practicalityLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isTechnology">IS_TECHNOLOGY =  #isTechnology#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyLy">TECHNOLOGY_LY =  #technologyLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="protected">PROTECTED =  #protected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tag">TAG =  #tag#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="conclusion">CONCLUSION =  #conclusion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlApplyZjpsResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_ZJPS ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="ryxxId">RYXX_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="novelty">NOVELTY</isNotEmpty>
			<isNotEmpty prepend=" , " property="noveltyLy">NOVELTY_LY</isNotEmpty>
			<isNotEmpty prepend=" , " property="creativity">CREATIVITY</isNotEmpty>
			<isNotEmpty prepend=" , " property="creativityLy">CREATIVITY_LY</isNotEmpty>
			<isNotEmpty prepend=" , " property="practicality">PRACTICALITY</isNotEmpty>
			<isNotEmpty prepend=" , " property="practicalityLy">PRACTICALITY_LY</isNotEmpty>
			<isNotEmpty prepend=" , " property="isTechnology">IS_TECHNOLOGY</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyLy">TECHNOLOGY_LY</isNotEmpty>
			<isNotEmpty prepend=" , " property="protected">PROTECTED</isNotEmpty>
			<isNotEmpty prepend=" , " property="tag">TAG</isNotEmpty>
			<isNotEmpty prepend=" , " property="conclusion">CONCLUSION</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="ryxxId">#ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="novelty">#novelty#</isNotEmpty>
			<isNotEmpty prepend=" , " property="noveltyLy">#noveltyLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="creativity">#creativity#</isNotEmpty>
			<isNotEmpty prepend=" , " property="creativityLy">#creativityLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="practicality">#practicality#</isNotEmpty>
			<isNotEmpty prepend=" , " property="practicalityLy">#practicalityLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isTechnology">#isTechnology#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyLy">#technologyLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="protected">#protected#</isNotEmpty>
			<isNotEmpty prepend=" , " property="tag">#tag#</isNotEmpty>
			<isNotEmpty prepend=" , " property="conclusion">#conclusion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_ZJPS
		WHERE 
		    RYXX_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_ZJPS
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID=#ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="novelty">NOVELTY=#novelty#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="noveltyLy">NOVELTY_LY=#noveltyLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="creativity">CREATIVITY=#creativity#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="creativityLy">CREATIVITY_LY=#creativityLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="practicality">PRACTICALITY=#practicality#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="practicalityLy">PRACTICALITY_LY=#practicalityLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isTechnology">IS_TECHNOLOGY=#isTechnology#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyLy">TECHNOLOGY_LY=#technologyLy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="protected">PROTECTED=#protected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tag">TAG=#tag#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="conclusion">CONCLUSION=#conclusion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlApplyZjpsResult">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_ZJPS	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="ryxxId">RYXX_ID=#ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyId">APPLY_ID=#applyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="novelty">NOVELTY=#novelty#</isNotEmpty>
			<isNotEmpty prepend=" , " property="noveltyLy">NOVELTY_LY=#noveltyLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="creativity">CREATIVITY=#creativity#</isNotEmpty>
			<isNotEmpty prepend=" , " property="creativityLy">CREATIVITY_LY=#creativityLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="practicality">PRACTICALITY=#practicality#</isNotEmpty>
			<isNotEmpty prepend=" , " property="practicalityLy">PRACTICALITY_LY=#practicalityLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isTechnology">IS_TECHNOLOGY=#isTechnology#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyLy">TECHNOLOGY_LY=#technologyLy#</isNotEmpty>
			<isNotEmpty prepend=" , " property="protected">PROTECTED=#protected#</isNotEmpty>
			<isNotEmpty prepend=" , " property="tag">TAG=#tag#</isNotEmpty>
			<isNotEmpty prepend=" , " property="conclusion">CONCLUSION=#conclusion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			RYXX_ID =#ryxxId#
	</update>

</sqlMap>