<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainFrom">
	<typeAlias alias="tkizlMaintainFromResult" type="com.baosight.bsvkkj.common.ki.domain.TkizlMaintainFrom"/>
	<select id="load" parameterClass="string" resultClass="tkizlMaintainFromResult">
		SELECT
			FROM_ID as "fromId" , <!-- 主键 -->
			GLDW_CODE as "gldwCode" , <!-- 管理单位 -->
			GLDW_NAME as "gldwName" , <!-- 管理单位名称 -->
			FROM_TYPE as "fromType" , <!-- 来源类型代码 -->
			FROM_NAME as "fromName" , <!-- 来源类型名称 -->
			FROM_URL as "fromUrl" , <!-- 来源信息获取地址 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
			CREATE_DATE as "createDate" , <!-- 创建时间 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
			UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
			DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
			RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_FROM
		WHERE
			FROM_ID = #fromId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkizlMaintainFromResult">
		SELECT
			FROM_ID as "fromId" ,
			GLDW_CODE as "gldwCode" ,
			GLDW_NAME as "gldwName" ,
			FROM_TYPE as "fromType" ,
			FROM_NAME as "fromName" ,
			FROM_URL as "fromUrl" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			DEL_STATUS as "delStatus" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			CREATE_DATE as "createDate" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			DELETE_DATE as "deleteDate" ,
			RECORD_VERSION as "recordVersion" 		
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_FROM
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="fromId">FROM_ID =  #fromId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromUrl">FROM_URL =  #fromUrl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tkizlMaintainFromResult">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_FROM ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="fromId">FROM_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">FROM_TYPE</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">FROM_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromUrl">FROM_URL</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="fromId">#fromId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">#fromType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">#fromName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromUrl">#fromUrl#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_FROM
		WHERE 
		    FROM_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_FROM
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="fromId">FROM_ID=#fromId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE=#fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME=#fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromUrl">FROM_URL=#fromUrl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkizlMaintainFromResult">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_FROM	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="fromId">FROM_ID=#fromId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gldwName">GLDW_NAME=#gldwName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromType">FROM_TYPE=#fromType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromName">FROM_NAME=#fromName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fromUrl">FROM_URL=#fromUrl#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
		</dynamic>
		WHERE
			FROM_ID =#fromId#
	</update>

</sqlMap>