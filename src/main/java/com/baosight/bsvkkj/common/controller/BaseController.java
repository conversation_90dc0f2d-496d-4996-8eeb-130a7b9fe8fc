package com.baosight.bsvkkj.common.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.util.StringUtils;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.thymeleaf.context.WebContext;
import org.thymeleaf.spring5.SpringTemplateEngine;
import org.thymeleaf.spring5.expression.ThymeleafEvaluationContext;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyEditorSupport;
import java.util.*;

/**
 * @ClassName: BaseController
 * @Description:
 * @author: z
 * @Company: xy
 * @date: 2020年9月7日 上午9:54:27
 * @param:
 */
public class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(BaseController.class);

    @Autowired
    protected HttpServletRequest request;
    @Autowired
    protected HttpServletResponse response;
    @Autowired
    protected ServletContext servletContext;
    @Autowired
    protected ApplicationContext applicationContext;
    @Autowired
    protected SpringTemplateEngine templateEngine;

    /**
     * 获得所有请求参数 不生效
     *
     * @return
     */
    public static Map<String, String> paramMap() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ra.getRequest();
        return ServletUtil.getParamMap(request);
    }

    /**
     * 默认 i
     *
     * @param map
     * @return
     */
    public static EiInfo getEiInfo(Map<String, Object> map) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.setBlock(getEiBlock(map));
        return eiInfo;
    }

    /**
     * @param eiBlockId
     * @param map
     * @return
     */
    public static EiInfo getEiInfo(String eiBlockId, Map<String, Object> map) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.setBlock(getEiBlock(eiBlockId, map));
        return eiInfo;
    }

    public static EiInfo getEiInfo(String eiBlockId, Object object) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.setBlock(getEiBlock(eiBlockId, object));
        return eiInfo;
    }

    /**
     * 塞入EiBlock 默认塞入i
     *
     * @return
     */
    public static EiBlock getEiBlock(Map<String, Object> map) {
        return getEiBlock("i", map);
    }

    /**
     * 获取参数
     *
     * @param blockId
     * @return
     */
    public static EiBlock getEiBlock(String blockId, Map<String, Object> map) {
        if (StrUtil.isBlank(blockId)) {
            blockId = "i";
        }

        EiBlock eiBlock = new EiBlock(blockId);
        eiBlock.addRow(map);
        return eiBlock;
    }

    /**
     * 获取参数
     *
     * @param blockId
     * @return
     */
    public static EiBlock getEiBlock(String blockId, Object object) {
        if (StrUtil.isBlank(blockId)) {
            blockId = "i";
        }

        EiBlock eiBlock = new EiBlock(blockId);
        eiBlock.addRow(object);
        return eiBlock;
    }

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                if (StrUtil.isNotBlank(text)) {
                    setValue(DateUtil.parse(text));
                }
            }
        });
    }

    /***
     * 返回分页
     * @param  eiInfo
     * @return
     */
    protected TableDataInfo getDataTable(EiInfo eiInfo) {
        EiBlock block = eiInfo.getBlock("result");
        if (block!=null) {
            Object pageData = block.get("pageData");
            if (pageData==null) {
                return new TableDataInfo();
            }
            if (pageData instanceof HashMap) {
                return BeanUtil.toBean(pageData, TableDataInfo.class);
            }
            return (TableDataInfo) block.get("pageData");
        } else {
            return new TableDataInfo(new ArrayList<>(), 0);
        }
    }

    /***
     * 返回eplat通用分页
     * @param  eiInfo
     * @return
     */
    protected TableDataInfo getDataTableByPlat(EiInfo eiInfo) {
        TableDataInfo rspData = new TableDataInfo();
        if (eiInfo.getStatus() < 0) {
            rspData.setCode(1);
            rspData.setMsg(eiInfo.getMsg());
        } else {
            EiBlock block = eiInfo.getBlock("result");
            if (block == null) {
                rspData.setCode(1);
                rspData.setMsg(eiInfo.getMsg());
            } else {
                rspData.setCode(0);
                rspData.setRows(block.getRows());
                if (null != block.getAttr().get("count")) {
                    rspData.setTotal((int) block.getAttr().get("count"));
                }
            }
        }
        return rspData;
    }

    /***
     * 返回eplat通用分页
     * @param  eiInfo
     * @return
     */
    protected EiInfo toPlatPageEiInfo(EiInfo eiInfo, Map map) {
        if (null == eiInfo.getBlock("result")) {
            eiInfo.addBlock("result");
        }
        EiBlock reslult = eiInfo.getBlock("result");
        reslult.set("showCount", "true");//显示总记录
        if (null == map) {
            reslult.set("offset", 0);
            reslult.set("limit", 10);
            return eiInfo;
        }
        Integer pageNum = (Integer) map.get("pageNum");
        Integer pageSize = (Integer) map.get("pageSize");
        if (null != pageNum && null != pageSize) {
            reslult.set("offset", (pageNum - 1) * pageSize);
            reslult.set("limit", pageSize);
        } else {
            reslult.set("offset", 0);
            reslult.set("limit", 10);
        }
        String displayOrder = (String) map.get("displayOrder");
        if (StringUtils.isNotEmpty(displayOrder)) {
            reslult.set("orderBy", displayOrder);
        }
        return eiInfo;
    }

    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(list.size());
        return rspData;
    }


    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StrUtil.format("redirect:{}", url);
    }





    public String render(String tmplPath, Map<String, Object> variables) {
        WebContext context = new WebContext(request, response, servletContext);
        context.setVariable(ThymeleafEvaluationContext.THYMELEAF_EVALUATION_CONTEXT_CONTEXT_VARIABLE_NAME,
                new ThymeleafEvaluationContext(applicationContext, null));
        context.setVariables(variables);

        String html = templateEngine.process(tmplPath, context);
        return html;
    }

}
