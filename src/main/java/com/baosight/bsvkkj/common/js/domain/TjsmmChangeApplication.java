package com.baosight.bsvkkj.common.js.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 变更申请对象 t_jsmm_change_application
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Getter
@Setter
@ToString
public class TjsmmChangeApplication extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 变更申请编号
     * CHANGE_ID
     */
    @Excel(name = "变更申请编号" , width = 30)
    private String changeId;

    /**
     * 技术秘密编号
     * TECHNOLOGY_ID
     */
    @Excel(name = "技术秘密编号" , width = 30)
    @Size(max = 36, message = "技术秘密编号最大为36位字符")
    private String technologyId;

    /**
     * 变更后保密期限
     * CHANGED_CONFIDENTIALITY_PERIOD
     */
    @Excel(name = "变更后保密期限" , width = 30)
    @Size(max = 36, message = "变更后保密期限最大为36位字符")
    private String changedConfidentialityPeriod;

    /**
     * 变更原因
     * CHANGE_REASON
     */
    @Excel(name = "变更原因" , width = 30)
    @Size(max = 1000, message = "变更原因最大为1000位字符")
    private String changeReason;

    /**
     * 状态
     * STATUS
     */
    @Excel(name = "状态" , width = 30)
    @Size(max = 36, message = "状态最大为36位字符")
    private String status;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
