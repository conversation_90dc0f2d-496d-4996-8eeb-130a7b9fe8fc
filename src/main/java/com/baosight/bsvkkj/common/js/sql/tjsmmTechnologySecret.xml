<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tjsmmTechnologySecret">
	<typeAlias alias="tjsmmTechnologySecretResult" type="com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret"/>
	<select id="load" parameterClass="string" resultClass="tjsmmTechnologySecretResult">
		SELECT
			TECHNOLOGY_ID as "technologyId" , <!-- 技术秘密ID -->
			TECHNOLOGY_NAME as "technologyName" , <!-- 技术秘密名称 -->
			SECRET_LEVEL as "secretLevel" , <!-- 类型 -->
			CONFIRM_NUM as "confirmNum" , <!-- 认定号 -->
			CONFIRM_DATE as "confirmDate" , <!-- 认定日期 -->
			DENY_DATE as "denyDate" , <!-- 否定日期 -->
			CONTACT_PERSON_CODE as "contactPersonCode" , <!-- 联系人工号 -->
			CONTACT_PERSON_NAME as "contactPersonName" , <!-- 联系人姓名 -->
			CONTACT_PERSON_EMAIL as "contactPersonEmail" , <!-- 联系人邮箱 -->
			CONTACT_PERSON_PHONE as "contactPersonPhone" , <!-- 联系人手机 -->
			CONTACT_PERSON_TEL as "contactPersonTel" , <!-- 联系人电话 -->
			FIRST_DEPT_CODE as "firstDeptCode" , <!-- 第一申报单位 -->
			FIRST_DEPT_NAME as "firstDeptName" , <!-- 第一申报单位名称 -->
			OWNERSHIP_NUM as "ownershipNum" , <!-- 权属 -->
			AUTH_DATE as "authDate" , <!-- 提出日期 -->
			AUTH_SOURCE as "authSource" , <!-- 来源 -->
			SOURCE_CODE as "sourceCode" , <!-- 来源编号 -->
			SOURCE_NAME as "sourceName" , <!-- 来源名称 -->
			OTHER_SOURCE as "otherSource" , <!-- 其他来源情况说明 -->
			APPLICATION_WAY as "applicationWay" , <!-- 应用方式 -->
			APPLICATION_DEPT_CODE as "applicationDeptCode" , <!-- 应用部门编号 -->
			APPLICATION_DEPT_NAME as "applicationDeptName" , <!-- 应用部门名称 -->
			USE_DATE as "useDate" , <!-- 初始应用日 -->
			UNUSED_REASON as "unusedReason" , <!-- 未应用原因 -->
			TECH_LABEL as "techLabel" , <!-- 标签 -->
			TECHNOLOGY_FIELD_ID as "technologyFieldId" , <!-- 技术领域 -->
			EXPECTED_RESULT as "expectedResult" , <!-- 预计效果 -->
			CONFIDENTIALITY_PERIOD as "confidentialityPeriod" , <!-- 保密期限 -->
			ABSTRACT_CONTENT as "abstractContent" , <!-- 摘要 -->
			STATUS as "status" , <!-- 状态 -->
			DEPT_ADMIN_CODE as "deptAdminCode" , <!-- 部门管理员工号 -->
			DEPT_ADMIN_NAME as "deptAdminName" , <!-- 部门管理员姓名 -->
			ADMIN_REVIEW_RESULT as "adminReviewResult" , <!-- 部门管理员评审结果 -->
			REVIEW_OPINION as "reviewOpinion" , <!-- 部门管理员评审意见 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			CREATE_DATE as "createDate" , <!-- 记录创建日期 -->
			UPDATE_DATE as "updateDate" , <!-- 记录修改日期 -->
			DELETE_DATE as "deleteDate" , <!-- 记录删除日期 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 记录创建人 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 记录修改人 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 记录删除人 -->
			RECORD_VERSION as "recordVersion" , <!-- 记录版本号 -->
			DEL_STATUS as "delStatus" , <!-- 删除状态 -->
			TECHNOLOGY_FIELD_NAME as "technologyFieldName"  <!-- 技术领域名称 -->
		FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET
		WHERE
			TECHNOLOGY_ID = #technologyId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tjsmmTechnologySecretResult">
		SELECT
			TECHNOLOGY_ID as "technologyId" ,
			TECHNOLOGY_NAME as "technologyName" ,
			SECRET_LEVEL as "secretLevel" ,
			CONFIRM_NUM as "confirmNum" ,
			CONFIRM_DATE as "confirmDate" ,
			DENY_DATE as "denyDate" ,
			CONTACT_PERSON_CODE as "contactPersonCode" ,
			CONTACT_PERSON_NAME as "contactPersonName" ,
			CONTACT_PERSON_EMAIL as "contactPersonEmail" ,
			CONTACT_PERSON_PHONE as "contactPersonPhone" ,
			CONTACT_PERSON_TEL as "contactPersonTel" ,
			FIRST_DEPT_CODE as "firstDeptCode" ,
			FIRST_DEPT_NAME as "firstDeptName" ,
			OWNERSHIP_NUM as "ownershipNum" ,
			AUTH_DATE as "authDate" ,
			AUTH_SOURCE as "authSource" ,
			SOURCE_CODE as "sourceCode" ,
			SOURCE_NAME as "sourceName" ,
			OTHER_SOURCE as "otherSource" ,
			APPLICATION_WAY as "applicationWay" ,
			APPLICATION_DEPT_CODE as "applicationDeptCode" ,
			APPLICATION_DEPT_NAME as "applicationDeptName" ,
			USE_DATE as "useDate" ,
			UNUSED_REASON as "unusedReason" ,
			TECH_LABEL as "techLabel" ,
			TECHNOLOGY_FIELD_ID as "technologyFieldId" ,
			EXPECTED_RESULT as "expectedResult" ,
			CONFIDENTIALITY_PERIOD as "confidentialityPeriod" ,
			ABSTRACT_CONTENT as "abstractContent" ,
			STATUS as "status" ,
			DEPT_ADMIN_CODE as "deptAdminCode" ,
			DEPT_ADMIN_NAME as "deptAdminName" ,
			ADMIN_REVIEW_RESULT as "adminReviewResult" ,
			REVIEW_OPINION as "reviewOpinion" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			CREATE_DATE as "createDate" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_DATE as "deleteDate" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			RECORD_VERSION as "recordVersion" ,
			DEL_STATUS as "delStatus" ,
			TECHNOLOGY_FIELD_NAME as "technologyFieldName" 		
		FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID =  #technologyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyName">TECHNOLOGY_NAME =  #technologyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="secretLevel">SECRET_LEVEL =  #secretLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="confirmNum">CONFIRM_NUM =  #confirmNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="confirmDate">CONFIRM_DATE =  #confirmDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="denyDate">DENY_DATE =  #denyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonCode">CONTACT_PERSON_CODE =  #contactPersonCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonName">CONTACT_PERSON_NAME =  #contactPersonName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonEmail">CONTACT_PERSON_EMAIL =  #contactPersonEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonPhone">CONTACT_PERSON_PHONE =  #contactPersonPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonTel">CONTACT_PERSON_TEL =  #contactPersonTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ownershipNum">OWNERSHIP_NUM =  #ownershipNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">AUTH_DATE =  #authDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authSource">AUTH_SOURCE =  #authSource#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE =  #sourceCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceName">SOURCE_NAME =  #sourceName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="otherSource">OTHER_SOURCE =  #otherSource#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applicationWay">APPLICATION_WAY =  #applicationWay#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applicationDeptCode">APPLICATION_DEPT_CODE =  #applicationDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applicationDeptName">APPLICATION_DEPT_NAME =  #applicationDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDate">USE_DATE =  #useDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="unusedReason">UNUSED_REASON =  #unusedReason#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techLabel">TECH_LABEL =  #techLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyFieldId">TECHNOLOGY_FIELD_ID =  #technologyFieldId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="expectedResult">EXPECTED_RESULT =  #expectedResult#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="confidentialityPeriod">CONFIDENTIALITY_PERIOD =  #confidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="abstractContent">ABSTRACT_CONTENT =  #abstractContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="status">STATUS =  #status#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptAdminCode">DEPT_ADMIN_CODE =  #deptAdminCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptAdminName">DEPT_ADMIN_NAME =  #deptAdminName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="adminReviewResult">ADMIN_REVIEW_RESULT =  #adminReviewResult#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reviewOpinion">REVIEW_OPINION =  #reviewOpinion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyFieldName">TECHNOLOGY_FIELD_NAME =  #technologyFieldName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tjsmmTechnologySecretResult">
		INSERT INTO ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="technologyId">TECHNOLOGY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyName">TECHNOLOGY_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="secretLevel">SECRET_LEVEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="confirmNum">CONFIRM_NUM</isNotEmpty>
			<isNotEmpty prepend=" , " property="confirmDate">CONFIRM_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="denyDate">DENY_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonCode">CONTACT_PERSON_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonName">CONTACT_PERSON_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonEmail">CONTACT_PERSON_EMAIL</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonPhone">CONTACT_PERSON_PHONE</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonTel">CONTACT_PERSON_TEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">FIRST_DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">FIRST_DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="ownershipNum">OWNERSHIP_NUM</isNotEmpty>
			<isNotEmpty prepend=" , " property="authDate">AUTH_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="authSource">AUTH_SOURCE</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceCode">SOURCE_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceName">SOURCE_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="otherSource">OTHER_SOURCE</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationWay">APPLICATION_WAY</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationDeptCode">APPLICATION_DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationDeptName">APPLICATION_DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDate">USE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="unusedReason">UNUSED_REASON</isNotEmpty>
			<isNotEmpty prepend=" , " property="techLabel">TECH_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyFieldId">TECHNOLOGY_FIELD_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="expectedResult">EXPECTED_RESULT</isNotEmpty>
			<isNotEmpty prepend=" , " property="confidentialityPeriod">CONFIDENTIALITY_PERIOD</isNotEmpty>
			<isNotEmpty prepend=" , " property="abstractContent">ABSTRACT_CONTENT</isNotEmpty>
			<isNotEmpty prepend=" , " property="status">STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptAdminCode">DEPT_ADMIN_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptAdminName">DEPT_ADMIN_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="adminReviewResult">ADMIN_REVIEW_RESULT</isNotEmpty>
			<isNotEmpty prepend=" , " property="reviewOpinion">REVIEW_OPINION</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyFieldName">TECHNOLOGY_FIELD_NAME</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="technologyId">#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyName">#technologyName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="secretLevel">#secretLevel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="confirmNum">#confirmNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="confirmDate">#confirmDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="denyDate">#denyDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonCode">#contactPersonCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonName">#contactPersonName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonEmail">#contactPersonEmail#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonPhone">#contactPersonPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonTel">#contactPersonTel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ownershipNum">#ownershipNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="authDate">#authDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="authSource">#authSource#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceCode">#sourceCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceName">#sourceName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="otherSource">#otherSource#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationWay">#applicationWay#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationDeptCode">#applicationDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationDeptName">#applicationDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDate">#useDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="unusedReason">#unusedReason#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techLabel">#techLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyFieldId">#technologyFieldId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="expectedResult">#expectedResult#</isNotEmpty>
			<isNotEmpty prepend=" , " property="confidentialityPeriod">#confidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="abstractContent">#abstractContent#</isNotEmpty>
			<isNotEmpty prepend=" , " property="status">#status#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptAdminCode">#deptAdminCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptAdminName">#deptAdminName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="adminReviewResult">#adminReviewResult#</isNotEmpty>
			<isNotEmpty prepend=" , " property="reviewOpinion">#reviewOpinion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyFieldName">#technologyFieldName#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET
		WHERE 
		    TECHNOLOGY_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID=#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyName">TECHNOLOGY_NAME=#technologyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="secretLevel">SECRET_LEVEL=#secretLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="confirmNum">CONFIRM_NUM=#confirmNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="confirmDate">CONFIRM_DATE=#confirmDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="denyDate">DENY_DATE=#denyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonCode">CONTACT_PERSON_CODE=#contactPersonCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonName">CONTACT_PERSON_NAME=#contactPersonName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonEmail">CONTACT_PERSON_EMAIL=#contactPersonEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonPhone">CONTACT_PERSON_PHONE=#contactPersonPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contactPersonTel">CONTACT_PERSON_TEL=#contactPersonTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME=#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ownershipNum">OWNERSHIP_NUM=#ownershipNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">AUTH_DATE=#authDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authSource">AUTH_SOURCE=#authSource#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE=#sourceCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceName">SOURCE_NAME=#sourceName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="otherSource">OTHER_SOURCE=#otherSource#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applicationWay">APPLICATION_WAY=#applicationWay#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applicationDeptCode">APPLICATION_DEPT_CODE=#applicationDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applicationDeptName">APPLICATION_DEPT_NAME=#applicationDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDate">USE_DATE=#useDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="unusedReason">UNUSED_REASON=#unusedReason#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techLabel">TECH_LABEL=#techLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyFieldId">TECHNOLOGY_FIELD_ID=#technologyFieldId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="expectedResult">EXPECTED_RESULT=#expectedResult#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="confidentialityPeriod">CONFIDENTIALITY_PERIOD=#confidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="abstractContent">ABSTRACT_CONTENT=#abstractContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="status">STATUS=#status#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptAdminCode">DEPT_ADMIN_CODE=#deptAdminCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptAdminName">DEPT_ADMIN_NAME=#deptAdminName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="adminReviewResult">ADMIN_REVIEW_RESULT=#adminReviewResult#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reviewOpinion">REVIEW_OPINION=#reviewOpinion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyFieldName">TECHNOLOGY_FIELD_NAME=#technologyFieldName#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tjsmmTechnologySecretResult">
		UPDATE  ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="technologyId">TECHNOLOGY_ID=#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyName">TECHNOLOGY_NAME=#technologyName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="secretLevel">SECRET_LEVEL=#secretLevel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="confirmNum">CONFIRM_NUM=#confirmNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="confirmDate">CONFIRM_DATE=#confirmDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="denyDate">DENY_DATE=#denyDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonCode">CONTACT_PERSON_CODE=#contactPersonCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonName">CONTACT_PERSON_NAME=#contactPersonName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonEmail">CONTACT_PERSON_EMAIL=#contactPersonEmail#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonPhone">CONTACT_PERSON_PHONE=#contactPersonPhone#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contactPersonTel">CONTACT_PERSON_TEL=#contactPersonTel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptCode">FIRST_DEPT_CODE=#firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="firstDeptName">FIRST_DEPT_NAME=#firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ownershipNum">OWNERSHIP_NUM=#ownershipNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="authDate">AUTH_DATE=#authDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="authSource">AUTH_SOURCE=#authSource#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceCode">SOURCE_CODE=#sourceCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceName">SOURCE_NAME=#sourceName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="otherSource">OTHER_SOURCE=#otherSource#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationWay">APPLICATION_WAY=#applicationWay#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationDeptCode">APPLICATION_DEPT_CODE=#applicationDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applicationDeptName">APPLICATION_DEPT_NAME=#applicationDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="useDate">USE_DATE=#useDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="unusedReason">UNUSED_REASON=#unusedReason#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techLabel">TECH_LABEL=#techLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyFieldId">TECHNOLOGY_FIELD_ID=#technologyFieldId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="expectedResult">EXPECTED_RESULT=#expectedResult#</isNotEmpty>
			<isNotEmpty prepend=" , " property="confidentialityPeriod">CONFIDENTIALITY_PERIOD=#confidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="abstractContent">ABSTRACT_CONTENT=#abstractContent#</isNotEmpty>
			<isNotEmpty prepend=" , " property="status">STATUS=#status#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptAdminCode">DEPT_ADMIN_CODE=#deptAdminCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptAdminName">DEPT_ADMIN_NAME=#deptAdminName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="adminReviewResult">ADMIN_REVIEW_RESULT=#adminReviewResult#</isNotEmpty>
			<isNotEmpty prepend=" , " property="reviewOpinion">REVIEW_OPINION=#reviewOpinion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyFieldName">TECHNOLOGY_FIELD_NAME=#technologyFieldName#</isNotEmpty>
		</dynamic>
		WHERE
			TECHNOLOGY_ID =#technologyId#
	</update>

</sqlMap>