package com.baosight.bsvkkj.common.js.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * 提出人信息对象 t_jsmm_technology_member
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Getter
@Setter
@ToString
public class TjsmmTechnologyMember extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 提出人记录ID
     * TECH_MEMBER_ID
     */
    private String techMemberId;

    /**
     * 技术秘密ID
     * TECHNOLOGY_ID
     */
    @Excel(name = "技术秘密ID" , width = 30)
    @Size(max = 36, message = "技术秘密ID最大为36位字符")
    private String technologyId;

    /**
     * 提出人类型
     * MEMBER_TYPE
     */
    @Excel(name = "提出人类型" , width = 30)
    private Integer memberType;

    /**
     * 提出人工号
     * MEMBER_CODE
     */
    @Excel(name = "提出人工号" , width = 30)
    @Size(max = 36, message = "提出人工号最大为36位字符")
    private String memberCode;

    /**
     * 提出人姓名
     * MEMBER_NAME
     */
    @Excel(name = "提出人姓名" , width = 30)
    @Size(max = 16, message = "提出人姓名最大为16位字符")
    private String memberName;

    /**
     * 提出人组织代码
     * MEMBER_DEPT_CODE
     */
    @Excel(name = "提出人组织代码" , width = 30)
    @Size(max = 36, message = "提出人组织代码最大为36位字符")
    private String memberDeptCode;

    /**
     * 提出人组织名称
     * MEMBER_DEPT_NAME
     */
    @Excel(name = "提出人组织名称" , width = 30)
    @Size(max = 100, message = "提出人组织名称最大为100位字符")
    private String memberDeptName;

    /**
     * 职称
     * POST_TITLE
     */
    @Excel(name = "职称" , width = 30)
    @Size(max = 100, message = "职称最大为100位字符")
    private String postTitle;

    /**
     * 岗位
     * POSITION
     */
    @Excel(name = "岗位" , width = 30)
    @Size(max = 100, message = "岗位最大为100位字符")
    private String position;

    /**
     * 贡献系数
     * CONTRIBUTION
     */
    @Excel(name = "贡献系数" , width = 30)
    private BigDecimal contribution;

    /**
     * 是否签过保密协议
     * SIGN_STATUS
     */
    @Excel(name = "是否签过保密协议" , width = 30)
    private Integer signStatus;

    /**
     * 备注
     * REMARK
     */
    @Excel(name = "备注" , width = 100)
    private String remark;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

}
