package com.baosight.bsvkkj.common.js.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 技术秘密对象 t_jsmm_technology_secret
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Getter
@Setter
@ToString
public class TjsmmTechnologySecret extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 技术秘密ID
     * TECHNOLOGY_ID
     */
    private String technologyId;

    /**
     * 技术秘密名称
     * TECHNOLOGY_NAME
     */
    @Excel(name = "技术秘密名称" , width = 30)
    @Size(max = 100, message = "技术秘密名称最大为100位字符")
    private String technologyName;

    /**
     * 类型
     * SECRET_LEVEL
     */
    @Excel(name = "类型" , width = 30)
    @Size(max = 16, message = "类型最大为16位字符")
    private String secretLevel;

    /**
     * 认定号
     * CONFIRM_NUM
     */
    @Excel(name = "认定号" , width = 30)
    @Size(max = 36, message = "认定号最大为36位字符")
    private String confirmNum;

    /**
     * 认定日期
     * CONFIRM_DATE
     */
    @Excel(name = "认定日期" , width = 30)
    @Size(max = 16, message = "认定日期最大为16位字符")
    private String confirmDate;

    /**
     * 否定日期
     * DENY_DATE
     */
    @Excel(name = "否定日期" , width = 30)
    @Size(max = 16, message = "否定日期最大为16位字符")
    private String denyDate;

    /**
     * 联系人工号
     * CONTACT_PERSON_CODE
     */
    @Excel(name = "联系人工号" , width = 30)
    @Size(max = 16, message = "联系人工号最大为16位字符")
    private String contactPersonCode;

    /**
     * 联系人姓名
     * CONTACT_PERSON_NAME
     */
    @Excel(name = "联系人姓名" , width = 30)
    @Size(max = 16, message = "联系人姓名最大为16位字符")
    private String contactPersonName;

    /**
     * 联系人邮箱
     * CONTACT_PERSON_EMAIL
     */
    @Excel(name = "联系人邮箱" , width = 30)
    @Size(max = 36, message = "联系人邮箱最大为36位字符")
    private String contactPersonEmail;

    /**
     * 联系人手机
     * CONTACT_PERSON_PHONE
     */
    @Excel(name = "联系人手机" , width = 30)
    @Size(max = 16, message = "联系人手机最大为16位字符")
    private String contactPersonPhone;

    /**
     * 联系人电话
     * CONTACT_PERSON_TEL
     */
    @Excel(name = "联系人电话" , width = 30)
    @Size(max = 16, message = "联系人电话最大为16位字符")
    private String contactPersonTel;

    /**
     * 第一申报单位
     * FIRST_DEPT_CODE
     */
    @Excel(name = "第一申报单位" , width = 30)
    @Size(max = 36, message = "第一申报单位最大为36位字符")
    private String firstDeptCode;

    /**
     * 第一申报单位名称
     * FIRST_DEPT_NAME
     */
    @Excel(name = "第一申报单位名称" , width = 30)
    @Size(max = 128, message = "第一申报单位名称最大为128位字符")
    private String firstDeptName;

    /**
     * 权属
     * OWNERSHIP_NUM
     */
    @Excel(name = "权属" , width = 30)
    @Size(max = 128, message = "权属最大为128位字符")
    private String ownershipNum;

    /**
     * 提出日期
     * AUTH_DATE
     */
    @Excel(name = "提出日期" , width = 30)
    @Size(max = 16, message = "提出日期最大为16位字符")
    private String authDate;

    /**
     * 来源
     * AUTH_SOURCE
     */
    @Excel(name = "来源" , width = 30)
    @Size(max = 128, message = "来源最大为128位字符")
    private String authSource;

    /**
     * 来源编号
     * SOURCE_CODE
     */
    @Excel(name = "来源编号" , width = 30)
    @Size(max = 36, message = "来源编号最大为36位字符")
    private String sourceCode;

    /**
     * 来源名称
     * SOURCE_NAME
     */
    @Excel(name = "来源名称" , width = 30)
    @Size(max = 128, message = "来源名称最大为128位字符")
    private String sourceName;

    /**
     * 其他来源情况说明
     * OTHER_SOURCE
     */
    @Excel(name = "其他来源情况说明" , width = 30)
    @Size(max = 128, message = "其他来源情况说明最大为128位字符")
    private String otherSource;

    /**
     * 应用方式
     * APPLICATION_WAY
     */
    @Excel(name = "应用方式" , width = 30)
    @Size(max = 16, message = "应用方式最大为16位字符")
    private String applicationWay;

    /**
     * 应用部门编号
     * APPLICATION_DEPT_CODE
     */
    @Excel(name = "应用部门编号" , width = 30)
    @Size(max = 36, message = "应用部门编号最大为36位字符")
    private String applicationDeptCode;

    /**
     * 应用部门名称
     * APPLICATION_DEPT_NAME
     */
    @Excel(name = "应用部门名称" , width = 30)
    @Size(max = 100, message = "应用部门名称最大为100位字符")
    private String applicationDeptName;

    /**
     * 初始应用日
     * USE_DATE
     */
    @Excel(name = "初始应用日" , width = 30)
    @Size(max = 16, message = "初始应用日最大为16位字符")
    private String useDate;

    /**
     * 未应用原因
     * UNUSED_REASON
     */
    @Excel(name = "未应用原因" , width = 30)
    @Size(max = 100, message = "未应用原因最大为100位字符")
    private String unusedReason;

    /**
     * 标签
     * TECH_LABEL
     */
    @Excel(name = "标签" , width = 30)
    @Size(max = 100, message = "标签最大为100位字符")
    private String techLabel;

    /**
     * 技术领域
     * TECHNOLOGY_FIELD_ID
     */
    @Excel(name = "技术领域" , width = 30)
    @Size(max = 256, message = "技术领域最大为256位字符")
    private String technologyFieldId;

    /**
     * 预计效果
     * EXPECTED_RESULT
     */
    @Excel(name = "预计效果" , width = 30)
    @Size(max = 200, message = "预计效果最大为200位字符")
    private String expectedResult;

    /**
     * 保密期限
     * CONFIDENTIALITY_PERIOD
     */
    @Excel(name = "保密期限" , width = 30)
    @Size(max = 36, message = "保密期限最大为36位字符")
    private String confidentialityPeriod;

    /**
     * 摘要
     * ABSTRACT_CONTENT
     */
    @Excel(name = "摘要" , width = 30)
    @Size(max = 2000, message = "摘要最大为2000位字符")
    private String abstractContent;

    /**
     * 状态
     * STATUS
     */
    @Excel(name = "状态" , width = 30)
    @Size(max = 36, message = "状态最大为36位字符")
    private String status;

    /**
     * 部门管理员工号
     * DEPT_ADMIN_CODE
     */
    @Excel(name = "部门管理员工号" , width = 30)
    @Size(max = 16, message = "部门管理员工号最大为16位字符")
    private String deptAdminCode;

    /**
     * 部门管理员姓名
     * DEPT_ADMIN_NAME
     */
    @Excel(name = "部门管理员姓名" , width = 30)
    @Size(max = 16, message = "部门管理员姓名最大为16位字符")
    private String deptAdminName;

    /**
     * 部门管理员评审结果
     * ADMIN_REVIEW_RESULT
     */
    @Excel(name = "部门管理员评审结果" , width = 30)
    @Size(max = 2, message = "部门管理员评审结果最大为2位字符")
    private String adminReviewResult;

    /**
     * 部门管理员评审意见
     * REVIEW_OPINION
     */
    @Excel(name = "部门管理员评审意见" , width = 30)
    @Size(max = 1000, message = "部门管理员评审意见最大为1000位字符")
    private String reviewOpinion;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

    /**
     * 技术领域名称
     * TECHNOLOGY_FIELD_NAME
     */
    @Excel(name = "技术领域名称" , width = 30)
    @Size(max = 100, message = "技术领域名称最大为100位字符")
    private String technologyFieldName;

}
