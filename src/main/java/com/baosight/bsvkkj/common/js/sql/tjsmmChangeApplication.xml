<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tjsmmChangeApplication">
	<typeAlias alias="tjsmmChangeApplicationResult" type="com.baosight.bsvkkj.common.js.domain.TjsmmChangeApplication"/>
	<select id="load" parameterClass="string" resultClass="tjsmmChangeApplicationResult">
		SELECT
			CHANGE_ID as "changeId" , <!-- 变更申请编号 -->
			TECHNOLOGY_ID as "technologyId" , <!-- 技术秘密编号 -->
			CHANGED_CONFIDENTIALITY_PERIOD as "changedConfidentialityPeriod" , <!-- 变更后保密期限 -->
			CHANGE_REASON as "changeReason" , <!-- 变更原因 -->
			STATUS as "status" , <!-- 状态 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			CREATE_DATE as "createDate" , <!-- 记录创建日期 -->
			UPDATE_DATE as "updateDate" , <!-- 记录修改日期 -->
			DELETE_DATE as "deleteDate" , <!-- 记录删除日期 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 记录创建人 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 记录修改人 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 记录删除人 -->
			RECORD_VERSION as "recordVersion" , <!-- 记录版本号 -->
			DEL_STATUS as "delStatus"  <!-- 删除状态 -->
		FROM ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION
		WHERE
			CHANGE_ID = #changeId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tjsmmChangeApplicationResult">
		SELECT
			CHANGE_ID as "changeId" ,
			TECHNOLOGY_ID as "technologyId" ,
			CHANGED_CONFIDENTIALITY_PERIOD as "changedConfidentialityPeriod" ,
			CHANGE_REASON as "changeReason" ,
			STATUS as "status" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			CREATE_DATE as "createDate" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_DATE as "deleteDate" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			RECORD_VERSION as "recordVersion" ,
			DEL_STATUS as "delStatus" 		
		FROM ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="changeId">CHANGE_ID =  #changeId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID =  #technologyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="changedConfidentialityPeriod">CHANGED_CONFIDENTIALITY_PERIOD =  #changedConfidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="changeReason">CHANGE_REASON =  #changeReason#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="status">STATUS =  #status#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tjsmmChangeApplicationResult">
		INSERT INTO ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="changeId">CHANGE_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyId">TECHNOLOGY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="changedConfidentialityPeriod">CHANGED_CONFIDENTIALITY_PERIOD</isNotEmpty>
			<isNotEmpty prepend=" , " property="changeReason">CHANGE_REASON</isNotEmpty>
			<isNotEmpty prepend=" , " property="status">STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="changeId">#changeId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyId">#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="changedConfidentialityPeriod">#changedConfidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="changeReason">#changeReason#</isNotEmpty>
			<isNotEmpty prepend=" , " property="status">#status#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION
		WHERE 
		    CHANGE_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="changeId">CHANGE_ID=#changeId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID=#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="changedConfidentialityPeriod">CHANGED_CONFIDENTIALITY_PERIOD=#changedConfidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="changeReason">CHANGE_REASON=#changeReason#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="status">STATUS=#status#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tjsmmChangeApplicationResult">
		UPDATE  ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="changeId">CHANGE_ID=#changeId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyId">TECHNOLOGY_ID=#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="changedConfidentialityPeriod">CHANGED_CONFIDENTIALITY_PERIOD=#changedConfidentialityPeriod#</isNotEmpty>
			<isNotEmpty prepend=" , " property="changeReason">CHANGE_REASON=#changeReason#</isNotEmpty>
			<isNotEmpty prepend=" , " property="status">STATUS=#status#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
		</dynamic>
		WHERE
			CHANGE_ID =#changeId#
	</update>

</sqlMap>