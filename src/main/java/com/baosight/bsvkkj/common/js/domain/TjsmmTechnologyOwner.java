package com.baosight.bsvkkj.common.js.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bsvkkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;


/**
 * 权利人单位对象 t_jsmm_technology_owner
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Getter
@Setter
@ToString
public class TjsmmTechnologyOwner extends AbstractDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 权利人单位ID
     * TECH_OWNER_ID
     */
    private String techOwnerId;

    /**
     * 技术秘密ID
     * TECHNOLOGY_ID
     */
    @Excel(name = "技术秘密ID" , width = 30)
    @Size(max = 36, message = "技术秘密ID最大为36位字符")
    private String technologyId;

    /**
     * 权利人单位编码
     * TECH_OWNER_CODE
     */
    @Excel(name = "权利人单位编码" , width = 30)
    @Size(max = 36, message = "权利人单位编码最大为36位字符")
    private String techOwnerCode;

    /**
     * 权利人单位名称
     * TECH_OWNER_NAME
     */
    @Excel(name = "权利人单位名称" , width = 30)
    @Size(max = 200, message = "权利人单位名称最大为200位字符")
    private String techOwnerName;

    /**
     * 扩展字段1
     * EXTRA1
     */
    @Excel(name = "扩展字段1" , width = 30)
    @Size(max = 30, message = "扩展字段1最大为30位字符")
    private String extra1;

    /**
     * 扩展字段2
     * EXTRA2
     */
    @Excel(name = "扩展字段2" , width = 30)
    @Size(max = 30, message = "扩展字段2最大为30位字符")
    private String extra2;

    /**
     * 扩展字段3
     * EXTRA3
     */
    @Excel(name = "扩展字段3" , width = 30)
    @Size(max = 30, message = "扩展字段3最大为30位字符")
    private String extra3;

    /**
     * 扩展字段4
     * EXTRA4
     */
    @Excel(name = "扩展字段4" , width = 30)
    @Size(max = 30, message = "扩展字段4最大为30位字符")
    private String extra4;

    /**
     * 扩展字段5
     * EXTRA5
     */
    @Excel(name = "扩展字段5" , width = 30)
    @Size(max = 30, message = "扩展字段5最大为30位字符")
    private String extra5;

    /**
     * 权利人单位类型
     * TECH_OWNER_TYPE
     */
    @Excel(name = "权利人单位类型" , width = 30)
    private Integer techOwnerType;

}
