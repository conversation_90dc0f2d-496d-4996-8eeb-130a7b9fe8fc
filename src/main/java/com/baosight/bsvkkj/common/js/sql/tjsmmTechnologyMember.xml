<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tjsmmTechnologyMember">
	<typeAlias alias="tjsmmTechnologyMemberResult" type="com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyMember"/>
	<select id="load" parameterClass="string" resultClass="tjsmmTechnologyMemberResult">
		SELECT
			TECH_MEMBER_ID as "techMemberId" , <!-- 提出人记录ID -->
			TECHNOLOGY_ID as "technologyId" , <!-- 技术秘密ID -->
			MEMBER_TYPE as "memberType" , <!-- 提出人类型 -->
			MEMBER_CODE as "memberCode" , <!-- 提出人工号 -->
			MEMBER_NAME as "memberName" , <!-- 提出人姓名 -->
			MEMBER_DEPT_CODE as "memberDeptCode" , <!-- 提出人组织代码 -->
			MEMBER_DEPT_NAME as "memberDeptName" , <!-- 提出人组织名称 -->
			POST_TITLE as "postTitle" , <!-- 职称 -->
			POSITION as "position" , <!-- 岗位 -->
			CONTRIBUTION as "contribution" , <!-- 贡献系数 -->
			SIGN_STATUS as "signStatus" , <!-- 是否签过保密协议 -->
			REMARK as "remark" , <!-- 备注 -->
			EXTRA1 as "extra1" , <!-- 扩展字段1 -->
			EXTRA2 as "extra2" , <!-- 扩展字段2 -->
			EXTRA3 as "extra3" , <!-- 扩展字段3 -->
			EXTRA4 as "extra4" , <!-- 扩展字段4 -->
			EXTRA5 as "extra5" , <!-- 扩展字段5 -->
			CREATE_DATE as "createDate" , <!-- 记录创建日期 -->
			UPDATE_DATE as "updateDate" , <!-- 记录修改日期 -->
			DELETE_DATE as "deleteDate" , <!-- 记录删除日期 -->
			CREATE_USER_LABEL as "createUserLabel" , <!-- 记录创建人 -->
			UPDATE_USER_LABEL as "updateUserLabel" , <!-- 记录修改人 -->
			DELETE_USER_LABEL as "deleteUserLabel" , <!-- 记录删除人 -->
			RECORD_VERSION as "recordVersion" , <!-- 记录版本号 -->
			DEL_STATUS as "delStatus"  <!-- 删除状态 -->
		FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_MEMBER
		WHERE
			TECH_MEMBER_ID = #techMemberId#
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tjsmmTechnologyMemberResult">
		SELECT
			TECH_MEMBER_ID as "techMemberId" ,
			TECHNOLOGY_ID as "technologyId" ,
			MEMBER_TYPE as "memberType" ,
			MEMBER_CODE as "memberCode" ,
			MEMBER_NAME as "memberName" ,
			MEMBER_DEPT_CODE as "memberDeptCode" ,
			MEMBER_DEPT_NAME as "memberDeptName" ,
			POST_TITLE as "postTitle" ,
			POSITION as "position" ,
			CONTRIBUTION as "contribution" ,
			SIGN_STATUS as "signStatus" ,
			REMARK as "remark" ,
			EXTRA1 as "extra1" ,
			EXTRA2 as "extra2" ,
			EXTRA3 as "extra3" ,
			EXTRA4 as "extra4" ,
			EXTRA5 as "extra5" ,
			CREATE_DATE as "createDate" ,
			UPDATE_DATE as "updateDate" ,
			DELETE_DATE as "deleteDate" ,
			CREATE_USER_LABEL as "createUserLabel" ,
			UPDATE_USER_LABEL as "updateUserLabel" ,
			DELETE_USER_LABEL as "deleteUserLabel" ,
			RECORD_VERSION as "recordVersion" ,
			DEL_STATUS as "delStatus" 		
		FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_MEMBER
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="techMemberId">TECH_MEMBER_ID =  #techMemberId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID =  #technologyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberType">MEMBER_TYPE =  #memberType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberCode">MEMBER_CODE =  #memberCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberName">MEMBER_NAME =  #memberName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberDeptCode">MEMBER_DEPT_CODE =  #memberDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberDeptName">MEMBER_DEPT_NAME =  #memberDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postTitle">POST_TITLE =  #postTitle#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="position">POSITION =  #position#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contribution">CONTRIBUTION =  #contribution#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signStatus">SIGN_STATUS =  #signStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="remark">REMARK =  #remark#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
		
	<insert id="insert" parameterClass="tjsmmTechnologyMemberResult">
		INSERT INTO ${zzzcSchema}.T_JSMM_TECHNOLOGY_MEMBER ( 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="techMemberId">TECH_MEMBER_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyId">TECHNOLOGY_ID</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberType">MEMBER_TYPE</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberCode">MEMBER_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberName">MEMBER_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberDeptCode">MEMBER_DEPT_CODE</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberDeptName">MEMBER_DEPT_NAME</isNotEmpty>
			<isNotEmpty prepend=" , " property="postTitle">POST_TITLE</isNotEmpty>
			<isNotEmpty prepend=" , " property="position">POSITION</isNotEmpty>
			<isNotEmpty prepend=" , " property="contribution">CONTRIBUTION</isNotEmpty>
			<isNotEmpty prepend=" , " property="signStatus">SIGN_STATUS</isNotEmpty>
			<isNotEmpty prepend=" , " property="remark">REMARK</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="techMemberId">#techMemberId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyId">#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberType">#memberType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberCode">#memberCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberName">#memberName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberDeptCode">#memberDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberDeptName">#memberDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postTitle">#postTitle#</isNotEmpty>
			<isNotEmpty prepend=" , " property="position">#position#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contribution">#contribution#</isNotEmpty>
			<isNotEmpty prepend=" , " property="signStatus">#signStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="remark">#remark#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_JSMM_TECHNOLOGY_MEMBER
		WHERE 
		    TECH_MEMBER_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_JSMM_TECHNOLOGY_MEMBER
		WHERE 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="techMemberId">TECH_MEMBER_ID=#techMemberId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID=#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberType">MEMBER_TYPE=#memberType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberCode">MEMBER_CODE=#memberCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberName">MEMBER_NAME=#memberName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberDeptCode">MEMBER_DEPT_CODE=#memberDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberDeptName">MEMBER_DEPT_NAME=#memberDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postTitle">POST_TITLE=#postTitle#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="position">POSITION=#position#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contribution">CONTRIBUTION=#contribution#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signStatus">SIGN_STATUS=#signStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="remark">REMARK=#remark#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tjsmmTechnologyMemberResult">
		UPDATE  ${zzzcSchema}.T_JSMM_TECHNOLOGY_MEMBER	
		SET 
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="techMemberId">TECH_MEMBER_ID=#techMemberId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="technologyId">TECHNOLOGY_ID=#technologyId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberType">MEMBER_TYPE=#memberType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberCode">MEMBER_CODE=#memberCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberName">MEMBER_NAME=#memberName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberDeptCode">MEMBER_DEPT_CODE=#memberDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="memberDeptName">MEMBER_DEPT_NAME=#memberDeptName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="postTitle">POST_TITLE=#postTitle#</isNotEmpty>
			<isNotEmpty prepend=" , " property="position">POSITION=#position#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contribution">CONTRIBUTION=#contribution#</isNotEmpty>
			<isNotEmpty prepend=" , " property="signStatus">SIGN_STATUS=#signStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="remark">REMARK=#remark#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
		</dynamic>
		WHERE
			TECH_MEMBER_ID =#techMemberId#
	</update>

</sqlMap>