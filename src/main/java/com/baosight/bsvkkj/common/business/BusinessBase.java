package com.baosight.bsvkkj.common.business;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


public class BusinessBase {
    public Dao dao;

    public Dao getDao() {
        return this.dao;
    }

    @Autowired
    public void setDao(Dao dao) {
        this.dao = dao;
    }

    /***
     * 分页
     * @param sqlName
     * @param param
     * @return
     */
    public TableDataInfo getPage(String sqlName, Map<String, Object> param) {
        Integer pageNum = null;
        Integer pageSize = null;
        if (null != param.get("pageNum")) {
            String pageNumString = String.valueOf(param.get("pageNum"));//微服务调用时为String
            pageNum = Integer.parseInt(pageNumString);
        }
        if (null != param.get("pageSize")) {
            String pageSizeString = String.valueOf(param.get("pageSize"));//微服务调用时为String
            pageSize = Integer.parseInt(pageSizeString);
        }
        String orderByColumn = (String) param.get("orderByColumn");
        String isAsc = (String) param.get("isAsc");
        String displayOrder = null;
        if (StringUtils.isNotBlank(orderByColumn) && StringUtils.isNotBlank(isAsc)) {
            displayOrder = (String) param.get("displayOrder");
            if (StringUtils.isNotBlank(displayOrder)) {
                displayOrder = displayOrder + "  " + orderByColumn + " " + isAsc;
            } else {
                displayOrder = " " + orderByColumn + " " + isAsc;
            }
            param.put("displayOrder", displayOrder);
        }

        pageNum = pageNum != null ? pageNum : 1;
        pageSize = pageSize != null ? pageSize : 10;
        List query = dao.query(sqlName, param, (pageNum - 1) * pageSize, pageSize);
        int count = 0;
        try {
            count = dao.count(sqlName, param);
        } catch (Exception e) {
            List list = dao.query(sqlName.split("\\.")[0] + ".count", param);
            if (list != null && list.size() > 0)
                count = (Integer) list.get(0);

        }
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(query);
        tableDataInfo.setTotal(count);
        return tableDataInfo;
    }
}
