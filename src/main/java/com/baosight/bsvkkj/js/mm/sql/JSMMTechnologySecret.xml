<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="JSMMTechnologySecret">
    <select id="queryMyApplication"  parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.TECHNOLOGY_ID as "technologyId" ,
        A.TECHNOLOGY_NAME as "technologyName" ,
        A.CONFIRM_NUM as "confirmNum" ,
        A.STATUS as "status" ,
        B.LAST_TIME as "lastTime",
        B.CURRENT_OPERATOR as "currentOperator",
        B.CURRENT_ACTIVITY_NAME as "currentActivityName"
        FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET A,
        ${ggmkSchema}.T_MPWF_FLOW_INFO B
        WHERE
        A.TECHNOLOGY_ID = B.BUSINESS_ID AND B.BUSINESS_TYPE = 'JSMM'
        <isNotEmpty prepend=" AND " property="technologyId">A.TECHNOLOGY_ID = #technologyId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="contactPersonCode">A.CONTACT_PERSON_CODE = #contactPersonCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="technologyName">A.TECHNOLOGY_NAME = #technologyName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="technologyNameLike">A.TECHNOLOGY_NAME LIKE '%$technologyNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="secretLevel">A.SECRET_LEVEL = #secretLevel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="confirmNum">A.CONFIRM_NUM = #confirmNum#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <!--处理过的申请-->
    <select id="queryYB"  parameterClass="hashmap" resultClass="hashmap">
        SELECT
        B.FLOW_CODE as "processCode" ,
        B.FLOW_ID as "flowId" ,
        B.CURRENT_ACTIVITY as "currentActivity" ,
        B.CURRENT_ACTIVITY_NAME as "currentActivityName",
        B.BUSINESS_ID as "businessId" ,
        B.BUSINESS_NAME as "businessName" ,
        B.BUSINESS_TYPE as "businessType" ,
        B.CURRENT_OPERATOR as "currentOperator" ,
        B.LAST_TIME as "lastTime" ,
        B.FLOW_ID as "processInstanceId" ,
        B.FLOW_STATE as "flowState",

        A.TECHNOLOGY_ID as "technologyId" ,
        A.TECHNOLOGY_NAME as "technologyName" ,
        A.CONFIRM_NUM as "confirmNum" ,
        A.CONTACT_PERSON_CODE as "contactPersonCode",
        A.CONTACT_PERSON_NAME as "contactPersonName"

        FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET A
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO B ON A.TECHNOLOGY_ID = B.BUSINESS_ID
        WHERE
        EXISTS (SELECT C.BUSINESS_ID FROM ${ggmkSchema}.V_MPWF_YB C WHERE A.TECHNOLOGY_ID = C.BUSINESS_ID AND C.ASSIGNEE_ID = #userLabel#)
        <isNotEmpty prepend=" AND " property="businessType">B.BUSINESS_TYPE = #businessType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowCode">B.FLOW_CODE = #flowCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">B.CURRENT_ACTIVITY = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">B.BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>

        <isNotEmpty prepend=" AND " property="technologyNameLike">A.TECHNOLOGY_NAME LIKE '%$technologyNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="delStatus">A.DEL_STATUS = #delStatus#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <!--综合查询-->
    <select id="inquiry"  parameterClass="hashmap" resultClass="hashmap">
        SELECT
        TECHNOLOGY_ID as "technologyId" ,
        TECHNOLOGY_NAME as "technologyName" ,
        SECRET_LEVEL as "secretLevel" ,
        CONFIRM_NUM as "confirmNum" ,
        CONFIRM_DATE as "confirmDate" ,
        DENY_DATE as "denyDate" ,
        CONTACT_PERSON_CODE as "contactPersonCode" ,
        CONTACT_PERSON_NAME as "contactPersonName" ,
        CONTACT_PERSON_EMAIL as "contactPersonEmail" ,
        CONTACT_PERSON_PHONE as "contactPersonPhone" ,
        CONTACT_PERSON_TEL as "contactPersonTel" ,
        FIRST_DEPT_CODE as "firstDeptCode" ,
        FIRST_DEPT_NAME as "firstDeptName" ,
        OWNERSHIP_NUM as "ownershipNum" ,
        AUTH_DATE as "authDate" ,
        AUTH_SOURCE as "authSource" ,
        SOURCE_CODE as "sourceCode" ,
        SOURCE_NAME as "sourceName" ,
        OTHER_SOURCE as "otherSource" ,
        APPLICATION_WAY as "applicationWay" ,
        APPLICATION_DEPT_CODE as "applicationDeptCode" ,
        APPLICATION_DEPT_NAME as "applicationDeptName" ,
        USE_DATE as "useDate" ,
        UNUSED_REASON as "unusedReason" ,
        TECH_LABEL as "techLabel" ,
        TECHNOLOGY_FIELD_ID as "technologyFieldId" ,
        EXPECTED_RESULT as "expectedResult" ,
        CONFIDENTIALITY_PERIOD as "confidentialityPeriod" ,
        ABSTRACT_CONTENT as "abstractContent" ,
        STATUS as "status" ,
        DEPT_ADMIN_CODE as "deptAdminCode" ,
        DEPT_ADMIN_NAME as "deptAdminName" ,
        ADMIN_REVIEW_RESULT as "adminReviewResult" ,
        REVIEW_OPINION as "reviewOpinion" ,
        EXTRA1 as "extra1" ,
        EXTRA2 as "extra2" ,
        EXTRA3 as "extra3" ,
        EXTRA4 as "extra4" ,
        EXTRA5 as "extra5" ,
        CREATE_DATE as "createDate" ,
        UPDATE_DATE as "updateDate" ,
        DELETE_DATE as "deleteDate" ,
        CREATE_USER_LABEL as "createUserLabel" ,
        UPDATE_USER_LABEL as "updateUserLabel" ,
        DELETE_USER_LABEL as "deleteUserLabel" ,
        RECORD_VERSION as "recordVersion" ,
        DEL_STATUS as "delStatus" ,
        TECHNOLOGY_FIELD_NAME as "technologyFieldName"
        FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID =  #technologyId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="technologyName">TECHNOLOGY_NAME =  #technologyName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="technologyNameLike">TECHNOLOGY_NAME LIKE '%$technologyNameLike$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secretLevel">SECRET_LEVEL =  #secretLevel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="confirmNum">CONFIRM_NUM =  #confirmNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="confirmDate">CONFIRM_DATE =  #confirmDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="confirmDateFrom">CONFIRM_DATE &gt;= #confirmDateFrom#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="confirmDateTo">CONFIRM_DATE &lt;= #confirmDateTo#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="denyDate">DENY_DATE =  #denyDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="contactPersonCode">CONTACT_PERSON_CODE =  #contactPersonCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="contactPersonName">CONTACT_PERSON_NAME =  #contactPersonName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="contactPersonEmail">CONTACT_PERSON_EMAIL =  #contactPersonEmail#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="contactPersonPhone">CONTACT_PERSON_PHONE =  #contactPersonPhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="contactPersonTel">CONTACT_PERSON_TEL =  #contactPersonTel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="ownershipNum">OWNERSHIP_NUM =  #ownershipNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="authDate">AUTH_DATE =  #authDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="authSource">AUTH_SOURCE =  #authSource#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE =  #sourceCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sourceName">SOURCE_NAME =  #sourceName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="otherSource">OTHER_SOURCE =  #otherSource#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="applicationWay">APPLICATION_WAY =  #applicationWay#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="applicationDeptCode">APPLICATION_DEPT_CODE =  #applicationDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="applicationDeptName">APPLICATION_DEPT_NAME =  #applicationDeptName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="useDate">USE_DATE =  #useDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="unusedReason">UNUSED_REASON =  #unusedReason#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techLabel">TECH_LABEL =  #techLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techLabelLike">TECH_LABEL LIKE '%$techLabelLike$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="technologyFieldId">TECHNOLOGY_FIELD_ID =  #technologyFieldId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="expectedResult">EXPECTED_RESULT =  #expectedResult#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="expectedResultLike">EXPECTED_RESULT LIKE '%$expectedResultLike$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="confidentialityPeriod">CONFIDENTIALITY_PERIOD =  #confidentialityPeriod#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="abstractContent">ABSTRACT_CONTENT =  #abstractContent#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">STATUS =  #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deptAdminCode">DEPT_ADMIN_CODE =  #deptAdminCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deptAdminName">DEPT_ADMIN_NAME =  #deptAdminName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="adminReviewResult">ADMIN_REVIEW_RESULT =  #adminReviewResult#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="reviewOpinion">REVIEW_OPINION =  #reviewOpinion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="technologyFieldName">TECHNOLOGY_FIELD_NAME =  #technologyFieldName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <select id="querySameSource"  parameterClass="hashmap" resultClass="hashmap">
        SELECT
        TECHNOLOGY_ID as "technologyId" ,
        TECHNOLOGY_NAME as "technologyName" ,
        CONFIRM_NUM as "confirmNum" ,
        STATUS as "status"
        FROM ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="technologyId">TECHNOLOGY_ID != #technologyId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE = #sourceCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">STATUS = #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
            <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
        </dynamic>
    </select>
</sqlMap>