package com.baosight.bsvkkj.js.mm.business;

import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyMember;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 提出人信息
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Component
public class BusinessJSMMTechnologyMember extends BusinessBase {

    private static final String NAME_SPACE = "tjsmmTechnologyMember";

    /**
     * 按technologyId查询
     *
     * @param technologyId
     * @return
     * */
    public List<TjsmmTechnologyMember> queryByTechId(String technologyId) {
        Map<String, Object> param = new HashMap<>();
        param.put("technologyId", technologyId);
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TjsmmTechnologyMember initBean(String operator) {
        TjsmmTechnologyMember technologyMember = new TjsmmTechnologyMember();
        return technologyMember;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TjsmmTechnologyMember> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TjsmmTechnologyMember> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param techMemberId
     * @return
     */
    public TjsmmTechnologyMember query(String techMemberId) {
        if (StringUtils.isBlank(techMemberId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("techMemberId", techMemberId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TjsmmTechnologyMember) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param technologyMember
     * @return
     */
    public TjsmmTechnologyMember insert(String operator, TjsmmTechnologyMember technologyMember) {
        technologyMember.initAdd(operator);
        if (StringUtils.isEmpty(technologyMember.getTechMemberId())) {
            technologyMember.setTechMemberId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", technologyMember);
        return technologyMember;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param technologyMember
     * @return
     */
    public TjsmmTechnologyMember update(String operator, TjsmmTechnologyMember technologyMember) {
        technologyMember.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", technologyMember);
        return technologyMember;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param techMemberId
     * @return
     */
    public TjsmmTechnologyMember deleteLogin(String operator, String techMemberId) {
        TjsmmTechnologyMember technologyMember = new TjsmmTechnologyMember();
        technologyMember.setTechMemberId(techMemberId);
        technologyMember.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", technologyMember);
        return technologyMember;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param techMemberId
     * @return
     */
    public int delete(String operator, String techMemberId) {
        return dao.delete(NAME_SPACE + ".delete", techMemberId);
    }

    /**
     * 按technologyId删除
     */
    public int deleteByTechId(String technologyId){
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("technologyId", technologyId);
        return dao.delete(NAME_SPACE + ".deleteByC", hashMap);
    }

}
