package com.baosight.bsvkkj.js.mm.service;

import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMWorkFlow;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ServiceJSMMWorkFlow extends PageService {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private BusinessJSMMWorkFlow businessJSMMWorkFlow;

    public EiInfo queryModuleDbList(EiInfo inInfo) {
        try {
            String operator = inInfo.getString("operator");
            if (StringUtils.isEmpty(operator)) {
                operator = UserSession.getLoginName();
            }
            String businessType = inInfo.getCellStr(EiConstant.queryBlock, 0, "businessType");
            List<Map<String, Object>> list = businessJSMMWorkFlow.queryModuleDbList(businessType, operator);
            inInfo.set("businessType", businessType);
            inInfo.addRows(EiConstant.resultBlock, list);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getStackTrace());
            throw new PlatException(e.getMessage());
        }
        return inInfo;
    }
    public EiInfo pageDB(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("assigneeId", loginName);
            queryData.put("displayOrder", "A.UPDATE_DATE DESC");
            TableDataInfo queryPage = businessJSMMWorkFlow.queryPage(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
