package com.baosight.bsvkkj.js.mm.vo;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyMember;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TechnologyMemberVO extends TjsmmTechnologyMember {

    private static final long serialVersionUID = 1L;

    public static TechnologyMemberVO initParent(TjsmmTechnologyMember techMember){
        TechnologyMemberVO techMemberVO = new TechnologyMemberVO();
        BeanUtil.copyProperties(techMember, techMemberVO, false);
        return techMemberVO;
    }
}
