package com.baosight.bsvkkj.js.mm.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyOwner;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 权利人单位Controller
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Controller
@RequestMapping("/jsmm/technologyOwner")
public class ControllerJSMMTechnologyOwner extends BaseController{
    private String prefix = "/jsmm/technologyOwner";
    
    @GetMapping()
    public String technologyOwner(){
        return prefix + "/technologyOwnerList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询权利人单位列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map, String technologyId) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        eiInfo.set("technologyId", technologyId);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMTechnologyOwner", "page");
		return getDataTable(query);
	}

    /**
     * 新增权利人单位
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("technologyOwner", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceJSMMTechnologyOwner", "initLoad", "technologyOwner"));
        return prefix + "/technologyOwner";
    }

    /**
     * 修改权利人单位
     */
    @GetMapping("/edit/{techOwnerId}")
    public String edit(@PathVariable("techOwnerId") String techOwnerId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("techOwnerId", techOwnerId);

        modelMap.put("technologyOwner", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologyOwner", "load", "technologyOwner"));
        return prefix + "/technologyOwner";
    }

    /**
     * 保存权利人单位
     */
    @PostMapping("/save/{technologyId}")
    @ResponseBody
    public AjaxResult save(@RequestBody List<TjsmmTechnologyOwner> ownerList, @PathVariable("technologyId") String technologyId){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", technologyId);
        eiInfo.set("ownerList", ownerList);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologyOwner", "save");
    }
    
    /**
     * 删除权利人单位
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("techOwnerId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologyOwner", "remove");
	}

    /**
     * 导出权利人单位数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TjsmmTechnologyOwner> list = (List<TjsmmTechnologyOwner>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologyOwner", "query", "list");
        ExcelUtil<TjsmmTechnologyOwner> util = new ExcelUtil<>(TjsmmTechnologyOwner.class);
        String title = "数据导出表";
		util.setSheet("权利人单位");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出权利人单位模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TjsmmTechnologyOwner> listExcel = new ArrayList<>();
        ExcelUtil<TjsmmTechnologyOwner> util = new ExcelUtil<>(TjsmmTechnologyOwner.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }
}
