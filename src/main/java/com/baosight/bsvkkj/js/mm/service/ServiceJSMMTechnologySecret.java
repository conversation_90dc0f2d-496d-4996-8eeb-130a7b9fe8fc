package com.baosight.bsvkkj.js.mm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologyMember;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologyOwner;
import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.flow.FlowJSMMRD;
import com.baosight.bsvkkj.js.mm.vo.TechnologySecretVO;
import com.baosight.bsvkkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bsvkkj.utils.expert.ExperReviewUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.apache.commons.lang3.StringUtils;
import org.omg.CORBA.PUBLIC_MEMBER;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologySecret;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;

import javax.annotation.Resource;
import java.util.*;

/**
 * 技术秘密Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class ServiceJSMMTechnologySecret extends PageService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BusinessJSMMTechnologySecret businessJSMMTechnologySecret;
    @Resource
    private BusinessJSMMTechnologyOwner businessJSMMTechnologyOwner;
    @Resource
    private BusinessJSMMTechnologyMember businessJSMMTechnologyMember;
    @Resource
    private FlowJSMMRD flowValidator;

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TechnologySecretVO techSecretVO = businessJSMMTechnologySecret.initBean(loginName);
            inInfo.set("technologySecretVO", techSecretVO);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TjsmmTechnologySecret> queryList = businessJSMMTechnologySecret.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryDBDetail(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String technologyId = (String) inInfo.get("technologyId");
            String taskId = (String) inInfo.get("taskId");

            TechnologySecretVO techSecretVO = businessJSMMTechnologySecret.queryDetail(operator, technologyId, taskId);

            inInfo.set("technologySecretVO", techSecretVO);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            String operator = UserSession.getLoginName();

            TableDataInfo queryPage = businessJSMMTechnologySecret.queryPage(operator, queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo save(EiInfo inInfo) {
        try {
            TechnologySecretVO techSecretVO = (TechnologySecretVO) inInfo.get("technologySecretVO");
            TjsmmTechnologySecret bean = BeanUtil.toBean(techSecretVO, TjsmmTechnologySecret.class);
            TjsmmTechnologySecret techSecret = businessJSMMTechnologySecret.save(UserSession.getLoginName(), bean);

            //附件
            Map<String, String> attachmentS = techSecretVO.getAttachmentS();
            if (attachmentS != null) {
                for (String key : attachmentS.keySet()) {
                    if (null != (attachmentS.get(key))) {
                        SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(techSecret.getTechnologyId(), attachmentS.get(key), "JSMM", key, null, null);
                    }
                }
            }

            //暂存流程意见
            WorkFlow workFlow = techSecretVO.getWorkFlow();
            if (null != workFlow) {
                if (StringUtils.isNotBlank(workFlow.getTaskId()) && StringUtils.isNotBlank(workFlow.getComment())) {
                    SWorkFlowUtil.updateComment(workFlow.getTaskId(), workFlow.getComment());
                }
            }

            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String technologyId = (String) inInfo.get("technologyId");
            String operator = UserSession.getLoginName();
            TjsmmTechnologySecret query = businessJSMMTechnologySecret.query(technologyId);
            TechnologySecretVO techSecretVO = TechnologySecretVO.initParent(query);

            WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByBusinessIdAndProcessCode(technologyId,JSMMConstant.PROCESS_CODE_JSMMRD);
            if (workFlow == null)
                workFlow = new WorkFlow(operator, JSMMConstant.PROCESS_CODE_JSMMRD, JSMMConstant.BUSINESS_TYPE, technologyId);
            techSecretVO.setWorkFlow(workFlow);

            inInfo.set("technologySecretVO", techSecretVO);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String technologyId = (String) inInfo.get("technologyId");
            if (StrUtil.isNotBlank(technologyId)) {
                for (String id : technologyId.split(",")) {
                    businessJSMMTechnologySecret.delete(loginName, id);
                    businessJSMMTechnologyMember.deleteByTechId(technologyId);
                    businessJSMMTechnologyOwner.deleteByTechId(technologyId);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo doSubmit(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TechnologySecretVO technologySecretVO = (TechnologySecretVO) inInfo.get("technologySecretVO");

            // 保存
            save(inInfo);
            TjsmmTechnologySecret techSecret = businessJSMMTechnologySecret.query(technologySecretVO.getTechnologyId());

            String msg = "";
            WorkFlow workFlow = flowValidator.beforeSecretSubmitValidate(operator, technologySecretVO);

            //流程终止
            if (JSMMConstant.PROCESS_STATUS_TEMP_TERMINATION.equals(workFlow.getFlowState())) {
                businessJSMMTechnologySecret.terminate(operator, workFlow.getBusinessId());
                msg = SWorkFlowUtil.endProcess(operator, workFlow);
            } else {
                if (StringUtils.isBlank(workFlow.getTaskId())) {
                    //启动流程
                    workFlow.setBusinessName(techSecret.getTechnologyName());
                    workFlow.setBusinessLabel(JSMMConstant.PROCESS_CODE_JSMMRD);
                    msg = SWorkFlowUtil.startProcessAndSubmit(operator, workFlow);
                    businessJSMMTechnologySecret.active(operator, workFlow.getBusinessId());
                } else {
                    //提交流程
                    msg = SWorkFlowUtil.submit(operator, workFlow);
                }
            }

            flowValidator.afterSecretSubmitValidate(operator,technologySecretVO);

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 提交或者启动流程获取下一步相关信息
     *
     * @param inInfo
     * @return
     */
    public EiInfo getNextSubmitWF(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TechnologySecretVO techSecretVO = (TechnologySecretVO) inInfo.get("technologySecretVO");

            WorkFlow workFlow = flowValidator.beforeSecretSubmitValidate(operator, techSecretVO);
            if (StringUtils.isEmpty(workFlow.getTaskId())) {
                workFlow = SWorkFlowUtil.getNextStartAndSubmitWF(workFlow);
            } else {
                workFlow = SWorkFlowUtil.getNextSubmitWF(workFlow);
            }
            inInfo.set("workFlow", workFlow);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            logger.error(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 流程退回
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturn(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TechnologySecretVO techSecretVO = (TechnologySecretVO) inInfo.get("technologySecretVO");
            //保存业务数据
            save(inInfo);
            //流程退回
            String msg = SWorkFlowUtil.doReturn(operator, techSecretVO.getWorkFlow());

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            logger.error(e.getMessage());
        }
        return inInfo;
    }

    /**
     * 处理过的申请
     *
     * @param inInfo
     * @return
     */
    public EiInfo pageYB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            String operator = UserSession.getLoginName();
            queryData.put("userLabel", operator);
            queryData.put("businessType", JSMMConstant.BUSINESS_TYPE);
            queryData.put("delStatus", "0");
            queryData.put("displayOrder", "A.UPDATE_DATE DESC");

            TableDataInfo queryPage = businessJSMMTechnologySecret.queryPageYB(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 获取评审信息
     */
    public EiInfo getPSInfo(EiInfo inInfo) {
        try {
            String technologyId = (String) inInfo.get("technologyId");
            String moduleCode = (String) inInfo.get("moduleCode");
            TjsmmTechnologySecret technologySecret = businessJSMMTechnologySecret.query(technologyId);
            if (null != technologySecret) {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("科技秘密编号",technologySecret.getTechnologyId());
                map.put("科技秘密名称",technologySecret.getTechnologyName());
                map.put("提出人",technologySecret.getContactPersonName());
                JSONObject psInfo = ExperReviewUtil.getReviewInfoEncapsulationToJsonStr(moduleCode,map,technologyId);
                psInfo.set(technologyId + "Name", technologySecret.getTechnologyName());
                inInfo.set("psInfo", Base64.encodeUrlSafe(psInfo.toJSONString(0)));
            }

            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo getSameSourceSecret(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String sourceId = (String) inInfo.get("sourceCode");
            String technologyId = (String) inInfo.get("technologyId");
            if (!StringUtils.isEmpty(sourceId)) {
                Map<String, Object> param = new HashMap<>();
                param.put("sourceCode", sourceId);
                param.put("technologyId", technologyId);
                param.put("status", JSMMConstant.PROCESS_STATUS_ENDED);

                List<TjsmmTechnologySecret> result = businessJSMMTechnologySecret.querySameSourceSecret(param);
                inInfo.set("secretList", result);

                inInfo.setMsg("success");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * S_JS_MM_01 获取来源编号为sourceId的技术秘密
     * @param inInfo
     * @return
     */
    public EiInfo getSecretByProjectCode(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String sourceCode = (String) inInfo.get("projectCode");
            if (!StringUtils.isEmpty(sourceCode)) {
                Map<String, Object> param = new HashMap<>();
                param.put("sourceCode", sourceCode);
                param.put("status", JSMMConstant.PROCESS_STATUS_ENDED);

                List<TjsmmTechnologySecret> result = businessJSMMTechnologySecret.queryList(operator, param);
                inInfo.set("secretList", result);

                inInfo.setMsg("success");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
