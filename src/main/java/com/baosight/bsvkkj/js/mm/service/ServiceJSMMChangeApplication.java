package com.baosight.bsvkkj.js.mm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologySecret;
import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.flow.FlowJSMMBG;
import com.baosight.bsvkkj.js.mm.vo.ChangeApplicationVO;
import com.baosight.bsvkkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMChangeApplication;
import com.baosight.bsvkkj.common.js.domain.TjsmmChangeApplication;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 变更申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class ServiceJSMMChangeApplication extends PageService{
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
	private BusinessJSMMChangeApplication businessJSMMChangeApplication;
    @Resource
    private BusinessJSMMTechnologySecret businessJSMMTechnologySecret;
    @Resource
    private FlowJSMMBG flowValidator;

    /**
	*
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            ChangeApplicationVO bean = businessJSMMChangeApplication.initBean(loginName);
            inInfo.set("changeApplicationVO", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TjsmmChangeApplication> queryList = businessJSMMChangeApplication.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try {
            Map<String, Object> queryData = getQueryData(inInfo);
            String operator = UserSession.getLoginName();

            TableDataInfo queryPage = businessJSMMChangeApplication.queryPage(operator,queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
	    try {
            ChangeApplicationVO changeApplicationVO = (ChangeApplicationVO) inInfo.get("changeApplicationVO");
            TjsmmChangeApplication bean = BeanUtil.toBean(changeApplicationVO, TjsmmChangeApplication.class);
            TjsmmChangeApplication changeApplication = businessJSMMChangeApplication.save(UserSession.getLoginName(), bean);

            //附件
            Map<String, String> attachmentS = changeApplicationVO.getAttachmentS();
            if (attachmentS != null) {
                for (String key : attachmentS.keySet()) {
                    if (null!=(attachmentS.get(key))) {
                        SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(changeApplication.getChangeId(), attachmentS.get(key), "JSMM", key, null, null);
                    }
                }
            }

            //意见
            WorkFlow workFlow = changeApplicationVO.getWorkFlow();
            if (null != workFlow) {
                if (StringUtils.isNotBlank(workFlow.getTaskId()) && StringUtils.isNotBlank(workFlow.getComment())) {
                    SWorkFlowUtil.updateComment(workFlow.getTaskId(), workFlow.getComment());
                }
            }

            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try {
            String changeId = (String) inInfo.get("changeId");
            String operator = UserSession.getLoginName();
            TjsmmChangeApplication query = businessJSMMChangeApplication.query(changeId);
            TjsmmTechnologySecret techSecret = businessJSMMTechnologySecret.query(query.getTechnologyId());
            ChangeApplicationVO changeApplicationVO = ChangeApplicationVO.initParent(query);

            WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByBusinessIdAndProcessCode(changeId,JSMMConstant.PROCESS_CODE_JSMMBG);
            if (workFlow == null){
                workFlow = new WorkFlow(operator, JSMMConstant.PROCESS_CODE_JSMMBG, JSMMConstant.BUSINESS_TYPE, changeId);
            }
            changeApplicationVO.setWorkFlow(workFlow);
            if (techSecret != null){
                changeApplicationVO.setTechSecret(techSecret);
            }

            inInfo.set("changeApplicationVO", changeApplicationVO);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo detail(EiInfo inInfo) {
        try {
            String changeId = (String) inInfo.get("changeId");
            String operator = UserSession.getLoginName();
            TjsmmChangeApplication query = businessJSMMChangeApplication.query(changeId);
            TjsmmTechnologySecret techSecret = businessJSMMTechnologySecret.query(query.getTechnologyId());
            ChangeApplicationVO changeApplicationVO = ChangeApplicationVO.initParent(query);
            changeApplicationVO.setTechSecret(techSecret);

            inInfo.set("changeApplicationVO", changeApplicationVO);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

	public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String changeId = (String) inInfo.get("changeId");
	        if (StrUtil.isNotBlank(changeId)) {
	            for (String id : changeId.split(",")) {
	                businessJSMMChangeApplication.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo doSubmit(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            ChangeApplicationVO changeApplicationVO = (ChangeApplicationVO) inInfo.get("changeApplicationVO");

            // 保存
            save(inInfo);
            TjsmmChangeApplication changeApplication = businessJSMMChangeApplication.query(changeApplicationVO.getChangeId());

            String msg = "";
            WorkFlow workFlow = flowValidator.beforeChangeSubmitValidate(operator, changeApplicationVO);

            //流程终止
            if (JSMMConstant.PROCESS_STATUS_TERMINATION.equals(workFlow.getFlowState())) {
                businessJSMMChangeApplication.termination(operator, workFlow.getBusinessId());
                msg = SWorkFlowUtil.endProcess(operator, workFlow);
            } else {
                if (StringUtils.isBlank(workFlow.getTaskId())) {
                    //启动流程
                    workFlow.setBusinessName(changeApplication.getChangeId());
                    workFlow.setBusinessLabel(JSMMConstant.PROCESS_CODE_JSMMBG);
                    msg = SWorkFlowUtil.startProcessAndSubmit(operator, workFlow);
                    businessJSMMChangeApplication.active(operator, workFlow.getBusinessId());
                } else {
                    //提交流程
                    msg = SWorkFlowUtil.submit(operator, workFlow);
                }
            }

            flowValidator.afterChangeSubmitValidate(operator,changeApplicationVO);

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 提交或者启动流程获取下一步相关信息
     *
     * @param inInfo
     * @return
     */
    public EiInfo getNextSubmitWF(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            ChangeApplicationVO changeApplicationVO = (ChangeApplicationVO) inInfo.get("changeApplicationVO");

            WorkFlow workFlow =flowValidator.beforeChangeSubmitValidate(operator, changeApplicationVO);
            if (StringUtils.isEmpty(workFlow.getTaskId())) {
                workFlow = SWorkFlowUtil.getNextStartAndSubmitWF(workFlow);
            } else {
                workFlow = SWorkFlowUtil.getNextSubmitWF(workFlow);
            }
            inInfo.set("workFlow", workFlow);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            logger.error(e.getMessage());
        }
        return inInfo;
    }

    public EiInfo queryDBDetail(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String changeId = (String) inInfo.get("changeId");
            String taskId = (String) inInfo.get("taskId");

            ChangeApplicationVO changeApplicationVO = businessJSMMChangeApplication.queryDetail(operator, changeId, taskId);
            TjsmmTechnologySecret techSecret = businessJSMMTechnologySecret.query(changeApplicationVO.getTechnologyId());
            if (techSecret != null) {
                changeApplicationVO.setTechSecret(techSecret);
            }

            inInfo.set("changeApplicationVO", changeApplicationVO);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
