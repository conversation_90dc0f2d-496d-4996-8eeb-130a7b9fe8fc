package com.baosight.bsvkkj.js.mm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologyOwner;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyOwner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 权利人单位Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class ServiceJSMMTechnologyOwner extends PageService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BusinessJSMMTechnologyOwner businessJSMMTechnologyOwner;

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TjsmmTechnologyOwner bean = businessJSMMTechnologyOwner.initBean(loginName);
            inInfo.set("technologyOwner", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TjsmmTechnologyOwner> queryList = businessJSMMTechnologyOwner.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            String technologyId = (String) inInfo.get("technologyId");
            if (technologyId.isEmpty()) {
                queryData.put("technologyId", "-1");
            } else {
                queryData.put("technologyId", technologyId);
            }

            TableDataInfo queryPage = businessJSMMTechnologyOwner.queryPage(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo save(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String technologyId = (String) inInfo.get("technologyId");
            List<TjsmmTechnologyOwner> ownerList = (List<TjsmmTechnologyOwner>) inInfo.get("ownerList");
            businessJSMMTechnologyOwner.deleteByTechId(technologyId);
            for (TjsmmTechnologyOwner owner : ownerList) {
                owner.setTechOwnerId(null);
                owner.setTechnologyId(technologyId);
                businessJSMMTechnologyOwner.insert(operator, owner);
            }
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String techOwnerId = (String) inInfo.get("techOwnerId");
            TjsmmTechnologyOwner query = businessJSMMTechnologyOwner.query(techOwnerId);
            inInfo.set("technologyOwner", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String techOwnerId = (String) inInfo.get("techOwnerId");
            if (StrUtil.isNotBlank(techOwnerId)) {
                for (String id : techOwnerId.split(",")) {
                    businessJSMMTechnologyOwner.delete(loginName, id);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
