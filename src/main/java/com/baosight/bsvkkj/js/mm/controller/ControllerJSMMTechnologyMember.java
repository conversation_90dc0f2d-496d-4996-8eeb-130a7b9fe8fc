package com.baosight.bsvkkj.js.mm.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyMember;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
/**
 * 提出人信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Controller
@RequestMapping("/jsmm/technologyMember")
public class ControllerJSMMTechnologyMember extends BaseController{
    private String prefix = "/jsmm/technologyMember";
    
    @GetMapping()
    public String technologyMember(){
        return prefix + "/technologyMemberList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/" + pageNo;
    }

     /**
     * 查询提出人信息列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map, String technologyId) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        eiInfo.set("technologyId", technologyId);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMTechnologyMember", "page");
		return getDataTable(query);
	}

    /**
     * 新增提出人信息
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("technologyMember", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceJSMMTechnologyMember", "initLoad", "technologyMember"));
        return prefix + "/technologyMember";
    }

    /**
     * 修改提出人信息
     */
    @GetMapping("/edit/{techMemberId}")
    public String edit(@PathVariable("techMemberId") String techMemberId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("techMemberId", techMemberId);

        modelMap.put("technologyMember", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologyMember", "load", "technologyMember"));
        return prefix + "/technologyMember";
    }

    /**
     * 保存提出人信息
     */
    @PostMapping("/save/{technologyId}")
    @ResponseBody
    public AjaxResult save(@RequestBody List<TjsmmTechnologyMember> techMemberList, @PathVariable("technologyId") String technologyId){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", technologyId);
        eiInfo.set("technologyMemberList", techMemberList);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologyMember", "save");
    }
    
    /**
     * 删除提出人信息
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("techMemberId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologyMember", "remove");
	}

    /**
     * 导出提出人信息数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TjsmmTechnologyMember> list = (List<TjsmmTechnologyMember>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologyMember", "query", "list");
        ExcelUtil<TjsmmTechnologyMember> util = new ExcelUtil<>(TjsmmTechnologyMember.class);
        String title = "数据导出表";
		util.setSheet("提出人信息");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出提出人信息模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TjsmmTechnologyMember> listExcel = new ArrayList<>();
        ExcelUtil<TjsmmTechnologyMember> util = new ExcelUtil<>(TjsmmTechnologyMember.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }

    /**
     * 获取组织
     */
    @GetMapping("/getOrg")
    @ResponseBody
    public AjaxResult getOrg(@RequestParam("userCode") String userCode) throws IOException {
        ADOrg adOrg = SOrgUtil.getMainOrgByUserCode(userCode);
        Map<String, Object> map = BeanUtil.beanToMap(adOrg);

        return AjaxResult.success(map);
    }

    /**
     * 修改提出人信息（暂时）
     */
    @GetMapping("/editTable")
    public String editTable(ModelMap modelMap){
        modelMap.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix +"/technologyMember";
    }
}
