package com.baosight.bsvkkj.js.mm.business;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.common.js.domain.TjsmmChangeApplication;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;

import java.util.*;

import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.vo.ChangeApplicationVO;
import com.baosight.bsvkkj.js.mm.vo.TechnologySecretVO;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.dto.ADUser;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.ad.utils.SUserUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 技术秘密
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Component
public class BusinessJSMMTechnologySecret extends BusinessBase {

    private static final String NAME_SPACE = "tjsmmTechnologySecret";

    private static final String CUSTOM_NAME_SPACE = "JSMMTechnologySecret";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TechnologySecretVO initBean(String operator) {
        TechnologySecretVO technologySecretVO = new TechnologySecretVO();

        // 自动获取
        technologySecretVO.setTechnologyId(BizIdUtil.INSTANCE.nextId());
        technologySecretVO.setContactPersonCode(operator);
        technologySecretVO.setContactPersonName(operator);
        technologySecretVO.setAuthDate(DateUtil.formatDate(new Date()));

        technologySecretVO.setOwnershipNum("0");

        // 用户与部门
        ADUser adUser = SUserUtil.getUserByUserCode(operator);
        if (ObjectUtil.isNotEmpty(adUser)) {
            technologySecretVO.setContactPersonName(adUser.getUserName());
            ADOrg mainOrgByUserCode = SOrgUtil.getMainOrgByUserCode(adUser.getUserCode());
            if (ObjectUtil.isNotEmpty(mainOrgByUserCode)) {
                technologySecretVO.setFirstDeptCode(mainOrgByUserCode.getOrgCode());
                technologySecretVO.setFirstDeptName(mainOrgByUserCode.getOrgName());
            }
        }

        // 流程相关
        WorkFlow workFlow = new WorkFlow(operator, JSMMConstant.PROCESS_CODE_JSMMRD, JSMMConstant.BUSINESS_TYPE, technologySecretVO.getTechnologyId());

        technologySecretVO.setWorkFlow(workFlow);

        return technologySecretVO;
    }

    /**
     * 查询List
     *
     * @param param
     * @return
     */
    public List<TjsmmTechnologySecret> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TjsmmTechnologySecret> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param technologyId
     * @return
     */
    public TjsmmTechnologySecret query(String technologyId) {
        if (StringUtils.isBlank(technologyId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("technologyId", technologyId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TjsmmTechnologySecret) query.get(0) : null;
    }


    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        String type = (String) param.get("type");
        if ("myApplication".equals(type)){ //我的申请
            param.put("contactPersonCode", operator);
            param.put("displayOrder", "A.UPDATE_DATE DESC");
            return getPage(CUSTOM_NAME_SPACE + ".queryMyApplication", param);
        }else if ("draftApplication".equals(type)){ //申请科技秘密
            param.put("contactPersonCode", operator);
            param.put("displayOrder", "UPDATE_DATE DESC");
            param.put("status",JSMMConstant.PROCESS_STATUS_DRAFT);
            return getPage(CUSTOM_NAME_SPACE + ".inquiry", param);
        }else if ("selectTechSecret".equals(type)){ //选择科技秘密（申请科技秘密变更）
            param.put("contactPersonCode", operator);
            param.put("status",JSMMConstant.PROCESS_STATUS_ENDED);
            return getPage(NAME_SPACE + ".query", param);
        }else if ("inquiry".equals(type)){ //综合查询
            param.put("displayOrder", "UPDATE_DATE DESC");
            return getPage(CUSTOM_NAME_SPACE + ".inquiry", param);
        }

        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param technologySecret
     * @return
     */
    public TjsmmTechnologySecret insert(String operator, TjsmmTechnologySecret technologySecret) {
        technologySecret.initAdd(operator);
        if (StringUtils.isEmpty(technologySecret.getTechnologyId())) {
            technologySecret.setTechnologyId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", technologySecret);
        return technologySecret;
    }

    /**
     * 修改
     *
     * @param operator
     * @param technologySecret
     * @return
     */
    public TjsmmTechnologySecret update(String operator, TjsmmTechnologySecret technologySecret) {
        technologySecret.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", technologySecret);
        return technologySecret;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param technologyId
     * @return
     */
    public TjsmmTechnologySecret deleteLogin(String operator, String technologyId) {
        TjsmmTechnologySecret technologySecret = new TjsmmTechnologySecret();
        technologySecret.setTechnologyId(technologyId);
        technologySecret.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", technologySecret);
        return technologySecret;
    }

    /**
     * 物理删除
     *
     * @param operator
     * @param technologyId
     * @return
     */
    public int delete(String operator, String technologyId) {
        return dao.delete(NAME_SPACE + ".delete", technologyId);
    }

    /**
     * 保存
     *
     * @param operator
     * @param technologySecret
     * @return
     */
    public TjsmmTechnologySecret save(String operator, TjsmmTechnologySecret technologySecret) {
        if (StringUtils.isEmpty(technologySecret.getTechnologyId())) {
            return insert(operator, technologySecret);
        } else {
            TjsmmTechnologySecret techSecret = query(technologySecret.getTechnologyId());
            if (null == techSecret) {
                return insert(operator, technologySecret);
            }
            return update(operator, technologySecret);
        }
    }

    /**
     * 启动流程，更改状态
     */
    public void active(String operator, String technologyId) {
        TjsmmTechnologySecret techSecret = query(technologyId);
        if (null == techSecret) {
            throw new BusinessException("找不到科技秘密信息!");
        }
        techSecret.setStatus(JSMMConstant.PROCESS_STATUS_ACTIVE);
        techSecret.setAuthDate(DateUtil.formatDate(new Date()));
        update(operator, techSecret);
    }

    /**
     * 流程结束
     */
    public void end(String operator, String technologyId) {
        TjsmmTechnologySecret techSecret = query(technologyId);
        if (null == techSecret) {
            throw new BusinessException("找不到科技秘密信息!");
        }
        techSecret.setStatus(JSMMConstant.PROCESS_STATUS_ENDED);
        techSecret.setConfirmDate(DateUtil.formatDate(new Date()));
        techSecret.setConfirmNum(SequenceGenerator.getNextSequence(JSMMConstant.GEN_SEQUENCE_NAME));
        update(operator, techSecret);
    }

    /**
     * 流程结束,否定
     */
    public void deny(String operator, String technologyId) {
        TjsmmTechnologySecret techSecret = query(technologyId);
        if (null == techSecret) {
            throw new BusinessException("找不到科技秘密信息!");
        }
        techSecret.setStatus(JSMMConstant.PROCESS_STATUS_TERMINATION);
        techSecret.setDenyDate(DateUtil.formatDate(new Date()));
        update(operator, techSecret);
    }

    /**
     * 终止流程，更改状态
     */
    public void terminate(String operator, String technologyId) {
        TjsmmTechnologySecret techSecret = query(technologyId);
        if (null == techSecret) {
            throw new BusinessException("找不到科技秘密信息!");
        }
        techSecret.setStatus(JSMMConstant.PROCESS_STATUS_TEMP_TERMINATION);
        update(operator, techSecret);
    }

    /**
     * 详细查询
     */
    public TechnologySecretVO queryDetail(String operator, String technologyId, String taskId) {
        TjsmmTechnologySecret techSecret = query(technologyId);
        WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
        if (null == techSecret) {
            throw new BusinessException("找不到科技秘密信息!");
        }

        TechnologySecretVO techSecretVO = TechnologySecretVO.initParent(techSecret);
        techSecretVO.setWorkFlow(workFlow);

        return techSecretVO;
    }

    /**
     * 已办
     * @param param
     * @return
     */
    public TableDataInfo queryPageYB(Map<String, Object> param) {

        return getPage(CUSTOM_NAME_SPACE + ".queryYB", param);
    }

    /**
     * 保密期限变更
     */
    public void changeEnd(String operator, ChangeApplicationVO changeApplicationVO) {
        TjsmmTechnologySecret techSecret = query(changeApplicationVO.getTechnologyId());
        if (null == techSecret) {
            throw new BusinessException("找不到科技秘密信息！");
        }
        techSecret.setConfidentialityPeriod(changeApplicationVO.getChangedConfidentialityPeriod());
        update(operator, techSecret);
    }

    /**
     * 查询同来源技术秘密
     *
     * @param param
     * @return
     */
    public List<TjsmmTechnologySecret> querySameSourceSecret(Map<String, Object> param) {
        return dao.query(CUSTOM_NAME_SPACE + ".querySameSource", param);
    }
}
