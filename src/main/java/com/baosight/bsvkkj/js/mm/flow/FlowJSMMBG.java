package com.baosight.bsvkkj.js.mm.flow;

import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMChangeApplication;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologySecret;
import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.vo.ChangeApplicationVO;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class FlowJSMMBG {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BusinessJSMMChangeApplication businessJSMMChangeApplication;
    @Resource
    private BusinessJSMMTechnologySecret businessJSMMTechnologySecret;

    public WorkFlow beforeChangeSubmitValidate(String operator, ChangeApplicationVO changeApplicationVO){
        WorkFlow workFlow = changeApplicationVO.getWorkFlow();

        Map<String, Object> variable = workFlow.getVariable();
        if (null == variable) {
            variable = new HashMap<>();
        }

        if (StringUtils.isEmpty(workFlow.getCurrentActivity())) {
            workFlow.setProcessCode(JSMMConstant.PROCESS_CODE_JSMMBG);

            ADOrg firstDept = SOrgUtil.getMainOrgByUserCode(operator);
            if (firstDept == null){
                throw new BusinessException("找不到用户的组织！");
            }
            variable.put("firstDeptCode",firstDept.getOrgCode());
            workFlow.setVariable(variable);
        }

        return workFlow;
    }

    public void afterChangeSubmitValidate(String operator, ChangeApplicationVO changeApplicationVO) {
        WorkFlow workFlow = changeApplicationVO.getWorkFlow();
        if (StringUtils.isBlank(workFlow.getTaskId()) && StringUtils.isBlank(workFlow.getCurrentActivity())){
            return;
        }

        WorkFlow mainWorkFlow = SWorkFlowUtil.getFlowInfoByProcessId(workFlow.getProcessInstanceId());
        if(mainWorkFlow != null){
            if (JSMMConstant.PROCESS_STATUS_ENDED.equals(mainWorkFlow.getFlowState())){
                businessJSMMTechnologySecret.changeEnd(operator, changeApplicationVO);
                businessJSMMChangeApplication.end(operator,changeApplicationVO.getChangeId());
            }
        }
    }
}
