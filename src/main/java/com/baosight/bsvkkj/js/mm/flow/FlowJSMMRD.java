package com.baosight.bsvkkj.js.mm.flow;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologySecret;
import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.vo.ChangeApplicationVO;
import com.baosight.bsvkkj.js.mm.vo.TechnologySecretVO;
import com.baosight.bsvkkj.mp.ad.dto.ADOrg;
import com.baosight.bsvkkj.mp.ad.utils.SOrgUtil;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Component
public class FlowJSMMRD {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BusinessJSMMTechnologySecret businessJSMMTechnologySecret;

    public WorkFlow beforeSecretSubmitValidate(String operator, TechnologySecretVO techSecretVO) throws Exception {
        WorkFlow workFlow = techSecretVO.getWorkFlow();
        Map<String, Object> variable = workFlow.getVariable();
        if (null == variable) {
            variable = new HashMap<>();
        }

        if (StringUtils.isEmpty(workFlow.getCurrentActivity())) {
            workFlow.setProcessCode(JSMMConstant.PROCESS_CODE_JSMMRD);

            ADOrg firstDept = SOrgUtil.getMainOrgByUserCode(operator);
            if (firstDept == null) {
                throw new BusinessException("找不到用户的组织！");
            }
            variable.put("firstDeptCode", firstDept.getOrgCode());
        }

        if (!StringUtils.isEmpty(techSecretVO.getAdminReviewDecision())) {
            if (techSecretVO.getAdminReviewDecision().equals("Y")) {
                variable.put("isTg", 1);
            } else {
                variable.put("isTg", 0);
            }
        }

        workFlow.setVariable(variable);
        return workFlow;
    }

    public void afterSecretSubmitValidate(String operator, TechnologySecretVO techSecretVO) throws Exception {
        WorkFlow workFlow = techSecretVO.getWorkFlow();
        if (StringUtils.isBlank(workFlow.getTaskId()) && StringUtils.isBlank(workFlow.getCurrentActivity())) {
            return;
        }

        WorkFlow mainWorkFlow = SWorkFlowUtil.getFlowInfoByProcessId(workFlow.getProcessInstanceId());
        if (mainWorkFlow != null) {
            if (JSMMConstant.PROCESS_STATUS_ENDED.equals(mainWorkFlow.getFlowState())) {
                Integer isTg = (Integer) mainWorkFlow.getVariable().get("isTg");
                if(isTg == 1){
                    businessJSMMTechnologySecret.end(operator, techSecretVO.getTechnologyId());
                }else {
                    businessJSMMTechnologySecret.deny(operator, techSecretVO.getTechnologyId());
                }
            }
        }
    }

}
