package com.baosight.bsvkkj.js.mm.business;

import com.baosight.bsvkkj.common.exception.BusinessException;
import com.baosight.bsvkkj.common.js.domain.TjsmmChangeApplication;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.vo.ChangeApplicationVO;
import com.baosight.bsvkkj.js.mm.vo.TechnologySecretVO;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 变更申请
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Component
public class BusinessJSMMChangeApplication extends BusinessBase {

    private static final String NAME_SPACE = "tjsmmChangeApplication";

    private static final String CUSTOM_NAME_SPACE = "JSMMChangeApplication";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public ChangeApplicationVO initBean(String operator) {
        TjsmmChangeApplication changeApplication = new TjsmmChangeApplication();
        changeApplication.setChangeId(BizIdUtil.INSTANCE.nextId());
        ChangeApplicationVO changeApplicationVO = ChangeApplicationVO.initParent(changeApplication);

        //流程相关
        WorkFlow workFlow = new WorkFlow(operator, JSMMConstant.PROCESS_CODE_JSMMBG, JSMMConstant.BUSINESS_TYPE, changeApplication.getChangeId());
        changeApplicationVO.setWorkFlow(workFlow);

        return changeApplicationVO;
    }

    /**
     * 查询List
     *
     * @param param
     * @return
     */
    public List<TjsmmChangeApplication> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TjsmmChangeApplication> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param changeId
     * @return
     */
    public TjsmmChangeApplication query(String changeId) {
        if (StringUtils.isBlank(changeId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("changeId", changeId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TjsmmChangeApplication) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        String type = (String) param.get("type");
        if ("draftApplication".equals(type)){
            param.put("status",JSMMConstant.PROCESS_STATUS_DRAFT);
            param.put("contactPersonCode",operator);
            return getPage(CUSTOM_NAME_SPACE + ".queryMyApplication", param);
        }else if ("myApplication".equals(type)){
            param.put("contactPersonCode", operator);
            param.put("displayOrder", "A.UPDATE_DATE DESC");
            return getPage(CUSTOM_NAME_SPACE + ".queryMyWFApplication", param);
        }
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param changeApplication
     * @return
     */
    public TjsmmChangeApplication insert(String operator, TjsmmChangeApplication changeApplication) {
        changeApplication.initAdd(operator);
        if (StringUtils.isEmpty(changeApplication.getChangeId())) {
            changeApplication.setChangeId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", changeApplication);
        return changeApplication;
    }

    /**
     * 修改
     *
     * @param operator
     * @param changeApplication
     * @return
     */
    public TjsmmChangeApplication update(String operator, TjsmmChangeApplication changeApplication) {
        changeApplication.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", changeApplication);
        return changeApplication;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param changeId
     * @return
     */
    public TjsmmChangeApplication deleteLogin(String operator, String changeId) {
        TjsmmChangeApplication changeApplication = new TjsmmChangeApplication();
        changeApplication.setChangeId(changeId);
        changeApplication.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", changeApplication);
        return changeApplication;
    }

    /**
     * 物理删除
     *
     * @param operator
     * @param changeId
     * @return
     */
    public int delete(String operator, String changeId) {
        return dao.delete(NAME_SPACE + ".delete", changeId);
    }

    /**
     * 保存
     *
     * @param operator
     * @param changeApplication
     * @return
     */
    public TjsmmChangeApplication save(String operator, TjsmmChangeApplication changeApplication) {
        if (StringUtils.isEmpty(changeApplication.getChangeId())) {
            return insert(operator, changeApplication);
        } else {
            if (null == query(changeApplication.getChangeId())) {
                return insert(operator, changeApplication);
            }
            return update(operator, changeApplication);
        }
    }

    /**
     * 启动流程，更改状态
     */
    public void active(String operator, String changeId) {
        TjsmmChangeApplication changeApplication = query(changeId);
        if (null == changeApplication) {
            throw new BusinessException("找不到变更申请信息!");
        }
        changeApplication.setStatus(JSMMConstant.PROCESS_STATUS_ACTIVE);
        update(operator, changeApplication);
    }

    /**
     * 终止流程，更改状态
     */
    public void termination(String operator, String changeId) {
        TjsmmChangeApplication changeApplication = query(changeId);
        if (null == changeApplication) {
            throw new BusinessException("找不到科技秘密信息!");
        }
        changeApplication.setStatus(JSMMConstant.PROCESS_STATUS_TERMINATION);
        update(operator, changeApplication);
    }

    /**
     * 详细查询
     */
    public ChangeApplicationVO queryDetail(String operator, String changeId, String taskId) {
        TjsmmChangeApplication changeApplication = query(changeId);
        WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
        if (null == changeApplication) {
            throw new BusinessException("找不到变更申请信息!");
        }

        ChangeApplicationVO changeApplicationVO = ChangeApplicationVO.initParent(changeApplication);
        changeApplicationVO.setWorkFlow(workFlow);

        return changeApplicationVO;
    }

    /**
     * 变更结束
     */
    public void end(String operator, String changeId) {
        TjsmmChangeApplication changeApplication = query(changeId);
        if (null == changeApplication) {
            throw new BusinessException("找不到对应的变更申请！");
        }
        changeApplication.setStatus(JSMMConstant.PROCESS_STATUS_ENDED);
        update(operator, changeApplication);
    }

}
