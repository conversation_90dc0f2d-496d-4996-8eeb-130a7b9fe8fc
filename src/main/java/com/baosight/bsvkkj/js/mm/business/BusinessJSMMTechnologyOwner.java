package com.baosight.bsvkkj.js.mm.business;

import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyOwner;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bsvkkj.utils.BizIdUtil;
import com.baosight.bsvkkj.common.business.BusinessBase;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

/**
 * 权利人单位
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Component
public class BusinessJSMMTechnologyOwner extends BusinessBase {

    private static final String NAME_SPACE = "tjsmmTechnologyOwner";

    /**
     * 初始化
     *
     * @param operator
     * @return
     */
    public TjsmmTechnologyOwner initBean(String operator) {
        TjsmmTechnologyOwner technologyOwner = new TjsmmTechnologyOwner();
        return technologyOwner;
    }

    /**
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
    public List<TjsmmTechnologyOwner> queryList(Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List
     *
     * @param operator
     * @param param
     * @return
     */
    public List<TjsmmTechnologyOwner> queryList(String operator, Map<String, Object> param) {
        return dao.query(NAME_SPACE + ".query", param);
    }

    /**
     * 查询
     *
     * @param techOwnerId
     * @return
     */
    public TjsmmTechnologyOwner query(String techOwnerId) {
        if (StringUtils.isBlank(techOwnerId)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("techOwnerId", techOwnerId);
        List query = dao.query(NAME_SPACE + ".query", hashMap);
        return query != null && !query.isEmpty() ? (TjsmmTechnologyOwner) query.get(0) : null;
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo queryPage(Map<String, Object> param) {
        return getPage(NAME_SPACE + ".query", param);
    }

    /**
     * 查询List 分页
     *
     * @param operator
     * @param param
     * @return
     */
    public TableDataInfo queryPage(String operator, Map<String, Object> param) {
        return queryPage(param);
    }

    /**
     * 新增记录
     *
     * @param operator
     * @param technologyOwner
     * @return
     */
    public TjsmmTechnologyOwner insert(String operator, TjsmmTechnologyOwner technologyOwner) {
        technologyOwner.initAdd(operator);
        if (StringUtils.isEmpty(technologyOwner.getTechOwnerId())) {
            technologyOwner.setTechOwnerId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", technologyOwner);
        return technologyOwner;
    }

    /**
     * 修改
	 *
	 * @param operator
     * @param technologyOwner
     * @return
     */
    public TjsmmTechnologyOwner update(String operator, TjsmmTechnologyOwner technologyOwner) {
        technologyOwner.initUpdate(operator);
        dao.update(NAME_SPACE + ".update", technologyOwner);
        return technologyOwner;
    }

    /**
     * 逻辑删除
     *
     * @param operator
     * @param techOwnerId
     * @return
     */
    public TjsmmTechnologyOwner deleteLogin(String operator, String techOwnerId) {
        TjsmmTechnologyOwner technologyOwner = new TjsmmTechnologyOwner();
        technologyOwner.setTechOwnerId(techOwnerId);
        technologyOwner.initLogicDel(operator);
        dao.update(NAME_SPACE + ".update", technologyOwner);
        return technologyOwner;
    }

    /**
     * 物理删除
     *
	 * @param operator
     * @param techOwnerId
     * @return
     */
    public int delete(String operator, String techOwnerId) {
        return dao.delete(NAME_SPACE + ".delete", techOwnerId);
    }

    /**
     * 按technologyId删除
     */
    public int deleteByTechId(String technologyId){
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("technologyId", technologyId);
        return dao.delete(NAME_SPACE + ".deleteByC", hashMap);
    }

}
