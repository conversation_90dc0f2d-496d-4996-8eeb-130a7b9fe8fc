package com.baosight.bsvkkj.js.mm.enums;

public class JSMMConstant {

    public static final String BUSINESS_TYPE = "JSMM";
    //秘密认定申请
    public static final String PROCESS_CODE_JSMMRD="JSMMRD01";
    //秘密变更申请
    public static final String PROCESS_CODE_JSMMBG="JSMMBG01";

    //流程状态相关 : ended:结束,active:活动,termination:终止,tempTermination:终止可恢复,suspended:挂起
    public static final String PROCESS_STATUS_DRAFT="draft";
    public static final String PROCESS_STATUS_ACTIVE="active";
    public static final String PROCESS_STATUS_TERMINATION="termination";
    public static final String PROCESS_STATUS_TEMP_TERMINATION="tempTermination";
    public static final String PROCESS_STATUS_ENDED="ended";
    public static final String PROCESS_STATUS_SUSPENDED="suspended";


    //流程编码
    public static final String PROCESS_JSMMRD_DRAFT="Manual1";
    public static final String PROCESS_JSMMRD_DW_ADMIN_REVIEW ="Manual2";
    public static final String PROCESS_JSMMRD_DW_LEADER_REVIEW="Manual3";
    public static final String PROCESS_JSMMRD_ZH_ADMIN_REVIEW="Manual4";
    public static final String PROCESS_JSMMRD_ZH_LEADER_REVIEW="Manual5";

    //序列号
    public static final String GEN_SEQUENCE_NAME="JSMM_CONFIRM_NO";
}
