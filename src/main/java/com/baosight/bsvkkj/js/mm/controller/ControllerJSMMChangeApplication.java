package com.baosight.bsvkkj.js.mm.controller;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.vo.ChangeApplicationVO;
import com.baosight.bsvkkj.js.mm.vo.TechnologySecretVO;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmChangeApplication;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;

/**
 * 变更申请Controller
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Controller
@RequestMapping("/jsmm/changeApplication")
public class ControllerJSMMChangeApplication extends BaseController {
    private String prefix = "/jsmm/changeApplication";

    @GetMapping()
    public String changeApplication() {
        return prefix + "/changeApplicationList";
    }

    @GetMapping("/draft")
    public String changeApplicationDraft() {
        return prefix + "/changeApplicationDraftList";
    }

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix + "/" + pageNo;
    }

    /**
     * 查询变更申请列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody Map<String, Object> map) {
        map.putAll(paramMap());
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMChangeApplication", "page");
        return getDataTable(query);
    }

    /**
     * 新增变更申请
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap) {
        modelMap.put("changeApplicationVO", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceJSMMChangeApplication", "initLoad", "changeApplicationVO"));
        return prefix + "/changeApplication";
    }

    /**
     * 修改变更申请
     */
    @GetMapping("/edit/{changeId}")
    public String edit(@PathVariable("changeId") String changeId,Boolean readOnly, ModelMap modelMap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeId", changeId);

        modelMap.put("changeApplicationVO", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMChangeApplication", "load", "changeApplicationVO"));
        return prefix + "/changeApplication";
    }

    /**
     * 修改变更申请
     */
    @GetMapping("/detail/{changeId}")
    public String detail(@PathVariable("changeId") String changeId,ModelMap modelMap) {
        modelMap.put("readOnly", true);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeId", changeId);

        modelMap.put("changeApplicationVO", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMChangeApplication", "detail", "changeApplicationVO"));
        return prefix + "/changeApplicationReadOnly";
    }

    /**
     * 保存变更申请
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated ChangeApplicationVO changeApplicationVO) {
        if (null == SWorkFlowUtil.getMainFlowInfoByBusinessId(changeApplicationVO.getChangeId())) {
            changeApplicationVO.setStatus(JSMMConstant.PROCESS_STATUS_DRAFT);
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeApplicationVO", changeApplicationVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMChangeApplication", "save");
    }

    /**
     * 删除变更申请
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeId", ids);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMChangeApplication", "remove");
    }

    /**
     * 导出变更申请数据列表
     */
    @PostMapping("/export")
    @ResponseBody
    public void export(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        List<TjsmmChangeApplication> list = (List<TjsmmChangeApplication>) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMChangeApplication", "query", "list");
        ExcelUtil<TjsmmChangeApplication> util = new ExcelUtil<>(TjsmmChangeApplication.class);
        String title = "数据导出表";
        util.setSheet("变更申请");
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(list).write(response.getOutputStream());
    }

    /**
     * 导出变更申请模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TjsmmChangeApplication> listExcel = new ArrayList<>();
        ExcelUtil<TjsmmChangeApplication> util = new ExcelUtil<>(TjsmmChangeApplication.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }

    /**
     * 修改变更申请
     */
    @GetMapping("/selectTechSecret")
    public String selectTechSecret(ModelMap modelMap) {
        modelMap.putAll(paramMap());
        return prefix + "/selectTechSecret";
    }

    /**
     * 提交流程
     */
    @PostMapping("/doSubmit")
    @ResponseBody
    public AjaxResult doSubmit(@Validated ChangeApplicationVO changeApplicationVO) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeApplicationVO", changeApplicationVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMChangeApplication", "doSubmit");
    }


    /**
     * 获取下一步提交相关操作
     */
    @PostMapping("/getNextSubmitWF")
    @ResponseBody
    public AjaxResult getNextSubmitWF(@Validated ChangeApplicationVO changeApplicationVO) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeApplicationVO", changeApplicationVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMChangeApplication", "getNextSubmitWF", "workFlow");
    }


    /**
     * 待办详细页面
     */
    @GetMapping("/queryDBDetail/{pageNo}/{changeId}/{taskId}")
    public String queryDBDetail(@PathVariable("pageNo") String pageNo,
                                @PathVariable("changeId") String changeId,
                                @PathVariable("taskId") String taskId,
                                ModelMap modelMap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("changeId", changeId);
        eiInfo.set("taskId", taskId);
        ChangeApplicationVO changeApplicationVO = (ChangeApplicationVO) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMChangeApplication", "queryDBDetail", "changeApplicationVO");

//        if ("DW_ADMIN_REVIEW".equals(pageNo)
//                || "DW_LEADER_REVIEW".equals(pageNo)
//                || "ZH_LEADER_REVIEW".equals(pageNo)) {
//            modelMap.put("needComment", true);
//            pageNo = "changeApplication";
//        } else if ("ZH_ADMIN_REVIEW".equals(pageNo)) {
//            modelMap.put("adminReview", true);
//            pageNo = "changeApplication";
//        }
        modelMap.put("needComment", true);
        pageNo = "changeApplication";

        modelMap.put("changeApplicationVO", changeApplicationVO);
        return prefix + "/" + pageNo;
    }
}
