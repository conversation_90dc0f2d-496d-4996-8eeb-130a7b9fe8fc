package com.baosight.bsvkkj.js.mm.controller;

import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;

import com.baosight.bsvkkj.utils.ServiceUtil;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 合同变更Controller
 *
 * <AUTHOR>
 * @date 2023-09-08
 */
@Controller
@RequestMapping("/jsmm/workFlow")
public class ControllerJSMMWorkFlow extends BaseController{
    private String prefix = "/jsmm";

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix + "/" + pageNo;
    }

    /**
     * 根据节点查询对应的待办数
     *
     * @param req
     * @param model
     * @return
     */
    @GetMapping("/JSMMDB00")
    public String queryBusinessTypeDbList(HttpServletRequest req, ModelMap model) {
        Map<String,String> query = paramMap();
        query.put("businessType", JSMMConstant.BUSINESS_TYPE);
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, query);
        EiInfo outInfo = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMWorkFlow", "queryModuleDbList");
        outInfo.setAttr(paramMap());
        model.put("ei", outInfo);
        return prefix + "/JSMMDB00";
    }
    /**
     * 待办列表
     * @param map
     * @return
     */
    @PostMapping("/pageDB")
    @ResponseBody
    public TableDataInfo pageDB(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMWorkFlow", "pageDB");
        return getDataTable(query);
    }
}
