package com.baosight.bsvkkj.js.mm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.service.PageService;
import org.springframework.stereotype.Service;
import com.baosight.bsvkkj.js.mm.business.BusinessJSMMTechnologyMember;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologyMember;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 提出人信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class ServiceJSMMTechnologyMember extends PageService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BusinessJSMMTechnologyMember businessJSMMTechnologyMember;

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TjsmmTechnologyMember bean = businessJSMMTechnologyMember.initBean(loginName);
            inInfo.set("technologyMember", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TjsmmTechnologyMember> queryList = businessJSMMTechnologyMember.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            String technologyId = (String) inInfo.get("technologyId");
            if (technologyId.isEmpty()) {
                queryData.put("technologyId", "-1");
            } else {
                queryData.put("technologyId", technologyId);
            }

            TableDataInfo queryPage = businessJSMMTechnologyMember.queryPage(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo save(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            String technologyId = (String) inInfo.get("technologyId");
            List<TjsmmTechnologyMember> memberList = (List<TjsmmTechnologyMember>) inInfo.get("technologyMemberList");
            businessJSMMTechnologyMember.deleteByTechId(technologyId);

            for (TjsmmTechnologyMember member : memberList) {
                member.setTechMemberId(null);
                member.setTechnologyId(technologyId);
                businessJSMMTechnologyMember.insert(operator, member);
            }
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String techMemberId = (String) inInfo.get("techMemberId");
            TjsmmTechnologyMember query = businessJSMMTechnologyMember.query(techMemberId);
            inInfo.set("technologyMember", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String techMemberId = (String) inInfo.get("techMemberId");
            if (StrUtil.isNotBlank(techMemberId)) {
                for (String id : techMemberId.split(",")) {
                    businessJSMMTechnologyMember.delete(loginName, id);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
