package com.baosight.bsvkkj.js.mm.vo;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class TechnologySecretVO extends TjsmmTechnologySecret {

    private static final long serialVersionUID = 1L;

    public static TechnologySecretVO initParent(TjsmmTechnologySecret techSecret){
        TechnologySecretVO techSecretVO = new TechnologySecretVO();
        BeanUtil.copyProperties(techSecret, techSecretVO, false);
        return techSecretVO;
    }

    private Map<String,String> attachmentS;

    private WorkFlow workFlow;

    private String adminReviewDecision;
}
