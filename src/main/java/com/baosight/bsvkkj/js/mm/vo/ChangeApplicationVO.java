package com.baosight.bsvkkj.js.mm.vo;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmChangeApplication;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;
import com.baosight.bsvkkj.mp.wf.dto.WorkFlow;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ChangeApplicationVO extends TjsmmChangeApplication {

    private static final long serialVersionUID = 1L;

    public static ChangeApplicationVO initParent(TjsmmChangeApplication changeApplication){
        ChangeApplicationVO changeApplicationVO = new ChangeApplicationVO();
        BeanUtil.copyProperties(changeApplication, changeApplicationVO, false);
        changeApplicationVO.setTechSecret(new TjsmmTechnologySecret());
        return changeApplicationVO;
    }

    private TjsmmTechnologySecret techSecret;

    private WorkFlow workFlow;

    private Map<String, String> attachmentS;
}
