<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="JSMMWorkFlow">
    <!-- 模块代码列表 -->
    <select id="queryModuleList" parameterClass="hashmap" resultClass="hashmap">
        SELECT
            A.FLOW_CODE as "processCode" ,
            C.PROCESS_NAME AS "processName" ,
            A.CURRENT_ACTIVITY as "currentActivity" ,
            A.CURRENT_ACTIVITY_NAME as "currentActivityName" ,
            A.BUSINESS_LABEL as "businessLabel" ,
            count(A.CURRENT_ACTIVITY) as "todoNum"
        FROM
            GGMK.T_MPWF_FLOW_INFO A
            JOIN "iplat4j".TEWPT00 B ON B.PROCESS_INSTANCE_ID = A.FLOW_ID
            JOIN "iplat4j".TEWPD01 C ON C.ACT_PROC_DEF_ID = B.ACT_PROCESS_DEF_ID
        WHERE
            B.STATE = 'open'
        <isNotEmpty prepend=" AND " property="processCode">A.FLOW_CODE = #processCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="processCodes">A.FLOW_CODE in($processCodes$)</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessType">business_type in ('JSMM')</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessName">business_name = #businessName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">business_name like '%$businessNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivityName">A.CURRENT_ACTIVITY_NAME = #currentActivityName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivityNameLike">A.CURRENT_ACTIVITY_NAME like
            '%$currentActivityNameLike$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="state">A.FLOW_STATE = #state#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
        GROUP BY A.FLOW_CODE, C.PROCESS_NAME, A.CURRENT_ACTIVITY, A.CURRENT_ACTIVITY_NAME, A.BUSINESS_LABEL
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>


    <!-- 取当前登陆用户的待办 -->
    <select id="queryDB" parameterClass="hashmap" resultClass="hashmap">
        SELECT
            A.FLOW_CODE AS "processCode" ,
            B.TASK_DEF_KEY AS "currentActivity" ,
            B.TASK_NAME AS "currentActivityName" ,
            A.FLOW_ID AS "flowId" ,
            A.BUSINESS_ID AS "businessId" ,
            A.BUSINESS_TYPE AS "businessType" ,
            A.BUSINESS_LABEL AS "businessLabel" ,
            A.BUSINESS_NAME AS "businessName" ,
            A.LAST_OPERATOR AS "lastOperator" ,
            A.LAST_OPERATOR_NAME AS "lastOperatorName" ,
            A.LAST_TIME AS "lastTime" ,
            A.PARENT_FLOW_ID AS "parentFlowId" ,
            A.DEPARTMENT_NO AS "departmentNo" ,
            B.FORM AS "pageNo" ,
            B.PROCESS_INSTANCE_ID AS "processInstanceId" ,
            B.TASK_NAME AS "taskName" ,
            B.TASK_ID AS "taskId" ,
            B.TASK_TYPE AS "taskType"
        FROM
            GGMK.T_MPWF_FLOW_INFO A,
            ${platSchema}.TEWPT00 B
        WHERE
        A.FLOW_ID = B.PROCESS_INSTANCE_ID
        <isNotEmpty prepend=" AND " property="businessType">BUSINESS_TYPE in ('JSMM')</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessLabel">B.BUSINESS_LABEL = #businessLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="userLabel">B.ASSIGNEE_ID = #userLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="assigneeId">B.ASSIGNEE_ID = #assigneeId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="state">B.STATE = #state#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowCode">FLOW_CODE = #flowCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">current_activity = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

   
</sqlMap>