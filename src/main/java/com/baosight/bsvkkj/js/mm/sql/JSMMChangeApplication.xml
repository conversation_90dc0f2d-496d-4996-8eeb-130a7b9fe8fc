<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="JSMMChangeApplication">
    <select id="queryMyApplication"  parameterClass="hashmap" resultClass="hashMap">
        SELECT
        A.CHANGE_ID as "changeId" ,
        A.TECHNOLOGY_ID as "technologyId" ,
        A.CHANGED_CONFIDENTIALITY_PERIOD as "changedConfidentialityPeriod" ,
        A.CHANGE_REASON as "changeReason" ,
        A.STATUS as "status" ,
        B.TECHNOLOGY_NAME as "technologyName" ,
        B.SECRET_LEVEL as "secretLevel" ,
        B.CONFIRM_NUM as "confirmNum" ,
        B.CONFIDENTIALITY_PERIOD as "confidentialityPeriod"
        FROM ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION A
        LEFT JOIN ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET B ON A.TECHNOLOGY_ID = B.TECHNOLOGY_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="changeId">A.CHANGE_ID = #changeId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="technologyId">A.TECHNOLOGY_ID = #technologyId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="changedConfidentialityPeriod">A.CHANGED_CONFIDENTIALITY_PERIOD =
                #changedConfidentialityPeriod#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="changeReason">A.CHANGE_REASON = #changeReason#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">A.STATUS = #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="contactPersonCode">B.CONTACT_PERSON_CODE = #contactPersonCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="technologyNameLike">B.TECHNOLOGY_NAME LIKE '%$technologyNameLike$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryMyWFApplication"  parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.CHANGE_ID as "changeId" ,
        A.TECHNOLOGY_ID as "technologyId" ,
        A.CHANGED_CONFIDENTIALITY_PERIOD as "changedConfidentialityPeriod" ,
        A.CHANGE_REASON as "changeReason" ,
        A.STATUS as "status" ,
        B.LAST_TIME as "lastTime",
        B.CURRENT_OPERATOR as "currentOperator",
        C.CONFIRM_NUM as "confirmNum",
        C.TECHNOLOGY_NAME as "technologyName"
        FROM ${zzzcSchema}.T_JSMM_CHANGE_APPLICATION A,
        ${ggmkSchema}.T_MPWF_FLOW_INFO B,
        ${zzzcSchema}.T_JSMM_TECHNOLOGY_SECRET C
        WHERE
        A.CHANGE_ID = B.BUSINESS_ID AND A.TECHNOLOGY_ID = C.TECHNOLOGY_ID AND B.BUSINESS_TYPE = 'JSMM'
        <isNotEmpty prepend=" AND " property="changeId">A.CHANGE_ID = #changeId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="technologyId">A.TECHNOLOGY_ID = #technologyId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">A.STATUS = #status#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="confirmNum">C.CONFIRM_NUM = #confirmNum#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="technologyNameLike">C.TECHNOLOGY_NAME LIKE '%$technologyNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

</sqlMap>