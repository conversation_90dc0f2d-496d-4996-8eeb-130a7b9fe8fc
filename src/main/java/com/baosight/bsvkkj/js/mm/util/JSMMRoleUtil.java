package com.baosight.bsvkkj.js.mm.util;

import com.baosight.bsvkkj.mp.ad.utils.SRoleUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Component;

@Component("JSMMRoleUtil")
public class JSMMRoleUtil {
    private static final String DW_ADMIN = "JSMM_DW_ADMIN";
    private static final String DW_LEADER = "JSMM_DW_LEADER";
    private static final String ZH_ADMIN = "JSMM_ZH_ADMIN";
    private static final String ZH_LEADER = "JSMM_ZH_LEADER";

    public static Boolean isZHAdmin(){
        String userCode = UserSession.getLoginName();
        return SRoleUtil.isRoleMember(ZH_ADMIN, userCode);
    }
}
