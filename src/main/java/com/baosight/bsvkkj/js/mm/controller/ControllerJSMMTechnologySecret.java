package com.baosight.bsvkkj.js.mm.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baosight.bsvkkj.common.controller.BaseController;
import com.baosight.bsvkkj.common.domain.AjaxResult;
import com.baosight.bsvkkj.common.domain.TableDataInfo;
import com.baosight.bsvkkj.common.domain.Ztree;
import com.baosight.bsvkkj.js.mm.enums.JSMMConstant;
import com.baosight.bsvkkj.js.mm.vo.TechnologySecretVO;
import com.baosight.bsvkkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bsvkkj.utils.ExportFileUtils;
import com.baosight.bsvkkj.utils.excel.ExcelUtil;
import com.baosight.bsvkkj.common.js.domain.TjsmmTechnologySecret;
import com.baosight.bsvkkj.utils.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;

/**
 * 技术秘密Controller
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Controller
@RequestMapping("/jsmm/technologySecret")
public class ControllerJSMMTechnologySecret extends BaseController {
    private String prefix = "/jsmm/technologySecret";

    @GetMapping()
    public String technologySecret() {
        return prefix + "/technologySecretList";
    }

    @GetMapping("/draft")
    public String technologySecretDraft() {
        return prefix + "/technologySecretDraftList";
    }

    @GetMapping("/myYB")
    public String myYBList() {return prefix + "/technologySecretYB";}

    @GetMapping("/inquiry")
    public String inquiry() {return prefix + "/technologySecretInquiry";}

    /**
     * 通用跳转页面
     *
     * @param pageNo
     * @param model
     * @return
     */
    @GetMapping("/toPage/{pageNo}")
    public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
        model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
        return prefix + "/" + pageNo;
    }

    /**
     * 查询技术秘密列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody Map<String, Object> map) {
        map.putAll(paramMap());
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMTechnologySecret", "page");
        return getDataTable(query);
    }

    /**
     * 新增技术秘密
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap) {
        modelMap.put("technologySecretVO", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceJSMMTechnologySecret", "initLoad", "technologySecretVO"));
        return prefix + "/technologySecret";
    }

    /**
     * 修改技术秘密
     */
    @GetMapping("/edit/{technologyId}")
    public String edit(@PathVariable("technologyId") String technologyId,ModelMap modelMap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", technologyId);

        modelMap.put("technologySecretVO", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologySecret", "load", "technologySecretVO"));
        return prefix + "/technologySecret";
    }

    /**
     * 技术秘密详情
     */
    @GetMapping("/detail/{technologyId}")
    public String detail(@PathVariable("technologyId") String technologyId,ModelMap modelMap) {
        modelMap.putAll(paramMap());
        modelMap.put("readOnly", true);
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", technologyId);

        modelMap.put("technologySecretVO", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologySecret", "load", "technologySecretVO"));
        return prefix + "/technologySecretReadOnly";
    }

    /**
     * 保存技术秘密
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TechnologySecretVO technologySecretVO) {
        if (null == SWorkFlowUtil.getMainFlowInfoByBusinessId(technologySecretVO.getTechnologyId())) {
            technologySecretVO.setStatus(JSMMConstant.PROCESS_STATUS_DRAFT);
        }
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologySecretVO", technologySecretVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologySecret", "save", "technologySecretId");
    }

    /**
     * 删除技术秘密
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", ids);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologySecret", "remove");
    }

    /**
     * 导出技术秘密模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) throws IOException {
        List<TjsmmTechnologySecret> listExcel = new ArrayList<>();
        ExcelUtil<TjsmmTechnologySecret> util = new ExcelUtil<>(TjsmmTechnologySecret.class);
        String title = "模板名称";
        util.setSheet(title);
        String filename = util.encodingFilename(title);
        ExportFileUtils.setAttachmentResponseHeader(response, filename);
        util.initExcel(listExcel).write(response.getOutputStream());
    }

    /**
     * 流程提交
     */
    @PostMapping("/doSubmit")
    @ResponseBody
    public AjaxResult doSubmit(@Validated TechnologySecretVO technologySecretVO) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologySecretVO", technologySecretVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologySecret", "doSubmit");
    }

    /**
     * 获取下一步提交相关操作
     */
    @PostMapping("/getNextSubmitWF")
    @ResponseBody
    public AjaxResult getNextSubmitWF(@Validated TechnologySecretVO technologySecretVO) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologySecretVO", technologySecretVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologySecret", "getNextSubmitWF", "workFlow");
    }

    /**
     * 待办详细页面
     */
    @GetMapping("/queryDBDetail/{pageNo}/{technologyId}/{taskId}")
    public String queryDBDetail(@PathVariable("pageNo") String pageNo,
                                @PathVariable("technologyId") String technologyId,
                                @PathVariable("taskId") String taskId,
                                ModelMap modelMap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", technologyId);
        eiInfo.set("taskId", taskId);
        TechnologySecretVO techSecretVO = (TechnologySecretVO) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologySecret", "queryDBDetail", "technologySecretVO");

        if ("DW_ADMIN_REVIEW".equals(pageNo)
                || "DW_LEADER_REVIEW".equals(pageNo)) {
            modelMap.put("needComment", true);
            pageNo = "technologySecretReadOnly";
        }else if ("ZH_LEADER_REVIEW".equals(pageNo)){
            modelMap.put("needComment", true);
            pageNo = "technologySecret";
        }else if ("ZH_ADMIN_REVIEW".equals(pageNo)) {
            modelMap.put("adminReview", true);

            //专家评审是否结束
            EiInfo param = new EiInfo();
            param.set("bizId", technologyId);
            param.set("moduleCode", "jsmm_rdps");
            param.set(EiConstant.serviceId, "S_MP_PS_02");
            EiInfo result = XServiceManager.call(param);
            List<Map<String,Object>> list = (List<Map<String, Object>>) result.get("list");
            if (list == null || list.isEmpty()){
                //未开始
                modelMap.put("psStatus",0);
            }else{
                Map<String, Object> psMap = list.get(0);
                Object isEnd = psMap.get("isEnd");
                if ("1".equals(isEnd)){
                    //已结束
                    modelMap.put("psStatus",2);
                }else {
                    //未结束
                    modelMap.put("psStatus",1);
                }
            }
            modelMap.put("needComment", true);
            pageNo = "technologySecret";
        }

        modelMap.put("technologySecretVO", techSecretVO);
        return prefix + "/" + pageNo;
    }

    /**
     * 流程退回
     */
    @PostMapping("/doReturn")
    @ResponseBody
    public AjaxResult doReturn(@Validated TechnologySecretVO technologySecretVO) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologySecretVO", technologySecretVO);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceJSMMTechnologySecret", "doReturn");
    }

    /**
     * 处理过的申请
     */
    @PostMapping("/pageYB")
    @ResponseBody
    public TableDataInfo pageYB(@RequestBody Map<String, Object> map) {
        map.putAll(paramMap());
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceJSMMTechnologySecret", "pageYB");
        return getDataTable(query);
    }

    /**
     * 专家评审信息
     */
    @PostMapping("/getPSInfo")
    @ResponseBody
    public String getPSInfo(String technologyId, String moduleCode, ModelMap modelMap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("technologyId", technologyId);
        eiInfo.set("moduleCode", moduleCode);
        return (String) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceJSMMTechnologySecret", "getPSInfo", "psInfo");
    }

    /**
     * 选择-技术领域
     */
    @GetMapping("/selectTechAreaTree")
    public String selectTechAreaTree(String selectType,ModelMap modelMap) {
        modelMap.put("selectType", selectType);
        return prefix + "/techAreaSelect";
    }

    @GetMapping("/techAreaTreeData")
    @ResponseBody
    public List<Ztree> techAreaTreeData() {
        EiInfo eiInfo = new EiInfo();
        return (List<Ztree>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKIZLMaintainTecharea", "tableData", "nodeInfo");
    }

    @GetMapping("/selectSource")
    public String selectSource(ModelMap modelMap) {
        return prefix + "/selectSource";
    }

    @PostMapping("sourceList")
    @ResponseBody
    public TableDataInfo sourceList(@RequestBody Map<String, Object> map) {
        map.putAll(paramMap());
        EiInfo eiInfo = new EiInfo();
        String operator = UserSession.getLoginName();
        eiInfo.set("operator", operator);
        eiInfo.set("tkyxmProject", map);
        eiInfo.set(EiConstant.serviceId, "KY_XM_CX01");
        EiInfo outInfo = XServiceManager.call(eiInfo);
        return getDataTable((List<?>) outInfo.get("tkyxmProjects"));
    }

    @GetMapping("/getSource")
    @ResponseBody
    public AjaxResult getSource(String sourceCode, String technologyId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("sourceCode", sourceCode);
        eiInfo.set("technologyId", technologyId);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        List<TjsmmTechnologySecret> secretList = (List<TjsmmTechnologySecret>) ServiceUtil.xLocalManagerToData(eiInfo,"ServiceJSMMTechnologySecret", "getSameSourceSecret", "secretList");

        EiInfo inInfo = new EiInfo();
        Map<String, Object> param = new HashMap<>();
        param.put("projectCode", sourceCode);
        inInfo.set(EiConstant.queryBlock, param);
        inInfo.set(EiConstant.serviceId, "S_KI_ZL_01");
        EiInfo outInfo = XServiceManager.call(inInfo);

        resultMap.put("secretList", secretList);
        resultMap.put("patentList", outInfo.get("list"));

        return AjaxResult.success(resultMap);
    }
}
